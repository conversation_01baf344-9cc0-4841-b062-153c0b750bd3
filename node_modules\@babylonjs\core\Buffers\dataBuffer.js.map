{"version": 3, "file": "dataBuffer.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Buffers/dataBuffer.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,OAAO,UAAU;IAcnB;;OAEG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC;IAChB,CAAC;IAOD;;OAEG;IACH;QA1BA;;WAEG;QACI,eAAU,GAAW,CAAC,CAAC;QAC9B,qDAAqD;QAC9C,aAAQ,GAAW,CAAC,CAAC;QAC5B;;WAEG;QACI,aAAQ,GAAY,KAAK,CAAC;QAkB7B,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;IAC1C,CAAC;;AA9Bc,mBAAQ,GAAG,CAAC,CAAC", "sourcesContent": ["/**\r\n * Class used to store gfx data (like WebGLBuffer)\r\n */\r\nexport class DataBuffer {\r\n    private static _Counter = 0;\r\n\r\n    /**\r\n     * Gets or sets the number of objects referencing this buffer\r\n     */\r\n    public references: number = 0;\r\n    /** Gets or sets the size of the underlying buffer */\r\n    public capacity: number = 0;\r\n    /**\r\n     * Gets or sets a boolean indicating if the buffer contains 32bits indices\r\n     */\r\n    public is32Bits: boolean = false;\r\n\r\n    /**\r\n     * Gets the underlying buffer\r\n     */\r\n    public get underlyingResource(): any {\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets the unique id of this buffer\r\n     */\r\n    public readonly uniqueId: number;\r\n\r\n    /**\r\n     * Constructs the buffer\r\n     */\r\n    constructor() {\r\n        this.uniqueId = DataBuffer._Counter++;\r\n    }\r\n}\r\n"]}