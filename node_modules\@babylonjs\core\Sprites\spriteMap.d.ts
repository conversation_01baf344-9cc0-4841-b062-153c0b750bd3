import type { IDisposable, Scene } from "../scene";
import { Vector2, Vector3 } from "../Maths/math.vector";
import { Texture } from "../Materials/Textures/texture";
import { RawTexture } from "../Materials/Textures/rawTexture";
import type { ISpriteJSONSprite, ISpriteJSONAtlas } from "./ISprites";
import "../Shaders/spriteMap.fragment";
import "../Shaders/spriteMap.vertex";
/**
 * Defines the basic options interface of a SpriteMap
 */
export interface ISpriteMapOptions {
    /**
     * Vector2 of the number of cells in the grid.
     */
    stageSize?: Vector2;
    /**
     * Vector2 of the size of the output plane in World Units.
     */
    outputSize?: Vector2;
    /**
     * Vector3 of the position of the output plane in World Units.
     */
    outputPosition?: Vector3;
    /**
     * Vector3 of the rotation of the output plane.
     */
    outputRotation?: Vector3;
    /**
     * number of layers that the system will reserve in resources.
     */
    layerCount?: number;
    /**
     * number of max animation frames a single cell will reserve in resources.
     */
    maxAnimationFrames?: number;
    /**
     * number cell index of the base tile when the system compiles.
     */
    baseTile?: number;
    /**
     * boolean flip the sprite after its been repositioned by the framing data.
     */
    flipU?: boolean;
    /**
     * Vector3 scalar of the global RGB values of the SpriteMap.
     */
    colorMultiply?: Vector3;
}
/**
 * Defines the IDisposable interface in order to be cleanable from resources.
 */
export interface ISpriteMap extends IDisposable {
    /**
     * String name of the SpriteMap.
     */
    name: string;
    /**
     * The JSON Array file from a https://www.codeandweb.com/texturepacker export.  Or similar structure.
     */
    atlasJSON: ISpriteJSONAtlas;
    /**
     * Texture of the SpriteMap.
     */
    spriteSheet: Texture;
    /**
     * The parameters to initialize the SpriteMap with.
     */
    options: ISpriteMapOptions;
}
/**
 * Class used to manage a grid restricted sprite deployment on an Output plane.
 */
export declare class SpriteMap implements ISpriteMap {
    /** The Name of the spriteMap */
    name: string;
    /** The JSON file with the frame and meta data */
    atlasJSON: ISpriteJSONAtlas;
    /** The systems Sprite Sheet Texture */
    spriteSheet: Texture;
    /** Arguments passed with the Constructor */
    options: ISpriteMapOptions;
    /** Public Sprite Storage array, parsed from atlasJSON */
    sprites: Array<ISpriteJSONSprite>;
    /** Returns the Number of Sprites in the System */
    get spriteCount(): number;
    /** Returns the Position of Output Plane*/
    get position(): Vector3;
    /** Returns the Position of Output Plane*/
    set position(v: Vector3);
    /** Returns the Rotation of Output Plane*/
    get rotation(): Vector3;
    /** Returns the Rotation of Output Plane*/
    set rotation(v: Vector3);
    /** Sets the AnimationMap*/
    get animationMap(): RawTexture;
    /** Sets the AnimationMap*/
    set animationMap(v: RawTexture);
    /** Scene that the SpriteMap was created in */
    private _scene;
    /** Texture Buffer of Float32 that holds tile frame data*/
    private _frameMap;
    /** Texture Buffers of Float32 that holds tileMap data*/
    private _tileMaps;
    /** Texture Buffer of Float32 that holds Animation Data*/
    private _animationMap;
    /** Custom ShaderMaterial Central to the System*/
    private _material;
    /** Custom ShaderMaterial Central to the System*/
    private _output;
    /** Systems Time Ticker*/
    private _time;
    /**
     * Creates a new SpriteMap
     * @param name defines the SpriteMaps Name
     * @param atlasJSON is the JSON file that controls the Sprites Frames and Meta
     * @param spriteSheet is the Texture that the Sprites are on.
     * @param options a basic deployment configuration
     * @param scene The Scene that the map is deployed on
     */
    constructor(name: string, atlasJSON: ISpriteJSONAtlas, spriteSheet: Texture, options: ISpriteMapOptions, scene: Scene);
    /**
     * Returns tileID location
     * @returns Vector2 the cell position ID
     */
    getTileID(): Vector2;
    /**
     * Gets the UV location of the mouse over the SpriteMap.
     * @returns Vector2 the UV position of the mouse interaction
     */
    getMousePosition(): Vector2;
    /**
     * Creates the "frame" texture Buffer
     * -------------------------------------
     * Structure of frames
     *  "filename": "Falling-Water-2.png",
     * "frame": {"x":69,"y":103,"w":24,"h":32},
     * "rotated": true,
     * "trimmed": true,
     * "spriteSourceSize": {"x":4,"y":0,"w":24,"h":32},
     * "sourceSize": {"w":32,"h":32}
     * @returns RawTexture of the frameMap
     */
    private _createFrameBuffer;
    /**
     * Creates the tileMap texture Buffer
     * @param buffer normally and array of numbers, or a false to generate from scratch
     * @param _layer indicates what layer for a logic trigger dealing with the baseTile.  The system uses this
     * @returns RawTexture of the tileMap
     */
    private _createTileBuffer;
    /**
     * Modifies the data of the tileMaps
     * @param _layer is the ID of the layer you want to edit on the SpriteMap
     * @param pos is the iVector2 Coordinates of the Tile
     * @param tile The SpriteIndex of the new Tile
     */
    changeTiles(_layer: number | undefined, pos: Vector2 | Vector2[], tile?: number): void;
    /**
     * Creates the animationMap texture Buffer
     * @param buffer normally and array of numbers, or a false to generate from scratch
     * @returns RawTexture of the animationMap
     */
    private _createTileAnimationBuffer;
    /**
     * Modifies the data of the animationMap
     * @param cellID is the Index of the Sprite
     * @param _frame is the target Animation frame
     * @param toCell is the Target Index of the next frame of the animation
     * @param time is a value between 0-1 that is the trigger for when the frame should change tiles
     * @param speed is a global scalar of the time variable on the map.
     */
    addAnimationToTile(cellID?: number, _frame?: number, toCell?: number, time?: number, speed?: number): void;
    /**
     * Exports the .tilemaps file
     */
    saveTileMaps(): void;
    /**
     * Imports the .tilemaps file
     * @param url of the .tilemaps file
     */
    loadTileMaps(url: string): void;
    /**
     * Release associated resources
     */
    dispose(): void;
}
