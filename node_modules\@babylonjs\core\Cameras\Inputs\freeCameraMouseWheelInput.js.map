{"version": 3, "file": "freeCameraMouseWheelInput.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Cameras/Inputs/freeCameraMouseWheelInput.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAElD,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AACrE,OAAO,EAAE,yBAAyB,EAAE,MAAM,gDAAgD,CAAC;AAC3F,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAC1D,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAEnD,gEAAgE;AAChE,IAAK,eAIJ;AAJD,WAAK,eAAe;IAChB,qEAAY,CAAA;IACZ,yEAAc,CAAA;IACd,+DAAS,CAAA;AACb,CAAC,EAJI,eAAe,KAAf,eAAe,QAInB;AAED;;;GAGG;AACH,MAAM,OAAO,yBAA0B,SAAQ,yBAAyB;IAAxE;;QAuSY,kBAAa,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC/B,oBAAe,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACjC,eAAU,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAEpC;;WAEG;QACK,kBAAa,GAA8B,eAAe,CAAC,YAAY,CAAC;QACxE,4BAAuB,GAAyB,UAAU,CAAC,CAAC,CAAC;QAC7D,kBAAa,GAA8B,eAAe,CAAC,YAAY,CAAC;QACxE,4BAAuB,GAAyB,UAAU,CAAC,CAAC,CAAC;QAC7D,kBAAa,GAA8B,IAAI,CAAC;QAChD,4BAAuB,GAAyB,IAAI,CAAC;IA6DjE,CAAC;IA1WG;;;OAGG;IACI,YAAY;QACf,OAAO,2BAA2B,CAAC;IACvC,CAAC;IAED;;;;OAIG;IAEH,IAAW,kBAAkB,CAAC,IAA0B;QACpD,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,YAAY,EAAE;YACtE,+CAA+C;YAC/C,OAAO;SACV;QACD,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC,YAAY,CAAC;QAClD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACH,IAAW,kBAAkB;QACzB,IAAI,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,YAAY,EAAE;YACrD,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED;;;;OAIG;IAEH,IAAW,kBAAkB,CAAC,IAA0B;QACpD,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,YAAY,EAAE;YACtE,+CAA+C;YAC/C,OAAO;SACV;QACD,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC,YAAY,CAAC;QAClD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACH,IAAW,kBAAkB;QACzB,IAAI,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,YAAY,EAAE;YACrD,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED;;;;OAIG;IAEH,IAAW,kBAAkB,CAAC,IAA0B;QACpD,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,YAAY,EAAE;YACtE,+CAA+C;YAC/C,OAAO;SACV;QACD,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC,YAAY,CAAC;QAClD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACH,IAAW,kBAAkB;QACzB,IAAI,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,YAAY,EAAE;YACrD,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED;;;;OAIG;IAEH,IAAW,oBAAoB,CAAC,IAA0B;QACtD,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,cAAc,EAAE;YACxE,+CAA+C;YAC/C,OAAO;SACV;QACD,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC,cAAc,CAAC;QACpD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACH,IAAW,oBAAoB;QAC3B,IAAI,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,cAAc,EAAE;YACvD,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED;;;;OAIG;IAEH,IAAW,oBAAoB,CAAC,IAA0B;QACtD,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,cAAc,EAAE;YACxE,+CAA+C;YAC/C,OAAO;SACV;QACD,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC,cAAc,CAAC;QACpD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACH,IAAW,oBAAoB;QAC3B,IAAI,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,cAAc,EAAE;YACvD,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED;;;;OAIG;IAEH,IAAW,oBAAoB,CAAC,IAA0B;QACtD,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,cAAc,EAAE;YACxE,+CAA+C;YAC/C,OAAO;SACV;QACD,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC,cAAc,CAAC;QACpD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACH,IAAW,oBAAoB;QAC3B,IAAI,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,cAAc,EAAE;YACvD,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED;;;;OAIG;IAEH,IAAW,eAAe,CAAC,IAA0B;QACjD,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,SAAS,EAAE;YACnE,+CAA+C;YAC/C,OAAO;SACV;QACD,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC,SAAS,CAAC;QAC/C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACH,IAAW,eAAe;QACtB,IAAI,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,SAAS,EAAE;YAClD,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED;;;;OAIG;IAEH,IAAW,eAAe,CAAC,IAA0B;QACjD,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,SAAS,EAAE;YACnE,+CAA+C;YAC/C,OAAO;SACV;QACD,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC,SAAS,CAAC;QAC/C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACH,IAAW,eAAe;QACtB,IAAI,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,SAAS,EAAE;YAClD,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED;;;;OAIG;IAEH,IAAW,eAAe,CAAC,IAA0B;QACjD,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,SAAS,EAAE;YACnE,+CAA+C;YAC/C,OAAO;SACV;QACD,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC,SAAS,CAAC;QAC/C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACH,IAAW,eAAe;QACtB,IAAI,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,SAAS,EAAE;YAClD,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED;;OAEG;IACI,WAAW;QACd,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,EAAE;YAC9E,OAAO;SACV;QAED,yDAAyD;QACzD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAE1B,oDAAoD;QACpD,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,oBAAoB,EAAE;YAC7C,iDAAiD;YACjD,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;SAC9B;QAED,+DAA+D;QAC/D,MAAM,qBAAqB,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QAC5C,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC;QAE/D,MAAM,oBAAoB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC5C,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,EAAE,qBAAqB,EAAE,oBAAoB,CAAC,CAAC;QAE9F,oCAAoC;QACpC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,GAAG,CAAC;QAC7D,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,GAAG,CAAC;QAC7D,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC;QAC7D,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAExD,yEAAyE;QACzE,KAAK,CAAC,WAAW,EAAE,CAAC;IACxB,CAAC;IAgBD;;;OAGG;IACK,aAAa;QACjB,4DAA4D;QAC5D,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAChG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAChG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;IACpG,CAAC;IAED;;;;;OAKG;IACK,qBAAqB;IACzB,wBAAwB;IACxB,KAAa;IACb,oCAAoC;IACpC,cAAyC;IACzC,4CAA4C;IAC5C,UAAgC;QAEhC,IAAI,KAAK,KAAK,CAAC,EAAE;YACb,6BAA6B;YAC7B,OAAO;SACV;QACD,IAAI,cAAc,KAAK,IAAI,IAAI,UAAU,KAAK,IAAI,EAAE;YAChD,mCAAmC;YACnC,OAAO;SACV;QAED,IAAI,MAAM,GAAG,IAAI,CAAC;QAClB,QAAQ,cAAc,EAAE;YACpB,KAAK,eAAe,CAAC,YAAY;gBAC7B,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC;gBAC5B,MAAM;YACV,KAAK,eAAe,CAAC,cAAc;gBAC/B,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC;gBAC9B,MAAM;YACV,KAAK,eAAe,CAAC,SAAS;gBAC1B,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;gBACzB,MAAM;SACb;QAED,QAAQ,UAAU,EAAE;YAChB,KAAK,UAAU,CAAC,CAAC;gBACb,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACxB,MAAM;YACV,KAAK,UAAU,CAAC,CAAC;gBACb,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;gBACxB,MAAM;YACV,KAAK,UAAU,CAAC,CAAC;gBACb,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;gBACxB,MAAM;SACb;IACL,CAAC;CACJ;AA5VG;IADC,SAAS,EAAE;mEAQX;AAoBD;IADC,SAAS,EAAE;mEAQX;AAoBD;IADC,SAAS,EAAE;mEAQX;AAoBD;IADC,SAAS,EAAE;qEAQX;AAoBD;IADC,SAAS,EAAE;qEAQX;AAoBD;IADC,SAAS,EAAE;qEAQX;AAoBD;IADC,SAAS,EAAE;gEAQX;AAoBD;IADC,SAAS,EAAE;gEAQX;AAoBD;IADC,SAAS,EAAE;gEAQX;AA+HC,gBAAiB,CAAC,2BAA2B,CAAC,GAAG,yBAAyB,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { serialize } from \"../../Misc/decorators\";\r\nimport type { FreeCamera } from \"../../Cameras/freeCamera\";\r\nimport { CameraInputTypes } from \"../../Cameras/cameraInputsManager\";\r\nimport { BaseCameraMouseWheelInput } from \"../../Cameras/Inputs/BaseCameraMouseWheelInput\";\r\nimport { Matrix, Vector3 } from \"../../Maths/math.vector\";\r\nimport { Coordinate } from \"../../Maths/math.axis\";\r\n\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nenum _CameraProperty {\r\n    MoveRelative,\r\n    RotateRelative,\r\n    MoveScene,\r\n}\r\n\r\n/**\r\n * Manage the mouse wheel inputs to control a free camera.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n */\r\nexport class FreeCameraMouseWheelInput extends BaseCameraMouseWheelInput {\r\n    /**\r\n     * Defines the camera the input is attached to.\r\n     */\r\n    public camera: FreeCamera;\r\n\r\n    /**\r\n     * Gets the class name of the current input.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"FreeCameraMouseWheelInput\";\r\n    }\r\n\r\n    /**\r\n     * Set which movement axis (relative to camera's orientation) the mouse\r\n     * wheel's X axis controls.\r\n     * @param axis The axis to be moved. Set null to clear.\r\n     */\r\n    @serialize()\r\n    public set wheelXMoveRelative(axis: Nullable<Coordinate>) {\r\n        if (axis === null && this._wheelXAction !== _CameraProperty.MoveRelative) {\r\n            // Attempting to clear different _wheelXAction.\r\n            return;\r\n        }\r\n        this._wheelXAction = _CameraProperty.MoveRelative;\r\n        this._wheelXActionCoordinate = axis;\r\n    }\r\n\r\n    /**\r\n     * Get the configured movement axis (relative to camera's orientation) the\r\n     * mouse wheel's X axis controls.\r\n     * @returns The configured axis or null if none.\r\n     */\r\n    public get wheelXMoveRelative(): Nullable<Coordinate> {\r\n        if (this._wheelXAction !== _CameraProperty.MoveRelative) {\r\n            return null;\r\n        }\r\n        return this._wheelXActionCoordinate;\r\n    }\r\n\r\n    /**\r\n     * Set which movement axis (relative to camera's orientation) the mouse\r\n     * wheel's Y axis controls.\r\n     * @param axis The axis to be moved. Set null to clear.\r\n     */\r\n    @serialize()\r\n    public set wheelYMoveRelative(axis: Nullable<Coordinate>) {\r\n        if (axis === null && this._wheelYAction !== _CameraProperty.MoveRelative) {\r\n            // Attempting to clear different _wheelYAction.\r\n            return;\r\n        }\r\n        this._wheelYAction = _CameraProperty.MoveRelative;\r\n        this._wheelYActionCoordinate = axis;\r\n    }\r\n\r\n    /**\r\n     * Get the configured movement axis (relative to camera's orientation) the\r\n     * mouse wheel's Y axis controls.\r\n     * @returns The configured axis or null if none.\r\n     */\r\n    public get wheelYMoveRelative(): Nullable<Coordinate> {\r\n        if (this._wheelYAction !== _CameraProperty.MoveRelative) {\r\n            return null;\r\n        }\r\n        return this._wheelYActionCoordinate;\r\n    }\r\n\r\n    /**\r\n     * Set which movement axis (relative to camera's orientation) the mouse\r\n     * wheel's Z axis controls.\r\n     * @param axis The axis to be moved. Set null to clear.\r\n     */\r\n    @serialize()\r\n    public set wheelZMoveRelative(axis: Nullable<Coordinate>) {\r\n        if (axis === null && this._wheelZAction !== _CameraProperty.MoveRelative) {\r\n            // Attempting to clear different _wheelZAction.\r\n            return;\r\n        }\r\n        this._wheelZAction = _CameraProperty.MoveRelative;\r\n        this._wheelZActionCoordinate = axis;\r\n    }\r\n\r\n    /**\r\n     * Get the configured movement axis (relative to camera's orientation) the\r\n     * mouse wheel's Z axis controls.\r\n     * @returns The configured axis or null if none.\r\n     */\r\n    public get wheelZMoveRelative(): Nullable<Coordinate> {\r\n        if (this._wheelZAction !== _CameraProperty.MoveRelative) {\r\n            return null;\r\n        }\r\n        return this._wheelZActionCoordinate;\r\n    }\r\n\r\n    /**\r\n     * Set which rotation axis (relative to camera's orientation) the mouse\r\n     * wheel's X axis controls.\r\n     * @param axis The axis to be moved. Set null to clear.\r\n     */\r\n    @serialize()\r\n    public set wheelXRotateRelative(axis: Nullable<Coordinate>) {\r\n        if (axis === null && this._wheelXAction !== _CameraProperty.RotateRelative) {\r\n            // Attempting to clear different _wheelXAction.\r\n            return;\r\n        }\r\n        this._wheelXAction = _CameraProperty.RotateRelative;\r\n        this._wheelXActionCoordinate = axis;\r\n    }\r\n\r\n    /**\r\n     * Get the configured rotation axis (relative to camera's orientation) the\r\n     * mouse wheel's X axis controls.\r\n     * @returns The configured axis or null if none.\r\n     */\r\n    public get wheelXRotateRelative(): Nullable<Coordinate> {\r\n        if (this._wheelXAction !== _CameraProperty.RotateRelative) {\r\n            return null;\r\n        }\r\n        return this._wheelXActionCoordinate;\r\n    }\r\n\r\n    /**\r\n     * Set which rotation axis (relative to camera's orientation) the mouse\r\n     * wheel's Y axis controls.\r\n     * @param axis The axis to be moved. Set null to clear.\r\n     */\r\n    @serialize()\r\n    public set wheelYRotateRelative(axis: Nullable<Coordinate>) {\r\n        if (axis === null && this._wheelYAction !== _CameraProperty.RotateRelative) {\r\n            // Attempting to clear different _wheelYAction.\r\n            return;\r\n        }\r\n        this._wheelYAction = _CameraProperty.RotateRelative;\r\n        this._wheelYActionCoordinate = axis;\r\n    }\r\n\r\n    /**\r\n     * Get the configured rotation axis (relative to camera's orientation) the\r\n     * mouse wheel's Y axis controls.\r\n     * @returns The configured axis or null if none.\r\n     */\r\n    public get wheelYRotateRelative(): Nullable<Coordinate> {\r\n        if (this._wheelYAction !== _CameraProperty.RotateRelative) {\r\n            return null;\r\n        }\r\n        return this._wheelYActionCoordinate;\r\n    }\r\n\r\n    /**\r\n     * Set which rotation axis (relative to camera's orientation) the mouse\r\n     * wheel's Z axis controls.\r\n     * @param axis The axis to be moved. Set null to clear.\r\n     */\r\n    @serialize()\r\n    public set wheelZRotateRelative(axis: Nullable<Coordinate>) {\r\n        if (axis === null && this._wheelZAction !== _CameraProperty.RotateRelative) {\r\n            // Attempting to clear different _wheelZAction.\r\n            return;\r\n        }\r\n        this._wheelZAction = _CameraProperty.RotateRelative;\r\n        this._wheelZActionCoordinate = axis;\r\n    }\r\n\r\n    /**\r\n     * Get the configured rotation axis (relative to camera's orientation) the\r\n     * mouse wheel's Z axis controls.\r\n     * @returns The configured axis or null if none.\r\n     */\r\n    public get wheelZRotateRelative(): Nullable<Coordinate> {\r\n        if (this._wheelZAction !== _CameraProperty.RotateRelative) {\r\n            return null;\r\n        }\r\n        return this._wheelZActionCoordinate;\r\n    }\r\n\r\n    /**\r\n     * Set which movement axis (relative to the scene) the mouse wheel's X axis\r\n     * controls.\r\n     * @param axis The axis to be moved. Set null to clear.\r\n     */\r\n    @serialize()\r\n    public set wheelXMoveScene(axis: Nullable<Coordinate>) {\r\n        if (axis === null && this._wheelXAction !== _CameraProperty.MoveScene) {\r\n            // Attempting to clear different _wheelXAction.\r\n            return;\r\n        }\r\n        this._wheelXAction = _CameraProperty.MoveScene;\r\n        this._wheelXActionCoordinate = axis;\r\n    }\r\n\r\n    /**\r\n     * Get the configured movement axis (relative to the scene) the mouse wheel's\r\n     * X axis controls.\r\n     * @returns The configured axis or null if none.\r\n     */\r\n    public get wheelXMoveScene(): Nullable<Coordinate> {\r\n        if (this._wheelXAction !== _CameraProperty.MoveScene) {\r\n            return null;\r\n        }\r\n        return this._wheelXActionCoordinate;\r\n    }\r\n\r\n    /**\r\n     * Set which movement axis (relative to the scene) the mouse wheel's Y axis\r\n     * controls.\r\n     * @param axis The axis to be moved. Set null to clear.\r\n     */\r\n    @serialize()\r\n    public set wheelYMoveScene(axis: Nullable<Coordinate>) {\r\n        if (axis === null && this._wheelYAction !== _CameraProperty.MoveScene) {\r\n            // Attempting to clear different _wheelYAction.\r\n            return;\r\n        }\r\n        this._wheelYAction = _CameraProperty.MoveScene;\r\n        this._wheelYActionCoordinate = axis;\r\n    }\r\n\r\n    /**\r\n     * Get the configured movement axis (relative to the scene) the mouse wheel's\r\n     * Y axis controls.\r\n     * @returns The configured axis or null if none.\r\n     */\r\n    public get wheelYMoveScene(): Nullable<Coordinate> {\r\n        if (this._wheelYAction !== _CameraProperty.MoveScene) {\r\n            return null;\r\n        }\r\n        return this._wheelYActionCoordinate;\r\n    }\r\n\r\n    /**\r\n     * Set which movement axis (relative to the scene) the mouse wheel's Z axis\r\n     * controls.\r\n     * @param axis The axis to be moved. Set null to clear.\r\n     */\r\n    @serialize()\r\n    public set wheelZMoveScene(axis: Nullable<Coordinate>) {\r\n        if (axis === null && this._wheelZAction !== _CameraProperty.MoveScene) {\r\n            // Attempting to clear different _wheelZAction.\r\n            return;\r\n        }\r\n        this._wheelZAction = _CameraProperty.MoveScene;\r\n        this._wheelZActionCoordinate = axis;\r\n    }\r\n\r\n    /**\r\n     * Get the configured movement axis (relative to the scene) the mouse wheel's\r\n     * Z axis controls.\r\n     * @returns The configured axis or null if none.\r\n     */\r\n    public get wheelZMoveScene(): Nullable<Coordinate> {\r\n        if (this._wheelZAction !== _CameraProperty.MoveScene) {\r\n            return null;\r\n        }\r\n        return this._wheelZActionCoordinate;\r\n    }\r\n\r\n    /**\r\n     * Called for each rendered frame.\r\n     */\r\n    public checkInputs(): void {\r\n        if (this._wheelDeltaX === 0 && this._wheelDeltaY === 0 && this._wheelDeltaZ == 0) {\r\n            return;\r\n        }\r\n\r\n        // Clear the camera properties that we might be updating.\r\n        this._moveRelative.setAll(0);\r\n        this._rotateRelative.setAll(0);\r\n        this._moveScene.setAll(0);\r\n\r\n        // Set the camera properties that are to be updated.\r\n        this._updateCamera();\r\n\r\n        if (this.camera.getScene().useRightHandedSystem) {\r\n            // TODO: Does this need done for worldUpdate too?\r\n            this._moveRelative.z *= -1;\r\n        }\r\n\r\n        // Convert updates relative to camera to world position update.\r\n        const cameraTransformMatrix = Matrix.Zero();\r\n        this.camera.getViewMatrix().invertToRef(cameraTransformMatrix);\r\n\r\n        const transformedDirection = Vector3.Zero();\r\n        Vector3.TransformNormalToRef(this._moveRelative, cameraTransformMatrix, transformedDirection);\r\n\r\n        // Apply updates to camera position.\r\n        this.camera.cameraRotation.x += this._rotateRelative.x / 200;\r\n        this.camera.cameraRotation.y += this._rotateRelative.y / 200;\r\n        this.camera.cameraDirection.addInPlace(transformedDirection);\r\n        this.camera.cameraDirection.addInPlace(this._moveScene);\r\n\r\n        // Call the base class implementation to handle observers and do cleanup.\r\n        super.checkInputs();\r\n    }\r\n\r\n    private _moveRelative = Vector3.Zero();\r\n    private _rotateRelative = Vector3.Zero();\r\n    private _moveScene = Vector3.Zero();\r\n\r\n    /**\r\n     * These are set to the desired default behaviour.\r\n     */\r\n    private _wheelXAction: Nullable<_CameraProperty> = _CameraProperty.MoveRelative;\r\n    private _wheelXActionCoordinate: Nullable<Coordinate> = Coordinate.X;\r\n    private _wheelYAction: Nullable<_CameraProperty> = _CameraProperty.MoveRelative;\r\n    private _wheelYActionCoordinate: Nullable<Coordinate> = Coordinate.Z;\r\n    private _wheelZAction: Nullable<_CameraProperty> = null;\r\n    private _wheelZActionCoordinate: Nullable<Coordinate> = null;\r\n\r\n    /**\r\n     * Update the camera according to any configured properties for the 3\r\n     * mouse-wheel axis.\r\n     */\r\n    private _updateCamera(): void {\r\n        // Do the camera updates for each of the 3 touch-wheel axis.\r\n        this._updateCameraProperty(this._wheelDeltaX, this._wheelXAction, this._wheelXActionCoordinate);\r\n        this._updateCameraProperty(this._wheelDeltaY, this._wheelYAction, this._wheelYActionCoordinate);\r\n        this._updateCameraProperty(this._wheelDeltaZ, this._wheelZAction, this._wheelZActionCoordinate);\r\n    }\r\n\r\n    /**\r\n     * Update one property of the camera.\r\n     * @param value\r\n     * @param cameraProperty\r\n     * @param coordinate\r\n     */\r\n    private _updateCameraProperty(\r\n        /* Mouse-wheel delta. */\r\n        value: number,\r\n        /* Camera property to be changed. */\r\n        cameraProperty: Nullable<_CameraProperty>,\r\n        /* Axis of Camera property to be changed. */\r\n        coordinate: Nullable<Coordinate>\r\n    ): void {\r\n        if (value === 0) {\r\n            // Mouse wheel has not moved.\r\n            return;\r\n        }\r\n        if (cameraProperty === null || coordinate === null) {\r\n            // Mouse wheel axis not configured.\r\n            return;\r\n        }\r\n\r\n        let action = null;\r\n        switch (cameraProperty) {\r\n            case _CameraProperty.MoveRelative:\r\n                action = this._moveRelative;\r\n                break;\r\n            case _CameraProperty.RotateRelative:\r\n                action = this._rotateRelative;\r\n                break;\r\n            case _CameraProperty.MoveScene:\r\n                action = this._moveScene;\r\n                break;\r\n        }\r\n\r\n        switch (coordinate) {\r\n            case Coordinate.X:\r\n                action.set(value, 0, 0);\r\n                break;\r\n            case Coordinate.Y:\r\n                action.set(0, value, 0);\r\n                break;\r\n            case Coordinate.Z:\r\n                action.set(0, 0, value);\r\n                break;\r\n        }\r\n    }\r\n}\r\n\r\n(<any>CameraInputTypes)[\"FreeCameraMouseWheelInput\"] = FreeCameraMouseWheelInput;\r\n"]}