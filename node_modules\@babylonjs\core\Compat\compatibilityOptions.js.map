{"version": 3, "file": "compatibilityOptions.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Compat/compatibilityOptions.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,OAAO,oBAAoB;;AAC7B;;GAEG;AACW,8CAAyB,GAAG,KAAK,CAAC", "sourcesContent": ["/**\r\n * Options used to control default behaviors regarding compatibility support\r\n */\r\nexport class CompatibilityOptions {\r\n    /**\r\n     * Defines if the system should use OpenGL convention for UVs when creating geometry or loading .babylon files (false by default)\r\n     */\r\n    public static UseOpenGLOrientationForUV = false;\r\n}\r\n"]}