{"version": 3, "file": "freeCameraTouchInput.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Cameras/Inputs/freeCameraTouchInput.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAIlD,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AAGrE,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAC1D,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEzC;;;GAGG;AACH,MAAM,OAAO,oBAAoB;IAkC7B;;;;OAIG;IACH;IACI;;OAEG;IACI,aAAa,KAAK;QAAlB,eAAU,GAAV,UAAU,CAAQ;QArC7B;;;WAGG;QAEI,4BAAuB,GAAW,QAAQ,CAAC;QAElD;;;WAGG;QAEI,yBAAoB,GAAW,KAAK,CAAC;QAE5C;;WAEG;QACI,uBAAkB,GAAY,KAAK,CAAC;QAEnC,aAAQ,GAAqB,IAAI,CAAC;QAClC,aAAQ,GAAqB,IAAI,CAAC;QAElC,oBAAe,GAAG,IAAI,KAAK,EAAU,CAAC;QAiB1C,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;IACtC,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,gBAA0B;QAC3C,8CAA8C;QAC9C,gBAAgB,GAAG,KAAK,CAAC,gCAAgC,CAAC,SAAS,CAAC,CAAC;QACrE,IAAI,gBAAgB,GAAuC,IAAI,CAAC;QAEhE,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE;YAClC,IAAI,CAAC,YAAY,GAAG,GAAG,EAAE;gBACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACzB,CAAC,CAAC;YAEF,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,EAAE,EAAE;gBACvB,MAAM,GAAG,GAAkB,CAAC,CAAC,KAAK,CAAC;gBAEnC,MAAM,YAAY,GAAG,GAAG,CAAC,WAAW,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,OAAO,GAAG,CAAC,WAAW,KAAK,WAAW,CAAC,CAAC;gBAE/G,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,YAAY,EAAE;oBAClC,OAAO;iBACV;gBAED,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW,EAAE;oBAC1C,IAAI,CAAC,gBAAgB,EAAE;wBACnB,GAAG,CAAC,cAAc,EAAE,CAAC;qBACxB;oBAED,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;oBAEzC,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;wBACnC,OAAO;qBACV;oBAED,gBAAgB,GAAG;wBACf,CAAC,EAAE,GAAG,CAAC,OAAO;wBACd,CAAC,EAAE,GAAG,CAAC,OAAO;qBACjB,CAAC;iBACL;qBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,SAAS,EAAE;oBAC/C,IAAI,CAAC,gBAAgB,EAAE;wBACnB,GAAG,CAAC,cAAc,EAAE,CAAC;qBACxB;oBAED,MAAM,KAAK,GAAW,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;oBAElE,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;wBACd,OAAO;qBACV;oBACD,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;oBAEtC,IAAI,KAAK,IAAI,CAAC,EAAE;wBACZ,OAAO;qBACV;oBACD,gBAAgB,GAAG,IAAI,CAAC;oBACxB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;oBACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;iBACxB;qBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW,EAAE;oBACjD,IAAI,CAAC,gBAAgB,EAAE;wBACnB,GAAG,CAAC,cAAc,EAAE,CAAC;qBACxB;oBAED,IAAI,CAAC,gBAAgB,EAAE;wBACnB,OAAO;qBACV;oBAED,MAAM,KAAK,GAAW,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;oBAElE,IAAI,KAAK,IAAI,CAAC,EAAE;wBACZ,OAAO;qBACV;oBAED,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,OAAO,GAAG,gBAAgB,CAAC,CAAC,CAAC;oBACjD,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,OAAO,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;iBACvD;YACL,CAAC,CAAC;SACL;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM;aACvB,QAAQ,EAAE;aACV,aAAa,CAAC,yBAAyB,CAAC,IAAI,CAAC,aAAa,EAAE,iBAAiB,CAAC,WAAW,GAAG,iBAAiB,CAAC,SAAS,GAAG,iBAAiB,CAAC,WAAW,CAAC,CAAC;QAE9J,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YACvC,MAAM,OAAO,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;YACzC,OAAO,IAAI,OAAO,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;SAClE;IACL,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,4BAA4B,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAClF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;aACzB;YAED,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBACvC,MAAM,OAAO,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;gBACzC,OAAO,IAAI,OAAO,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;gBAClE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;aAC5B;YACD,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;YAChC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACxB;IACL,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;YAClD,OAAO;SACV;QACD,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE;YAC5C,OAAO;SACV;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC;QAEvE,MAAM,YAAY,GAAG,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAErJ,IAAI,YAAY,EAAE;YACd,MAAM,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC;SAC3E;aAAM;YACH,MAAM,KAAK,GAAG,MAAM,CAAC,wBAAwB,EAAE,CAAC;YAChD,MAAM,SAAS,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,oBAAoB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAE/H,MAAM,CAAC,yBAAyB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,qBAAqB,CAAC,CAAC;YACxG,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,OAAO,CAAC,oBAAoB,CAAC,SAAS,EAAE,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC;SAC5G;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,sBAAsB,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,OAAO,CAAC;IACnB,CAAC;CACJ;AA/LG;IADC,SAAS,EAAE;qEACsC;AAOlD;IADC,SAAS,EAAE;kEACgC;AA0L1C,gBAAiB,CAAC,sBAAsB,CAAC,GAAG,oBAAoB,CAAC", "sourcesContent": ["import { serialize } from \"../../Misc/decorators\";\r\nimport type { Observer, EventState } from \"../../Misc/observable\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { ICameraInput } from \"../../Cameras/cameraInputsManager\";\r\nimport { CameraInputTypes } from \"../../Cameras/cameraInputsManager\";\r\nimport type { FreeCamera } from \"../../Cameras/freeCamera\";\r\nimport type { PointerInfo } from \"../../Events/pointerEvents\";\r\nimport { PointerEventTypes } from \"../../Events/pointerEvents\";\r\nimport { Matrix, Vector3 } from \"../../Maths/math.vector\";\r\nimport { Tools } from \"../../Misc/tools\";\r\nimport type { IPointerEvent } from \"../../Events/deviceInputEvents\";\r\n/**\r\n * Manage the touch inputs to control the movement of a free camera.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n */\r\nexport class FreeCameraTouchInput implements ICameraInput<FreeCamera> {\r\n    /**\r\n     * Defines the camera the input is attached to.\r\n     */\r\n    public camera: FreeCamera;\r\n\r\n    /**\r\n     * Defines the touch sensibility for rotation.\r\n     * The lower the faster.\r\n     */\r\n    @serialize()\r\n    public touchAngularSensibility: number = 200000.0;\r\n\r\n    /**\r\n     * Defines the touch sensibility for move.\r\n     * The lower the faster.\r\n     */\r\n    @serialize()\r\n    public touchMoveSensibility: number = 250.0;\r\n\r\n    /**\r\n     * Swap touch actions so that one touch is used for rotation and multiple for movement\r\n     */\r\n    public singleFingerRotate: boolean = false;\r\n\r\n    private _offsetX: Nullable<number> = null;\r\n    private _offsetY: Nullable<number> = null;\r\n\r\n    private _pointerPressed = new Array<number>();\r\n    private _pointerInput?: (p: PointerInfo, s: EventState) => void;\r\n    private _observer: Nullable<Observer<PointerInfo>>;\r\n    private _onLostFocus: Nullable<(e: FocusEvent) => any>;\r\n    private _isSafari: boolean;\r\n\r\n    /**\r\n     * Manage the touch inputs to control the movement of a free camera.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n     * @param allowMouse Defines if mouse events can be treated as touch events\r\n     */\r\n    constructor(\r\n        /**\r\n         * Define if mouse events can be treated as touch events\r\n         */\r\n        public allowMouse = false\r\n    ) {\r\n        this._isSafari = Tools.IsSafari();\r\n    }\r\n\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public attachControl(noPreventDefault?: boolean): void {\r\n        // eslint-disable-next-line prefer-rest-params\r\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(arguments);\r\n        let previousPosition: Nullable<{ x: number; y: number }> = null;\r\n\r\n        if (this._pointerInput === undefined) {\r\n            this._onLostFocus = () => {\r\n                this._offsetX = null;\r\n                this._offsetY = null;\r\n            };\r\n\r\n            this._pointerInput = (p) => {\r\n                const evt = <IPointerEvent>p.event;\r\n\r\n                const isMouseEvent = evt.pointerType === \"mouse\" || (this._isSafari && typeof evt.pointerType === \"undefined\");\r\n\r\n                if (!this.allowMouse && isMouseEvent) {\r\n                    return;\r\n                }\r\n\r\n                if (p.type === PointerEventTypes.POINTERDOWN) {\r\n                    if (!noPreventDefault) {\r\n                        evt.preventDefault();\r\n                    }\r\n\r\n                    this._pointerPressed.push(evt.pointerId);\r\n\r\n                    if (this._pointerPressed.length !== 1) {\r\n                        return;\r\n                    }\r\n\r\n                    previousPosition = {\r\n                        x: evt.clientX,\r\n                        y: evt.clientY,\r\n                    };\r\n                } else if (p.type === PointerEventTypes.POINTERUP) {\r\n                    if (!noPreventDefault) {\r\n                        evt.preventDefault();\r\n                    }\r\n\r\n                    const index: number = this._pointerPressed.indexOf(evt.pointerId);\r\n\r\n                    if (index === -1) {\r\n                        return;\r\n                    }\r\n                    this._pointerPressed.splice(index, 1);\r\n\r\n                    if (index != 0) {\r\n                        return;\r\n                    }\r\n                    previousPosition = null;\r\n                    this._offsetX = null;\r\n                    this._offsetY = null;\r\n                } else if (p.type === PointerEventTypes.POINTERMOVE) {\r\n                    if (!noPreventDefault) {\r\n                        evt.preventDefault();\r\n                    }\r\n\r\n                    if (!previousPosition) {\r\n                        return;\r\n                    }\r\n\r\n                    const index: number = this._pointerPressed.indexOf(evt.pointerId);\r\n\r\n                    if (index != 0) {\r\n                        return;\r\n                    }\r\n\r\n                    this._offsetX = evt.clientX - previousPosition.x;\r\n                    this._offsetY = -(evt.clientY - previousPosition.y);\r\n                }\r\n            };\r\n        }\r\n\r\n        this._observer = this.camera\r\n            .getScene()\r\n            ._inputManager._addCameraPointerObserver(this._pointerInput, PointerEventTypes.POINTERDOWN | PointerEventTypes.POINTERUP | PointerEventTypes.POINTERMOVE);\r\n\r\n        if (this._onLostFocus) {\r\n            const engine = this.camera.getEngine();\r\n            const element = engine.getInputElement();\r\n            element && element.addEventListener(\"blur\", this._onLostFocus);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    public detachControl(): void {\r\n        if (this._pointerInput) {\r\n            if (this._observer) {\r\n                this.camera.getScene()._inputManager._removeCameraPointerObserver(this._observer);\r\n                this._observer = null;\r\n            }\r\n\r\n            if (this._onLostFocus) {\r\n                const engine = this.camera.getEngine();\r\n                const element = engine.getInputElement();\r\n                element && element.removeEventListener(\"blur\", this._onLostFocus);\r\n                this._onLostFocus = null;\r\n            }\r\n            this._pointerPressed.length = 0;\r\n            this._offsetX = null;\r\n            this._offsetY = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Update the current camera state depending on the inputs that have been used this frame.\r\n     * This is a dynamically created lambda to avoid the performance penalty of looping for inputs in the render loop.\r\n     */\r\n    public checkInputs(): void {\r\n        if (this._offsetX === null || this._offsetY === null) {\r\n            return;\r\n        }\r\n        if (this._offsetX === 0 && this._offsetY === 0) {\r\n            return;\r\n        }\r\n\r\n        const camera = this.camera;\r\n        camera.cameraRotation.y = this._offsetX / this.touchAngularSensibility;\r\n\r\n        const rotateCamera = (this.singleFingerRotate && this._pointerPressed.length === 1) || (!this.singleFingerRotate && this._pointerPressed.length > 1);\r\n\r\n        if (rotateCamera) {\r\n            camera.cameraRotation.x = -this._offsetY / this.touchAngularSensibility;\r\n        } else {\r\n            const speed = camera._computeLocalCameraSpeed();\r\n            const direction = new Vector3(0, 0, this.touchMoveSensibility !== 0 ? (speed * this._offsetY) / this.touchMoveSensibility : 0);\r\n\r\n            Matrix.RotationYawPitchRollToRef(camera.rotation.y, camera.rotation.x, 0, camera._cameraRotationMatrix);\r\n            camera.cameraDirection.addInPlace(Vector3.TransformCoordinates(direction, camera._cameraRotationMatrix));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of the current input.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"FreeCameraTouchInput\";\r\n    }\r\n\r\n    /**\r\n     * Get the friendly name associated with the input class.\r\n     * @returns the input friendly name\r\n     */\r\n    public getSimpleName(): string {\r\n        return \"touch\";\r\n    }\r\n}\r\n\r\n(<any>CameraInputTypes)[\"FreeCameraTouchInput\"] = FreeCameraTouchInput;\r\n"]}