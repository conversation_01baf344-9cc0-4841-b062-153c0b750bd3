{"version": 3, "file": "framingBehavior.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Behaviors/Cameras/framingBehavior.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,eAAe,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AAE1E,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAGnD,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AAGzD,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAE3D,OAAO,EAAE,SAAS,EAAE,MAAM,4BAA4B,CAAC;AAEvD;;;GAGG;AACH,MAAM,OAAO,eAAe;IAA5B;QAQI;;WAEG;QACI,0CAAqC,GAAG,IAAI,UAAU,EAAQ,CAAC;QAE9D,UAAK,GAAG,eAAe,CAAC,mBAAmB,CAAC;QAC5C,iBAAY,GAAG,GAAG,CAAC;QACnB,mBAAc,GAAG,GAAG,CAAC;QACrB,sBAAiB,GAAG,GAAG,CAAC;QACxB,yBAAoB,GAAG,IAAI,CAAC;QAC5B,6BAAwB,GAAG,IAAI,CAAC;QAChC,wBAAmB,GAAG,KAAK,CAAC;QAC5B,iBAAY,GAAG,IAAI,CAAC;QAgI5B;;;WAGG;QACI,0CAAqC,GAAG,IAAI,CAAC;QAO5C,mBAAc,GAAG,KAAK,CAAC;QACvB,yBAAoB,GAAG,CAAC,QAAQ,CAAC;QAyEzC,kBAAkB;QACV,iBAAY,GAAG,IAAI,KAAK,EAAc,CAAC;QACvC,qBAAgB,GAAG,KAAK,CAAC;IAmTrC,CAAC;IA7hBG;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,SAAS,CAAC;IACrB,CAAC;IA0BD;;OAEG;IACH,IAAW,IAAI,CAAC,IAAY;QACxB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,IAAW,WAAW,CAAC,MAAc;QACjC,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,aAAa,CAAC,KAAa;QAClC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACH,IAAW,gBAAgB,CAAC,SAAiB;QACzC,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;IACvC,CAAC;IAED;;;OAGG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;OAGG;IACH,IAAW,mBAAmB,CAAC,KAAa;QACxC,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;IACtC,CAAC;IAED;;;OAGG;IACH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAW,uBAAuB,CAAC,IAAY;QAC3C,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAW,uBAAuB;QAC9B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB,CAAC,IAAa;QACvC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAW,WAAW,CAAC,IAAY;QAC/B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAgBD;;OAEG;IACI,IAAI;QACP,aAAa;IACjB,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,MAAuB;QACjC,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;QAE9C,eAAe,CAAC,cAAc,CAAC,aAAa,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QAEzE,IAAI,CAAC,+BAA+B,GAAG,KAAK,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,EAAE;YACvF,IAAI,cAAc,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW,EAAE;gBACvD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC3B,OAAO;aACV;YAED,IAAI,cAAc,CAAC,IAAI,KAAK,iBAAiB,CAAC,SAAS,EAAE;gBACrD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;aAC/B;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,4BAA4B,GAAG,MAAM,CAAC,6BAA6B,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YAClF,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE;oBAClC,IAAI,CAAC,qCAAqC,CAAC,eAAe,EAAE,CAAC;gBACjE,CAAC,CAAC,CAAC;aACN;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,2BAA2B,GAAG,MAAM,CAAC,4BAA4B,CAAC,GAAG,CAAC,GAAG,EAAE;YAC5E,qGAAqG;YACrG,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAE7B,uGAAuG;YACvG,qDAAqD;YACrD,IAAI,CAAC,0BAA0B,EAAE,CAAC;QACtC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,OAAO;SACV;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;QAE9C,IAAI,IAAI,CAAC,+BAA+B,EAAE;YACtC,KAAK,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;SAC7E;QAED,IAAI,IAAI,CAAC,2BAA2B,EAAE;YAClC,IAAI,CAAC,eAAe,CAAC,4BAA4B,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;SAC9F;QAED,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACnC,IAAI,CAAC,eAAe,CAAC,6BAA6B,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;SAChG;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAChC,CAAC;IASD;;;;;OAKG;IACI,UAAU,CAAC,IAAkB,EAAE,kBAA2B,KAAK,EAAE,iBAAuC,IAAI;QAC/G,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAE9B,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,WAAW,CAAC;QACvD,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,YAAY,EAAE,WAAW,CAAC,YAAY,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC;IACjH,CAAC;IAED;;;;;OAKG;IACI,mBAAmB,CAAC,IAAkB,EAAE,kBAA2B,KAAK,EAAE,iBAAuC,IAAI;QACxH,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAE9B,MAAM,WAAW,GAAG,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;QAC3D,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,GAAG,EAAE,WAAW,CAAC,GAAG,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC;IAC/F,CAAC;IAED;;;;;OAKG;IACI,qBAAqB,CAAC,MAAsB,EAAE,kBAA2B,KAAK,EAAE,iBAAuC,IAAI;QAC9H,MAAM,GAAG,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QAC9E,MAAM,GAAG,GAAG,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAEjF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;YACjE,OAAO,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YACjD,OAAO,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;SACpD;QAED,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,GAAG,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC;IACvE,CAAC;IAED;;;;;;OAMG;IACI,kBAAkB,CAAC,YAAqB,EAAE,YAAqB,EAAE,kBAA2B,KAAK,EAAE,iBAAuC,IAAI;QACjJ,IAAI,UAAmB,CAAC;QAExB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,OAAO;SACV;QAED,sGAAsG;QACtG,MAAM,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC;QAC9B,MAAM,GAAG,GAAG,YAAY,CAAC,CAAC,CAAC;QAC3B,MAAM,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;QAClE,MAAM,WAAW,GAAG,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEnE,IAAI,eAAe,EAAE;YACjB,UAAU,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;SAC/C;aAAM;YACH,MAAM,WAAW,GAAG,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAClD,UAAU,GAAG,IAAI,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;SACvE;QAED,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzB,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC,eAAe,CAAC,QAAQ,EAAE,SAAS,CAAC,qBAAqB,EAAE,EAAE,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC;SACrI;QAED,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,UAAU,GAAG,SAAS,CAAC,YAAY,CAAC,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACpK,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SACtC;QAED,0CAA0C;QAC1C,gEAAgE;QAChE,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,IAAI,CAAC,KAAK,KAAK,eAAe,CAAC,mBAAmB,EAAE;YACpD,MAAM,QAAQ,GAAG,IAAI,CAAC,4CAA4C,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;YAC/F,IAAI,IAAI,CAAC,qCAAqC,EAAE;gBAC5C,IAAI,CAAC,eAAe,CAAC,gBAAgB,GAAG,WAAW,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;aAC5F;YACD,MAAM,GAAG,QAAQ,CAAC;SACrB;aAAM,IAAI,IAAI,CAAC,KAAK,KAAK,eAAe,CAAC,oBAAoB,EAAE;YAC5D,MAAM,GAAG,IAAI,CAAC,4CAA4C,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;YACvF,IAAI,IAAI,CAAC,qCAAqC,IAAI,IAAI,CAAC,eAAe,CAAC,gBAAgB,KAAK,IAAI,EAAE;gBAC9F,IAAI,CAAC,eAAe,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;aACrE;SACJ;QAED,oBAAoB;QACpB,IAAI,IAAI,CAAC,qCAAqC,EAAE;YAC5C,MAAM,MAAM,GAAG,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,MAAM,EAAE,CAAC;YAC5D,IAAI,CAAC,eAAe,CAAC,kBAAkB,GAAG,IAAI,GAAG,MAAM,CAAC;YACxD,IAAI,CAAC,eAAe,CAAC,cAAc,GAAG,GAAG,GAAG,MAAM,CAAC;SACtD;QAED,2BAA2B;QAC3B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzB,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC,eAAe,CAAC,QAAQ,EAAE,SAAS,CAAC,mBAAmB,EAAE,EAAE,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC;SACnI;QAED,UAAU,GAAG,SAAS,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE;YAC7J,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,cAAc,EAAE;gBAChB,cAAc,EAAE,CAAC;aACpB;YAED,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,sBAAsB,EAAE;gBACrE,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC;aACrC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SACtC;IACL,CAAC;IAED;;;;;;OAMG;IACO,4CAA4C,CAAC,YAAqB,EAAE,YAAqB;QAC/F,MAAM,IAAI,GAAG,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACjD,MAAM,uBAAuB,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAC9C,MAAM,YAAY,GAAY,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAEtD,+BAA+B;QAC/B,6FAA6F;QAC7F,MAAM,oBAAoB,GAAG,uBAAuB,GAAG,GAAG,CAAC;QAE3D,mBAAmB;QACnB,MAAM,MAAM,GAAG,oBAAoB,GAAG,IAAI,CAAC,YAAY,CAAC;QACxD,MAAM,4BAA4B,GAAG,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QACvG,MAAM,0BAA0B,GAAG,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QACrG,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,4BAA4B,EAAE,0BAA0B,CAAC,CAAC;QAClF,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC;QAEpC,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,CAAC,CAAC;SACZ;QAED,IAAI,MAAM,CAAC,gBAAgB,IAAI,IAAI,CAAC,KAAK,KAAK,eAAe,CAAC,oBAAoB,EAAE;YAChF,mCAAmC;YACnC,QAAQ,GAAG,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC;SACtF;QAED,sCAAsC;QACtC,IAAI,MAAM,CAAC,gBAAgB,EAAE;YACzB,QAAQ,GAAG,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC;SACtF;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;OAGG;IACK,0BAA0B;QAC9B,IAAI,IAAI,CAAC,oBAAoB,GAAG,CAAC,EAAE;YAC/B,OAAO;SACV;QAED,MAAM,oBAAoB,GAAG,aAAa,CAAC,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAC3E,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC3D,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;QAEhC,qDAAqD;QACrD,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,SAAS,IAAI,oBAAoB,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAClJ,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAE7B,4BAA4B;YAC5B,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEzB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;gBACvB,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC,eAAe,CAAC,MAAM,EAAE,SAAS,CAAC,mBAAmB,EAAE,EAAE,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC;aAC/H;YAED,MAAM,SAAS,GAAG,SAAS,CAAC,YAAY,CACpC,MAAM,EACN,WAAW,EACX,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,EAC/B,EAAE,EACF,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,oBAAoB,EACzB,GAAG,EAAE;gBACD,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC7B,CAAC,CACJ,CAAC;YAEF,IAAI,SAAS,EAAE;gBACX,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aACrC;SACJ;IACL,CAAC;IAED;;;OAGG;IACK,gBAAgB;QACpB,+BAA+B;QAC/B,gCAAgC;QAChC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC;QAEpC,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;SACzB;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC;QAC7C,MAAM,WAAW,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAElD,oEAAoE;QACpE,wFAAwF;QACxF,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QAE/C,wFAAwF;QACxF,yFAAyF;QACzF,4BAA4B;QAC5B,MAAM,aAAa,GAAG,aAAa,GAAG,WAAW,CAAC;QAElD,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACK,oBAAoB;QACxB,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;IAClC,CAAC;IAED;;OAEG;IACK,qBAAqB;QACzB,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,oBAAoB,GAAG,aAAa,CAAC,GAAG,CAAC;YAC9C,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,oBAAoB,EAAE,CAAC;SAC/B;IACL,CAAC;IAED;;OAEG;IACI,iBAAiB;QACpB,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,EAAE,CAAC;SACxC;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YAC7B,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;gBACtB,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC3C,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;aAC/B;YACD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;SAC7B;IACL,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,CACH,IAAI,CAAC,eAAe,CAAC,mBAAmB,KAAK,CAAC;YAC9C,IAAI,CAAC,eAAe,CAAC,kBAAkB,KAAK,CAAC;YAC7C,IAAI,CAAC,eAAe,CAAC,oBAAoB,KAAK,CAAC;YAC/C,IAAI,CAAC,eAAe,CAAC,gBAAgB,KAAK,CAAC;YAC3C,IAAI,CAAC,eAAe,CAAC,gBAAgB,KAAK,CAAC;YAC3C,IAAI,CAAC,cAAc,CACtB,CAAC;IACN,CAAC;;AA3fD;;GAEG;AACW,8BAAc,GAAG,IAAI,eAAe,EAAE,CAAC;AAErD;;GAEG;AACW,0BAAU,GAAG,cAAc,CAAC,oBAAoB,CAAC;AAqf/D,UAAU;AAEV;;GAEG;AACW,oCAAoB,GAAG,CAAC,CAAC;AAEvC;;GAEG;AACW,mCAAmB,GAAG,CAAC,CAAC", "sourcesContent": ["import type { Behavior } from \"../../Behaviors/behavior\";\r\nimport type { Camera } from \"../../Cameras/camera\";\r\nimport type { ArcRotateCamera } from \"../../Cameras/arcRotateCamera\";\r\nimport { ExponentialEase, EasingFunction } from \"../../Animations/easing\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { PointerInfoPre } from \"../../Events/pointerEvents\";\r\nimport { PointerEventTypes } from \"../../Events/pointerEvents\";\r\nimport { PrecisionDate } from \"../../Misc/precisionDate\";\r\n\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport { Vector3, Vector2 } from \"../../Maths/math.vector\";\r\nimport type { Animatable } from \"../../Animations/animatable\";\r\nimport { Animation } from \"../../Animations/animation\";\r\n\r\n/**\r\n * The framing behavior (FramingBehavior) is designed to automatically position an ArcRotateCamera when its target is set to a mesh. It is also useful if you want to prevent the camera to go under a virtual horizontal plane.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors/cameraBehaviors#framing-behavior\r\n */\r\nexport class FramingBehavior implements Behavior<ArcRotateCamera> {\r\n    /**\r\n     * Gets the name of the behavior.\r\n     */\r\n    public get name(): string {\r\n        return \"Framing\";\r\n    }\r\n\r\n    /**\r\n     * An event triggered when the animation to zoom on target mesh has ended\r\n     */\r\n    public onTargetFramingAnimationEndObservable = new Observable<void>();\r\n\r\n    private _mode = FramingBehavior.FitFrustumSidesMode;\r\n    private _radiusScale = 1.0;\r\n    private _positionScale = 0.5;\r\n    private _defaultElevation = 0.3;\r\n    private _elevationReturnTime = 1500;\r\n    private _elevationReturnWaitTime = 1000;\r\n    private _zoomStopsAnimation = false;\r\n    private _framingTime = 1500;\r\n\r\n    /**\r\n     * The easing function used by animations\r\n     */\r\n    public static EasingFunction = new ExponentialEase();\r\n\r\n    /**\r\n     * The easing mode used by animations\r\n     */\r\n    public static EasingMode = EasingFunction.EASINGMODE_EASEINOUT;\r\n\r\n    /**\r\n     * Sets the current mode used by the behavior\r\n     */\r\n    public set mode(mode: number) {\r\n        this._mode = mode;\r\n    }\r\n\r\n    /**\r\n     * Gets current mode used by the behavior.\r\n     */\r\n    public get mode(): number {\r\n        return this._mode;\r\n    }\r\n\r\n    /**\r\n     * Sets the scale applied to the radius (1 by default)\r\n     */\r\n    public set radiusScale(radius: number) {\r\n        this._radiusScale = radius;\r\n    }\r\n\r\n    /**\r\n     * Gets the scale applied to the radius\r\n     */\r\n    public get radiusScale(): number {\r\n        return this._radiusScale;\r\n    }\r\n\r\n    /**\r\n     * Sets the scale to apply on Y axis to position camera focus. 0.5 by default which means the center of the bounding box.\r\n     */\r\n    public set positionScale(scale: number) {\r\n        this._positionScale = scale;\r\n    }\r\n\r\n    /**\r\n     * Gets the scale to apply on Y axis to position camera focus. 0.5 by default which means the center of the bounding box.\r\n     */\r\n    public get positionScale(): number {\r\n        return this._positionScale;\r\n    }\r\n\r\n    /**\r\n     * Sets the angle above/below the horizontal plane to return to when the return to default elevation idle\r\n     * behaviour is triggered, in radians.\r\n     */\r\n    public set defaultElevation(elevation: number) {\r\n        this._defaultElevation = elevation;\r\n    }\r\n\r\n    /**\r\n     * Gets the angle above/below the horizontal plane to return to when the return to default elevation idle\r\n     * behaviour is triggered, in radians.\r\n     */\r\n    public get defaultElevation() {\r\n        return this._defaultElevation;\r\n    }\r\n\r\n    /**\r\n     * Sets the time (in milliseconds) taken to return to the default beta position.\r\n     * Negative value indicates camera should not return to default.\r\n     */\r\n    public set elevationReturnTime(speed: number) {\r\n        this._elevationReturnTime = speed;\r\n    }\r\n\r\n    /**\r\n     * Gets the time (in milliseconds) taken to return to the default beta position.\r\n     * Negative value indicates camera should not return to default.\r\n     */\r\n    public get elevationReturnTime(): number {\r\n        return this._elevationReturnTime;\r\n    }\r\n\r\n    /**\r\n     * Sets the delay (in milliseconds) taken before the camera returns to the default beta position.\r\n     */\r\n    public set elevationReturnWaitTime(time: number) {\r\n        this._elevationReturnWaitTime = time;\r\n    }\r\n\r\n    /**\r\n     * Gets the delay (in milliseconds) taken before the camera returns to the default beta position.\r\n     */\r\n    public get elevationReturnWaitTime(): number {\r\n        return this._elevationReturnWaitTime;\r\n    }\r\n\r\n    /**\r\n     * Sets the flag that indicates if user zooming should stop animation.\r\n     */\r\n    public set zoomStopsAnimation(flag: boolean) {\r\n        this._zoomStopsAnimation = flag;\r\n    }\r\n\r\n    /**\r\n     * Gets the flag that indicates if user zooming should stop animation.\r\n     */\r\n    public get zoomStopsAnimation(): boolean {\r\n        return this._zoomStopsAnimation;\r\n    }\r\n\r\n    /**\r\n     * Sets the transition time when framing the mesh, in milliseconds\r\n     */\r\n    public set framingTime(time: number) {\r\n        this._framingTime = time;\r\n    }\r\n\r\n    /**\r\n     * Gets the transition time when framing the mesh, in milliseconds\r\n     */\r\n    public get framingTime() {\r\n        return this._framingTime;\r\n    }\r\n\r\n    /**\r\n     * Define if the behavior should automatically change the configured\r\n     * camera limits and sensibilities.\r\n     */\r\n    public autoCorrectCameraLimitsAndSensibility = true;\r\n\r\n    // Default behavior functions\r\n    private _onPrePointerObservableObserver: Nullable<Observer<PointerInfoPre>>;\r\n    private _onAfterCheckInputsObserver: Nullable<Observer<Camera>>;\r\n    private _onMeshTargetChangedObserver: Nullable<Observer<Nullable<AbstractMesh>>>;\r\n    private _attachedCamera: Nullable<ArcRotateCamera>;\r\n    private _isPointerDown = false;\r\n    private _lastInteractionTime = -Infinity;\r\n\r\n    /**\r\n     * Initializes the behavior.\r\n     */\r\n    public init(): void {\r\n        // Do nothing\r\n    }\r\n\r\n    /**\r\n     * Attaches the behavior to its arc rotate camera.\r\n     * @param camera Defines the camera to attach the behavior to\r\n     */\r\n    public attach(camera: ArcRotateCamera): void {\r\n        this._attachedCamera = camera;\r\n        const scene = this._attachedCamera.getScene();\r\n\r\n        FramingBehavior.EasingFunction.setEasingMode(FramingBehavior.EasingMode);\r\n\r\n        this._onPrePointerObservableObserver = scene.onPrePointerObservable.add((pointerInfoPre) => {\r\n            if (pointerInfoPre.type === PointerEventTypes.POINTERDOWN) {\r\n                this._isPointerDown = true;\r\n                return;\r\n            }\r\n\r\n            if (pointerInfoPre.type === PointerEventTypes.POINTERUP) {\r\n                this._isPointerDown = false;\r\n            }\r\n        });\r\n\r\n        this._onMeshTargetChangedObserver = camera.onMeshTargetChangedObservable.add((mesh) => {\r\n            if (mesh) {\r\n                this.zoomOnMesh(mesh, undefined, () => {\r\n                    this.onTargetFramingAnimationEndObservable.notifyObservers();\r\n                });\r\n            }\r\n        });\r\n\r\n        this._onAfterCheckInputsObserver = camera.onAfterCheckInputsObservable.add(() => {\r\n            // Stop the animation if there is user interaction and the animation should stop for this interaction\r\n            this._applyUserInteraction();\r\n\r\n            // Maintain the camera above the ground. If the user pulls the camera beneath the ground plane, lift it\r\n            // back to the default position after a given timeout\r\n            this._maintainCameraAboveGround();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Detaches the behavior from its current arc rotate camera.\r\n     */\r\n    public detach(): void {\r\n        if (!this._attachedCamera) {\r\n            return;\r\n        }\r\n\r\n        const scene = this._attachedCamera.getScene();\r\n\r\n        if (this._onPrePointerObservableObserver) {\r\n            scene.onPrePointerObservable.remove(this._onPrePointerObservableObserver);\r\n        }\r\n\r\n        if (this._onAfterCheckInputsObserver) {\r\n            this._attachedCamera.onAfterCheckInputsObservable.remove(this._onAfterCheckInputsObserver);\r\n        }\r\n\r\n        if (this._onMeshTargetChangedObserver) {\r\n            this._attachedCamera.onMeshTargetChangedObservable.remove(this._onMeshTargetChangedObserver);\r\n        }\r\n\r\n        this._attachedCamera = null;\r\n    }\r\n\r\n    // Framing control\r\n    private _animatables = new Array<Animatable>();\r\n    private _betaIsAnimating = false;\r\n    private _betaTransition: Animation;\r\n    private _radiusTransition: Animation;\r\n    private _vectorTransition: Animation;\r\n\r\n    /**\r\n     * Targets the given mesh and updates zoom level accordingly.\r\n     * @param mesh  The mesh to target.\r\n     * @param focusOnOriginXZ Determines if the camera should focus on 0 in the X and Z axis instead of the mesh\r\n     * @param onAnimationEnd Callback triggered at the end of the framing animation\r\n     */\r\n    public zoomOnMesh(mesh: AbstractMesh, focusOnOriginXZ: boolean = false, onAnimationEnd: Nullable<() => void> = null): void {\r\n        mesh.computeWorldMatrix(true);\r\n\r\n        const boundingBox = mesh.getBoundingInfo().boundingBox;\r\n        this.zoomOnBoundingInfo(boundingBox.minimumWorld, boundingBox.maximumWorld, focusOnOriginXZ, onAnimationEnd);\r\n    }\r\n\r\n    /**\r\n     * Targets the given mesh with its children and updates zoom level accordingly.\r\n     * @param mesh  The mesh to target.\r\n     * @param focusOnOriginXZ Determines if the camera should focus on 0 in the X and Z axis instead of the mesh\r\n     * @param onAnimationEnd Callback triggered at the end of the framing animation\r\n     */\r\n    public zoomOnMeshHierarchy(mesh: AbstractMesh, focusOnOriginXZ: boolean = false, onAnimationEnd: Nullable<() => void> = null): void {\r\n        mesh.computeWorldMatrix(true);\r\n\r\n        const boundingBox = mesh.getHierarchyBoundingVectors(true);\r\n        this.zoomOnBoundingInfo(boundingBox.min, boundingBox.max, focusOnOriginXZ, onAnimationEnd);\r\n    }\r\n\r\n    /**\r\n     * Targets the given meshes with their children and updates zoom level accordingly.\r\n     * @param meshes  The mesh to target.\r\n     * @param focusOnOriginXZ Determines if the camera should focus on 0 in the X and Z axis instead of the mesh\r\n     * @param onAnimationEnd Callback triggered at the end of the framing animation\r\n     */\r\n    public zoomOnMeshesHierarchy(meshes: AbstractMesh[], focusOnOriginXZ: boolean = false, onAnimationEnd: Nullable<() => void> = null): void {\r\n        const min = new Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n        const max = new Vector3(-Number.MAX_VALUE, -Number.MAX_VALUE, -Number.MAX_VALUE);\r\n\r\n        for (let i = 0; i < meshes.length; i++) {\r\n            const boundingInfo = meshes[i].getHierarchyBoundingVectors(true);\r\n            Vector3.CheckExtends(boundingInfo.min, min, max);\r\n            Vector3.CheckExtends(boundingInfo.max, min, max);\r\n        }\r\n\r\n        this.zoomOnBoundingInfo(min, max, focusOnOriginXZ, onAnimationEnd);\r\n    }\r\n\r\n    /**\r\n     * Targets the bounding box info defined by its extends and updates zoom level accordingly.\r\n     * @param minimumWorld Determines the smaller position of the bounding box extend\r\n     * @param maximumWorld Determines the bigger position of the bounding box extend\r\n     * @param focusOnOriginXZ Determines if the camera should focus on 0 in the X and Z axis instead of the mesh\r\n     * @param onAnimationEnd Callback triggered at the end of the framing animation\r\n     */\r\n    public zoomOnBoundingInfo(minimumWorld: Vector3, maximumWorld: Vector3, focusOnOriginXZ: boolean = false, onAnimationEnd: Nullable<() => void> = null): void {\r\n        let zoomTarget: Vector3;\r\n\r\n        if (!this._attachedCamera) {\r\n            return;\r\n        }\r\n\r\n        // Find target by interpolating from bottom of bounding box in world-space to top via framingPositionY\r\n        const bottom = minimumWorld.y;\r\n        const top = maximumWorld.y;\r\n        const zoomTargetY = bottom + (top - bottom) * this._positionScale;\r\n        const radiusWorld = maximumWorld.subtract(minimumWorld).scale(0.5);\r\n\r\n        if (focusOnOriginXZ) {\r\n            zoomTarget = new Vector3(0, zoomTargetY, 0);\r\n        } else {\r\n            const centerWorld = minimumWorld.add(radiusWorld);\r\n            zoomTarget = new Vector3(centerWorld.x, zoomTargetY, centerWorld.z);\r\n        }\r\n\r\n        if (!this._vectorTransition) {\r\n            this._vectorTransition = Animation.CreateAnimation(\"target\", Animation.ANIMATIONTYPE_VECTOR3, 60, FramingBehavior.EasingFunction);\r\n        }\r\n\r\n        this._betaIsAnimating = true;\r\n        let animatable = Animation.TransitionTo(\"target\", zoomTarget, this._attachedCamera, this._attachedCamera.getScene(), 60, this._vectorTransition, this._framingTime);\r\n        if (animatable) {\r\n            this._animatables.push(animatable);\r\n        }\r\n\r\n        // sets the radius and lower radius bounds\r\n        // Small delta ensures camera is not always at lower zoom limit.\r\n        let radius = 0;\r\n        if (this._mode === FramingBehavior.FitFrustumSidesMode) {\r\n            const position = this._calculateLowerRadiusFromModelBoundingSphere(minimumWorld, maximumWorld);\r\n            if (this.autoCorrectCameraLimitsAndSensibility) {\r\n                this._attachedCamera.lowerRadiusLimit = radiusWorld.length() + this._attachedCamera.minZ;\r\n            }\r\n            radius = position;\r\n        } else if (this._mode === FramingBehavior.IgnoreBoundsSizeMode) {\r\n            radius = this._calculateLowerRadiusFromModelBoundingSphere(minimumWorld, maximumWorld);\r\n            if (this.autoCorrectCameraLimitsAndSensibility && this._attachedCamera.lowerRadiusLimit === null) {\r\n                this._attachedCamera.lowerRadiusLimit = this._attachedCamera.minZ;\r\n            }\r\n        }\r\n\r\n        // Set sensibilities\r\n        if (this.autoCorrectCameraLimitsAndSensibility) {\r\n            const extend = maximumWorld.subtract(minimumWorld).length();\r\n            this._attachedCamera.panningSensibility = 5000 / extend;\r\n            this._attachedCamera.wheelPrecision = 100 / radius;\r\n        }\r\n\r\n        // transition to new radius\r\n        if (!this._radiusTransition) {\r\n            this._radiusTransition = Animation.CreateAnimation(\"radius\", Animation.ANIMATIONTYPE_FLOAT, 60, FramingBehavior.EasingFunction);\r\n        }\r\n\r\n        animatable = Animation.TransitionTo(\"radius\", radius, this._attachedCamera, this._attachedCamera.getScene(), 60, this._radiusTransition, this._framingTime, () => {\r\n            this.stopAllAnimations();\r\n            if (onAnimationEnd) {\r\n                onAnimationEnd();\r\n            }\r\n\r\n            if (this._attachedCamera && this._attachedCamera.useInputToRestoreState) {\r\n                this._attachedCamera.storeState();\r\n            }\r\n        });\r\n\r\n        if (animatable) {\r\n            this._animatables.push(animatable);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Calculates the lowest radius for the camera based on the bounding box of the mesh.\r\n     * @param minimumWorld\r\n     * @param maximumWorld\r\n     * @returns The minimum distance from the primary mesh's center point at which the camera must be kept in order\r\n     *\t\t to fully enclose the mesh in the viewing frustum.\r\n     */\r\n    protected _calculateLowerRadiusFromModelBoundingSphere(minimumWorld: Vector3, maximumWorld: Vector3): number {\r\n        const size = maximumWorld.subtract(minimumWorld);\r\n        const boxVectorGlobalDiagonal = size.length();\r\n        const frustumSlope: Vector2 = this._getFrustumSlope();\r\n\r\n        // Formula for setting distance\r\n        // (Good explanation: http://stackoverflow.com/questions/2866350/move-camera-to-fit-3d-scene)\r\n        const radiusWithoutFraming = boxVectorGlobalDiagonal * 0.5;\r\n\r\n        // Horizon distance\r\n        const radius = radiusWithoutFraming * this._radiusScale;\r\n        const distanceForHorizontalFrustum = radius * Math.sqrt(1.0 + 1.0 / (frustumSlope.x * frustumSlope.x));\r\n        const distanceForVerticalFrustum = radius * Math.sqrt(1.0 + 1.0 / (frustumSlope.y * frustumSlope.y));\r\n        let distance = Math.max(distanceForHorizontalFrustum, distanceForVerticalFrustum);\r\n        const camera = this._attachedCamera;\r\n\r\n        if (!camera) {\r\n            return 0;\r\n        }\r\n\r\n        if (camera.lowerRadiusLimit && this._mode === FramingBehavior.IgnoreBoundsSizeMode) {\r\n            // Don't exceed the requested limit\r\n            distance = distance < camera.lowerRadiusLimit ? camera.lowerRadiusLimit : distance;\r\n        }\r\n\r\n        // Don't exceed the upper radius limit\r\n        if (camera.upperRadiusLimit) {\r\n            distance = distance > camera.upperRadiusLimit ? camera.upperRadiusLimit : distance;\r\n        }\r\n\r\n        return distance;\r\n    }\r\n\r\n    /**\r\n     * Keeps the camera above the ground plane. If the user pulls the camera below the ground plane, the camera\r\n     * is automatically returned to its default position (expected to be above ground plane).\r\n     */\r\n    private _maintainCameraAboveGround(): void {\r\n        if (this._elevationReturnTime < 0) {\r\n            return;\r\n        }\r\n\r\n        const timeSinceInteraction = PrecisionDate.Now - this._lastInteractionTime;\r\n        const defaultBeta = Math.PI * 0.5 - this._defaultElevation;\r\n        const limitBeta = Math.PI * 0.5;\r\n\r\n        // Bring the camera back up if below the ground plane\r\n        if (this._attachedCamera && !this._betaIsAnimating && this._attachedCamera.beta > limitBeta && timeSinceInteraction >= this._elevationReturnWaitTime) {\r\n            this._betaIsAnimating = true;\r\n\r\n            //Transition to new position\r\n            this.stopAllAnimations();\r\n\r\n            if (!this._betaTransition) {\r\n                this._betaTransition = Animation.CreateAnimation(\"beta\", Animation.ANIMATIONTYPE_FLOAT, 60, FramingBehavior.EasingFunction);\r\n            }\r\n\r\n            const animatabe = Animation.TransitionTo(\r\n                \"beta\",\r\n                defaultBeta,\r\n                this._attachedCamera,\r\n                this._attachedCamera.getScene(),\r\n                60,\r\n                this._betaTransition,\r\n                this._elevationReturnTime,\r\n                () => {\r\n                    this._clearAnimationLocks();\r\n                    this.stopAllAnimations();\r\n                }\r\n            );\r\n\r\n            if (animatabe) {\r\n                this._animatables.push(animatabe);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns the frustum slope based on the canvas ratio and camera FOV\r\n     * @returns The frustum slope represented as a Vector2 with X and Y slopes\r\n     */\r\n    private _getFrustumSlope(): Vector2 {\r\n        // Calculate the viewport ratio\r\n        // Aspect Ratio is Height/Width.\r\n        const camera = this._attachedCamera;\r\n\r\n        if (!camera) {\r\n            return Vector2.Zero();\r\n        }\r\n\r\n        const engine = camera.getScene().getEngine();\r\n        const aspectRatio = engine.getAspectRatio(camera);\r\n\r\n        // Camera FOV is the vertical field of view (top-bottom) in radians.\r\n        // Slope of the frustum top/bottom planes in view space, relative to the forward vector.\r\n        const frustumSlopeY = Math.tan(camera.fov / 2);\r\n\r\n        // Slope of the frustum left/right planes in view space, relative to the forward vector.\r\n        // Provides the amount that one side (e.g. left) of the frustum gets wider for every unit\r\n        // along the forward vector.\r\n        const frustumSlopeX = frustumSlopeY * aspectRatio;\r\n\r\n        return new Vector2(frustumSlopeX, frustumSlopeY);\r\n    }\r\n\r\n    /**\r\n     * Removes all animation locks. Allows new animations to be added to any of the arcCamera properties.\r\n     */\r\n    private _clearAnimationLocks(): void {\r\n        this._betaIsAnimating = false;\r\n    }\r\n\r\n    /**\r\n     *  Applies any current user interaction to the camera. Takes into account maximum alpha rotation.\r\n     */\r\n    private _applyUserInteraction(): void {\r\n        if (this.isUserIsMoving) {\r\n            this._lastInteractionTime = PrecisionDate.Now;\r\n            this.stopAllAnimations();\r\n            this._clearAnimationLocks();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Stops and removes all animations that have been applied to the camera\r\n     */\r\n    public stopAllAnimations(): void {\r\n        if (this._attachedCamera) {\r\n            this._attachedCamera.animations = [];\r\n        }\r\n\r\n        while (this._animatables.length) {\r\n            if (this._animatables[0]) {\r\n                this._animatables[0].onAnimationEnd = null;\r\n                this._animatables[0].stop();\r\n            }\r\n            this._animatables.shift();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets a value indicating if the user is moving the camera\r\n     */\r\n    public get isUserIsMoving(): boolean {\r\n        if (!this._attachedCamera) {\r\n            return false;\r\n        }\r\n\r\n        return (\r\n            this._attachedCamera.inertialAlphaOffset !== 0 ||\r\n            this._attachedCamera.inertialBetaOffset !== 0 ||\r\n            this._attachedCamera.inertialRadiusOffset !== 0 ||\r\n            this._attachedCamera.inertialPanningX !== 0 ||\r\n            this._attachedCamera.inertialPanningY !== 0 ||\r\n            this._isPointerDown\r\n        );\r\n    }\r\n\r\n    // Statics\r\n\r\n    /**\r\n     * The camera can move all the way towards the mesh.\r\n     */\r\n    public static IgnoreBoundsSizeMode = 0;\r\n\r\n    /**\r\n     * The camera is not allowed to zoom closer to the mesh than the point at which the adjusted bounding sphere touches the frustum sides\r\n     */\r\n    public static FitFrustumSidesMode = 1;\r\n}\r\n"]}