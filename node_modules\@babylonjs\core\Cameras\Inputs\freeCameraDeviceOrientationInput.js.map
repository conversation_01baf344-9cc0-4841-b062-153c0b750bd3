{"version": 3, "file": "freeCameraDeviceOrientationInput.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Cameras/Inputs/freeCameraDeviceOrientationInput.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AAErE,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AACrD,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AACzC,OAAO,EAAE,uBAAuB,EAAE,MAAM,uCAAuC,CAAC;AAChF,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAkBnD;;;;GAIG;AACH,uBAAuB,CAAC,SAAS,CAAC,oBAAoB,GAAG,UAAU,YAAqB;IACpF,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;QAC/B,IAAI,CAAC,uBAAuB,GAAG,IAAI,gCAAgC,EAAE,CAAC;QACtE,IAAI,YAAY,EAAE;YACd,IAAI,CAAC,uBAAuB,CAAC,YAAY,GAAG,YAAY,CAAC;SAC5D;QACD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;KAC1C;IAED,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF;;;;GAIG;AACH,MAAM,OAAO,gCAAgC;IAezC;;;;OAIG;IACI,MAAM,CAAC,6BAA6B,CAAC,OAAgB;QACxD,OAAO,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YAC5B,IAAI,QAAQ,GAAG,KAAK,CAAC;YACrB,MAAM,YAAY,GAAG,GAAG,EAAE;gBACtB,MAAM,CAAC,mBAAmB,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC;gBAC9D,QAAQ,GAAG,IAAI,CAAC;gBAChB,GAAG,EAAE,CAAC;YACV,CAAC,CAAC;YAEF,6CAA6C;YAC7C,IAAI,OAAO,EAAE;gBACT,UAAU,CAAC,GAAG,EAAE;oBACZ,IAAI,CAAC,QAAQ,EAAE;wBACX,MAAM,CAAC,mBAAmB,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC;wBAC9D,GAAG,CAAC,yCAAyC,CAAC,CAAC;qBAClD;gBACL,CAAC,EAAE,OAAO,CAAC,CAAC;aACf;YAED,IAAI,OAAO,sBAAsB,KAAK,WAAW,IAAI,OAAa,sBAAuB,CAAC,iBAAiB,KAAK,UAAU,EAAE;gBAClH,sBAAuB;qBACxB,iBAAiB,EAAE;qBACnB,IAAI,CAAC,CAAC,QAAgB,EAAE,EAAE;oBACvB,IAAI,QAAQ,IAAI,SAAS,EAAE;wBACvB,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC;qBAC9D;yBAAM;wBACH,KAAK,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;qBACzC;gBACL,CAAC,CAAC;qBACD,KAAK,CAAC,CAAC,KAAU,EAAE,EAAE;oBAClB,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC;aACV;iBAAM;gBACH,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC;aAC9D;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAMD;;;OAGG;IACH;QA/DQ,4BAAuB,GAAW,CAAC,CAAC;QAGpC,sBAAiB,GAAe,IAAI,UAAU,EAAE,CAAC;QAEjD,WAAM,GAAW,CAAC,CAAC;QACnB,UAAK,GAAW,CAAC,CAAC;QAClB,WAAM,GAAW,CAAC,CAAC;QAE3B,sGAAsG;QAC/F,iBAAY,GAAW,CAAC,CAAC;QA6ChC;;WAEG;QACI,0CAAqC,GAAG,IAAI,UAAU,EAAQ,CAAC;QA8D9D,wBAAmB,GAAG,GAAG,EAAE;YAC/B,IAAI,CAAC,uBAAuB;gBACnB,MAAM,CAAC,WAAW,KAAK,SAAS;oBACjC,CAAC,CAAC,CAAO,MAAM,CAAC,WAAY;oBAC5B,CAAC,CAAO,MAAM,CAAC,MAAO,CAAC,WAAW,IAAU,MAAM,CAAC,MAAO,CAAC,WAAW,CAAC,OAAO,CAAC;wBAC/E,CAAC,CAAO,MAAM,CAAC,MAAO,CAAC,WAAW,CAAC,KAAK;wBACxC,CAAC,CAAC,CAAC,CAAC;YACZ,IAAI,CAAC,uBAAuB,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,uBAAuB,GAAG,CAAC,CAAC,CAAC;YAClF,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC;QAChI,CAAC,CAAC;QAEM,uBAAkB,GAAG,CAAC,GAA2B,EAAE,EAAE;YACzD,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1G,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtG,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC7G;iBAAM;gBACH,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjD,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9C,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;aACpD;YAED,IAAI,GAAG,CAAC,KAAK,KAAK,IAAI,EAAE;gBACpB,IAAI,CAAC,qCAAqC,CAAC,eAAe,EAAE,CAAC;aAChE;QACL,CAAC,CAAC;QAjFE,IAAI,CAAC,iBAAiB,GAAG,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/E,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAW,MAAM,CAAC,MAAkB;QAChC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE;YAC1D,IAAI,CAAC,OAAO,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;SACtD;QACD,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;gBACtC,IAAI,CAAC,qCAAqC,CAAC,KAAK,EAAE,CAAC;YACvD,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,aAAa,EAAE,CAAC;QAEtE,IAAI,UAAU,EAAE;YACZ,MAAM,YAAY,GAAG,GAAG,EAAE;gBACtB,UAAW,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBAC5E,UAAW,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBAC3E,+EAA+E;gBAC/E,oBAAoB;gBACpB,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC/B,CAAC,CAAC;YACF,IAAI,OAAO,sBAAsB,KAAK,WAAW,IAAI,OAAa,sBAAuB,CAAC,iBAAiB,KAAK,UAAU,EAAE;gBAClH,sBAAuB;qBACxB,iBAAiB,EAAE;qBACnB,IAAI,CAAC,CAAC,QAAgB,EAAE,EAAE;oBACvB,IAAI,QAAQ,KAAK,SAAS,EAAE;wBACxB,YAAY,EAAE,CAAC;qBAClB;yBAAM;wBACH,KAAK,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;qBACzC;gBACL,CAAC,CAAC;qBACD,KAAK,CAAC,CAAC,KAAU,EAAE,EAAE;oBAClB,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC;aACV;iBAAM;gBACH,YAAY,EAAE,CAAC;aAClB;SACJ;IACL,CAAC;IA6BD;;OAEG;IACI,aAAa;QAChB,MAAM,CAAC,mBAAmB,CAAC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC1E,MAAM,CAAC,mBAAmB,CAAC,mBAAmB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACzE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IACpB,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,+DAA+D;QAC/D,mGAAmG;QACnG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,OAAO;SACV;QACD,UAAU,CAAC,yBAAyB,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QAC/J,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACxE,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACxE,oBAAoB;QACpB,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,kCAAkC,CAAC;IAC9C,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,mBAAmB,CAAC;IAC/B,CAAC;CACJ;AAEK,gBAAiB,CAAC,kCAAkC,CAAC,GAAG,gCAAgC,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport type { ICameraInput } from \"../../Cameras/cameraInputsManager\";\r\nimport { CameraInputTypes } from \"../../Cameras/cameraInputsManager\";\r\nimport type { FreeCamera } from \"../../Cameras/freeCamera\";\r\nimport { Quaternion } from \"../../Maths/math.vector\";\r\nimport { Tools } from \"../../Misc/tools\";\r\nimport { FreeCameraInputsManager } from \"../../Cameras/freeCameraInputsManager\";\r\nimport { Observable } from \"../../Misc/observable\";\r\n\r\n// Module augmentation to abstract orientation inputs from camera.\r\ndeclare module \"../../Cameras/freeCameraInputsManager\" {\r\n    export interface FreeCameraInputsManager {\r\n        /**\r\n         * @internal\r\n         */\r\n        _deviceOrientationInput: Nullable<FreeCameraDeviceOrientationInput>;\r\n        /**\r\n         * Add orientation input support to the input manager.\r\n         * @param smoothFactor deviceOrientation smoothing. 0: no smoothing, 1: new data ignored, 0.9 recommended for smoothing\r\n         * @returns the current input manager\r\n         */\r\n        addDeviceOrientation(smoothFactor?: number): FreeCameraInputsManager;\r\n    }\r\n}\r\n\r\n/**\r\n * Add orientation input support to the input manager.\r\n * @param smoothFactor deviceOrientation smoothing. 0: no smoothing, 1: new data ignored, 0.9 recommended for smoothing\r\n * @returns the current input manager\r\n */\r\nFreeCameraInputsManager.prototype.addDeviceOrientation = function (smoothFactor?: number): FreeCameraInputsManager {\r\n    if (!this._deviceOrientationInput) {\r\n        this._deviceOrientationInput = new FreeCameraDeviceOrientationInput();\r\n        if (smoothFactor) {\r\n            this._deviceOrientationInput.smoothFactor = smoothFactor;\r\n        }\r\n        this.add(this._deviceOrientationInput);\r\n    }\r\n\r\n    return this;\r\n};\r\n\r\n/**\r\n * Takes information about the orientation of the device as reported by the deviceorientation event to orient the camera.\r\n * Screen rotation is taken into account.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n */\r\nexport class FreeCameraDeviceOrientationInput implements ICameraInput<FreeCamera> {\r\n    private _camera: FreeCamera;\r\n\r\n    private _screenOrientationAngle: number = 0;\r\n\r\n    private _constantTranform: Quaternion;\r\n    private _screenQuaternion: Quaternion = new Quaternion();\r\n\r\n    private _alpha: number = 0;\r\n    private _beta: number = 0;\r\n    private _gamma: number = 0;\r\n\r\n    /** alpha+beta+gamma smoothing. 0: no smoothing, 1: new data ignored, 0.9 recommended for smoothing */\r\n    public smoothFactor: number = 0;\r\n\r\n    /**\r\n     * Can be used to detect if a device orientation sensor is available on a device\r\n     * @param timeout amount of time in milliseconds to wait for a response from the sensor (default: infinite)\r\n     * @returns a promise that will resolve on orientation change\r\n     */\r\n    public static WaitForOrientationChangeAsync(timeout?: number): Promise<void> {\r\n        return new Promise((res, rej) => {\r\n            let gotValue = false;\r\n            const eventHandler = () => {\r\n                window.removeEventListener(\"deviceorientation\", eventHandler);\r\n                gotValue = true;\r\n                res();\r\n            };\r\n\r\n            // If timeout is populated reject the promise\r\n            if (timeout) {\r\n                setTimeout(() => {\r\n                    if (!gotValue) {\r\n                        window.removeEventListener(\"deviceorientation\", eventHandler);\r\n                        rej(\"WaitForOrientationChangeAsync timed out\");\r\n                    }\r\n                }, timeout);\r\n            }\r\n\r\n            if (typeof DeviceOrientationEvent !== \"undefined\" && typeof (<any>DeviceOrientationEvent).requestPermission === \"function\") {\r\n                (<any>DeviceOrientationEvent)\r\n                    .requestPermission()\r\n                    .then((response: string) => {\r\n                        if (response == \"granted\") {\r\n                            window.addEventListener(\"deviceorientation\", eventHandler);\r\n                        } else {\r\n                            Tools.Warn(\"Permission not granted.\");\r\n                        }\r\n                    })\r\n                    .catch((error: any) => {\r\n                        Tools.Error(error);\r\n                    });\r\n            } else {\r\n                window.addEventListener(\"deviceorientation\", eventHandler);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _onDeviceOrientationChangedObservable = new Observable<void>();\r\n    /**\r\n     * Instantiates a new input\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n     */\r\n    constructor() {\r\n        this._constantTranform = new Quaternion(-Math.sqrt(0.5), 0, 0, Math.sqrt(0.5));\r\n        this._orientationChanged();\r\n    }\r\n\r\n    /**\r\n     * Define the camera controlled by the input.\r\n     */\r\n    public get camera(): FreeCamera {\r\n        return this._camera;\r\n    }\r\n\r\n    public set camera(camera: FreeCamera) {\r\n        this._camera = camera;\r\n        if (this._camera != null && !this._camera.rotationQuaternion) {\r\n            this._camera.rotationQuaternion = new Quaternion();\r\n        }\r\n        if (this._camera) {\r\n            this._camera.onDisposeObservable.add(() => {\r\n                this._onDeviceOrientationChangedObservable.clear();\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     */\r\n    public attachControl(): void {\r\n        const hostWindow = this.camera.getScene().getEngine().getHostWindow();\r\n\r\n        if (hostWindow) {\r\n            const eventHandler = () => {\r\n                hostWindow!.addEventListener(\"orientationchange\", this._orientationChanged);\r\n                hostWindow!.addEventListener(\"deviceorientation\", this._deviceOrientation);\r\n                //In certain cases, the attach control is called AFTER orientation was changed,\r\n                //So this is needed.\r\n                this._orientationChanged();\r\n            };\r\n            if (typeof DeviceOrientationEvent !== \"undefined\" && typeof (<any>DeviceOrientationEvent).requestPermission === \"function\") {\r\n                (<any>DeviceOrientationEvent)\r\n                    .requestPermission()\r\n                    .then((response: string) => {\r\n                        if (response === \"granted\") {\r\n                            eventHandler();\r\n                        } else {\r\n                            Tools.Warn(\"Permission not granted.\");\r\n                        }\r\n                    })\r\n                    .catch((error: any) => {\r\n                        Tools.Error(error);\r\n                    });\r\n            } else {\r\n                eventHandler();\r\n            }\r\n        }\r\n    }\r\n\r\n    private _orientationChanged = () => {\r\n        this._screenOrientationAngle =\r\n            <any>window.orientation !== undefined\r\n                ? +(<any>window.orientation)\r\n                : (<any>window.screen).orientation && (<any>window.screen).orientation[\"angle\"]\r\n                ? (<any>window.screen).orientation.angle\r\n                : 0;\r\n        this._screenOrientationAngle = -Tools.ToRadians(this._screenOrientationAngle / 2);\r\n        this._screenQuaternion.copyFromFloats(0, Math.sin(this._screenOrientationAngle), 0, Math.cos(this._screenOrientationAngle));\r\n    };\r\n\r\n    private _deviceOrientation = (evt: DeviceOrientationEvent) => {\r\n        if (this.smoothFactor) {\r\n            this._alpha = evt.alpha !== null ? Tools.SmoothAngleChange(this._alpha, evt.alpha, this.smoothFactor) : 0;\r\n            this._beta = evt.beta !== null ? Tools.SmoothAngleChange(this._beta, evt.beta, this.smoothFactor) : 0;\r\n            this._gamma = evt.gamma !== null ? Tools.SmoothAngleChange(this._gamma, evt.gamma, this.smoothFactor) : 0;\r\n        } else {\r\n            this._alpha = evt.alpha !== null ? evt.alpha : 0;\r\n            this._beta = evt.beta !== null ? evt.beta : 0;\r\n            this._gamma = evt.gamma !== null ? evt.gamma : 0;\r\n        }\r\n\r\n        if (evt.alpha !== null) {\r\n            this._onDeviceOrientationChangedObservable.notifyObservers();\r\n        }\r\n    };\r\n\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    public detachControl(): void {\r\n        window.removeEventListener(\"orientationchange\", this._orientationChanged);\r\n        window.removeEventListener(\"deviceorientation\", this._deviceOrientation);\r\n        this._alpha = 0;\r\n    }\r\n\r\n    /**\r\n     * Update the current camera state depending on the inputs that have been used this frame.\r\n     * This is a dynamically created lambda to avoid the performance penalty of looping for inputs in the render loop.\r\n     */\r\n    public checkInputs(): void {\r\n        //if no device orientation provided, don't update the rotation.\r\n        //Only testing against alpha under the assumption thatnorientation will never be so exact when set.\r\n        if (!this._alpha) {\r\n            return;\r\n        }\r\n        Quaternion.RotationYawPitchRollToRef(Tools.ToRadians(this._alpha), Tools.ToRadians(this._beta), -Tools.ToRadians(this._gamma), this.camera.rotationQuaternion);\r\n        this._camera.rotationQuaternion.multiplyInPlace(this._screenQuaternion);\r\n        this._camera.rotationQuaternion.multiplyInPlace(this._constantTranform);\r\n        //Mirror on XY Plane\r\n        this._camera.rotationQuaternion.z *= -1;\r\n        this._camera.rotationQuaternion.w *= -1;\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of the current input.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"FreeCameraDeviceOrientationInput\";\r\n    }\r\n\r\n    /**\r\n     * Get the friendly name associated with the input class.\r\n     * @returns the input friendly name\r\n     */\r\n    public getSimpleName(): string {\r\n        return \"deviceOrientation\";\r\n    }\r\n}\r\n\r\n(<any>CameraInputTypes)[\"FreeCameraDeviceOrientationInput\"] = FreeCameraDeviceOrientationInput;\r\n"]}