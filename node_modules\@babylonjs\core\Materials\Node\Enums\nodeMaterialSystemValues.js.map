{"version": 3, "file": "nodeMaterialSystemValues.js", "sourceRoot": "", "sources": ["../../../../../../lts/core/generated/Materials/Node/Enums/nodeMaterialSystemValues.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,CAAN,IAAY,wBAuBX;AAvBD,WAAY,wBAAwB;IAChC,YAAY;IACZ,yEAAS,CAAA;IACT,WAAW;IACX,uEAAQ,CAAA;IACR,iBAAiB;IACjB,mFAAc,CAAA;IACd,qBAAqB;IACrB,2FAAkB,CAAA;IAClB,gBAAgB;IAChB,iFAAa,CAAA;IACb,0BAA0B;IAC1B,qGAAuB,CAAA;IACvB,qBAAqB;IACrB,2FAAkB,CAAA;IAClB,gBAAgB;IAChB,+EAAY,CAAA;IACZ,iBAAiB;IACjB,iFAAa,CAAA;IACb,wBAAwB;IACxB,gGAAqB,CAAA;IACrB,qBAAqB;IACrB,0FAAkB,CAAA;AACtB,CAAC,EAvBW,wBAAwB,KAAxB,wBAAwB,QAuBnC", "sourcesContent": ["/**\r\n * Enum used to define system values e.g. values automatically provided by the system\r\n */\r\nexport enum NodeMaterialSystemValues {\r\n    /** World */\r\n    World = 1,\r\n    /** View */\r\n    View = 2,\r\n    /** Projection */\r\n    Projection = 3,\r\n    /** ViewProjection */\r\n    ViewProjection = 4,\r\n    /** WorldView */\r\n    WorldView = 5,\r\n    /** WorldViewProjection */\r\n    WorldViewProjection = 6,\r\n    /** CameraPosition */\r\n    CameraPosition = 7,\r\n    /** Fog Color */\r\n    FogColor = 8,\r\n    /** Delta time */\r\n    DeltaTime = 9,\r\n    /** Camera parameters */\r\n    CameraParameters = 10,\r\n    /** Material alpha */\r\n    MaterialAlpha = 11,\r\n}\r\n"]}