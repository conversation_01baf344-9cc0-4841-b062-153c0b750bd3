{"version": 3, "file": "camera.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Cameras/camera.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AACxF,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAIhD,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AACnE,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAI/B,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAC/C,OAAO,EAAE,QAAQ,EAAE,MAAM,wBAAwB,CAAC;AAClD,OAAO,EAAE,OAAO,EAAE,MAAM,uBAAuB,CAAC;AAEhD,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAQjD;;;GAGG;AACH,MAAM,OAAO,MAAO,SAAQ,IAAI;IAoF5B;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,QAAQ,CAAC,WAAoB;QACpC,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC;IACjC,CAAC;IAKD;;;OAGG;IACH,IAAW,QAAQ,CAAC,GAAY;QAC5B,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;IACzB,CAAC;IAED,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;;QACjB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,kBAAkB,EAAE;YACzC,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM,CAAC,sBAAsB,EAAE;gBAChD,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;gBAC3C,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACjD;iBAAM;gBACH,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;gBAC3C,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;aACjD;SACJ;aAAM;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,cAAc,EAAE,GAAG,GAAG,CAAC;YAC1D,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,eAAe,EAAE,GAAG,GAAG,CAAC;YAE5D,CAAC,GAAG,CAAC,MAAA,IAAI,CAAC,UAAU,mCAAI,SAAS,CAAC,GAAG,CAAC,MAAA,IAAI,CAAC,SAAS,mCAAI,CAAC,SAAS,CAAC,CAAC;YACpE,CAAC,GAAG,CAAC,MAAA,IAAI,CAAC,QAAQ,mCAAI,UAAU,CAAC,GAAG,CAAC,MAAA,IAAI,CAAC,WAAW,mCAAI,CAAC,UAAU,CAAC,CAAC;SACzE;QAED,OAAO,CAAC,GAAG,CAAC,CAAC;IACjB,CAAC;IAQD,IAAW,SAAS,CAAC,KAAuB;QACxC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;YACtC,SAAS,CAAC,SAAS,GAAG,KAAK,CAAC;SAC/B;IACL,CAAC;IAGD,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAQD,IAAW,UAAU,CAAC,KAAuB;QACzC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAEzB,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;YACtC,SAAS,CAAC,UAAU,GAAG,KAAK,CAAC;SAChC;IACL,CAAC;IAGD,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAQD,IAAW,WAAW,CAAC,KAAuB;QAC1C,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAE1B,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;YACtC,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC;SACjC;IACL,CAAC;IAGD,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAQD,IAAW,QAAQ,CAAC,KAAuB;QACvC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;YACtC,SAAS,CAAC,QAAQ,GAAG,KAAK,CAAC;SAC9B;IACL,CAAC;IAGD,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IA2CD,IAAI,IAAI,CAAC,IAAY;QACjB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAElB,wCAAwC;QACxC,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;YACtC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC;SACzB;IACL,CAAC;IAGD,IAAI,IAAI;QACJ,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IA+HD;;;;;;;;OAQG;IACH,YAAY,IAAY,EAAE,QAAiB,EAAE,KAAa,EAAE,4BAA4B,GAAG,IAAI;QAC3F,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAjUvB,gBAAgB;QAET,cAAS,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAcxB,cAAS,GAAG,OAAO,CAAC,EAAE,EAAE,CAAC;QAuCnC;;;WAGG;QACK,eAAU,GAAqB,IAAI,CAAC;QAe5C;;;WAGG;QACK,gBAAW,GAAqB,IAAI,CAAC;QAe7C;;;WAGG;QACK,iBAAY,GAAqB,IAAI,CAAC;QAe9C;;;WAGG;QACK,cAAS,GAAqB,IAAI,CAAC;QAe3C;;WAEG;QAEI,QAAG,GAAG,GAAG,CAAC;QAEjB;;;;WAIG;QAEI,wBAAmB,GAAG,CAAC,CAAC;QAE/B;;;;WAIG;QAEI,SAAI,GAAG,CAAC,CAAC;QAEhB;;;;WAIG;QAEI,SAAI,GAAG,OAAO,CAAC;QAEtB;;;WAGG;QAEI,YAAO,GAAG,GAAG,CAAC;QAErB;;WAEG;QACK,UAAK,GAAG,MAAM,CAAC,kBAAkB,CAAC;QAe1C;;;WAGG;QACI,mBAAc,GAAG,KAAK,CAAC;QAE9B;;;WAGG;QACI,aAAQ,GAAG,IAAI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAE/C;;;WAGG;QAEI,cAAS,GAAW,UAAU,CAAC;QAEtC;;WAEG;QAEI,YAAO,GAAW,MAAM,CAAC,sBAAsB,CAAC;QAEvD;;;;WAIG;QAEI,kBAAa,GAAG,MAAM,CAAC,aAAa,CAAC;QAc5C;;;;;;WAMG;QACI,wBAAmB,GAAG,IAAI,KAAK,EAAuB,CAAC;QAC9D;;;;WAIG;QACI,uBAAkB,GAAkC,IAAI,CAAC;QAEhE;;WAEG;QACI,kCAA6B,GAAG,IAAI,UAAU,EAAU,CAAC;QAChE;;WAEG;QACI,wCAAmC,GAAG,IAAI,UAAU,EAAU,CAAC;QACtE;;WAEG;QACI,iCAA4B,GAAG,IAAI,UAAU,EAAU,CAAC;QAC/D;;WAEG;QACI,6BAAwB,GAAG,IAAI,UAAU,EAAU,CAAC;QAE3D;;WAEG;QACI,gBAAW,GAAY,KAAK,CAAC;QAepC,gBAAgB;QACT,gBAAW,GAAG,IAAI,KAAK,EAAU,CAAC;QAI/B,qBAAgB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QAC/C,gBAAgB;QACT,mBAAc,GAAG,KAAK,CAAC;QAE9B,gBAAgB;QACT,sBAAiB,GAAG,IAAI,MAAM,EAAE,CAAC;QAExC,gBAAgB;QACT,mBAAc,GAAG,IAAI,KAAK,EAAyB,CAAC;QAE3D,gBAAgB;QACT,kBAAa,GAAG,IAAI,UAAU,CAAe,GAAG,CAAC,CAAC;QAE/C,oBAAe,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE3C,gBAAgB;QACT,wBAAmB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QACvC,kCAA6B,GAAG,KAAK,CAAC;QACtC,qBAAgB,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QAEjC,0BAAqB,GAAG,IAAI,CAAC;QAG7B,sBAAiB,GAAe,UAAU,CAAC,QAAQ,EAAE,CAAC;QAqE9D,gBAAgB;QACA,cAAS,GAAG,IAAI,CAAC;QAonBjC,gBAAgB;QACT,kBAAa,GAAG,KAAK,CAAC;QAQ7B,gBAAgB;QACT,mBAAc,GAAG,KAAK,CAAC;QAtrB1B,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAEhC,IAAI,4BAA4B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,EAAE;YAC/D,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,GAAG,IAAI,CAAC;SACvC;QAED,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,kBAAkB,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC;IACzF,CAAC;IAED;;;OAGG;IACI,UAAU;QACb,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC;QAE3B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACO,mBAAmB;QACzB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;QAE3B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE;YAC5B,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,QAAQ,CAAC;IACpB,CAAC;IAKD;;;;OAIG;IACI,QAAQ,CAAC,WAAqB;QACjC,IAAI,GAAG,GAAG,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;QAC/B,GAAG,IAAI,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACxC,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC7C,GAAG,IAAI,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;aACxE;SACJ;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;OAEG;IACI,uBAAuB;QAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAC;QAElD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACjF,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;;OAGG;IACI,eAAe;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,IAAU;QAC1B,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACnD,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,aAAa,GAAG,KAAK;QAChC,IAAI,aAAa,EAAE;YACf,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,cAAc,EAAE;gBAClC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE;oBACrB,OAAO,KAAK,CAAC;iBAChB;aACJ;SACJ;QACD,OAAO,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;IACxC,CAAC;IAED,gBAAgB;IACT,UAAU;QACb,KAAK,CAAC,UAAU,EAAE,CAAC;QAEnB,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QACzF,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QAEzF,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,SAAS,CAAC;QAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,SAAS,CAAC;QAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,SAAS,CAAC;QAE7B,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,SAAS,CAAC;QAChC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,SAAS,CAAC;QAEpC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;QAClC,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,SAAS,CAAC;QACnC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,SAAS,CAAC;QACpC,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,SAAS,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,SAAS,CAAC;QACpC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,SAAS,CAAC;IACzC,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,iBAA2B;QAC3C,IAAI,CAAC,iBAAiB,EAAE;YACpB,KAAK,CAAC,YAAY,EAAE,CAAC;SACxB;QAED,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7C,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACjD,CAAC;IAED,gBAAgB;IACT,eAAe;QAClB,OAAO,IAAI,CAAC,yBAAyB,EAAE,IAAI,IAAI,CAAC,+BAA+B,EAAE,CAAC;IACtF,CAAC;IAED,gBAAgB;IACT,yBAAyB;QAC5B,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,EAAE;YAC1B,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACvI,CAAC;IAED,gBAAgB;IACT,+BAA+B;QAClC,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC;QAE/G,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAEhC,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,kBAAkB,EAAE;YACzC,KAAK;gBACD,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG;oBAC5B,IAAI,CAAC,MAAM,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO;oBACpC,IAAI,CAAC,MAAM,CAAC,WAAW,KAAK,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC;oBACvD,IAAI,CAAC,MAAM,CAAC,mBAAmB,KAAK,IAAI,CAAC,mBAAmB,CAAC;SACpE;aAAM;YACH,KAAK;gBACD,IAAI,CAAC,MAAM,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS;oBACxC,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,IAAI,CAAC,UAAU;oBAC1C,IAAI,CAAC,MAAM,CAAC,WAAW,KAAK,IAAI,CAAC,WAAW;oBAC5C,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ;oBACtC,IAAI,CAAC,MAAM,CAAC,WAAW,KAAK,MAAM,CAAC,cAAc,EAAE;oBACnD,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,MAAM,CAAC,eAAe,EAAE,CAAC;SAC7D;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAcD;;;;;OAKG;IACI,aAAa,CAAC,QAAc,EAAE,iBAA2B,IAAS,CAAC;IAW1E;;;;OAIG;IACI,aAAa,CAAC,QAAc,IAAS,CAAC;IAE7C;;OAEG;IACI,MAAM;QACT,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,IAAI,CAAC,aAAa,KAAK,MAAM,CAAC,aAAa,EAAE;YAC7C,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC5B;QAED,+DAA+D;QAC/D,6EAA6E;QAC7E,sEAAsE;QACtE,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAED,gBAAgB;IACT,YAAY;QACf,IAAI,CAAC,4BAA4B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAC5D,CAAC;IAED,gBAAgB;IAChB,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;;OAGG;IACI,oBAAoB;QACvB,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;YACnE,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;gBACvC,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;aACvC;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,8BAA8B;QAClC,yBAAyB;QACzB,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACrD,IAAI,gBAAgB,EAAE;YAClB,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;SACvC;QAED,2FAA2F;QAC3F,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YACzD,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,cAAc,GAAG,GAAG,CAAC,eAAe,CAAC;YAE3C,uDAAuD;YACvD,IAAI,cAAc,EAAE;gBAChB,MAAM,MAAM,GAAG,cAAc,CAAC,aAAa,EAAE,KAAK,MAAM,CAAC;gBACzD,IAAI,MAAM,EAAE;oBACR,kHAAkH;oBAClH,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,CAAC;iBACzD;gBACD,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;gBACzE,cAAc,CAAC,gBAAgB,EAAE,CAAC;aACrC;iBAAM;gBACH,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aACrD;SACJ;IACL,CAAC;IAED;;;;;;OAMG;IACI,iBAAiB,CAAC,WAAwB,EAAE,WAA6B,IAAI;QAChF,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE;YAC5E,MAAM,CAAC,KAAK,CAAC,gEAAgE,CAAC,CAAC;YAC/E,OAAO,CAAC,CAAC;SACZ;QAED,IAAI,QAAQ,IAAI,IAAI,IAAI,QAAQ,GAAG,CAAC,EAAE;YAClC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SACzC;aAAM,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE;YAC/C,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,GAAG,WAAW,CAAC;SAC/C;aAAM;YACH,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;SACxD;QACD,IAAI,CAAC,8BAA8B,EAAE,CAAC,CAAC,uCAAuC;QAE9E,iBAAiB;QACjB,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;YAC7B,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC;SAC7C;QAED,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;IACpD,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,WAAwB;QAC7C,MAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACrD,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;YACZ,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;SACnC;QAED,iBAAiB;QACjB,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;YAC7B,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC;SAC7C;QAED,IAAI,CAAC,8BAA8B,EAAE,CAAC,CAAC,uCAAuC;IAClF,CAAC;IAED;;OAEG;IACI,cAAc;QACjB,IAAI,IAAI,CAAC,yBAAyB,EAAE,EAAE;YAClC,OAAO,IAAI,CAAC,YAAY,CAAC;SAC5B;QAED,kEAAkE;QAClE,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,gBAAgB;IACT,cAAc;QACjB,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,KAAe;QAChC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,yBAAyB,EAAE,EAAE;YAC5C,OAAO,IAAI,CAAC,mBAAmB,CAAC;SACnC;QAED,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACjD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAC;QACtD,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAElC,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE;YAChE,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;SAC3G;QAED,gDAAgD;QAChD,IAAI,IAAI,CAAC,MAAM,IAAK,IAAI,CAAC,MAAiB,CAAC,6BAA6B,EAAE;YACrE,IAAI,CAAC,MAAiB,CAAC,6BAA6B,CAAC,eAAe,CAAC,IAAI,CAAC,MAAgB,CAAC,CAAC;SAChG;QAED,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEzD,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAExD,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;;;;OAKG;IACI,sBAAsB,CAAC,UAAmB;QAC7C,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;QAC1C,IAAI,UAAU,KAAK,SAAS,EAAE;YAC1B,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC;SACvC;IACL,CAAC;IAED;;OAEG;IACI,wBAAwB;QAC3B,IAAI,CAAC,6BAA6B,GAAG,KAAK,CAAC;IAC/C,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,KAAe;;QACtC,IAAI,IAAI,CAAC,6BAA6B,IAAI,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,+BAA+B,EAAE,CAAC,EAAE;YAC1F,OAAO,IAAI,CAAC,iBAAiB,CAAC;SACjC;QAED,QAAQ;QACR,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAE7B,SAAS;QACT,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAElC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,MAAM,YAAY,GAAG,MAAM,CAAC,qBAAqB,CAAC;QAClD,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,kBAAkB,EAAE;YACzC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;YAC3B,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACtD,IAAI,CAAC,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;YAE3D,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE;gBAChB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;aACnB;YAED,IAAI,mBAUK,CAAC;YACV,IAAI,KAAK,CAAC,oBAAoB,EAAE;gBAC5B,mBAAmB,GAAG,MAAM,CAAC,qBAAqB,CAAC;aACtD;iBAAM;gBACH,mBAAmB,GAAG,MAAM,CAAC,qBAAqB,CAAC;aACtD;YAED,mBAAmB,CACf,IAAI,CAAC,GAAG,EACR,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAC3B,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EACpC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EACpC,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,OAAO,KAAK,MAAM,CAAC,sBAAsB,EAC9C,MAAM,CAAC,eAAe,EACtB,IAAI,CAAC,mBAAmB,EACxB,YAAY,CACf,CAAC;SACL;aAAM;YACH,MAAM,SAAS,GAAG,MAAM,CAAC,cAAc,EAAE,GAAG,GAAG,CAAC;YAChD,MAAM,UAAU,GAAG,MAAM,CAAC,eAAe,EAAE,GAAG,GAAG,CAAC;YAClD,IAAI,KAAK,CAAC,oBAAoB,EAAE;gBAC5B,MAAM,CAAC,qBAAqB,CACxB,MAAA,IAAI,CAAC,SAAS,mCAAI,CAAC,SAAS,EAC5B,MAAA,IAAI,CAAC,UAAU,mCAAI,SAAS,EAC5B,MAAA,IAAI,CAAC,WAAW,mCAAI,CAAC,UAAU,EAC/B,MAAA,IAAI,CAAC,QAAQ,mCAAI,UAAU,EAC3B,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EACpC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EACpC,IAAI,CAAC,iBAAiB,EACtB,MAAM,CAAC,eAAe,CACzB,CAAC;aACL;iBAAM;gBACH,MAAM,CAAC,qBAAqB,CACxB,MAAA,IAAI,CAAC,SAAS,mCAAI,CAAC,SAAS,EAC5B,MAAA,IAAI,CAAC,UAAU,mCAAI,SAAS,EAC5B,MAAA,IAAI,CAAC,WAAW,mCAAI,CAAC,UAAU,EAC/B,MAAA,IAAI,CAAC,QAAQ,mCAAI,UAAU,EAC3B,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EACpC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EACpC,IAAI,CAAC,iBAAiB,EACtB,MAAM,CAAC,eAAe,CACzB,CAAC;aACL;YAED,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YACvC,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;YACzC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;YAC3C,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YACrC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;YAClD,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;SACvD;QAED,IAAI,CAAC,mCAAmC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE/D,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,uBAAuB;QAC1B,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACtF,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAEO,oBAAoB;QACxB,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC7B,OAAO;SACV;QAED,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE/B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAClE;aAAM;YACH,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;SACtE;QAED,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;IACvC,CAAC;IAED;;;;;;OAMG;IACI,WAAW,CAAC,MAAiB,EAAE,eAAe,GAAG,KAAK;QACzD,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,IAAI,eAAe,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/C,IAAI,MAAM,GAAG,KAAK,CAAC;YACnB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;gBAC5B,GAAG,CAAC,oBAAoB,EAAE,CAAC;gBAC3B,MAAM,GAAG,MAAM,IAAI,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;SACjB;aAAM;YACH,OAAO,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAClD;IACL,CAAC;IAED;;;;;OAKG;IACI,qBAAqB,CAAC,MAAiB;QAC1C,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,OAAO,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;;OAMG;IACH,6DAA6D;IACtD,aAAa,CAAC,MAAM,GAAG,GAAG,EAAE,SAAkB,EAAE,MAAgB;QACnE,MAAM,WAAW,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;;;;;;OAOG;IACH,6DAA6D;IACtD,kBAAkB,CAAC,MAAW,EAAE,MAAM,GAAG,GAAG,EAAE,SAAkB,EAAE,MAAgB;QACrF,MAAM,WAAW,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,YAAsB,EAAE,0BAA0B,GAAG,KAAK;QACrE,cAAc;QACd,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,CAAC;QAC3C,IAAI,CAAC,mCAAmC,CAAC,KAAK,EAAE,CAAC;QACjD,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,CAAC;QAC1C,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QAEtC,SAAS;QACT,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;SACvB;QAED,aAAa;QACb,IAAI,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAEpC,oBAAoB;QACpB,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACnC,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YAChC,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;YACtC,IAAI,MAAM,EAAE;gBACR,MAAM,CAAC,OAAO,EAAE,CAAC;aACpB;SACJ;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC1D,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;gBACZ,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aAClD;YACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAChC;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;SAClC;aAAM,IAAI,IAAI,CAAC,aAAa,KAAK,MAAM,CAAC,aAAa,EAAE;YACpD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;SAClC;aAAM;YACH,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;YACnC,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE;gBACb,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;gBAC3C,IAAI,WAAW,EAAE;oBACb,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;iBAC7B;aACJ;SACJ;QAED,iBAAiB;QACjB,IAAI,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;QACxC,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE;YACb,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;SACzC;QACD,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;QAEpC,gBAAgB;QAChB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAE7B,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEnE,KAAK,CAAC,OAAO,CAAC,YAAY,EAAE,0BAA0B,CAAC,CAAC;IAC5D,CAAC;IAID;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAID;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7B,OAAO,IAAI,CAAC;SACf;QACD,OAAmB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7B,OAAO,IAAI,CAAC;SACf;QACD,OAAmB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7B,OAAO,IAAI,CAAC;SACf;QACD,OAAsB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAE,CAAC,SAAS,EAAE,CAAC;IAC3D,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7B,OAAO,IAAI,CAAC;SACf;QACD,OAAsB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAE,CAAC,SAAS,EAAE,CAAC;IAC3D,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,IAAY,EAAE,SAAc;QAChD,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;YAC7B,OAAO;SACV;QAED,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YAChC,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;YAEtC,IAAI,MAAM,EAAE;gBACR,MAAM,CAAC,OAAO,EAAE,CAAC;aACpB;SACJ;QACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,+GAA+G;QAC/G,6HAA6H;QAC7H,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,GAAG,SAAS,CAAC,kBAAkB,IAAI,MAAM,CAAC;QAClF,IAAI,CAAC,gBAAgB,CAAC,eAAe,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,GAAG,MAAM,CAAC,CAAC;QAE3G,sCAAsC;QACtC,IAAI,IAAI,CAAC,aAAa,KAAK,MAAM,CAAC,aAAa,EAAE;YAC7C,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;YAC7D,IAAI,UAAU,EAAE;gBACZ,UAAU,CAAC,aAAa,GAAG,IAAI,CAAC;aACnC;YACD,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;YAC9D,IAAI,WAAW,EAAE;gBACb,WAAW,CAAC,cAAc,GAAG,IAAI,CAAC;aACrC;YACD,IAAI,UAAU,IAAI,WAAW,EAAE;gBAC3B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAClC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aACtC;SACJ;QAED,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAE5B,IAAI,CAAC,8BAA8B,EAAE,CAAC;QACtC,IAAI,CAAC,MAAM,EAAE,CAAC;IAClB,CAAC;IAED,6DAA6D;IACnD,WAAW,CAAC,SAAc;QAChC,QAAQ;IACZ,CAAC;IAED,gBAAgB;IACT,sBAAsB;QACzB,MAAM,CAAC,qBAAqB,CACxB,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,cAAc,EAC9C,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,WAAW,EAC3C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAClC,IAAI,EACJ,IAAI,CAAC,SAAS,EAAE,CAAC,eAAe,CACnC,CAAC;QACF,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC1G,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAES,2BAA2B;QACjC,gBAAgB;IACpB,CAAC;IAES,gCAAgC;QACtC,gBAAgB;IACpB,CAAC;IAED;;;;OAIG;IACI,yBAAyB;QAC5B,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACI,mBAAmB;QACtB,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAC,IAAY,EAAE,KAAU;QACjD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;SAC9B;QACD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QACpC,iBAAiB;QACjB,IAAI,IAAI,KAAK,oBAAoB,EAAE;YAC/B,IAAI,CAAC,gBAAgB,CAAC,eAAe,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC;SAC3E;IACL,CAAC;IAED;;;OAGG;IACH,6DAA6D;IACtD,eAAe,CAAC,IAAY,EAAE,WAAmB;QACpD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC9C,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YACrC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YACrC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;YACnC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACxD;QAED,qCAAqC;QACrC,IAAI,IAAI,CAAC,aAAa,KAAK,MAAM,CAAC,8BAA8B,EAAE;YAC9D,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;SAC/E;IACL,CAAC;IAED,gBAAgB;IACT,YAAY,KAAI,CAAC;IAExB;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAG,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChE,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE7C,OAAO;QACP,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAE/C,SAAS;QACT,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,CAAC;SACvD;QAED,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;SAC9C;QACD,aAAa;QACb,mBAAmB,CAAC,0BAA0B,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;QAC1E,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAE7D,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAEjD,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,IAAY,EAAE,YAA4B,IAAI;QACvD,MAAM,MAAM,GAAG,mBAAmB,CAAC,KAAK,CACpC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,wBAAwB,CAAC,EACjI,IAAI,CACP,CAAC;QACF,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;QACnB,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC;QAE1B,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAEhD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,SAAkB;QAClC,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE9B,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAE1C,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,IAAW,gBAAgB;QACvB,IAAI,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEnE,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,SAAkB,EAAE,MAAe;QACxD,OAAO,CAAC,oBAAoB,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,CAAC;IAC3E,CAAC;IAED;;;;;;;;OAQG;IACH,gEAAgE;IAChE,MAAM,CAAC,sBAAsB,CAAC,IAAY,EAAE,IAAY,EAAE,KAAY,EAAE,sBAA8B,CAAC,EAAE,2BAAoC,IAAI;QAC7I,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE;YACtD,gEAAgE;YAChE,mBAAmB,EAAE,mBAAmB;YACxC,wBAAwB,EAAE,wBAAwB;SACrD,CAAC,CAAC;QAEH,IAAI,eAAe,EAAE;YACjB,OAAqB,eAAe,CAAC;SACxC;QAED,8BAA8B;QAC9B,OAAO,GAAG,EAAE,CAAC,MAAM,CAAC,0BAA0B,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAChE,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC;IACjC,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,YAAiB,EAAE,KAAY;QAC/C,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;QAC/B,MAAM,SAAS,GAAG,MAAM,CAAC,sBAAsB,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,YAAY,CAAC,mBAAmB,EAAE,YAAY,CAAC,wBAAwB,CAAC,CAAC;QAEzJ,MAAM,MAAM,GAAG,mBAAmB,CAAC,KAAK,CAAC,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;QAEzE,SAAS;QACT,IAAI,YAAY,CAAC,QAAQ,KAAK,SAAS,EAAE;YACrC,MAAM,CAAC,gBAAgB,GAAG,YAAY,CAAC,QAAQ,CAAC;SACnD;QAED,wBAAwB;QACxB,IAAI,YAAY,CAAC,mBAAmB,KAAK,SAAS,EAAE;YAChD,MAAM,CAAC,2BAA2B,GAAG,YAAY,CAAC,mBAAmB,CAAC;SACzE;QAED,8DAA8D;QAC9D,IAAI,MAAM,CAAC,MAAM,EAAE;YACf,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAElC,MAAM,CAAC,YAAY,EAAE,CAAC;SACzB;QAED,IAAI,YAAY,CAAC,QAAQ,EAAE;YACvB,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,6BAA6B;SAC5F;QAED,IAAU,MAAO,CAAC,WAAW,EAAE;YAC3B,yBAAyB;YACzB,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAClC,MAAO,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;SACvE;QAED,SAAS;QACT,IAAI,YAAY,CAAC,MAAM,EAAE;YACrB,IAAU,MAAO,CAAC,SAAS,EAAE;gBACnB,MAAO,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;aACnE;SACJ;QAED,2BAA2B;QAC3B,IAAI,YAAY,CAAC,aAAa,EAAE;YAC5B,MAAM,SAAS,GAAG,YAAY,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,kBAAkB,EAAE,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YACnH,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;SAClE;QAED,aAAa;QACb,IAAI,YAAY,CAAC,UAAU,EAAE;YACzB,KAAK,IAAI,cAAc,GAAG,CAAC,EAAE,cAAc,GAAG,YAAY,CAAC,UAAU,CAAC,MAAM,EAAE,cAAc,EAAE,EAAE;gBAC5F,MAAM,eAAe,GAAG,YAAY,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;gBAChE,MAAM,aAAa,GAAG,QAAQ,CAAC,mBAAmB,CAAC,CAAC;gBACpD,IAAI,aAAa,EAAE;oBACf,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;iBAChE;aACJ;YACD,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;SAC1D;QAED,IAAI,YAAY,CAAC,WAAW,EAAE;YAC1B,KAAK,CAAC,cAAc,CAAC,MAAM,EAAE,YAAY,CAAC,eAAe,EAAE,YAAY,CAAC,aAAa,EAAE,YAAY,CAAC,eAAe,EAAE,YAAY,CAAC,gBAAgB,IAAI,GAAG,CAAC,CAAC;SAC9J;QAED,sFAAsF;QACtF,IAAI,YAAY,CAAC,SAAS,KAAK,SAAS,EAAE;YACtC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;SAC7C;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;;AAz7CD;;GAEG;AACH,6DAA6D;AAC/C,iCAA0B,GAAG,CAAC,IAAY,EAAE,KAAY,EAAU,EAAE;IAC9E,MAAM,WAAW,CAAC,iBAAiB,CAAC,CAAC;AACzC,CAAC,CAAC;AAEF;;;;GAIG;AACoB,yBAAkB,GAAG,SAAS,CAAC,kBAAkB,CAAC;AACzE;;;GAGG;AACoB,0BAAmB,GAAG,SAAS,CAAC,mBAAmB,CAAC;AAE3E;;;GAGG;AACoB,6BAAsB,GAAG,SAAS,CAAC,sBAAsB,CAAC;AACjF;;GAEG;AACoB,+BAAwB,GAAG,SAAS,CAAC,wBAAwB,CAAC;AAErF;;;GAGG;AACoB,oBAAa,GAAG,SAAS,CAAC,aAAa,CAAC;AAC/D;;;GAGG;AACoB,qCAA8B,GAAG,SAAS,CAAC,8BAA8B,CAAC;AACjG;;GAEG;AACoB,gDAAyC,GAAG,SAAS,CAAC,yCAAyC,CAAC;AACvH;;GAEG;AACoB,iDAA0C,GAAG,SAAS,CAAC,0CAA0C,CAAC;AACzH;;GAEG;AACoB,sCAA+B,GAAG,SAAS,CAAC,+BAA+B,CAAC;AACnG;;GAEG;AACoB,uCAAgC,GAAG,SAAS,CAAC,gCAAgC,CAAC;AACrG;;GAEG;AACoB,kBAAW,GAAG,SAAS,CAAC,WAAW,CAAC;AAC3D;;GAEG;AACoB,qBAAc,GAAG,SAAS,CAAC,cAAc,CAAC;AACjE;;GAEG;AACoB,sBAAe,GAAG,SAAS,CAAC,eAAe,CAAC;AAEnE;;GAEG;AACW,+CAAwC,GAAG,KAAK,CAAC;AAS/D;IADC,kBAAkB,CAAC,UAAU,CAAC;yCACG;AAclC;IADC,kBAAkB,CAAC,UAAU,CAAC;yCACI;AAsDnC;IADC,SAAS,EAAE;uCAGX;AAiBD;IADC,SAAS,EAAE;wCAGX;AAiBD;IADC,SAAS,EAAE;yCAGX;AAiBD;IADC,SAAS,EAAE;sCAGX;AAMD;IADC,SAAS,EAAE;mCACK;AAQjB;IADC,SAAS,EAAE;mDACmB;AAQ/B;IADC,SAAS,EAAE;oCACI;AAQhB;IADC,SAAS,EAAE;oCACU;AAOtB;IADC,SAAS,EAAE;uCACS;AAgBrB;IADC,SAAS,EAAE;kCAGX;AAmBD;IADC,SAAS,EAAE;yCAC0B;AAMtC;IADC,SAAS,EAAE;uCAC2C;AAQvD;IADC,SAAS,EAAE;6CACgC;AAM5C;IADC,SAAS,EAAE;kDACsB;AAMlC;IADC,SAAS,EAAE;wDAC6B", "sourcesContent": ["import { serialize, SerializationHelper, serializeAsVector3 } from \"../Misc/decorators\";\r\nimport { SmartArray } from \"../Misc/smartArray\";\r\nimport { Tools } from \"../Misc/tools\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { CameraInputsManager } from \"./cameraInputsManager\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Matrix, Vector3, Quaternion } from \"../Maths/math.vector\";\r\nimport { Node } from \"../node\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { ICullable } from \"../Culling/boundingInfo\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { GetClass } from \"../Misc/typeStore\";\r\nimport { _WarnImport } from \"../Misc/devTools\";\r\nimport { Viewport } from \"../Maths/math.viewport\";\r\nimport { Frustum } from \"../Maths/math.frustum\";\r\nimport type { Plane } from \"../Maths/math.plane\";\r\nimport { Constants } from \"../Engines/constants\";\r\n\r\ndeclare type PostProcess = import(\"../PostProcesses/postProcess\").PostProcess;\r\ndeclare type RenderTargetTexture = import(\"../Materials/Textures/renderTargetTexture\").RenderTargetTexture;\r\ndeclare type FreeCamera = import(\"./freeCamera\").FreeCamera;\r\ndeclare type TargetCamera = import(\"./targetCamera\").TargetCamera;\r\ndeclare type Ray = import(\"../Culling/ray\").Ray;\r\n\r\n/**\r\n * This is the base class of all the camera used in the application.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras\r\n */\r\nexport class Camera extends Node {\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public static _CreateDefaultParsedCamera = (name: string, scene: Scene): Camera => {\r\n        throw _WarnImport(\"UniversalCamera\");\r\n    };\r\n\r\n    /**\r\n     * This is the default projection mode used by the cameras.\r\n     * It helps recreating a feeling of perspective and better appreciate depth.\r\n     * This is the best way to simulate real life cameras.\r\n     */\r\n    public static readonly PERSPECTIVE_CAMERA = Constants.PERSPECTIVE_CAMERA;\r\n    /**\r\n     * This helps creating camera with an orthographic mode.\r\n     * Orthographic is commonly used in engineering as a means to produce object specifications that communicate dimensions unambiguously, each line of 1 unit length (cm, meter..whatever) will appear to have the same length everywhere on the drawing. This allows the drafter to dimension only a subset of lines and let the reader know that other lines of that length on the drawing are also that length in reality. Every parallel line in the drawing is also parallel in the object.\r\n     */\r\n    public static readonly ORTHOGRAPHIC_CAMERA = Constants.ORTHOGRAPHIC_CAMERA;\r\n\r\n    /**\r\n     * This is the default FOV mode for perspective cameras.\r\n     * This setting aligns the upper and lower bounds of the viewport to the upper and lower bounds of the camera frustum.\r\n     */\r\n    public static readonly FOVMODE_VERTICAL_FIXED = Constants.FOVMODE_VERTICAL_FIXED;\r\n    /**\r\n     * This setting aligns the left and right bounds of the viewport to the left and right bounds of the camera frustum.\r\n     */\r\n    public static readonly FOVMODE_HORIZONTAL_FIXED = Constants.FOVMODE_HORIZONTAL_FIXED;\r\n\r\n    /**\r\n     * This specifies there is no need for a camera rig.\r\n     * Basically only one eye is rendered corresponding to the camera.\r\n     */\r\n    public static readonly RIG_MODE_NONE = Constants.RIG_MODE_NONE;\r\n    /**\r\n     * Simulates a camera Rig with one blue eye and one red eye.\r\n     * This can be use with 3d blue and red glasses.\r\n     */\r\n    public static readonly RIG_MODE_STEREOSCOPIC_ANAGLYPH = Constants.RIG_MODE_STEREOSCOPIC_ANAGLYPH;\r\n    /**\r\n     * Defines that both eyes of the camera will be rendered side by side with a parallel target.\r\n     */\r\n    public static readonly RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_PARALLEL = Constants.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_PARALLEL;\r\n    /**\r\n     * Defines that both eyes of the camera will be rendered side by side with a none parallel target.\r\n     */\r\n    public static readonly RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_CROSSEYED = Constants.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_CROSSEYED;\r\n    /**\r\n     * Defines that both eyes of the camera will be rendered over under each other.\r\n     */\r\n    public static readonly RIG_MODE_STEREOSCOPIC_OVERUNDER = Constants.RIG_MODE_STEREOSCOPIC_OVERUNDER;\r\n    /**\r\n     * Defines that both eyes of the camera will be rendered on successive lines interlaced for passive 3d monitors.\r\n     */\r\n    public static readonly RIG_MODE_STEREOSCOPIC_INTERLACED = Constants.RIG_MODE_STEREOSCOPIC_INTERLACED;\r\n    /**\r\n     * Defines that both eyes of the camera should be renderered in a VR mode (carbox).\r\n     */\r\n    public static readonly RIG_MODE_VR = Constants.RIG_MODE_VR;\r\n    /**\r\n     * Defines that both eyes of the camera should be renderered in a VR mode (webVR).\r\n     */\r\n    public static readonly RIG_MODE_WEBVR = Constants.RIG_MODE_WEBVR;\r\n    /**\r\n     * Custom rig mode allowing rig cameras to be populated manually with any number of cameras\r\n     */\r\n    public static readonly RIG_MODE_CUSTOM = Constants.RIG_MODE_CUSTOM;\r\n\r\n    /**\r\n     * Defines if by default attaching controls should prevent the default javascript event to continue.\r\n     */\r\n    public static ForceAttachControlToAlwaysPreventDefault = false;\r\n\r\n    /**\r\n     * Define the input manager associated with the camera.\r\n     */\r\n    public inputs: CameraInputsManager<Camera>;\r\n\r\n    /** @internal */\r\n    @serializeAsVector3(\"position\")\r\n    public _position = Vector3.Zero();\r\n\r\n    /**\r\n     * Define the current local position of the camera in the scene\r\n     */\r\n    public get position(): Vector3 {\r\n        return this._position;\r\n    }\r\n\r\n    public set position(newPosition: Vector3) {\r\n        this._position = newPosition;\r\n    }\r\n\r\n    @serializeAsVector3(\"upVector\")\r\n    protected _upVector = Vector3.Up();\r\n\r\n    /**\r\n     * The vector the camera should consider as up.\r\n     * (default is Vector3(0, 1, 0) aka Vector3.Up())\r\n     */\r\n    public set upVector(vec: Vector3) {\r\n        this._upVector = vec;\r\n    }\r\n\r\n    public get upVector() {\r\n        return this._upVector;\r\n    }\r\n\r\n    /**\r\n     * The screen area in scene units squared\r\n     */\r\n    public get screenArea(): number {\r\n        let x = 0;\r\n        let y = 0;\r\n        if (this.mode === Camera.PERSPECTIVE_CAMERA) {\r\n            if (this.fovMode === Camera.FOVMODE_VERTICAL_FIXED) {\r\n                y = this.minZ * 2 * Math.tan(this.fov / 2);\r\n                x = this.getEngine().getAspectRatio(this) * y;\r\n            } else {\r\n                x = this.minZ * 2 * Math.tan(this.fov / 2);\r\n                y = x / this.getEngine().getAspectRatio(this);\r\n            }\r\n        } else {\r\n            const halfWidth = this.getEngine().getRenderWidth() / 2.0;\r\n            const halfHeight = this.getEngine().getRenderHeight() / 2.0;\r\n\r\n            x = (this.orthoRight ?? halfWidth) - (this.orthoLeft ?? -halfWidth);\r\n            y = (this.orthoTop ?? halfHeight) - (this.orthoBottom ?? -halfHeight);\r\n        }\r\n\r\n        return x * y;\r\n    }\r\n\r\n    /**\r\n     * Define the current limit on the left side for an orthographic camera\r\n     * In scene unit\r\n     */\r\n    private _orthoLeft: Nullable<number> = null;\r\n\r\n    public set orthoLeft(value: Nullable<number>) {\r\n        this._orthoLeft = value;\r\n\r\n        for (const rigCamera of this._rigCameras) {\r\n            rigCamera.orthoLeft = value;\r\n        }\r\n    }\r\n\r\n    @serialize()\r\n    public get orthoLeft(): Nullable<number> {\r\n        return this._orthoLeft;\r\n    }\r\n\r\n    /**\r\n     * Define the current limit on the right side for an orthographic camera\r\n     * In scene unit\r\n     */\r\n    private _orthoRight: Nullable<number> = null;\r\n\r\n    public set orthoRight(value: Nullable<number>) {\r\n        this._orthoRight = value;\r\n\r\n        for (const rigCamera of this._rigCameras) {\r\n            rigCamera.orthoRight = value;\r\n        }\r\n    }\r\n\r\n    @serialize()\r\n    public get orthoRight(): Nullable<number> {\r\n        return this._orthoRight;\r\n    }\r\n\r\n    /**\r\n     * Define the current limit on the bottom side for an orthographic camera\r\n     * In scene unit\r\n     */\r\n    private _orthoBottom: Nullable<number> = null;\r\n\r\n    public set orthoBottom(value: Nullable<number>) {\r\n        this._orthoBottom = value;\r\n\r\n        for (const rigCamera of this._rigCameras) {\r\n            rigCamera.orthoBottom = value;\r\n        }\r\n    }\r\n\r\n    @serialize()\r\n    public get orthoBottom(): Nullable<number> {\r\n        return this._orthoBottom;\r\n    }\r\n\r\n    /**\r\n     * Define the current limit on the top side for an orthographic camera\r\n     * In scene unit\r\n     */\r\n    private _orthoTop: Nullable<number> = null;\r\n\r\n    public set orthoTop(value: Nullable<number>) {\r\n        this._orthoTop = value;\r\n\r\n        for (const rigCamera of this._rigCameras) {\r\n            rigCamera.orthoTop = value;\r\n        }\r\n    }\r\n\r\n    @serialize()\r\n    public get orthoTop(): Nullable<number> {\r\n        return this._orthoTop;\r\n    }\r\n\r\n    /**\r\n     * Field Of View is set in Radians. (default is 0.8)\r\n     */\r\n    @serialize()\r\n    public fov = 0.8;\r\n\r\n    /**\r\n     * Projection plane tilt around the X axis (horizontal), set in Radians. (default is 0)\r\n     * Can be used to make vertical lines in world space actually vertical on the screen.\r\n     * See https://forum.babylonjs.com/t/add-vertical-shift-to-3ds-max-exporter-babylon-cameras/17480\r\n     */\r\n    @serialize()\r\n    public projectionPlaneTilt = 0;\r\n\r\n    /**\r\n     * Define the minimum distance the camera can see from.\r\n     * This is important to note that the depth buffer are not infinite and the closer it starts\r\n     * the more your scene might encounter depth fighting issue.\r\n     */\r\n    @serialize()\r\n    public minZ = 1;\r\n\r\n    /**\r\n     * Define the maximum distance the camera can see to.\r\n     * This is important to note that the depth buffer are not infinite and the further it end\r\n     * the more your scene might encounter depth fighting issue.\r\n     */\r\n    @serialize()\r\n    public maxZ = 10000.0;\r\n\r\n    /**\r\n     * Define the default inertia of the camera.\r\n     * This helps giving a smooth feeling to the camera movement.\r\n     */\r\n    @serialize()\r\n    public inertia = 0.9;\r\n\r\n    /**\r\n     * Define the mode of the camera (Camera.PERSPECTIVE_CAMERA or Camera.ORTHOGRAPHIC_CAMERA)\r\n     */\r\n    private _mode = Camera.PERSPECTIVE_CAMERA;\r\n    set mode(mode: number) {\r\n        this._mode = mode;\r\n\r\n        // Pass the mode down to the rig cameras\r\n        for (const rigCamera of this._rigCameras) {\r\n            rigCamera.mode = mode;\r\n        }\r\n    }\r\n\r\n    @serialize()\r\n    get mode(): number {\r\n        return this._mode;\r\n    }\r\n\r\n    /**\r\n     * Define whether the camera is intermediate.\r\n     * This is useful to not present the output directly to the screen in case of rig without post process for instance\r\n     */\r\n    public isIntermediate = false;\r\n\r\n    /**\r\n     * Define the viewport of the camera.\r\n     * This correspond to the portion of the screen the camera will render to in normalized 0 to 1 unit.\r\n     */\r\n    public viewport = new Viewport(0, 0, 1.0, 1.0);\r\n\r\n    /**\r\n     * Restricts the camera to viewing objects with the same layerMask.\r\n     * A camera with a layerMask of 1 will render mesh.layerMask & camera.layerMask!== 0\r\n     */\r\n    @serialize()\r\n    public layerMask: number = 0x0fffffff;\r\n\r\n    /**\r\n     * fovMode sets the camera frustum bounds to the viewport bounds. (default is FOVMODE_VERTICAL_FIXED)\r\n     */\r\n    @serialize()\r\n    public fovMode: number = Camera.FOVMODE_VERTICAL_FIXED;\r\n\r\n    /**\r\n     * Rig mode of the camera.\r\n     * This is useful to create the camera with two \"eyes\" instead of one to create VR or stereoscopic scenes.\r\n     * This is normally controlled byt the camera themselves as internal use.\r\n     */\r\n    @serialize()\r\n    public cameraRigMode = Camera.RIG_MODE_NONE;\r\n\r\n    /**\r\n     * Defines the distance between both \"eyes\" in case of a RIG\r\n     */\r\n    @serialize()\r\n    public interaxialDistance: number;\r\n\r\n    /**\r\n     * Defines if stereoscopic rendering is done side by side or over under.\r\n     */\r\n    @serialize()\r\n    public isStereoscopicSideBySide: boolean;\r\n\r\n    /**\r\n     * Defines the list of custom render target which are rendered to and then used as the input to this camera's render. Eg. display another camera view on a TV in the main scene\r\n     * This is pretty helpful if you wish to make a camera render to a texture you could reuse somewhere\r\n     * else in the scene. (Eg. security camera)\r\n     *\r\n     * To change the final output target of the camera, camera.outputRenderTarget should be used instead (eg. webXR renders to a render target corresponding to an HMD)\r\n     */\r\n    public customRenderTargets = new Array<RenderTargetTexture>();\r\n    /**\r\n     * When set, the camera will render to this render target instead of the default canvas\r\n     *\r\n     * If the desire is to use the output of a camera as a texture in the scene consider using camera.customRenderTargets instead\r\n     */\r\n    public outputRenderTarget: Nullable<RenderTargetTexture> = null;\r\n\r\n    /**\r\n     * Observable triggered when the camera view matrix has changed.\r\n     */\r\n    public onViewMatrixChangedObservable = new Observable<Camera>();\r\n    /**\r\n     * Observable triggered when the camera Projection matrix has changed.\r\n     */\r\n    public onProjectionMatrixChangedObservable = new Observable<Camera>();\r\n    /**\r\n     * Observable triggered when the inputs have been processed.\r\n     */\r\n    public onAfterCheckInputsObservable = new Observable<Camera>();\r\n    /**\r\n     * Observable triggered when reset has been called and applied to the camera.\r\n     */\r\n    public onRestoreStateObservable = new Observable<Camera>();\r\n\r\n    /**\r\n     * Is this camera a part of a rig system?\r\n     */\r\n    public isRigCamera: boolean = false;\r\n\r\n    /**\r\n     * If isRigCamera set to true this will be set with the parent camera.\r\n     * The parent camera is not (!) necessarily the .parent of this camera (like in the case of XR)\r\n     */\r\n    public rigParent?: Camera;\r\n\r\n    /**\r\n     * Render pass id used by the camera to render into the main framebuffer\r\n     */\r\n    public renderPassId: number;\r\n\r\n    /** @internal */\r\n    public _cameraRigParams: any;\r\n    /** @internal */\r\n    public _rigCameras = new Array<Camera>();\r\n    /** @internal */\r\n    public _rigPostProcess: Nullable<PostProcess>;\r\n\r\n    protected _webvrViewMatrix = Matrix.Identity();\r\n    /** @internal */\r\n    public _skipRendering = false;\r\n\r\n    /** @internal */\r\n    public _projectionMatrix = new Matrix();\r\n\r\n    /** @internal */\r\n    public _postProcesses = new Array<Nullable<PostProcess>>();\r\n\r\n    /** @internal */\r\n    public _activeMeshes = new SmartArray<AbstractMesh>(256);\r\n\r\n    protected _globalPosition = Vector3.Zero();\r\n\r\n    /** @internal */\r\n    public _computedViewMatrix = Matrix.Identity();\r\n    private _doNotComputeProjectionMatrix = false;\r\n    private _transformMatrix = Matrix.Zero();\r\n    private _frustumPlanes: Plane[];\r\n    private _refreshFrustumPlanes = true;\r\n    private _storedFov: number;\r\n    private _stateStored: boolean;\r\n    private _absoluteRotation: Quaternion = Quaternion.Identity();\r\n\r\n    /**\r\n     * Instantiates a new camera object.\r\n     * This should not be used directly but through the inherited cameras: ArcRotate, Free...\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras\r\n     * @param name Defines the name of the camera in the scene\r\n     * @param position Defines the position of the camera\r\n     * @param scene Defines the scene the camera belongs too\r\n     * @param setActiveOnSceneIfNoneActive Defines if the camera should be set as active after creation if no other camera have been defined in the scene\r\n     */\r\n    constructor(name: string, position: Vector3, scene?: Scene, setActiveOnSceneIfNoneActive = true) {\r\n        super(name, scene);\r\n\r\n        this.getScene().addCamera(this);\r\n\r\n        if (setActiveOnSceneIfNoneActive && !this.getScene().activeCamera) {\r\n            this.getScene().activeCamera = this;\r\n        }\r\n\r\n        this.position = position;\r\n        this.renderPassId = this.getScene().getEngine().createRenderPassId(`Camera ${name}`);\r\n    }\r\n\r\n    /**\r\n     * Store current camera state (fov, position, etc..)\r\n     * @returns the camera\r\n     */\r\n    public storeState(): Camera {\r\n        this._stateStored = true;\r\n        this._storedFov = this.fov;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Restores the camera state values if it has been stored. You must call storeState() first\r\n     */\r\n    protected _restoreStateValues(): boolean {\r\n        if (!this._stateStored) {\r\n            return false;\r\n        }\r\n\r\n        this.fov = this._storedFov;\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Restored camera state. You must call storeState() first.\r\n     * @returns true if restored and false otherwise\r\n     */\r\n    public restoreState(): boolean {\r\n        if (this._restoreStateValues()) {\r\n            this.onRestoreStateObservable.notifyObservers(this);\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of the camera.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"Camera\";\r\n    }\r\n\r\n    /** @internal */\r\n    public readonly _isCamera = true;\r\n\r\n    /**\r\n     * Gets a string representation of the camera useful for debug purpose.\r\n     * @param fullDetails Defines that a more verbose level of logging is required\r\n     * @returns the string representation\r\n     */\r\n    public toString(fullDetails?: boolean): string {\r\n        let ret = \"Name: \" + this.name;\r\n        ret += \", type: \" + this.getClassName();\r\n        if (this.animations) {\r\n            for (let i = 0; i < this.animations.length; i++) {\r\n                ret += \", animation[0]: \" + this.animations[i].toString(fullDetails);\r\n            }\r\n        }\r\n        return ret;\r\n    }\r\n\r\n    /**\r\n     * Automatically tilts the projection plane, using `projectionPlaneTilt`, to correct the perspective effect on vertical lines.\r\n     */\r\n    public applyVerticalCorrection() {\r\n        const rot = this.absoluteRotation.toEulerAngles();\r\n\r\n        this.projectionPlaneTilt = this._scene.useRightHandedSystem ? -rot.x : rot.x;\r\n    }\r\n\r\n    /**\r\n     * Gets the current world space position of the camera.\r\n     */\r\n    public get globalPosition(): Vector3 {\r\n        return this._globalPosition;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of active meshes this frame (meshes no culled or excluded by lod s in the frame)\r\n     * @returns the active meshe list\r\n     */\r\n    public getActiveMeshes(): SmartArray<AbstractMesh> {\r\n        return this._activeMeshes;\r\n    }\r\n\r\n    /**\r\n     * Check whether a mesh is part of the current active mesh list of the camera\r\n     * @param mesh Defines the mesh to check\r\n     * @returns true if active, false otherwise\r\n     */\r\n    public isActiveMesh(mesh: Mesh): boolean {\r\n        return this._activeMeshes.indexOf(mesh) !== -1;\r\n    }\r\n\r\n    /**\r\n     * Is this camera ready to be used/rendered\r\n     * @param completeCheck defines if a complete check (including post processes) has to be done (false by default)\r\n     * @returns true if the camera is ready\r\n     */\r\n    public isReady(completeCheck = false): boolean {\r\n        if (completeCheck) {\r\n            for (const pp of this._postProcesses) {\r\n                if (pp && !pp.isReady()) {\r\n                    return false;\r\n                }\r\n            }\r\n        }\r\n        return super.isReady(completeCheck);\r\n    }\r\n\r\n    /** @internal */\r\n    public _initCache() {\r\n        super._initCache();\r\n\r\n        this._cache.position = new Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n        this._cache.upVector = new Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n\r\n        this._cache.mode = undefined;\r\n        this._cache.minZ = undefined;\r\n        this._cache.maxZ = undefined;\r\n\r\n        this._cache.fov = undefined;\r\n        this._cache.fovMode = undefined;\r\n        this._cache.aspectRatio = undefined;\r\n\r\n        this._cache.orthoLeft = undefined;\r\n        this._cache.orthoRight = undefined;\r\n        this._cache.orthoBottom = undefined;\r\n        this._cache.orthoTop = undefined;\r\n        this._cache.renderWidth = undefined;\r\n        this._cache.renderHeight = undefined;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _updateCache(ignoreParentClass?: boolean): void {\r\n        if (!ignoreParentClass) {\r\n            super._updateCache();\r\n        }\r\n\r\n        this._cache.position.copyFrom(this.position);\r\n        this._cache.upVector.copyFrom(this.upVector);\r\n    }\r\n\r\n    /** @internal */\r\n    public _isSynchronized(): boolean {\r\n        return this._isSynchronizedViewMatrix() && this._isSynchronizedProjectionMatrix();\r\n    }\r\n\r\n    /** @internal */\r\n    public _isSynchronizedViewMatrix(): boolean {\r\n        if (!super._isSynchronized()) {\r\n            return false;\r\n        }\r\n\r\n        return this._cache.position.equals(this.position) && this._cache.upVector.equals(this.upVector) && this.isSynchronizedWithParent();\r\n    }\r\n\r\n    /** @internal */\r\n    public _isSynchronizedProjectionMatrix(): boolean {\r\n        let check = this._cache.mode === this.mode && this._cache.minZ === this.minZ && this._cache.maxZ === this.maxZ;\r\n\r\n        if (!check) {\r\n            return false;\r\n        }\r\n\r\n        const engine = this.getEngine();\r\n\r\n        if (this.mode === Camera.PERSPECTIVE_CAMERA) {\r\n            check =\r\n                this._cache.fov === this.fov &&\r\n                this._cache.fovMode === this.fovMode &&\r\n                this._cache.aspectRatio === engine.getAspectRatio(this) &&\r\n                this._cache.projectionPlaneTilt === this.projectionPlaneTilt;\r\n        } else {\r\n            check =\r\n                this._cache.orthoLeft === this.orthoLeft &&\r\n                this._cache.orthoRight === this.orthoRight &&\r\n                this._cache.orthoBottom === this.orthoBottom &&\r\n                this._cache.orthoTop === this.orthoTop &&\r\n                this._cache.renderWidth === engine.getRenderWidth() &&\r\n                this._cache.renderHeight === engine.getRenderHeight();\r\n        }\r\n\r\n        return check;\r\n    }\r\n\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public attachControl(noPreventDefault?: boolean): void;\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * @param ignored defines an ignored parameter kept for backward compatibility.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     * BACK COMPAT SIGNATURE ONLY.\r\n     */\r\n    public attachControl(ignored: any, noPreventDefault?: boolean): void;\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * This function is here because typescript removes the typing of the last function.\r\n     * @param _ignored defines an ignored parameter kept for backward compatibility.\r\n     * @param _noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public attachControl(_ignored?: any, _noPreventDefault?: boolean): void {}\r\n\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    public detachControl(): void;\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     * @param ignored defines an ignored parameter kept for backward compatibility.\r\n     */\r\n    public detachControl(ignored?: any): void;\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     * This function is here because typescript removes the typing of the last function.\r\n     * @param _ignored defines an ignored parameter kept for backward compatibility.\r\n     */\r\n    public detachControl(_ignored?: any): void {}\r\n\r\n    /**\r\n     * Update the camera state according to the different inputs gathered during the frame.\r\n     */\r\n    public update(): void {\r\n        this._checkInputs();\r\n        if (this.cameraRigMode !== Camera.RIG_MODE_NONE) {\r\n            this._updateRigCameras();\r\n        }\r\n\r\n        // Attempt to update the camera's view and projection matrices.\r\n        // This call is being made because these matrices are no longer being updated\r\n        // as a part of the picking ray process (in addition to scene.render).\r\n        this.getViewMatrix();\r\n        this.getProjectionMatrix();\r\n    }\r\n\r\n    /** @internal */\r\n    public _checkInputs(): void {\r\n        this.onAfterCheckInputsObservable.notifyObservers(this);\r\n    }\r\n\r\n    /** @internal */\r\n    public get rigCameras(): Camera[] {\r\n        return this._rigCameras;\r\n    }\r\n\r\n    /**\r\n     * Gets the post process used by the rig cameras\r\n     */\r\n    public get rigPostProcess(): Nullable<PostProcess> {\r\n        return this._rigPostProcess;\r\n    }\r\n\r\n    /**\r\n     * Internal, gets the first post process.\r\n     * @returns the first post process to be run on this camera.\r\n     */\r\n    public _getFirstPostProcess(): Nullable<PostProcess> {\r\n        for (let ppIndex = 0; ppIndex < this._postProcesses.length; ppIndex++) {\r\n            if (this._postProcesses[ppIndex] !== null) {\r\n                return this._postProcesses[ppIndex];\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private _cascadePostProcessesToRigCams(): void {\r\n        // invalidate framebuffer\r\n        const firstPostProcess = this._getFirstPostProcess();\r\n        if (firstPostProcess) {\r\n            firstPostProcess.markTextureDirty();\r\n        }\r\n\r\n        // glue the rigPostProcess to the end of the user postprocesses & assign to each sub-camera\r\n        for (let i = 0, len = this._rigCameras.length; i < len; i++) {\r\n            const cam = this._rigCameras[i];\r\n            const rigPostProcess = cam._rigPostProcess;\r\n\r\n            // for VR rig, there does not have to be a post process\r\n            if (rigPostProcess) {\r\n                const isPass = rigPostProcess.getEffectName() === \"pass\";\r\n                if (isPass) {\r\n                    // any rig which has a PassPostProcess for rig[0], cannot be isIntermediate when there are also user postProcesses\r\n                    cam.isIntermediate = this._postProcesses.length === 0;\r\n                }\r\n                cam._postProcesses = this._postProcesses.slice(0).concat(rigPostProcess);\r\n                rigPostProcess.markTextureDirty();\r\n            } else {\r\n                cam._postProcesses = this._postProcesses.slice(0);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Attach a post process to the camera.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/postProcesses/usePostProcesses#attach-postprocess\r\n     * @param postProcess The post process to attach to the camera\r\n     * @param insertAt The position of the post process in case several of them are in use in the scene\r\n     * @returns the position the post process has been inserted at\r\n     */\r\n    public attachPostProcess(postProcess: PostProcess, insertAt: Nullable<number> = null): number {\r\n        if (!postProcess.isReusable() && this._postProcesses.indexOf(postProcess) > -1) {\r\n            Logger.Error(\"You're trying to reuse a post process not defined as reusable.\");\r\n            return 0;\r\n        }\r\n\r\n        if (insertAt == null || insertAt < 0) {\r\n            this._postProcesses.push(postProcess);\r\n        } else if (this._postProcesses[insertAt] === null) {\r\n            this._postProcesses[insertAt] = postProcess;\r\n        } else {\r\n            this._postProcesses.splice(insertAt, 0, postProcess);\r\n        }\r\n        this._cascadePostProcessesToRigCams(); // also ensures framebuffer invalidated\r\n\r\n        // Update prePass\r\n        if (this._scene.prePassRenderer) {\r\n            this._scene.prePassRenderer.markAsDirty();\r\n        }\r\n\r\n        return this._postProcesses.indexOf(postProcess);\r\n    }\r\n\r\n    /**\r\n     * Detach a post process to the camera.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/postProcesses/usePostProcesses#attach-postprocess\r\n     * @param postProcess The post process to detach from the camera\r\n     */\r\n    public detachPostProcess(postProcess: PostProcess): void {\r\n        const idx = this._postProcesses.indexOf(postProcess);\r\n        if (idx !== -1) {\r\n            this._postProcesses[idx] = null;\r\n        }\r\n\r\n        // Update prePass\r\n        if (this._scene.prePassRenderer) {\r\n            this._scene.prePassRenderer.markAsDirty();\r\n        }\r\n\r\n        this._cascadePostProcessesToRigCams(); // also ensures framebuffer invalidated\r\n    }\r\n\r\n    /**\r\n     * Gets the current world matrix of the camera\r\n     */\r\n    public getWorldMatrix(): Matrix {\r\n        if (this._isSynchronizedViewMatrix()) {\r\n            return this._worldMatrix;\r\n        }\r\n\r\n        // Getting the the view matrix will also compute the world matrix.\r\n        this.getViewMatrix();\r\n\r\n        return this._worldMatrix;\r\n    }\r\n\r\n    /** @internal */\r\n    public _getViewMatrix(): Matrix {\r\n        return Matrix.Identity();\r\n    }\r\n\r\n    /**\r\n     * Gets the current view matrix of the camera.\r\n     * @param force forces the camera to recompute the matrix without looking at the cached state\r\n     * @returns the view matrix\r\n     */\r\n    public getViewMatrix(force?: boolean): Matrix {\r\n        if (!force && this._isSynchronizedViewMatrix()) {\r\n            return this._computedViewMatrix;\r\n        }\r\n\r\n        this.updateCache();\r\n        this._computedViewMatrix = this._getViewMatrix();\r\n        this._currentRenderId = this.getScene().getRenderId();\r\n        this._childUpdateId++;\r\n\r\n        this._refreshFrustumPlanes = true;\r\n\r\n        if (this._cameraRigParams && this._cameraRigParams.vrPreViewMatrix) {\r\n            this._computedViewMatrix.multiplyToRef(this._cameraRigParams.vrPreViewMatrix, this._computedViewMatrix);\r\n        }\r\n\r\n        // Notify parent camera if rig camera is changed\r\n        if (this.parent && (this.parent as Camera).onViewMatrixChangedObservable) {\r\n            (this.parent as Camera).onViewMatrixChangedObservable.notifyObservers(this.parent as Camera);\r\n        }\r\n\r\n        this.onViewMatrixChangedObservable.notifyObservers(this);\r\n\r\n        this._computedViewMatrix.invertToRef(this._worldMatrix);\r\n\r\n        return this._computedViewMatrix;\r\n    }\r\n\r\n    /**\r\n     * Freeze the projection matrix.\r\n     * It will prevent the cache check of the camera projection compute and can speed up perf\r\n     * if no parameter of the camera are meant to change\r\n     * @param projection Defines manually a projection if necessary\r\n     */\r\n    public freezeProjectionMatrix(projection?: Matrix): void {\r\n        this._doNotComputeProjectionMatrix = true;\r\n        if (projection !== undefined) {\r\n            this._projectionMatrix = projection;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Unfreeze the projection matrix if it has previously been freezed by freezeProjectionMatrix.\r\n     */\r\n    public unfreezeProjectionMatrix(): void {\r\n        this._doNotComputeProjectionMatrix = false;\r\n    }\r\n\r\n    /**\r\n     * Gets the current projection matrix of the camera.\r\n     * @param force forces the camera to recompute the matrix without looking at the cached state\r\n     * @returns the projection matrix\r\n     */\r\n    public getProjectionMatrix(force?: boolean): Matrix {\r\n        if (this._doNotComputeProjectionMatrix || (!force && this._isSynchronizedProjectionMatrix())) {\r\n            return this._projectionMatrix;\r\n        }\r\n\r\n        // Cache\r\n        this._cache.mode = this.mode;\r\n        this._cache.minZ = this.minZ;\r\n        this._cache.maxZ = this.maxZ;\r\n\r\n        // Matrix\r\n        this._refreshFrustumPlanes = true;\r\n\r\n        const engine = this.getEngine();\r\n        const scene = this.getScene();\r\n        const reverseDepth = engine.useReverseDepthBuffer;\r\n        if (this.mode === Camera.PERSPECTIVE_CAMERA) {\r\n            this._cache.fov = this.fov;\r\n            this._cache.fovMode = this.fovMode;\r\n            this._cache.aspectRatio = engine.getAspectRatio(this);\r\n            this._cache.projectionPlaneTilt = this.projectionPlaneTilt;\r\n\r\n            if (this.minZ <= 0) {\r\n                this.minZ = 0.1;\r\n            }\r\n\r\n            let getProjectionMatrix: (\r\n                fov: number,\r\n                aspect: number,\r\n                znear: number,\r\n                zfar: number,\r\n                result: Matrix,\r\n                isVerticalFovFixed: boolean,\r\n                halfZRange: boolean,\r\n                projectionPlaneTilt: number,\r\n                reverseDepthBufferMode: boolean\r\n            ) => void;\r\n            if (scene.useRightHandedSystem) {\r\n                getProjectionMatrix = Matrix.PerspectiveFovRHToRef;\r\n            } else {\r\n                getProjectionMatrix = Matrix.PerspectiveFovLHToRef;\r\n            }\r\n\r\n            getProjectionMatrix(\r\n                this.fov,\r\n                engine.getAspectRatio(this),\r\n                reverseDepth ? this.maxZ : this.minZ,\r\n                reverseDepth ? this.minZ : this.maxZ,\r\n                this._projectionMatrix,\r\n                this.fovMode === Camera.FOVMODE_VERTICAL_FIXED,\r\n                engine.isNDCHalfZRange,\r\n                this.projectionPlaneTilt,\r\n                reverseDepth\r\n            );\r\n        } else {\r\n            const halfWidth = engine.getRenderWidth() / 2.0;\r\n            const halfHeight = engine.getRenderHeight() / 2.0;\r\n            if (scene.useRightHandedSystem) {\r\n                Matrix.OrthoOffCenterRHToRef(\r\n                    this.orthoLeft ?? -halfWidth,\r\n                    this.orthoRight ?? halfWidth,\r\n                    this.orthoBottom ?? -halfHeight,\r\n                    this.orthoTop ?? halfHeight,\r\n                    reverseDepth ? this.maxZ : this.minZ,\r\n                    reverseDepth ? this.minZ : this.maxZ,\r\n                    this._projectionMatrix,\r\n                    engine.isNDCHalfZRange\r\n                );\r\n            } else {\r\n                Matrix.OrthoOffCenterLHToRef(\r\n                    this.orthoLeft ?? -halfWidth,\r\n                    this.orthoRight ?? halfWidth,\r\n                    this.orthoBottom ?? -halfHeight,\r\n                    this.orthoTop ?? halfHeight,\r\n                    reverseDepth ? this.maxZ : this.minZ,\r\n                    reverseDepth ? this.minZ : this.maxZ,\r\n                    this._projectionMatrix,\r\n                    engine.isNDCHalfZRange\r\n                );\r\n            }\r\n\r\n            this._cache.orthoLeft = this.orthoLeft;\r\n            this._cache.orthoRight = this.orthoRight;\r\n            this._cache.orthoBottom = this.orthoBottom;\r\n            this._cache.orthoTop = this.orthoTop;\r\n            this._cache.renderWidth = engine.getRenderWidth();\r\n            this._cache.renderHeight = engine.getRenderHeight();\r\n        }\r\n\r\n        this.onProjectionMatrixChangedObservable.notifyObservers(this);\r\n\r\n        return this._projectionMatrix;\r\n    }\r\n\r\n    /**\r\n     * Gets the transformation matrix (ie. the multiplication of view by projection matrices)\r\n     * @returns a Matrix\r\n     */\r\n    public getTransformationMatrix(): Matrix {\r\n        this._computedViewMatrix.multiplyToRef(this._projectionMatrix, this._transformMatrix);\r\n        return this._transformMatrix;\r\n    }\r\n\r\n    private _updateFrustumPlanes(): void {\r\n        if (!this._refreshFrustumPlanes) {\r\n            return;\r\n        }\r\n\r\n        this.getTransformationMatrix();\r\n\r\n        if (!this._frustumPlanes) {\r\n            this._frustumPlanes = Frustum.GetPlanes(this._transformMatrix);\r\n        } else {\r\n            Frustum.GetPlanesToRef(this._transformMatrix, this._frustumPlanes);\r\n        }\r\n\r\n        this._refreshFrustumPlanes = false;\r\n    }\r\n\r\n    /**\r\n     * Checks if a cullable object (mesh...) is in the camera frustum\r\n     * This checks the bounding box center. See isCompletelyInFrustum for a full bounding check\r\n     * @param target The object to check\r\n     * @param checkRigCameras If the rig cameras should be checked (eg. with webVR camera both eyes should be checked) (Default: false)\r\n     * @returns true if the object is in frustum otherwise false\r\n     */\r\n    public isInFrustum(target: ICullable, checkRigCameras = false): boolean {\r\n        this._updateFrustumPlanes();\r\n\r\n        if (checkRigCameras && this.rigCameras.length > 0) {\r\n            let result = false;\r\n            this.rigCameras.forEach((cam) => {\r\n                cam._updateFrustumPlanes();\r\n                result = result || target.isInFrustum(cam._frustumPlanes);\r\n            });\r\n            return result;\r\n        } else {\r\n            return target.isInFrustum(this._frustumPlanes);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Checks if a cullable object (mesh...) is in the camera frustum\r\n     * Unlike isInFrustum this checks the full bounding box\r\n     * @param target The object to check\r\n     * @returns true if the object is in frustum otherwise false\r\n     */\r\n    public isCompletelyInFrustum(target: ICullable): boolean {\r\n        this._updateFrustumPlanes();\r\n\r\n        return target.isCompletelyInFrustum(this._frustumPlanes);\r\n    }\r\n\r\n    /**\r\n     * Gets a ray in the forward direction from the camera.\r\n     * @param length Defines the length of the ray to create\r\n     * @param transform Defines the transform to apply to the ray, by default the world matrix is used to create a workd space ray\r\n     * @param origin Defines the start point of the ray which defaults to the camera position\r\n     * @returns the forward ray\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public getForwardRay(length = 100, transform?: Matrix, origin?: Vector3): Ray {\r\n        throw _WarnImport(\"Ray\");\r\n    }\r\n\r\n    /**\r\n     * Gets a ray in the forward direction from the camera.\r\n     * @param refRay the ray to (re)use when setting the values\r\n     * @param length Defines the length of the ray to create\r\n     * @param transform Defines the transform to apply to the ray, by default the world matrx is used to create a workd space ray\r\n     * @param origin Defines the start point of the ray which defaults to the camera position\r\n     * @returns the forward ray\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public getForwardRayToRef(refRay: Ray, length = 100, transform?: Matrix, origin?: Vector3): Ray {\r\n        throw _WarnImport(\"Ray\");\r\n    }\r\n\r\n    /**\r\n     * Releases resources associated with this node.\r\n     * @param doNotRecurse Set to true to not recurse into each children (recurse into each children by default)\r\n     * @param disposeMaterialAndTextures Set to true to also dispose referenced materials and textures (false by default)\r\n     */\r\n    public dispose(doNotRecurse?: boolean, disposeMaterialAndTextures = false): void {\r\n        // Observables\r\n        this.onViewMatrixChangedObservable.clear();\r\n        this.onProjectionMatrixChangedObservable.clear();\r\n        this.onAfterCheckInputsObservable.clear();\r\n        this.onRestoreStateObservable.clear();\r\n\r\n        // Inputs\r\n        if (this.inputs) {\r\n            this.inputs.clear();\r\n        }\r\n\r\n        // Animations\r\n        this.getScene().stopAnimation(this);\r\n\r\n        // Remove from scene\r\n        this.getScene().removeCamera(this);\r\n        while (this._rigCameras.length > 0) {\r\n            const camera = this._rigCameras.pop();\r\n            if (camera) {\r\n                camera.dispose();\r\n            }\r\n        }\r\n\r\n        if (this._parentContainer) {\r\n            const index = this._parentContainer.cameras.indexOf(this);\r\n            if (index > -1) {\r\n                this._parentContainer.cameras.splice(index, 1);\r\n            }\r\n            this._parentContainer = null;\r\n        }\r\n\r\n        // Postprocesses\r\n        if (this._rigPostProcess) {\r\n            this._rigPostProcess.dispose(this);\r\n            this._rigPostProcess = null;\r\n            this._postProcesses.length = 0;\r\n        } else if (this.cameraRigMode !== Camera.RIG_MODE_NONE) {\r\n            this._rigPostProcess = null;\r\n            this._postProcesses.length = 0;\r\n        } else {\r\n            let i = this._postProcesses.length;\r\n            while (--i >= 0) {\r\n                const postProcess = this._postProcesses[i];\r\n                if (postProcess) {\r\n                    postProcess.dispose(this);\r\n                }\r\n            }\r\n        }\r\n\r\n        // Render targets\r\n        let i = this.customRenderTargets.length;\r\n        while (--i >= 0) {\r\n            this.customRenderTargets[i].dispose();\r\n        }\r\n        this.customRenderTargets.length = 0;\r\n\r\n        // Active Meshes\r\n        this._activeMeshes.dispose();\r\n\r\n        this.getScene().getEngine().releaseRenderPassId(this.renderPassId);\r\n\r\n        super.dispose(doNotRecurse, disposeMaterialAndTextures);\r\n    }\r\n\r\n    /** @internal */\r\n    public _isLeftCamera = false;\r\n    /**\r\n     * Gets the left camera of a rig setup in case of Rigged Camera\r\n     */\r\n    public get isLeftCamera(): boolean {\r\n        return this._isLeftCamera;\r\n    }\r\n\r\n    /** @internal */\r\n    public _isRightCamera = false;\r\n    /**\r\n     * Gets the right camera of a rig setup in case of Rigged Camera\r\n     */\r\n    public get isRightCamera(): boolean {\r\n        return this._isRightCamera;\r\n    }\r\n\r\n    /**\r\n     * Gets the left camera of a rig setup in case of Rigged Camera\r\n     */\r\n    public get leftCamera(): Nullable<FreeCamera> {\r\n        if (this._rigCameras.length < 1) {\r\n            return null;\r\n        }\r\n        return <FreeCamera>this._rigCameras[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the right camera of a rig setup in case of Rigged Camera\r\n     */\r\n    public get rightCamera(): Nullable<FreeCamera> {\r\n        if (this._rigCameras.length < 2) {\r\n            return null;\r\n        }\r\n        return <FreeCamera>this._rigCameras[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the left camera target of a rig setup in case of Rigged Camera\r\n     * @returns the target position\r\n     */\r\n    public getLeftTarget(): Nullable<Vector3> {\r\n        if (this._rigCameras.length < 1) {\r\n            return null;\r\n        }\r\n        return (<TargetCamera>this._rigCameras[0]).getTarget();\r\n    }\r\n\r\n    /**\r\n     * Gets the right camera target of a rig setup in case of Rigged Camera\r\n     * @returns the target position\r\n     */\r\n    public getRightTarget(): Nullable<Vector3> {\r\n        if (this._rigCameras.length < 2) {\r\n            return null;\r\n        }\r\n        return (<TargetCamera>this._rigCameras[1]).getTarget();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public setCameraRigMode(mode: number, rigParams: any): void {\r\n        if (this.cameraRigMode === mode) {\r\n            return;\r\n        }\r\n\r\n        while (this._rigCameras.length > 0) {\r\n            const camera = this._rigCameras.pop();\r\n\r\n            if (camera) {\r\n                camera.dispose();\r\n            }\r\n        }\r\n        this.cameraRigMode = mode;\r\n        this._cameraRigParams = {};\r\n        //we have to implement stereo camera calcultating left and right viewpoints from interaxialDistance and target,\r\n        //not from a given angle as it is now, but until that complete code rewriting provisional stereoHalfAngle value is introduced\r\n        this._cameraRigParams.interaxialDistance = rigParams.interaxialDistance || 0.0637;\r\n        this._cameraRigParams.stereoHalfAngle = Tools.ToRadians(this._cameraRigParams.interaxialDistance / 0.0637);\r\n\r\n        // create the rig cameras, unless none\r\n        if (this.cameraRigMode !== Camera.RIG_MODE_NONE) {\r\n            const leftCamera = this.createRigCamera(this.name + \"_L\", 0);\r\n            if (leftCamera) {\r\n                leftCamera._isLeftCamera = true;\r\n            }\r\n            const rightCamera = this.createRigCamera(this.name + \"_R\", 1);\r\n            if (rightCamera) {\r\n                rightCamera._isRightCamera = true;\r\n            }\r\n            if (leftCamera && rightCamera) {\r\n                this._rigCameras.push(leftCamera);\r\n                this._rigCameras.push(rightCamera);\r\n            }\r\n        }\r\n\r\n        this._setRigMode(rigParams);\r\n\r\n        this._cascadePostProcessesToRigCams();\r\n        this.update();\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _setRigMode(rigParams: any) {\r\n        // no-op\r\n    }\r\n\r\n    /** @internal */\r\n    public _getVRProjectionMatrix(): Matrix {\r\n        Matrix.PerspectiveFovLHToRef(\r\n            this._cameraRigParams.vrMetrics.aspectRatioFov,\r\n            this._cameraRigParams.vrMetrics.aspectRatio,\r\n            this.minZ,\r\n            this.maxZ,\r\n            this._cameraRigParams.vrWorkMatrix,\r\n            true,\r\n            this.getEngine().isNDCHalfZRange\r\n        );\r\n        this._cameraRigParams.vrWorkMatrix.multiplyToRef(this._cameraRigParams.vrHMatrix, this._projectionMatrix);\r\n        return this._projectionMatrix;\r\n    }\r\n\r\n    protected _updateCameraRotationMatrix() {\r\n        //Here for WebVR\r\n    }\r\n\r\n    protected _updateWebVRCameraRotationMatrix() {\r\n        //Here for WebVR\r\n    }\r\n\r\n    /**\r\n     * This function MUST be overwritten by the different WebVR cameras available.\r\n     * The context in which it is running is the RIG camera. So 'this' is the TargetCamera, left or right.\r\n     * @internal\r\n     */\r\n    public _getWebVRProjectionMatrix(): Matrix {\r\n        return Matrix.Identity();\r\n    }\r\n\r\n    /**\r\n     * This function MUST be overwritten by the different WebVR cameras available.\r\n     * The context in which it is running is the RIG camera. So 'this' is the TargetCamera, left or right.\r\n     * @internal\r\n     */\r\n    public _getWebVRViewMatrix(): Matrix {\r\n        return Matrix.Identity();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public setCameraRigParameter(name: string, value: any) {\r\n        if (!this._cameraRigParams) {\r\n            this._cameraRigParams = {};\r\n        }\r\n        this._cameraRigParams[name] = value;\r\n        //provisionnally:\r\n        if (name === \"interaxialDistance\") {\r\n            this._cameraRigParams.stereoHalfAngle = Tools.ToRadians(value / 0.0637);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * needs to be overridden by children so sub has required properties to be copied\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public createRigCamera(name: string, cameraIndex: number): Nullable<Camera> {\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * May need to be overridden by children\r\n     * @internal\r\n     */\r\n    public _updateRigCameras() {\r\n        for (let i = 0; i < this._rigCameras.length; i++) {\r\n            this._rigCameras[i].minZ = this.minZ;\r\n            this._rigCameras[i].maxZ = this.maxZ;\r\n            this._rigCameras[i].fov = this.fov;\r\n            this._rigCameras[i].upVector.copyFrom(this.upVector);\r\n        }\r\n\r\n        // only update viewport when ANAGLYPH\r\n        if (this.cameraRigMode === Camera.RIG_MODE_STEREOSCOPIC_ANAGLYPH) {\r\n            this._rigCameras[0].viewport = this._rigCameras[1].viewport = this.viewport;\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _setupInputs() {}\r\n\r\n    /**\r\n     * Serialiaze the camera setup to a json representation\r\n     * @returns the JSON representation\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = SerializationHelper.Serialize(this);\r\n        serializationObject.uniqueId = this.uniqueId;\r\n\r\n        // Type\r\n        serializationObject.type = this.getClassName();\r\n\r\n        // Parent\r\n        if (this.parent) {\r\n            this.parent._serializeAsParent(serializationObject);\r\n        }\r\n\r\n        if (this.inputs) {\r\n            this.inputs.serialize(serializationObject);\r\n        }\r\n        // Animations\r\n        SerializationHelper.AppendSerializedAnimations(this, serializationObject);\r\n        serializationObject.ranges = this.serializeAnimationRanges();\r\n\r\n        serializationObject.isEnabled = this.isEnabled();\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Clones the current camera.\r\n     * @param name The cloned camera name\r\n     * @param newParent The cloned camera's new parent (none by default)\r\n     * @returns the cloned camera\r\n     */\r\n    public clone(name: string, newParent: Nullable<Node> = null): Camera {\r\n        const camera = SerializationHelper.Clone(\r\n            Camera.GetConstructorFromName(this.getClassName(), name, this.getScene(), this.interaxialDistance, this.isStereoscopicSideBySide),\r\n            this\r\n        );\r\n        camera.name = name;\r\n        camera.parent = newParent;\r\n\r\n        this.onClonedObservable.notifyObservers(camera);\r\n\r\n        return camera;\r\n    }\r\n\r\n    /**\r\n     * Gets the direction of the camera relative to a given local axis.\r\n     * @param localAxis Defines the reference axis to provide a relative direction.\r\n     * @returns the direction\r\n     */\r\n    public getDirection(localAxis: Vector3): Vector3 {\r\n        const result = Vector3.Zero();\r\n\r\n        this.getDirectionToRef(localAxis, result);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Returns the current camera absolute rotation\r\n     */\r\n    public get absoluteRotation(): Quaternion {\r\n        this.getWorldMatrix().decompose(undefined, this._absoluteRotation);\r\n\r\n        return this._absoluteRotation;\r\n    }\r\n\r\n    /**\r\n     * Gets the direction of the camera relative to a given local axis into a passed vector.\r\n     * @param localAxis Defines the reference axis to provide a relative direction.\r\n     * @param result Defines the vector to store the result in\r\n     */\r\n    public getDirectionToRef(localAxis: Vector3, result: Vector3): void {\r\n        Vector3.TransformNormalToRef(localAxis, this.getWorldMatrix(), result);\r\n    }\r\n\r\n    /**\r\n     * Gets a camera constructor for a given camera type\r\n     * @param type The type of the camera to construct (should be equal to one of the camera class name)\r\n     * @param name The name of the camera the result will be able to instantiate\r\n     * @param scene The scene the result will construct the camera in\r\n     * @param interaxial_distance In case of stereoscopic setup, the distance between both eyes\r\n     * @param isStereoscopicSideBySide In case of stereoscopic setup, should the sereo be side b side\r\n     * @returns a factory method to construct the camera\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    static GetConstructorFromName(type: string, name: string, scene: Scene, interaxial_distance: number = 0, isStereoscopicSideBySide: boolean = true): () => Camera {\r\n        const constructorFunc = Node.Construct(type, name, scene, {\r\n            // eslint-disable-next-line @typescript-eslint/naming-convention\r\n            interaxial_distance: interaxial_distance,\r\n            isStereoscopicSideBySide: isStereoscopicSideBySide,\r\n        });\r\n\r\n        if (constructorFunc) {\r\n            return <() => Camera>constructorFunc;\r\n        }\r\n\r\n        // Default to universal camera\r\n        return () => Camera._CreateDefaultParsedCamera(name, scene);\r\n    }\r\n\r\n    /**\r\n     * Compute the world  matrix of the camera.\r\n     * @returns the camera world matrix\r\n     */\r\n    public computeWorldMatrix(): Matrix {\r\n        return this.getWorldMatrix();\r\n    }\r\n\r\n    /**\r\n     * Parse a JSON and creates the camera from the parsed information\r\n     * @param parsedCamera The JSON to parse\r\n     * @param scene The scene to instantiate the camera in\r\n     * @returns the newly constructed camera\r\n     */\r\n    public static Parse(parsedCamera: any, scene: Scene): Camera {\r\n        const type = parsedCamera.type;\r\n        const construct = Camera.GetConstructorFromName(type, parsedCamera.name, scene, parsedCamera.interaxial_distance, parsedCamera.isStereoscopicSideBySide);\r\n\r\n        const camera = SerializationHelper.Parse(construct, parsedCamera, scene);\r\n\r\n        // Parent\r\n        if (parsedCamera.parentId !== undefined) {\r\n            camera._waitingParentId = parsedCamera.parentId;\r\n        }\r\n\r\n        // Parent instance index\r\n        if (parsedCamera.parentInstanceIndex !== undefined) {\r\n            camera._waitingParentInstanceIndex = parsedCamera.parentInstanceIndex;\r\n        }\r\n\r\n        //If camera has an input manager, let it parse inputs settings\r\n        if (camera.inputs) {\r\n            camera.inputs.parse(parsedCamera);\r\n\r\n            camera._setupInputs();\r\n        }\r\n\r\n        if (parsedCamera.upVector) {\r\n            camera.upVector = Vector3.FromArray(parsedCamera.upVector); // need to force the upVector\r\n        }\r\n\r\n        if ((<any>camera).setPosition) {\r\n            // need to force position\r\n            camera.position.copyFromFloats(0, 0, 0);\r\n            (<any>camera).setPosition(Vector3.FromArray(parsedCamera.position));\r\n        }\r\n\r\n        // Target\r\n        if (parsedCamera.target) {\r\n            if ((<any>camera).setTarget) {\r\n                (<any>camera).setTarget(Vector3.FromArray(parsedCamera.target));\r\n            }\r\n        }\r\n\r\n        // Apply 3d rig, when found\r\n        if (parsedCamera.cameraRigMode) {\r\n            const rigParams = parsedCamera.interaxial_distance ? { interaxialDistance: parsedCamera.interaxial_distance } : {};\r\n            camera.setCameraRigMode(parsedCamera.cameraRigMode, rigParams);\r\n        }\r\n\r\n        // Animations\r\n        if (parsedCamera.animations) {\r\n            for (let animationIndex = 0; animationIndex < parsedCamera.animations.length; animationIndex++) {\r\n                const parsedAnimation = parsedCamera.animations[animationIndex];\r\n                const internalClass = GetClass(\"BABYLON.Animation\");\r\n                if (internalClass) {\r\n                    camera.animations.push(internalClass.Parse(parsedAnimation));\r\n                }\r\n            }\r\n            Node.ParseAnimationRanges(camera, parsedCamera, scene);\r\n        }\r\n\r\n        if (parsedCamera.autoAnimate) {\r\n            scene.beginAnimation(camera, parsedCamera.autoAnimateFrom, parsedCamera.autoAnimateTo, parsedCamera.autoAnimateLoop, parsedCamera.autoAnimateSpeed || 1.0);\r\n        }\r\n\r\n        // Check if isEnabled is defined to be back compatible with prior serialized versions.\r\n        if (parsedCamera.isEnabled !== undefined) {\r\n            camera.setEnabled(parsedCamera.isEnabled);\r\n        }\r\n\r\n        return camera;\r\n    }\r\n}\r\n"]}