{"version": 3, "file": "bouncingBehavior.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Behaviors/Cameras/bouncingBehavior.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,QAAQ,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AAKnE,OAAO,EAAE,SAAS,EAAE,MAAM,4BAA4B,CAAC;AAEvD;;;GAGG;AACH,MAAM,OAAO,gBAAgB;IAA7B;QAkBI;;WAEG;QACI,uBAAkB,GAAG,GAAG,CAAC;QAEhC;;WAEG;QACI,+BAA0B,GAAG,CAAC,CAAC;QAEtC;;WAEG;QACI,+BAA0B,GAAG,CAAC,CAAC,CAAC;QAE/B,yBAAoB,GAAG,KAAK,CAAC;QA6FrC,aAAa;QACL,uBAAkB,GAAY,KAAK,CAAC;QACpC,4BAAuB,GAAwB,IAAI,CAAC;QACpD,iBAAY,GAAG,IAAI,KAAK,EAAc,CAAC;IAgFnD,CAAC;IAhNG;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,UAAU,CAAC;IACtB,CAAC;IA6BD;;OAEG;IACH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED;;;OAGG;IACH,IAAW,mBAAmB,CAAC,KAAc;QACzC,IAAI,IAAI,CAAC,oBAAoB,KAAK,KAAK,EAAE;YACrC,OAAO;SACV;QAED,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAElC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC;QACpC,IAAI,CAAC,MAAM,EAAE;YACT,OAAO;SACV;QAED,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,4BAA4B,GAAG,MAAM,CAAC,6BAA6B,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBAClF,IAAI,CAAC,IAAI,EAAE;oBACP,OAAO;iBACV;gBAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,cAAc,CAAC;gBAEvD,IAAI,CAAC,0BAA0B,GAAG,QAAQ,GAAG,IAAI,CAAC;gBAClD,IAAI,CAAC,0BAA0B,GAAG,QAAQ,GAAG,IAAI,CAAC;YACtD,CAAC,CAAC,CAAC;SACN;aAAM,IAAI,IAAI,CAAC,4BAA4B,EAAE;YAC1C,MAAM,CAAC,6BAA6B,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;SAClF;IACL,CAAC;IAOD;;OAEG;IACI,IAAI;QACP,aAAa;IACjB,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,MAAuB;QACjC,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;QAC9B,IAAI,CAAC,2BAA2B,GAAG,MAAM,CAAC,4BAA4B,CAAC,GAAG,CAAC,GAAG,EAAE;YAC5E,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;gBACvB,OAAO;aACV;YAED,qDAAqD;YACrD,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,EAAE;gBAC9D,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;aACpE;YAED,qDAAqD;YACrD,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,EAAE;gBAC9D,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;aACpE;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,OAAO;SACV;QACD,IAAI,IAAI,CAAC,2BAA2B,EAAE;YAClC,IAAI,CAAC,eAAe,CAAC,4BAA4B,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;SAC9F;QACD,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACnC,IAAI,CAAC,eAAe,CAAC,6BAA6B,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;SAChG;QACD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAChC,CAAC;IAQD;;;;OAIG;IACK,gBAAgB,CAAC,WAA6B;QAClD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,WAAW,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACzE,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACK,0BAA0B,CAAC,WAAmB;QAClD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC/B,gBAAgB,CAAC,cAAc,CAAC,aAAa,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YAC3E,IAAI,CAAC,uBAAuB,GAAG,SAAS,CAAC,eAAe,CAAC,QAAQ,EAAE,SAAS,CAAC,mBAAmB,EAAE,EAAE,EAAE,gBAAgB,CAAC,cAAc,CAAC,CAAC;SAC1I;QACD,0CAA0C;QAC1C,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;QACjE,IAAI,CAAC,eAAe,CAAC,cAAc,GAAG,QAAQ,CAAC;QAC/C,IAAI,CAAC,eAAe,CAAC,oBAAoB,GAAG,CAAC,CAAC;QAE9C,8BAA8B;QAC9B,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,MAAM,UAAU,GAAG,SAAS,CAAC,YAAY,CACrC,QAAQ,EACR,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,WAAW,EACzC,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,EAC/B,EAAE,EACF,IAAI,CAAC,uBAAuB,EAC5B,IAAI,CAAC,kBAAkB,EACvB,GAAG,EAAE,CAAC,IAAI,CAAC,oBAAoB,EAAE,CACpC,CAAC;QAEF,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SACtC;IACL,CAAC;IAED;;OAEG;IACO,oBAAoB;QAC1B,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAEhC,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC;SACpE;IACL,CAAC;IAED;;OAEG;IACI,iBAAiB;QACpB,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,EAAE,CAAC;SACxC;QACD,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YAC7B,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3C,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAC5B,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;SAC7B;IACL,CAAC;;AAxMD;;GAEG;AACW,+BAAc,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC;AAEjD;;GAEG;AACW,2BAAU,GAAG,cAAc,CAAC,kBAAkB,CAAC", "sourcesContent": ["import type { Behavior } from \"../../Behaviors/behavior\";\r\nimport type { Camera } from \"../../Cameras/camera\";\r\nimport type { ArcRotateCamera } from \"../../Cameras/arcRotateCamera\";\r\nimport { BackEase, EasingFunction } from \"../../Animations/easing\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { Animatable } from \"../../Animations/animatable\";\r\nimport { Animation } from \"../../Animations/animation\";\r\n\r\n/**\r\n * Add a bouncing effect to an ArcRotateCamera when reaching a specified minimum and maximum radius\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors/cameraBehaviors#bouncing-behavior\r\n */\r\nexport class BouncingBehavior implements Behavior<ArcRotateCamera> {\r\n    /**\r\n     * Gets the name of the behavior.\r\n     */\r\n    public get name(): string {\r\n        return \"Bouncing\";\r\n    }\r\n\r\n    /**\r\n     * The easing function used by animations\r\n     */\r\n    public static EasingFunction = new BackEase(0.3);\r\n\r\n    /**\r\n     * The easing mode used by animations\r\n     */\r\n    public static EasingMode = EasingFunction.EASINGMODE_EASEOUT;\r\n\r\n    /**\r\n     * The duration of the animation, in milliseconds\r\n     */\r\n    public transitionDuration = 450;\r\n\r\n    /**\r\n     * Length of the distance animated by the transition when lower radius is reached\r\n     */\r\n    public lowerRadiusTransitionRange = 2;\r\n\r\n    /**\r\n     * Length of the distance animated by the transition when upper radius is reached\r\n     */\r\n    public upperRadiusTransitionRange = -2;\r\n\r\n    private _autoTransitionRange = false;\r\n\r\n    /**\r\n     * Gets a value indicating if the lowerRadiusTransitionRange and upperRadiusTransitionRange are defined automatically\r\n     */\r\n    public get autoTransitionRange(): boolean {\r\n        return this._autoTransitionRange;\r\n    }\r\n\r\n    /**\r\n     * Sets a value indicating if the lowerRadiusTransitionRange and upperRadiusTransitionRange are defined automatically\r\n     * Transition ranges will be set to 5% of the bounding box diagonal in world space\r\n     */\r\n    public set autoTransitionRange(value: boolean) {\r\n        if (this._autoTransitionRange === value) {\r\n            return;\r\n        }\r\n\r\n        this._autoTransitionRange = value;\r\n\r\n        const camera = this._attachedCamera;\r\n        if (!camera) {\r\n            return;\r\n        }\r\n\r\n        if (value) {\r\n            this._onMeshTargetChangedObserver = camera.onMeshTargetChangedObservable.add((mesh) => {\r\n                if (!mesh) {\r\n                    return;\r\n                }\r\n\r\n                mesh.computeWorldMatrix(true);\r\n                const diagonal = mesh.getBoundingInfo().diagonalLength;\r\n\r\n                this.lowerRadiusTransitionRange = diagonal * 0.05;\r\n                this.upperRadiusTransitionRange = diagonal * 0.05;\r\n            });\r\n        } else if (this._onMeshTargetChangedObserver) {\r\n            camera.onMeshTargetChangedObservable.remove(this._onMeshTargetChangedObserver);\r\n        }\r\n    }\r\n\r\n    // Connection\r\n    private _attachedCamera: Nullable<ArcRotateCamera>;\r\n    private _onAfterCheckInputsObserver: Nullable<Observer<Camera>>;\r\n    private _onMeshTargetChangedObserver: Nullable<Observer<Nullable<AbstractMesh>>>;\r\n\r\n    /**\r\n     * Initializes the behavior.\r\n     */\r\n    public init(): void {\r\n        // Do nothing\r\n    }\r\n\r\n    /**\r\n     * Attaches the behavior to its arc rotate camera.\r\n     * @param camera Defines the camera to attach the behavior to\r\n     */\r\n    public attach(camera: ArcRotateCamera): void {\r\n        this._attachedCamera = camera;\r\n        this._onAfterCheckInputsObserver = camera.onAfterCheckInputsObservable.add(() => {\r\n            if (!this._attachedCamera) {\r\n                return;\r\n            }\r\n\r\n            // Add the bounce animation to the lower radius limit\r\n            if (this._isRadiusAtLimit(this._attachedCamera.lowerRadiusLimit)) {\r\n                this._applyBoundRadiusAnimation(this.lowerRadiusTransitionRange);\r\n            }\r\n\r\n            // Add the bounce animation to the upper radius limit\r\n            if (this._isRadiusAtLimit(this._attachedCamera.upperRadiusLimit)) {\r\n                this._applyBoundRadiusAnimation(this.upperRadiusTransitionRange);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Detaches the behavior from its current arc rotate camera.\r\n     */\r\n    public detach(): void {\r\n        if (!this._attachedCamera) {\r\n            return;\r\n        }\r\n        if (this._onAfterCheckInputsObserver) {\r\n            this._attachedCamera.onAfterCheckInputsObservable.remove(this._onAfterCheckInputsObserver);\r\n        }\r\n        if (this._onMeshTargetChangedObserver) {\r\n            this._attachedCamera.onMeshTargetChangedObservable.remove(this._onMeshTargetChangedObserver);\r\n        }\r\n        this._attachedCamera = null;\r\n    }\r\n\r\n    // Animations\r\n    private _radiusIsAnimating: boolean = false;\r\n    private _radiusBounceTransition: Nullable<Animation> = null;\r\n    private _animatables = new Array<Animatable>();\r\n    private _cachedWheelPrecision: number;\r\n\r\n    /**\r\n     * Checks if the camera radius is at the specified limit. Takes into account animation locks.\r\n     * @param radiusLimit The limit to check against.\r\n     * @returns Bool to indicate if at limit.\r\n     */\r\n    private _isRadiusAtLimit(radiusLimit: Nullable<number>): boolean {\r\n        if (!this._attachedCamera) {\r\n            return false;\r\n        }\r\n\r\n        if (this._attachedCamera.radius === radiusLimit && !this._radiusIsAnimating) {\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Applies an animation to the radius of the camera, extending by the radiusDelta.\r\n     * @param radiusDelta The delta by which to animate to. Can be negative.\r\n     */\r\n    private _applyBoundRadiusAnimation(radiusDelta: number): void {\r\n        if (!this._attachedCamera) {\r\n            return;\r\n        }\r\n\r\n        if (!this._radiusBounceTransition) {\r\n            BouncingBehavior.EasingFunction.setEasingMode(BouncingBehavior.EasingMode);\r\n            this._radiusBounceTransition = Animation.CreateAnimation(\"radius\", Animation.ANIMATIONTYPE_FLOAT, 60, BouncingBehavior.EasingFunction);\r\n        }\r\n        // Prevent zoom until bounce has completed\r\n        this._cachedWheelPrecision = this._attachedCamera.wheelPrecision;\r\n        this._attachedCamera.wheelPrecision = Infinity;\r\n        this._attachedCamera.inertialRadiusOffset = 0;\r\n\r\n        // Animate to the radius limit\r\n        this.stopAllAnimations();\r\n        this._radiusIsAnimating = true;\r\n        const animatable = Animation.TransitionTo(\r\n            \"radius\",\r\n            this._attachedCamera.radius + radiusDelta,\r\n            this._attachedCamera,\r\n            this._attachedCamera.getScene(),\r\n            60,\r\n            this._radiusBounceTransition,\r\n            this.transitionDuration,\r\n            () => this._clearAnimationLocks()\r\n        );\r\n\r\n        if (animatable) {\r\n            this._animatables.push(animatable);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Removes all animation locks. Allows new animations to be added to any of the camera properties.\r\n     */\r\n    protected _clearAnimationLocks(): void {\r\n        this._radiusIsAnimating = false;\r\n\r\n        if (this._attachedCamera) {\r\n            this._attachedCamera.wheelPrecision = this._cachedWheelPrecision;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Stops and removes all animations that have been applied to the camera\r\n     */\r\n    public stopAllAnimations(): void {\r\n        if (this._attachedCamera) {\r\n            this._attachedCamera.animations = [];\r\n        }\r\n        while (this._animatables.length) {\r\n            this._animatables[0].onAnimationEnd = null;\r\n            this._animatables[0].stop();\r\n            this._animatables.shift();\r\n        }\r\n    }\r\n}\r\n"]}