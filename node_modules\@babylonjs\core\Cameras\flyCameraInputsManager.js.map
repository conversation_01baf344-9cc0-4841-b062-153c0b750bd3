{"version": 3, "file": "flyCameraInputsManager.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Cameras/flyCameraInputsManager.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EAAE,mBAAmB,EAAE,MAAM,uCAAuC,CAAC;AAC5E,OAAO,EAAE,sBAAsB,EAAE,MAAM,0CAA0C,CAAC;AAElF;;;;GAIG;AACH,MAAM,OAAO,sBAAuB,SAAQ,mBAA8B;IACtE;;;OAGG;IACH,YAAY,MAAiB;QACzB,KAAK,CAAC,MAAM,CAAC,CAAC;IAClB,CAAC;IAED;;;OAGG;IACH,WAAW;QACP,IAAI,CAAC,GAAG,CAAC,IAAI,sBAAsB,EAAE,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACH,QAAQ;QACJ,IAAI,CAAC,GAAG,CAAC,IAAI,mBAAmB,EAAE,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ", "sourcesContent": ["import type { FlyCamera } from \"./flyCamera\";\r\nimport { CameraInputsManager } from \"./cameraInputsManager\";\r\nimport { FlyCameraMouseInput } from \"../Cameras/Inputs/flyCameraMouseInput\";\r\nimport { FlyCameraKeyboardInput } from \"../Cameras/Inputs/flyCameraKeyboardInput\";\r\n\r\n/**\r\n * Default Inputs manager for the FlyCamera.\r\n * It groups all the default supported inputs for ease of use.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n */\r\nexport class FlyCameraInputsManager extends CameraInputsManager<FlyCamera> {\r\n    /**\r\n     * Instantiates a new FlyCameraInputsManager.\r\n     * @param camera Defines the camera the inputs belong to.\r\n     */\r\n    constructor(camera: FlyCamera) {\r\n        super(camera);\r\n    }\r\n\r\n    /**\r\n     * Add keyboard input support to the input manager.\r\n     * @returns the new FlyCameraKeyboardMoveInput().\r\n     */\r\n    addKeyboard(): FlyCameraInputsManager {\r\n        this.add(new FlyCameraKeyboardInput());\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Add mouse input support to the input manager.\r\n     * @returns the new FlyCameraMouseInput().\r\n     */\r\n    addMouse(): FlyCameraInputsManager {\r\n        this.add(new FlyCameraMouseInput());\r\n        return this;\r\n    }\r\n}\r\n"]}