import type { IWebXRFeature } from "../webXRFeaturesManager";
import type { WebXRSessionManager } from "../webXRSessionManager";
import { Observable } from "../../Misc/observable";
import { Matrix } from "../../Maths/math.vector";
import type { TransformNode } from "../../Meshes/transformNode";
import { WebXRAbstractFeature } from "./WebXRAbstractFeature";
/**
 * An interface for all Hit test features
 */
export interface IWebXRHitTestFeature<T extends IWebXRLegacyHitResult> extends IWebXRFeature {
    /**
     * Triggered when new babylon (transformed) hit test results are available
     */
    onHitTestResultObservable: Observable<T[]>;
}
/**
 * Options used for hit testing
 */
export interface IWebXRLegacyHitTestOptions {
    /**
     * Only test when user interacted with the scene. Default - hit test every frame
     */
    testOnPointerDownOnly?: boolean;
    /**
     * The node to use to transform the local results to world coordinates
     */
    worldParentNode?: TransformNode;
}
/**
 * Interface defining the babylon result of raycasting/hit-test
 */
export interface IWebXRLegacyHitResult {
    /**
     * Transformation matrix that can be applied to a node that will put it in the hit point location
     */
    transformationMatrix: Matrix;
    /**
     * The native hit test result
     */
    xrHitResult: XRHitResult | XRHitTestResult;
}
/**
 * The currently-working hit-test module.
 * Hit test (or Ray-casting) is used to interact with the real world.
 * For further information read here - https://github.com/immersive-web/hit-test
 */
export declare class WebXRHitTestLegacy extends WebXRAbstractFeature implements IWebXRHitTestFeature<IWebXRLegacyHitResult> {
    /**
     * options to use when constructing this feature
     */
    readonly options: IWebXRLegacyHitTestOptions;
    private _direction;
    private _mat;
    private _onSelectEnabled;
    private _origin;
    /**
     * The module's name
     */
    static readonly Name = "xr-hit-test";
    /**
     * The (Babylon) version of this module.
     * This is an integer representing the implementation version.
     * This number does not correspond to the WebXR specs version
     */
    static readonly Version = 1;
    /**
     * Populated with the last native XR Hit Results
     */
    lastNativeXRHitResults: XRHitResult[];
    /**
     * Triggered when new babylon (transformed) hit test results are available
     */
    onHitTestResultObservable: Observable<IWebXRLegacyHitResult[]>;
    /**
     * Creates a new instance of the (legacy version) hit test feature
     * @param _xrSessionManager an instance of WebXRSessionManager
     * @param options options to use when constructing this feature
     */
    constructor(_xrSessionManager: WebXRSessionManager, 
    /**
     * options to use when constructing this feature
     */
    options?: IWebXRLegacyHitTestOptions);
    /**
     * execute a hit test with an XR Ray
     *
     * @param xrSession a native xrSession that will execute this hit test
     * @param xrRay the ray (position and direction) to use for ray-casting
     * @param referenceSpace native XR reference space to use for the hit-test
     * @param filter filter function that will filter the results
     * @returns a promise that resolves with an array of native XR hit result in xr coordinates system
     */
    static XRHitTestWithRay(xrSession: XRSession, xrRay: XRRay, referenceSpace: XRReferenceSpace, filter?: (result: XRHitResult) => boolean): Promise<XRHitResult[]>;
    /**
     * Execute a hit test on the current running session using a select event returned from a transient input (such as touch)
     * @param event the (select) event to use to select with
     * @param referenceSpace the reference space to use for this hit test
     * @returns a promise that resolves with an array of native XR hit result in xr coordinates system
     */
    static XRHitTestWithSelectEvent(event: XRInputSourceEvent, referenceSpace: XRReferenceSpace): Promise<XRHitResult[]>;
    /**
     * attach this feature
     * Will usually be called by the features manager
     *
     * @returns true if successful.
     */
    attach(): boolean;
    /**
     * detach this feature.
     * Will usually be called by the features manager
     *
     * @returns true if successful.
     */
    detach(): boolean;
    /**
     * Dispose this feature and all of the resources attached
     */
    dispose(): void;
    protected _onXRFrame(frame: XRFrame): void;
    private _onHitTestResults;
    private _onSelect;
}
