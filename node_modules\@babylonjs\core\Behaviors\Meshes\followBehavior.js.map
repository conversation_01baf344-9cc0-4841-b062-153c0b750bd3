{"version": 3, "file": "followBehavior.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Behaviors/Meshes/followBehavior.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AACtE,OAAO,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AAEjD,OAAO,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;AAErD;;;GAGG;AACH,MAAM,OAAO,cAAc;IAA3B;QAGI,iCAAiC;QACzB,mBAAc,GAAe,IAAI,UAAU,EAAE,CAAC;QAC9C,gBAAW,GAAc,CAAC,IAAI,OAAO,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,OAAO,EAAE,CAAC,CAAC;QACnI,eAAU,GAAW,IAAI,MAAM,EAAE,CAAC;QAClC,mBAAc,GAAW,IAAI,MAAM,EAAE,CAAC;QACtC,gBAAW,GAAY,IAAI,OAAO,EAAE,CAAC;QACrC,oBAAe,GAAY,IAAI,OAAO,EAAE,CAAC;QACzC,iBAAY,GAAY,IAAI,OAAO,EAAE,CAAC;QAKtC,qBAAgB,GAAY,IAAI,OAAO,EAAE,CAAC;QAC1C,uBAAkB,GAAe,IAAI,UAAU,EAAE,CAAC;QAClD,cAAS,GAAW,CAAC,CAAC,CAAC;QACvB,wBAAmB,GAAG,IAAI,CAAC;QAOnC;;WAEG;QACI,oBAAe,GAAG,IAAI,CAAC;QAE9B;;;WAGG;QACI,aAAQ,GAAG,GAAG,CAAC;QAEtB;;WAEG;QACI,6BAAwB,GAAG,KAAK,CAAC;QAExC;;;WAGG;QACI,gBAAW,GAAG,EAAE,CAAC;QAExB;;WAEG;QACI,2BAAsB,GAAG,EAAE,CAAC;QAEnC;;WAEG;QACI,6BAAwB,GAAG,EAAE,CAAC;QACrC;;WAEG;QACI,kCAA6B,GAAG,EAAE,CAAC;QAC1C;;WAEG;QACI,wBAAmB,GAAG,KAAK,CAAC;QACnC;;WAEG;QACI,qBAAgB,GAAG,KAAK,CAAC;QAChC;;WAEG;QACI,wBAAmB,GAAG,CAAC,CAAC;QAC/B;;WAEG;QACI,oBAAe,GAAG,GAAG,CAAC;QAC7B;;WAEG;QACI,oBAAe,GAAG,CAAC,CAAC;QAC3B;;WAEG;QACI,oBAAe,GAAG,GAAG,CAAC;QAE7B;;WAEG;QACI,2BAAsB,GAAG,KAAK,CAAC;QAEtC;;WAEG;QACI,wBAAmB,GAAG,CAAC,CAAC;QAE/B;;;WAGG;QACI,aAAQ,GAAG,IAAI,CAAC;IA6V3B,CAAC;IA3VG;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;IAC5D,CAAC;IAED,IAAW,cAAc,CAAC,MAAwB;QAC9C,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;OAEG;IACI,IAAI,KAAI,CAAC;IAEhB;;;;OAIG;IACI,MAAM,CAAC,SAAwB,EAAE,cAAuB;QAC3D,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;QACnC,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QAE9B,IAAI,cAAc,EAAE;YAChB,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;SACxC;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,QAAQ;QACX,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACpC,CAAC;IAEO,2BAA2B,CAAC,MAAe,EAAE,MAAe;QAChE,iBAAiB;QACjB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACrC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACrC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAE7B,MAAM,CAAC,SAAS,EAAE,CAAC;QACnB,MAAM,CAAC,SAAS,EAAE,CAAC;QAEnB,OAAO,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IAChE,CAAC;IAEO,SAAS,CAAC,MAAe;QAC7B,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC;IAEO,cAAc,CAAC,eAAwB,EAAE,gBAAyB,KAAK;QAC3E,IAAI,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC;QACvC,IAAI,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC;QACvC,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAE7C,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACtC,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QACpC,IAAI,eAAe,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;QACzC,SAAS,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;QAE/C,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,8FAA8F;YAC9F,6FAA6F;YAC7F,0DAA0D;YAC1D,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC;YACtD,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC;YAEtD,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC1D,SAAS,CAAC,YAAY,CAAC,eAAe,GAAG,iBAAiB,CAAC,CAAC;YAC5D,eAAe,GAAG,iBAAiB,CAAC;SACvC;QAED,IAAI,eAAe,GAAG,eAAe,CAAC;QAEtC,IAAI,aAAa,EAAE;YACf,eAAe,GAAG,eAAe,CAAC;SACrC;aAAM;YACH,eAAe,GAAG,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;SAC7E;QAED,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;QAElE,OAAO,eAAe,KAAK,eAAe,CAAC;IAC/C,CAAC;IAEO,mBAAmB,CAAC,eAAwB;QAChD,IAAI,IAAI,CAAC,mBAAmB,KAAK,CAAC,EAAE;YAChC,eAAe,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;SAC5G;IACL,CAAC;IAEO,uBAAuB,CAAC,MAAe,EAAE,UAAsB;QACnE,UAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;IACpK,CAAC;IAEO,iBAAiB,CAAC,UAAkB;QACxC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAClC,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxE,KAAK,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9B,OAAO,CAAC,oBAAoB,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QAC3D,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;QACd,OAAO,CAAC,SAAS,EAAE,CAAC;QACpB,OAAO,CAAC,oBAAoB,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;QAEvD,UAAU,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAC7F,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QAC9D,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAC3D,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEtD,0EAA0E;QAC1E,wDAAwD;QACxD,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC;IAEO,aAAa,CAAC,UAAkB,EAAE,eAAwB;QAC9D,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACpC,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxE,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAClC,KAAK,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE9B,6DAA6D;QAC7D,OAAO,CAAC,oBAAoB,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QAC3D,OAAO,CAAC,oBAAoB,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;QAEvD,iBAAiB;QACjB,MAAM,EAAE,GAAG,OAAO,CAAC,UAAU,CAAC;QAE9B,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC;QAEtC,IAAI,IAAI,GAAG,OAAO,EAAE;YAChB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,cAAc,GAAG,KAAK,CAAC;QAC3B,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC;QAEzC,kBAAkB;QAClB,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,MAAM,KAAK,GAAG,OAAO,CAAC,6BAA6B,CAAC,eAAe,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;YACrF,UAAU,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;YACzD,eAAe,CAAC,uBAAuB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;SAC1E;aAAM;YACH,MAAM,KAAK,GAAG,CAAC,OAAO,CAAC,6BAA6B,CAAC,eAAe,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;YACtF,MAAM,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;YAC1E,IAAI,KAAK,GAAG,CAAC,WAAW,EAAE;gBACtB,UAAU,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC,KAAK,GAAG,WAAW,EAAE,YAAY,CAAC,CAAC;gBACxE,eAAe,CAAC,uBAAuB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;gBACvE,cAAc,GAAG,IAAI,CAAC;aACzB;iBAAM,IAAI,KAAK,GAAG,WAAW,EAAE;gBAC5B,UAAU,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC,KAAK,GAAG,WAAW,EAAE,YAAY,CAAC,CAAC;gBACxE,eAAe,CAAC,uBAAuB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;gBACvE,cAAc,GAAG,IAAI,CAAC;aACzB;SACJ;QAED,kBAAkB;QAClB,MAAM,KAAK,GAAG,IAAI,CAAC,2BAA2B,CAAC,eAAe,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrH,MAAM,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QAC5E,IAAI,KAAK,GAAG,CAAC,WAAW,EAAE;YACtB,UAAU,CAAC,iBAAiB,CAAC,EAAE,EAAE,CAAC,KAAK,GAAG,WAAW,EAAE,YAAY,CAAC,CAAC;YACrE,eAAe,CAAC,uBAAuB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;YACvE,cAAc,GAAG,IAAI,CAAC;SACzB;aAAM,IAAI,KAAK,GAAG,WAAW,EAAE;YAC5B,UAAU,CAAC,iBAAiB,CAAC,EAAE,EAAE,CAAC,KAAK,GAAG,WAAW,EAAE,YAAY,CAAC,CAAC;YACrE,eAAe,CAAC,uBAAuB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;YACvE,cAAc,GAAG,IAAI,CAAC;SACzB;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEO,iBAAiB,CAAC,eAAwB,EAAE,kBAA8B;;QAC9E,6DAA6D;QAC7D,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACvC,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;QAElE,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAClC,mEAAmE;QACnE,EAAE,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3B,8CAA8C;QAC9C,OAAO,CAAC,UAAU,CAAC,UAAU,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;QAC1C,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;QAE9B,IAAI,MAAM,GAAG,OAAO,EAAE;YAClB,OAAO;SACV;QAED,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAElC,OAAO,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;QAC1C,IAAI,MAAA,IAAI,CAAC,YAAY,0CAAE,QAAQ,GAAG,oBAAoB,EAAE;YACpD,UAAU,CAAC,wBAAwB,CAAC,UAAU,EAAE,EAAE,EAAE,kBAAkB,CAAC,CAAC;SAC3E;aAAM;YACH,UAAU,CAAC,wBAAwB,CAAC,UAAU,EAAE,EAAE,EAAE,kBAAkB,CAAC,CAAC;SAC3E;IACL,CAAC;IAEO,0BAA0B,CAAC,eAAwB,EAAE,OAAgB;QACzE,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAC1C,aAAa,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QACxC,aAAa,CAAC,SAAS,EAAE,CAAC;QAE1B,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,6BAA6B,CAAC,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;QAC1G,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,6BAA6B,CAAC;IACxE,CAAC;IAEO,eAAe,CAAC,MAAc;QAClC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,EAAE;YACpC,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;YAC3C,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAElC,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACvD,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAC9C,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;YACnD,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;YAChD,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC;YACvC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;YAC5C,UAAU,CAAC,MAAM,EAAE,CAAC;YAEpB,OAAO,CAAC,yBAAyB,CAAC,KAAK,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC;YACvE,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC;YACnC,QAAQ,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACjC,OAAO,CAAC,yBAAyB,CAAC,QAAQ,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;YACnE,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YACjD,eAAe,CAAC,eAAe,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAEvD,IAAI,IAAI,CAAC,wBAAwB,EAAE;gBAC/B,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;aACtC;YAED,IAAI,cAAc,GAAG,KAAK,CAAC;YAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;YACjC,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxE,OAAO,CAAC,oBAAoB,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;YAE3D,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC;YACzC,WAAW,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5E,OAAO,CAAC,oBAAoB,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;YAEpE,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;aACxE;iBAAM;gBACH,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBACvB,MAAM,eAAe,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC;oBACjD,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;iBACnE;qBAAM;oBACH,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;iBACpE;aACJ;YAED,IAAI,eAAe,GAAG,KAAK,CAAC;YAC5B,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC3B,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;gBACvE,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;aAC7C;YAED,IAAI,IAAI,CAAC,sBAAsB,EAAE;gBAC7B,eAAe,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;aACvF;YAED,IAAI,cAAc,IAAI,eAAe,IAAI,IAAI,CAAC,0BAA0B,CAAC,eAAe,EAAE,WAAW,CAAC,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAChI,IAAI,CAAC,iBAAiB,CAAC,eAAe,EAAE,kBAAkB,CAAC,CAAC;aAC/D;YAED,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAC7C,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;YAEjC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;SAC1C;IACL,CAAC;IAEO,sBAAsB,CAAC,OAAe;QAC1C,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAC9D,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE;YACvC,IAAI,CAAC,YAAY,CAAC,kBAAkB,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;SAChE;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;QAC3C,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAElC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC1G,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACvE,OAAO;SACV;QAED,WAAW;QACX,MAAM,gBAAgB,GAAG,IAAI,OAAO,EAAE,CAAC;QACvC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;QAC1G,OAAO,CAAC,WAAW,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;QACvG,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;QAChE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QAEtD,WAAW;QACX,MAAM,eAAe,GAAG,IAAI,UAAU,EAAE,CAAC;QACzC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;QAC/D,UAAU,CAAC,WAAW,CAAC,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;QAE/H,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC3C,CAAC;IAEO,eAAe;QACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;YACjE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACtB,OAAO;aACV;YAED,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACxB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC1C,IAAI,CAAC,sBAAsB,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;YACnD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1B,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,kBAAkB;QACtB,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SACrE;IACL,CAAC;CACJ", "sourcesContent": ["import type { Behavior } from \"../behavior\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { Camera } from \"../../Cameras/camera\";\r\nimport { Matrix, Quaternion, Vector3 } from \"../../Maths/math.vector\";\r\nimport { <PERSON>alar } from \"../../Maths/math.scalar\";\r\nimport type { TransformNode } from \"../../Meshes/transformNode\";\r\nimport { Epsilon } from \"../../Maths/math.constants\";\r\n\r\n/**\r\n * A behavior that when attached to a mesh will follow a camera\r\n * @since 5.0.0\r\n */\r\nexport class FollowBehavior implements Behavior<TransformNode> {\r\n    private _scene: Scene;\r\n\r\n    // Memory cache to avoid GC usage\r\n    private _tmpQuaternion: Quaternion = new Quaternion();\r\n    private _tmpVectors: Vector3[] = [new Vector3(), new Vector3(), new Vector3(), new Vector3(), new Vector3(), new Vector3(), new Vector3()];\r\n    private _tmpMatrix: Matrix = new Matrix();\r\n    private _tmpInvertView: Matrix = new Matrix();\r\n    private _tmpForward: Vector3 = new Vector3();\r\n    private _tmpNodeForward: Vector3 = new Vector3();\r\n    private _tmpPosition: Vector3 = new Vector3();\r\n\r\n    private _followedCamera: Nullable<Camera>;\r\n    private _onBeforeRender: Nullable<Observer<Scene>>;\r\n\r\n    private _workingPosition: Vector3 = new Vector3();\r\n    private _workingQuaternion: Quaternion = new Quaternion();\r\n    private _lastTick: number = -1;\r\n    private _recenterNextUpdate = true;\r\n\r\n    /**\r\n     * Attached node of this behavior\r\n     */\r\n    public attachedNode: Nullable<TransformNode>;\r\n\r\n    /**\r\n     * Set to false if the node should strictly follow the camera without any interpolation time\r\n     */\r\n    public interpolatePose = true;\r\n\r\n    /**\r\n     * Rate of interpolation of position and rotation of the attached node.\r\n     * Higher values will give a slower interpolation.\r\n     */\r\n    public lerpTime = 500;\r\n\r\n    /**\r\n     * If the behavior should ignore the pitch and roll of the camera.\r\n     */\r\n    public ignoreCameraPitchAndRoll = false;\r\n\r\n    /**\r\n     * Pitch offset from camera (relative to Max Distance)\r\n     * Is only effective if `ignoreCameraPitchAndRoll` is set to `true`.\r\n     */\r\n    public pitchOffset = 15;\r\n\r\n    /**\r\n     * The vertical angle from the camera forward axis to the owner will not exceed this value\r\n     */\r\n    public maxViewVerticalDegrees = 30;\r\n\r\n    /**\r\n     * The horizontal angle from the camera forward axis to the owner will not exceed this value\r\n     */\r\n    public maxViewHorizontalDegrees = 30;\r\n    /**\r\n     * The attached node will not reorient until the angle between its forward vector and the vector to the camera is greater than this value\r\n     */\r\n    public orientToCameraDeadzoneDegrees = 60;\r\n    /**\r\n     * Option to ignore distance clamping\r\n     */\r\n    public ignoreDistanceClamp = false;\r\n    /**\r\n     * Option to ignore angle clamping\r\n     */\r\n    public ignoreAngleClamp = false;\r\n    /**\r\n     * Max vertical distance between the attachedNode and camera\r\n     */\r\n    public verticalMaxDistance = 0;\r\n    /**\r\n     *  Default distance from eye to attached node, i.e. the sphere radius\r\n     */\r\n    public defaultDistance = 0.8;\r\n    /**\r\n     *  Max distance from eye to attached node, i.e. the sphere radius\r\n     */\r\n    public maximumDistance = 2;\r\n    /**\r\n     *  Min distance from eye to attached node, i.e. the sphere radius\r\n     */\r\n    public minimumDistance = 0.3;\r\n\r\n    /**\r\n     * Ignore vertical movement and lock the Y position of the object.\r\n     */\r\n    public useFixedVerticalOffset = false;\r\n\r\n    /**\r\n     * Fixed vertical position offset distance.\r\n     */\r\n    public fixedVerticalOffset = 0;\r\n\r\n    /**\r\n     * Enables/disables the behavior\r\n     * @internal\r\n     */\r\n    public _enabled = true;\r\n\r\n    /**\r\n     * The camera that should be followed by this behavior\r\n     */\r\n    public get followedCamera(): Nullable<Camera> {\r\n        return this._followedCamera || this._scene.activeCamera;\r\n    }\r\n\r\n    public set followedCamera(camera: Nullable<Camera>) {\r\n        this._followedCamera = camera;\r\n    }\r\n\r\n    /**\r\n     *  The name of the behavior\r\n     */\r\n    public get name(): string {\r\n        return \"Follow\";\r\n    }\r\n\r\n    /**\r\n     *  Initializes the behavior\r\n     */\r\n    public init() {}\r\n\r\n    /**\r\n     * Attaches the follow behavior\r\n     * @param ownerNode The mesh that will be following once attached\r\n     * @param followedCamera The camera that should be followed by the node\r\n     */\r\n    public attach(ownerNode: TransformNode, followedCamera?: Camera): void {\r\n        this._scene = ownerNode.getScene();\r\n        this.attachedNode = ownerNode;\r\n\r\n        if (followedCamera) {\r\n            this.followedCamera = followedCamera;\r\n        }\r\n\r\n        this._addObservables();\r\n    }\r\n\r\n    /**\r\n     *  Detaches the behavior from the mesh\r\n     */\r\n    public detach(): void {\r\n        this.attachedNode = null;\r\n        this._removeObservables();\r\n    }\r\n\r\n    /**\r\n     * Recenters the attached node in front of the camera on the next update\r\n     */\r\n    public recenter() {\r\n        this._recenterNextUpdate = true;\r\n    }\r\n\r\n    private _angleBetweenVectorAndPlane(vector: Vector3, normal: Vector3) {\r\n        // Work on copies\r\n        this._tmpVectors[0].copyFrom(vector);\r\n        vector = this._tmpVectors[0];\r\n        this._tmpVectors[1].copyFrom(normal);\r\n        normal = this._tmpVectors[1];\r\n\r\n        vector.normalize();\r\n        normal.normalize();\r\n\r\n        return Math.PI / 2 - Math.acos(Vector3.Dot(vector, normal));\r\n    }\r\n\r\n    private _length2D(vector: Vector3) {\r\n        return Math.sqrt(vector.x * vector.x + vector.z * vector.z);\r\n    }\r\n\r\n    private _distanceClamp(currentToTarget: Vector3, moveToDefault: boolean = false) {\r\n        let minDistance = this.minimumDistance;\r\n        let maxDistance = this.maximumDistance;\r\n        const defaultDistance = this.defaultDistance;\r\n\r\n        const direction = this._tmpVectors[0];\r\n        direction.copyFrom(currentToTarget);\r\n        let currentDistance = direction.length();\r\n        direction.normalizeFromLength(currentDistance);\r\n\r\n        if (this.ignoreCameraPitchAndRoll) {\r\n            // If we don't account for pitch offset, the casted object will float up/down as the reference\r\n            // gets closer to it because we will still be casting in the direction of the pitched offset.\r\n            // To fix this, only modify the XZ position of the object.\r\n            minDistance = this._length2D(direction) * minDistance;\r\n            maxDistance = this._length2D(direction) * maxDistance;\r\n\r\n            const currentDistance2D = this._length2D(currentToTarget);\r\n            direction.scaleInPlace(currentDistance / currentDistance2D);\r\n            currentDistance = currentDistance2D;\r\n        }\r\n\r\n        let clampedDistance = currentDistance;\r\n\r\n        if (moveToDefault) {\r\n            clampedDistance = defaultDistance;\r\n        } else {\r\n            clampedDistance = Scalar.Clamp(currentDistance, minDistance, maxDistance);\r\n        }\r\n\r\n        currentToTarget.copyFrom(direction).scaleInPlace(clampedDistance);\r\n\r\n        return currentDistance !== clampedDistance;\r\n    }\r\n\r\n    private _applyVerticalClamp(currentToTarget: Vector3) {\r\n        if (this.verticalMaxDistance !== 0) {\r\n            currentToTarget.y = Scalar.Clamp(currentToTarget.y, -this.verticalMaxDistance, this.verticalMaxDistance);\r\n        }\r\n    }\r\n\r\n    private _toOrientationQuatToRef(vector: Vector3, quaternion: Quaternion) {\r\n        Quaternion.RotationYawPitchRollToRef(Math.atan2(vector.x, vector.z), Math.atan2(vector.y, Math.sqrt(vector.z * vector.z + vector.x * vector.x)), 0, quaternion);\r\n    }\r\n\r\n    private _applyPitchOffset(invertView: Matrix) {\r\n        const forward = this._tmpVectors[0];\r\n        const right = this._tmpVectors[1];\r\n        forward.copyFromFloats(0, 0, this._scene.useRightHandedSystem ? -1 : 1);\r\n        right.copyFromFloats(1, 0, 0);\r\n        Vector3.TransformNormalToRef(forward, invertView, forward);\r\n        forward.y = 0;\r\n        forward.normalize();\r\n        Vector3.TransformNormalToRef(right, invertView, right);\r\n\r\n        Quaternion.RotationAxisToRef(right, (this.pitchOffset * Math.PI) / 180, this._tmpQuaternion);\r\n        forward.rotateByQuaternionToRef(this._tmpQuaternion, forward);\r\n        this._toOrientationQuatToRef(forward, this._tmpQuaternion);\r\n        this._tmpQuaternion.toRotationMatrix(this._tmpMatrix);\r\n\r\n        // Since we already extracted position from the invert view matrix, we can\r\n        // disregard the position part of the matrix in the copy\r\n        invertView.copyFrom(this._tmpMatrix);\r\n    }\r\n\r\n    private _angularClamp(invertView: Matrix, currentToTarget: Vector3): boolean {\r\n        const forward = this._tmpVectors[5];\r\n        forward.copyFromFloats(0, 0, this._scene.useRightHandedSystem ? -1 : 1);\r\n        const right = this._tmpVectors[6];\r\n        right.copyFromFloats(1, 0, 0);\r\n\r\n        // forward and right are related to camera frame of reference\r\n        Vector3.TransformNormalToRef(forward, invertView, forward);\r\n        Vector3.TransformNormalToRef(right, invertView, right);\r\n\r\n        // Up is global Z\r\n        const up = Vector3.UpReadOnly;\r\n\r\n        const dist = currentToTarget.length();\r\n\r\n        if (dist < Epsilon) {\r\n            return false;\r\n        }\r\n\r\n        let angularClamped = false;\r\n        const rotationQuat = this._tmpQuaternion;\r\n\r\n        // X-axis leashing\r\n        if (this.ignoreCameraPitchAndRoll) {\r\n            const angle = Vector3.GetAngleBetweenVectorsOnPlane(currentToTarget, forward, right);\r\n            Quaternion.RotationAxisToRef(right, angle, rotationQuat);\r\n            currentToTarget.rotateByQuaternionToRef(rotationQuat, currentToTarget);\r\n        } else {\r\n            const angle = -Vector3.GetAngleBetweenVectorsOnPlane(currentToTarget, forward, right);\r\n            const minMaxAngle = ((this.maxViewVerticalDegrees * Math.PI) / 180) * 0.5;\r\n            if (angle < -minMaxAngle) {\r\n                Quaternion.RotationAxisToRef(right, -angle - minMaxAngle, rotationQuat);\r\n                currentToTarget.rotateByQuaternionToRef(rotationQuat, currentToTarget);\r\n                angularClamped = true;\r\n            } else if (angle > minMaxAngle) {\r\n                Quaternion.RotationAxisToRef(right, -angle + minMaxAngle, rotationQuat);\r\n                currentToTarget.rotateByQuaternionToRef(rotationQuat, currentToTarget);\r\n                angularClamped = true;\r\n            }\r\n        }\r\n\r\n        // Y-axis leashing\r\n        const angle = this._angleBetweenVectorAndPlane(currentToTarget, right) * (this._scene.useRightHandedSystem ? -1 : 1);\r\n        const minMaxAngle = ((this.maxViewHorizontalDegrees * Math.PI) / 180) * 0.5;\r\n        if (angle < -minMaxAngle) {\r\n            Quaternion.RotationAxisToRef(up, -angle - minMaxAngle, rotationQuat);\r\n            currentToTarget.rotateByQuaternionToRef(rotationQuat, currentToTarget);\r\n            angularClamped = true;\r\n        } else if (angle > minMaxAngle) {\r\n            Quaternion.RotationAxisToRef(up, -angle + minMaxAngle, rotationQuat);\r\n            currentToTarget.rotateByQuaternionToRef(rotationQuat, currentToTarget);\r\n            angularClamped = true;\r\n        }\r\n\r\n        return angularClamped;\r\n    }\r\n\r\n    private _orientationClamp(currentToTarget: Vector3, rotationQuaternion: Quaternion) {\r\n        // Construct a rotation quat from up vector and target vector\r\n        const toFollowed = this._tmpVectors[0];\r\n        toFollowed.copyFrom(currentToTarget).scaleInPlace(-1).normalize();\r\n\r\n        const up = this._tmpVectors[1];\r\n        const right = this._tmpVectors[2];\r\n        // We use global up vector to orient the following node (global +Y)\r\n        up.copyFromFloats(0, 1, 0);\r\n\r\n        // Gram-Schmidt to create an orthonormal frame\r\n        Vector3.CrossToRef(toFollowed, up, right);\r\n        const length = right.length();\r\n\r\n        if (length < Epsilon) {\r\n            return;\r\n        }\r\n\r\n        right.normalizeFromLength(length);\r\n\r\n        Vector3.CrossToRef(right, toFollowed, up);\r\n        if (this.attachedNode?.getScene().useRightHandedSystem) {\r\n            Quaternion.FromLookDirectionRHToRef(toFollowed, up, rotationQuaternion);\r\n        } else {\r\n            Quaternion.FromLookDirectionLHToRef(toFollowed, up, rotationQuaternion);\r\n        }\r\n    }\r\n\r\n    private _passedOrientationDeadzone(currentToTarget: Vector3, forward: Vector3) {\r\n        const leashToFollow = this._tmpVectors[5];\r\n        leashToFollow.copyFrom(currentToTarget);\r\n        leashToFollow.normalize();\r\n\r\n        const angle = Math.abs(Vector3.GetAngleBetweenVectorsOnPlane(forward, leashToFollow, Vector3.UpReadOnly));\r\n        return (angle * 180) / Math.PI > this.orientToCameraDeadzoneDegrees;\r\n    }\r\n\r\n    private _updateLeashing(camera: Camera) {\r\n        if (this.attachedNode && this._enabled) {\r\n            const oldParent = this.attachedNode.parent;\r\n            this.attachedNode.setParent(null);\r\n\r\n            const worldMatrix = this.attachedNode.getWorldMatrix();\r\n            const currentToTarget = this._workingPosition;\r\n            const rotationQuaternion = this._workingQuaternion;\r\n            const pivot = this.attachedNode.getPivotPoint();\r\n            const invertView = this._tmpInvertView;\r\n            invertView.copyFrom(camera.getViewMatrix());\r\n            invertView.invert();\r\n\r\n            Vector3.TransformCoordinatesToRef(pivot, worldMatrix, currentToTarget);\r\n            const position = this._tmpPosition;\r\n            position.copyFromFloats(0, 0, 0);\r\n            Vector3.TransformCoordinatesToRef(position, worldMatrix, position);\r\n            position.scaleInPlace(-1).subtractInPlace(pivot);\r\n            currentToTarget.subtractInPlace(camera.globalPosition);\r\n\r\n            if (this.ignoreCameraPitchAndRoll) {\r\n                this._applyPitchOffset(invertView);\r\n            }\r\n\r\n            let angularClamped = false;\r\n            const forward = this._tmpForward;\r\n            forward.copyFromFloats(0, 0, this._scene.useRightHandedSystem ? -1 : 1);\r\n            Vector3.TransformNormalToRef(forward, invertView, forward);\r\n\r\n            const nodeForward = this._tmpNodeForward;\r\n            nodeForward.copyFromFloats(0, 0, this._scene.useRightHandedSystem ? -1 : 1);\r\n            Vector3.TransformNormalToRef(nodeForward, worldMatrix, nodeForward);\r\n\r\n            if (this._recenterNextUpdate) {\r\n                currentToTarget.copyFrom(forward).scaleInPlace(this.defaultDistance);\r\n            } else {\r\n                if (this.ignoreAngleClamp) {\r\n                    const currentDistance = currentToTarget.length();\r\n                    currentToTarget.copyFrom(forward).scaleInPlace(currentDistance);\r\n                } else {\r\n                    angularClamped = this._angularClamp(invertView, currentToTarget);\r\n                }\r\n            }\r\n\r\n            let distanceClamped = false;\r\n            if (!this.ignoreDistanceClamp) {\r\n                distanceClamped = this._distanceClamp(currentToTarget, angularClamped);\r\n                this._applyVerticalClamp(currentToTarget);\r\n            }\r\n\r\n            if (this.useFixedVerticalOffset) {\r\n                currentToTarget.y = position.y - camera.globalPosition.y + this.fixedVerticalOffset;\r\n            }\r\n\r\n            if (angularClamped || distanceClamped || this._passedOrientationDeadzone(currentToTarget, nodeForward) || this._recenterNextUpdate) {\r\n                this._orientationClamp(currentToTarget, rotationQuaternion);\r\n            }\r\n\r\n            this._workingPosition.subtractInPlace(pivot);\r\n            this._recenterNextUpdate = false;\r\n\r\n            this.attachedNode.setParent(oldParent);\r\n        }\r\n    }\r\n\r\n    private _updateTransformToGoal(elapsed: number) {\r\n        if (!this.attachedNode || !this.followedCamera || !this._enabled) {\r\n            return;\r\n        }\r\n\r\n        if (!this.attachedNode.rotationQuaternion) {\r\n            this.attachedNode.rotationQuaternion = Quaternion.Identity();\r\n        }\r\n\r\n        const oldParent = this.attachedNode.parent;\r\n        this.attachedNode.setParent(null);\r\n\r\n        if (!this.interpolatePose) {\r\n            this.attachedNode.position.copyFrom(this.followedCamera.globalPosition).addInPlace(this._workingPosition);\r\n            this.attachedNode.rotationQuaternion.copyFrom(this._workingQuaternion);\r\n            return;\r\n        }\r\n\r\n        // position\r\n        const currentDirection = new Vector3();\r\n        currentDirection.copyFrom(this.attachedNode.position).subtractInPlace(this.followedCamera.globalPosition);\r\n        Vector3.SmoothToRef(currentDirection, this._workingPosition, elapsed, this.lerpTime, currentDirection);\r\n        currentDirection.addInPlace(this.followedCamera.globalPosition);\r\n        this.attachedNode.position.copyFrom(currentDirection);\r\n\r\n        // rotation\r\n        const currentRotation = new Quaternion();\r\n        currentRotation.copyFrom(this.attachedNode.rotationQuaternion);\r\n        Quaternion.SmoothToRef(currentRotation, this._workingQuaternion, elapsed, this.lerpTime, this.attachedNode.rotationQuaternion);\r\n\r\n        this.attachedNode.setParent(oldParent);\r\n    }\r\n\r\n    private _addObservables() {\r\n        this._lastTick = Date.now();\r\n        this._onBeforeRender = this._scene.onBeforeRenderObservable.add(() => {\r\n            if (!this.followedCamera) {\r\n                return;\r\n            }\r\n\r\n            const tick = Date.now();\r\n            this._updateLeashing(this.followedCamera);\r\n            this._updateTransformToGoal(tick - this._lastTick);\r\n            this._lastTick = tick;\r\n        });\r\n    }\r\n\r\n    private _removeObservables() {\r\n        if (this._onBeforeRender) {\r\n            this._scene.onBeforeRenderObservable.remove(this._onBeforeRender);\r\n        }\r\n    }\r\n}\r\n"]}