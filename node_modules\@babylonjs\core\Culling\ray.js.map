{"version": 3, "file": "ray.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Culling/ray.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEnE,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AACxD,OAAO,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAC;AAGlE,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AACjC,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAIrD;;GAEG;AACH,MAAM,OAAO,GAAG;IAKZ;;;;;OAKG;IACH;IACI,mBAAmB;IACZ,MAAe;IACtB,gBAAgB;IACT,SAAkB;IACzB,wBAAwB;IACjB,SAAiB,MAAM,CAAC,SAAS;QAJjC,WAAM,GAAN,MAAM,CAAS;QAEf,cAAS,GAAT,SAAS,CAAS;QAElB,WAAM,GAAN,MAAM,CAA2B;IACzC,CAAC;IAEJ,UAAU;IAEV;;;OAGG;IACI,KAAK;QACR,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7E,CAAC;IAED;;;;;;;OAOG;IACI,mBAAmB,CAAC,OAA+B,EAAE,OAA+B,EAAE,uBAA+B,CAAC;QACzH,MAAM,UAAU,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,GAAG,oBAAoB,EAAE,OAAO,CAAC,CAAC,GAAG,oBAAoB,EAAE,OAAO,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC;QAC3J,MAAM,UAAU,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,GAAG,oBAAoB,EAAE,OAAO,CAAC,CAAC,GAAG,oBAAoB,EAAE,OAAO,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC;QAC3J,IAAI,CAAC,GAAG,GAAG,CAAC;QACZ,IAAI,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;QAChC,IAAI,GAAW,CAAC;QAChB,IAAI,GAAW,CAAC;QAChB,IAAI,GAAW,CAAC;QAChB,IAAI,IAAY,CAAC;QACjB,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,EAAE;YACxC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE;gBAC9D,OAAO,KAAK,CAAC;aAChB;SACJ;aAAM;YACH,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAC7B,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YAC3C,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YAC3C,IAAI,GAAG,KAAK,CAAC,QAAQ,EAAE;gBACnB,GAAG,GAAG,QAAQ,CAAC;aAClB;YAED,IAAI,GAAG,GAAG,GAAG,EAAE;gBACX,IAAI,GAAG,GAAG,CAAC;gBACX,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,IAAI,CAAC;aACd;YAED,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACrB,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAEnC,IAAI,CAAC,GAAG,QAAQ,EAAE;gBACd,OAAO,KAAK,CAAC;aAChB;SACJ;QAED,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,EAAE;YACxC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE;gBAC9D,OAAO,KAAK,CAAC;aAChB;SACJ;aAAM;YACH,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAC7B,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YAC3C,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YAE3C,IAAI,GAAG,KAAK,CAAC,QAAQ,EAAE;gBACnB,GAAG,GAAG,QAAQ,CAAC;aAClB;YAED,IAAI,GAAG,GAAG,GAAG,EAAE;gBACX,IAAI,GAAG,GAAG,CAAC;gBACX,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,IAAI,CAAC;aACd;YAED,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACrB,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAEnC,IAAI,CAAC,GAAG,QAAQ,EAAE;gBACd,OAAO,KAAK,CAAC;aAChB;SACJ;QAED,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,EAAE;YACxC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE;gBAC9D,OAAO,KAAK,CAAC;aAChB;SACJ;aAAM;YACH,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAC7B,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YAC3C,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YAE3C,IAAI,GAAG,KAAK,CAAC,QAAQ,EAAE;gBACnB,GAAG,GAAG,QAAQ,CAAC;aAClB;YAED,IAAI,GAAG,GAAG,GAAG,EAAE;gBACX,IAAI,GAAG,GAAG,CAAC;gBACX,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,IAAI,CAAC;aACd;YAED,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACrB,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAEnC,IAAI,CAAC,GAAG,QAAQ,EAAE;gBACd,OAAO,KAAK,CAAC;aAChB;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,aAAa,CAAC,GAA+B,EAAE,uBAA+B,CAAC;QAClF,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;IACpF,CAAC;IAED;;;;;OAKG;IACI,gBAAgB,CAAC,MAAqC,EAAE,uBAA+B,CAAC;QAC3F,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAC1C,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAC1C,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAC1C,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACnC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,oBAAoB,CAAC;QACpD,MAAM,EAAE,GAAG,MAAM,GAAG,MAAM,CAAC;QAE3B,IAAI,IAAI,IAAI,EAAE,EAAE;YACZ,OAAO,IAAI,CAAC;SACf;QAED,MAAM,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,GAAG,EAAE;YACX,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;QAE9B,OAAO,IAAI,IAAI,EAAE,CAAC;IACtB,CAAC;IAED;;;;;;OAMG;IACI,kBAAkB,CAAC,OAA+B,EAAE,OAA+B,EAAE,OAA+B;QACvH,MAAM,KAAK,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACjC,MAAM,KAAK,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACjC,MAAM,IAAI,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,IAAI,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,IAAI,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAEhC,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACtC,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACtC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAChD,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAErC,IAAI,GAAG,KAAK,CAAC,EAAE;YACX,OAAO,IAAI,CAAC;SACf;QAED,MAAM,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC;QAEvB,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAEzC,MAAM,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC;QAE5C,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,EAAE;YACpB,OAAO,IAAI,CAAC;SACf;QAED,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAEtC,MAAM,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC;QAEtD,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE;YACzB,OAAO,IAAI,CAAC;SACf;QAED,6DAA6D;QAC7D,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC;QACnD,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE;YACxB,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,gBAAgB,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;IAC3D,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,KAA2B;QAC9C,IAAI,QAAgB,CAAC;QACrB,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1D,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,mBAAmB,EAAE;YACzC,OAAO,IAAI,CAAC;SACf;aAAM;YACH,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACvD,QAAQ,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC;YAC1C,IAAI,QAAQ,GAAG,GAAG,EAAE;gBAChB,IAAI,QAAQ,GAAG,CAAC,mBAAmB,EAAE;oBACjC,OAAO,IAAI,CAAC;iBACf;qBAAM;oBACH,OAAO,CAAC,CAAC;iBACZ;aACJ;YAED,OAAO,QAAQ,CAAC;SACnB;IACL,CAAC;IACD;;;;;OAKG;IACI,cAAc,CAAC,IAAY,EAAE,SAAiB,CAAC;QAClD,QAAQ,IAAI,EAAE;YACV,KAAK,GAAG,CAAC,CAAC;gBACN,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;gBACtD,IAAI,CAAC,GAAG,CAAC,EAAE;oBACP,OAAO,IAAI,CAAC;iBACf;gBACD,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aAC5G;YACD,KAAK,GAAG,CAAC,CAAC;gBACN,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;gBACtD,IAAI,CAAC,GAAG,CAAC,EAAE;oBACP,OAAO,IAAI,CAAC;iBACf;gBACD,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aAC5G;YACD,KAAK,GAAG,CAAC,CAAC;gBACN,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;gBACtD,IAAI,CAAC,GAAG,CAAC,EAAE;oBACP,OAAO,IAAI,CAAC;iBACf;gBACD,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;aAC5G;YACD;gBACI,OAAO,IAAI,CAAC;SACnB;IACL,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,IAAiC,EAAE,SAAmB;QACxE,MAAM,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,cAAc,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAEtC,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SAC9C;aAAM;YACH,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;SAC1C;QAED,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IACpD,CAAC;IAED;;;;;;OAMG;IACI,gBAAgB,CAAC,MAA0C,EAAE,SAAmB,EAAE,OAA4B;QACjH,IAAI,OAAO,EAAE;YACT,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;SACtB;aAAM;YACH,OAAO,GAAG,EAAE,CAAC;SAChB;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;YAE3D,IAAI,QAAQ,CAAC,GAAG,EAAE;gBACd,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC1B;SACJ;QAED,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAEvC,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,mBAAmB,CAAC,YAAwC,EAAE,YAAwC;QAC1G,IAAI,YAAY,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,EAAE;YAC/C,OAAO,CAAC,CAAC,CAAC;SACb;aAAM,IAAI,YAAY,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,EAAE;YACtD,OAAO,CAAC,CAAC;SACZ;aAAM;YACH,OAAO,CAAC,CAAC;SACZ;IACL,CAAC;IAKD;;;;;;OAMG;IACH,mBAAmB,CAAC,IAA4B,EAAE,IAA4B,EAAE,SAAiB;QAC7F,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;QACtB,MAAM,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACpC,MAAM,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAE5B,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAErB,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzB,MAAM,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,cAAc;QAC3C,MAAM,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5B,MAAM,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,cAAc;QAC3C,MAAM,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5B,MAAM,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5B,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,cAAc;QACvC,IAAI,EAAU,EACV,EAAE,GAAG,CAAC,CAAC,CAAC,oCAAoC;QAChD,IAAI,EAAU,EACV,EAAE,GAAG,CAAC,CAAC,CAAC,oCAAoC;QAEhD,wDAAwD;QACxD,IAAI,CAAC,GAAG,GAAG,CAAC,SAAS,EAAE;YACnB,gCAAgC;YAChC,EAAE,GAAG,GAAG,CAAC,CAAC,qCAAqC;YAC/C,EAAE,GAAG,GAAG,CAAC,CAAC,4CAA4C;YACtD,EAAE,GAAG,CAAC,CAAC;YACP,EAAE,GAAG,CAAC,CAAC;SACV;aAAM;YACH,+CAA+C;YAC/C,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACnB,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACnB,IAAI,EAAE,GAAG,GAAG,EAAE;gBACV,oCAAoC;gBACpC,EAAE,GAAG,GAAG,CAAC;gBACT,EAAE,GAAG,CAAC,CAAC;gBACP,EAAE,GAAG,CAAC,CAAC;aACV;iBAAM,IAAI,EAAE,GAAG,EAAE,EAAE;gBAChB,oCAAoC;gBACpC,EAAE,GAAG,EAAE,CAAC;gBACR,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;gBACX,EAAE,GAAG,CAAC,CAAC;aACV;SACJ;QAED,IAAI,EAAE,GAAG,GAAG,EAAE;YACV,oCAAoC;YACpC,EAAE,GAAG,GAAG,CAAC;YACT,6BAA6B;YAC7B,IAAI,CAAC,CAAC,GAAG,GAAG,EAAE;gBACV,EAAE,GAAG,GAAG,CAAC;aACZ;iBAAM,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE;gBACf,EAAE,GAAG,EAAE,CAAC;aACX;iBAAM;gBACH,EAAE,GAAG,CAAC,CAAC,CAAC;gBACR,EAAE,GAAG,CAAC,CAAC;aACV;SACJ;aAAM,IAAI,EAAE,GAAG,EAAE,EAAE;YAChB,oCAAoC;YACpC,EAAE,GAAG,EAAE,CAAC;YACR,6BAA6B;YAC7B,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE;gBACd,EAAE,GAAG,CAAC,CAAC;aACV;iBAAM,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBACnB,EAAE,GAAG,EAAE,CAAC;aACX;iBAAM;gBACH,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;gBACZ,EAAE,GAAG,CAAC,CAAC;aACV;SACJ;QACD,2CAA2C;QAC3C,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;QACxD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;QAExD,+CAA+C;QAC/C,MAAM,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QACtB,MAAM,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QACtB,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAClB,MAAM,EAAE,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACjC,GAAG,CAAC,aAAa,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,oBAAoB;QAEhD,MAAM,aAAa,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,aAAa,EAAE,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC,6BAA6B;QAE9H,IAAI,aAAa,EAAE;YACf,OAAO,GAAG,CAAC,MAAM,EAAE,CAAC;SACvB;QACD,OAAO,CAAC,CAAC,CAAC;IACd,CAAC;IAED;;;;;;;;;;;OAWG;IACI,MAAM,CACT,CAAS,EACT,CAAS,EACT,aAAqB,EACrB,cAAsB,EACtB,KAA4B,EAC5B,IAA2B,EAC3B,UAAiC,EACjC,uBAAgC,KAAK;QAErC,IAAI,oBAAoB,EAAE;YACtB,mGAAmG;YACnG,2GAA2G;YAC3G,yGAAyG;YACzG,6EAA6E;YAC7E,uEAAuE;YACvE,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE;gBAClB,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;aAChC;YAED,GAAG,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,EAAE,cAAc,EAAE,MAAM,CAAC,gBAAgB,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;YAElH,MAAM,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAChC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YACtB,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;SACjD;aAAM;YACH,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,EAAE,cAAc,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;SACxF;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,UAAU;IACV;;;OAGG;IACI,MAAM,CAAC,IAAI;QACd,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;IACnD,CAAC;IAED;;;;;;;;;;OAUG;IACI,MAAM,CAAC,SAAS,CACnB,CAAS,EACT,CAAS,EACT,aAAqB,EACrB,cAAsB,EACtB,KAA4B,EAC5B,IAA2B,EAC3B,UAAiC;QAEjC,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;QAE1B,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,EAAE,cAAc,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;IACvF,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,eAAe,CAAC,MAAe,EAAE,GAAY,EAAE,QAA+B,MAAM,CAAC,gBAAgB;QAC/G,MAAM,SAAS,GAAG,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACvC,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QAC5G,SAAS,CAAC,SAAS,EAAE,CAAC;QAEtB,OAAO,GAAG,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC;IACpE,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,SAAS,CAAC,GAAuB,EAAE,MAA6B;QAC1E,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnE,GAAG,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAExC,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,cAAc,CAAC,GAAuB,EAAE,MAA6B,EAAE,MAAW;QAC5F,OAAO,CAAC,yBAAyB,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QACrE,OAAO,CAAC,oBAAoB,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QACtE,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAE3B,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC;QAC7B,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;QAEzB,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;YAC3B,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;YACtB,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC;YACb,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC;YACb,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC;YACb,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC;SACxB;IACL,CAAC;IAED;;;;;;;;;OASG;IACI,iBAAiB,CACpB,OAAc,EACd,OAAc,EACd,aAAqB,EACrB,cAAsB,EACtB,KAA4B,EAC5B,IAA2B,EAC3B,UAAiC;;QAEjC,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACpC,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClC,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QACzC,MAAM,CAAC,MAAM,EAAE,CAAC;QAEhB,MAAM,gBAAgB,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/C,gBAAgB,CAAC,CAAC,GAAG,CAAC,OAAO,GAAG,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACvD,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,GAAG,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC3D,gBAAgB,CAAC,CAAC,GAAG,CAAA,MAAA,WAAW,CAAC,iBAAiB,0CAAE,eAAe,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE7E,uFAAuF;QACvF,MAAM,eAAe,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC;QACjH,MAAM,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACvC,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACtC,OAAO,CAAC,iCAAiC,CAAC,gBAAgB,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QAC9E,OAAO,CAAC,iCAAiC,CAAC,eAAe,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAE5E,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC/B,OAAO,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAChD,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;IAC/B,CAAC;;AA9lBuB,eAAW,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;AAC9D,eAAW,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;AA2UzB,aAAS,GAAG,UAAU,CAAC;AACvB,SAAK,GAAG,IAAI,CAAC;AAmUhC,KAAK,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,CAAS,EAAE,CAAS,EAAE,KAAuB,EAAE,MAAwB,EAAE,eAAe,GAAG,KAAK;IACzI,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;IAE1B,IAAI,CAAC,qBAAqB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;IAEzE,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,qBAAqB,GAAG,UACpC,CAAS,EACT,CAAS,EACT,KAAuB,EACvB,MAAW,EACX,MAAwB,EACxB,eAAe,GAAG,KAAK,EACvB,oBAAoB,GAAG,KAAK;IAE5B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;IAEhC,IAAI,CAAC,MAAM,EAAE;QACT,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO,IAAI,CAAC;SACf;QAED,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;KAC9B;IAED,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC;IACvC,MAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC;IAE5F,6CAA6C;IAC7C,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,uBAAuB,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC;IACtD,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,uBAAuB,EAAE,GAAG,CAAC,MAAM,CAAC,eAAe,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;IAErG,MAAM,CAAC,MAAM,CACT,CAAC,EACD,CAAC,EACD,QAAQ,CAAC,KAAK,EACd,QAAQ,CAAC,MAAM,EACf,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,EACvC,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,EAAE,EAClE,MAAM,CAAC,mBAAmB,EAAE,EAC5B,oBAAoB,CACvB,CAAC;IACF,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,6BAA6B,GAAG,UAAU,CAAS,EAAE,CAAS,EAAE,MAAe;IAC3F,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;IAE1B,IAAI,CAAC,kCAAkC,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IAE9D,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,kCAAkC,GAAG,UAAU,CAAS,EAAE,CAAS,EAAE,MAAW,EAAE,MAAe;IAC7G,IAAI,CAAC,WAAW,EAAE;QACd,OAAO,IAAI,CAAC;KACf;IAED,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;IAEhC,IAAI,CAAC,MAAM,EAAE;QACT,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SAC5C;QAED,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;KAC9B;IAED,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC;IACvC,MAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC;IAC5F,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;IAEnC,6CAA6C;IAC7C,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,uBAAuB,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC;IACtD,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,uBAAuB,EAAE,GAAG,CAAC,MAAM,CAAC,eAAe,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;IACrG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,mBAAmB,EAAE,CAAC,CAAC;IACvG,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,oBAAoB,GAAG,UACnC,WAAkC,EAClC,WAAkE,EAClE,IAAkB,EAClB,KAAa,EACb,SAAmB,EACnB,gBAA0B,EAC1B,iBAA4C,EAC5C,gBAA0B;IAE1B,MAAM,GAAG,GAAG,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAE1D,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,SAAS,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;IAC7G,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;QACxB,OAAO,IAAI,CAAC;KACf;IAED,IAAI,CAAC,SAAS,IAAI,WAAW,IAAI,IAAI,IAAI,MAAM,CAAC,QAAQ,IAAI,WAAW,CAAC,QAAQ,EAAE;QAC9E,OAAO,IAAI,CAAC;KACf;IAED,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,aAAa,GAAG,UAC5B,WAAkE,EAClE,SAA2C,EAC3C,SAAmB,EACnB,gBAA0B,EAC1B,iBAA4C;IAE5C,IAAI,WAAW,GAAG,IAAI,CAAC;IAEvB,MAAM,2BAA2B,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,sBAAsB,KAAK,IAAI,CAAC,YAAY,CAAC,CAAC;IACjJ,MAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,YAAY,CAAC;IAEvE,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;QACjE,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAEpC,IAAI,SAAS,EAAE;YACX,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;gBAClB,SAAS;aACZ;SACJ;aAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACjE,SAAS;SACZ;QAED,MAAM,YAAY,GAAG,2BAA2B,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACxF,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;QAEnE,IAAI,IAAI,CAAC,gBAAgB,IAAK,IAAa,CAAC,yBAAyB,EAAE;YACnE,8EAA8E;YAC9E,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;YAC/G,IAAI,MAAM,EAAE;gBACR,IAAI,gBAAgB,EAAE;oBAClB,iEAAiE;oBACjE,OAAO,MAAM,CAAC;iBACjB;gBACD,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACvC,MAAM,YAAY,GAAI,IAAa,CAAC,4BAA4B,EAAE,CAAC;gBACnE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBACtD,MAAM,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;oBACvC,UAAU,CAAC,aAAa,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;oBAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,IAAI,CAAC,CAAC;oBAE1I,IAAI,MAAM,EAAE;wBACR,WAAW,GAAG,MAAM,CAAC;wBACrB,WAAW,CAAC,iBAAiB,GAAG,KAAK,CAAC;wBAEtC,IAAI,SAAS,EAAE;4BACX,OAAO,WAAW,CAAC;yBACtB;qBACJ;iBACJ;aACJ;SACJ;aAAM;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;YAEhI,IAAI,MAAM,EAAE;gBACR,WAAW,GAAG,MAAM,CAAC;gBAErB,IAAI,SAAS,EAAE;oBACX,OAAO,WAAW,CAAC;iBACtB;aACJ;SACJ;KACJ;IAED,OAAO,WAAW,IAAI,IAAI,WAAW,EAAE,CAAC;AAC5C,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,kBAAkB,GAAG,UACjC,WAAkE,EAClE,SAA2C,EAC3C,iBAA4C;IAE5C,IAAI,CAAC,WAAW,EAAE;QACd,OAAO,IAAI,CAAC;KACf;IACD,MAAM,YAAY,GAAG,IAAI,KAAK,EAAe,CAAC;IAC9C,MAAM,2BAA2B,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,sBAAsB,KAAK,IAAI,CAAC,YAAY,CAAC,CAAC;IACjJ,MAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,YAAY,CAAC;IAEvE,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;QACjE,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAEpC,IAAI,SAAS,EAAE;YACX,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;gBAClB,SAAS;aACZ;SACJ;aAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACjE,SAAS;SACZ;QAED,MAAM,YAAY,GAAG,2BAA2B,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACxF,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;QAEnE,IAAI,IAAI,CAAC,gBAAgB,IAAK,IAAa,CAAC,yBAAyB,EAAE;YACnE,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;YACxG,IAAI,MAAM,EAAE;gBACR,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACvC,MAAM,YAAY,GAAI,IAAa,CAAC,4BAA4B,EAAE,CAAC;gBACnE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBACtD,MAAM,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;oBACvC,UAAU,CAAC,aAAa,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;oBAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAiB,EAAE,IAAI,CAAC,CAAC;oBAEpH,IAAI,MAAM,EAAE;wBACR,MAAM,CAAC,iBAAiB,GAAG,KAAK,CAAC;wBACjC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;qBAC7B;iBACJ;aACJ;SACJ;aAAM;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAiB,CAAC,CAAC;YAE1G,IAAI,MAAM,EAAE;gBACR,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAC7B;SACJ;KACJ;IAED,OAAO,YAAY,CAAC;AACxB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,oBAAoB,GAAG,UACnC,CAAS,EACT,CAAS,EACT,SAA2C,EAC3C,SAAmB,EACnB,MAAyB;IAEzB,IAAI,CAAC,WAAW,EAAE;QACd,OAAO,IAAI,CAAC;KACf;IACD,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAC7B,CAAC,KAAK,EAAE,EAAE;QACN,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;SACrC;QAED,IAAI,CAAC,qBAAqB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,eAAe,EAAE,MAAM,IAAI,IAAI,CAAC,CAAC;QAC9E,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC,EACD,SAAS,EACT,SAAS,EACT,IAAI,CACP,CAAC;IACF,IAAI,MAAM,EAAE;QACR,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,QAAQ,EAAE,EAAE,MAAM,IAAI,IAAI,CAAC,CAAC;KAC/E;IACD,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,EAAE;IACxD,GAAG,EAAE,GAAG,EAAE,CAAC,IAAI;IACf,UAAU,EAAE,KAAK;IACjB,YAAY,EAAE,KAAK;CACtB,CAAC,CAAC;AAEH,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,UACnB,CAAS,EACT,CAAS,EACT,SAA2C,EAC3C,SAAmB,EACnB,MAAyB,EACzB,iBAA4C,EAC5C,qBAAqB,GAAG,KAAK;IAE7B,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAC7B,CAAC,KAAK,EAAE,oBAAoB,EAAE,EAAE;QAC5B,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;SACrC;QAED,IAAI,CAAC,qBAAqB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,eAAe,EAAE,MAAM,IAAI,IAAI,EAAE,KAAK,EAAE,oBAAoB,CAAC,CAAC;QAC3G,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC,EACD,SAAS,EACT,SAAS,EACT,KAAK,EACL,iBAAiB,CACpB,CAAC;IACF,IAAI,MAAM,EAAE;QACR,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,QAAQ,EAAE,EAAE,MAAM,IAAI,IAAI,CAAC,CAAC;KAC/E;IACD,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,WAAW,GAAG,UAC1B,GAAQ,EACR,SAA2C,EAC3C,SAAmB,EACnB,iBAA4C;IAE5C,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAC7B,CAAC,KAAK,EAAE,EAAE;QACN,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;YACjC,IAAI,CAAC,yBAAyB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;SACtD;QACD,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAElD,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAC9B,IAAI,CAAC,sBAAsB,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;SAC5C;QAED,GAAG,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC,yBAAyB,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACrF,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC,EACD,SAAS,EACT,SAAS,EACT,KAAK,EACL,iBAAiB,CACpB,CAAC;IACF,IAAI,MAAM,EAAE;QACR,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;KACpB;IACD,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,SAAS,GAAG,UACxB,CAAS,EACT,CAAS,EACT,SAA2C,EAC3C,MAAe,EACf,iBAA4C;IAE5C,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,IAAI,IAAI,CAAC,EAAE,SAAS,EAAE,iBAAiB,CAAC,CAAC;AAChI,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,GAAQ,EAAE,SAA2C,EAAE,iBAA4C;IAC5I,OAAO,IAAI,CAAC,kBAAkB,CAC1B,CAAC,KAAK,EAAE,EAAE;QACN,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;YACjC,IAAI,CAAC,yBAAyB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;SACtD;QACD,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAElD,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAC9B,IAAI,CAAC,sBAAsB,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;SAC5C;QAED,GAAG,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC,yBAAyB,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACrF,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC,EACD,SAAS,EACT,iBAAiB,CACpB,CAAC;AACN,CAAC,CAAC;AAEF,MAAM,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,MAAM,GAAG,GAAG,EAAE,SAAkB,EAAE,MAAgB;IACzF,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;AAC/G,CAAC,CAAC;AAEF,MAAM,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,MAAW,EAAE,MAAM,GAAG,GAAG,EAAE,SAAkB,EAAE,MAAgB;IAC3G,IAAI,CAAC,SAAS,EAAE;QACZ,SAAS,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;KACrC;IACD,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;IAEvB,IAAI,CAAC,MAAM,EAAE;QACT,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KACzC;SAAM;QACH,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;KAClC;IACD,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3E,OAAO,CAAC,oBAAoB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAEtF,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;IAEhE,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC", "sourcesContent": ["import type { DeepImmutable, Nullable, float } from \"../types\";\r\nimport { ArrayTools } from \"../Misc/arrayTools\";\r\nimport { Matrix, Vector3, TmpVectors } from \"../Maths/math.vector\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { PickingInfo } from \"../Collisions/pickingInfo\";\r\nimport { IntersectionInfo } from \"../Collisions/intersectionInfo\";\r\nimport type { BoundingBox } from \"./boundingBox\";\r\nimport type { BoundingSphere } from \"./boundingSphere\";\r\nimport { Scene } from \"../scene\";\r\nimport { Camera } from \"../Cameras/camera\";\r\nimport type { Plane } from \"../Maths/math.plane\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\n\r\ndeclare type Mesh = import(\"../Meshes/mesh\").Mesh;\r\n\r\n/**\r\n * Class representing a ray with position and direction\r\n */\r\nexport class Ray {\r\n    private static readonly _TmpVector3 = ArrayTools.BuildArray(6, Vector3.Zero);\r\n    private static _RayDistant = Ray.Zero();\r\n    private _tmpRay: Ray;\r\n\r\n    /**\r\n     * Creates a new ray\r\n     * @param origin origin point\r\n     * @param direction direction\r\n     * @param length length of the ray\r\n     */\r\n    constructor(\r\n        /** origin point */\r\n        public origin: Vector3,\r\n        /** direction */\r\n        public direction: Vector3,\r\n        /** length of the ray */\r\n        public length: number = Number.MAX_VALUE\r\n    ) {}\r\n\r\n    // Methods\r\n\r\n    /**\r\n     * Clone the current ray\r\n     * @returns a new ray\r\n     */\r\n    public clone(): Ray {\r\n        return new Ray(this.origin.clone(), this.direction.clone(), this.length);\r\n    }\r\n\r\n    /**\r\n     * Checks if the ray intersects a box\r\n     * This does not account for the ray length by design to improve perfs.\r\n     * @param minimum bound of the box\r\n     * @param maximum bound of the box\r\n     * @param intersectionTreshold extra extend to be added to the box in all direction\r\n     * @returns if the box was hit\r\n     */\r\n    public intersectsBoxMinMax(minimum: DeepImmutable<Vector3>, maximum: DeepImmutable<Vector3>, intersectionTreshold: number = 0): boolean {\r\n        const newMinimum = Ray._TmpVector3[0].copyFromFloats(minimum.x - intersectionTreshold, minimum.y - intersectionTreshold, minimum.z - intersectionTreshold);\r\n        const newMaximum = Ray._TmpVector3[1].copyFromFloats(maximum.x + intersectionTreshold, maximum.y + intersectionTreshold, maximum.z + intersectionTreshold);\r\n        let d = 0.0;\r\n        let maxValue = Number.MAX_VALUE;\r\n        let inv: number;\r\n        let min: number;\r\n        let max: number;\r\n        let temp: number;\r\n        if (Math.abs(this.direction.x) < 0.0000001) {\r\n            if (this.origin.x < newMinimum.x || this.origin.x > newMaximum.x) {\r\n                return false;\r\n            }\r\n        } else {\r\n            inv = 1.0 / this.direction.x;\r\n            min = (newMinimum.x - this.origin.x) * inv;\r\n            max = (newMaximum.x - this.origin.x) * inv;\r\n            if (max === -Infinity) {\r\n                max = Infinity;\r\n            }\r\n\r\n            if (min > max) {\r\n                temp = min;\r\n                min = max;\r\n                max = temp;\r\n            }\r\n\r\n            d = Math.max(min, d);\r\n            maxValue = Math.min(max, maxValue);\r\n\r\n            if (d > maxValue) {\r\n                return false;\r\n            }\r\n        }\r\n\r\n        if (Math.abs(this.direction.y) < 0.0000001) {\r\n            if (this.origin.y < newMinimum.y || this.origin.y > newMaximum.y) {\r\n                return false;\r\n            }\r\n        } else {\r\n            inv = 1.0 / this.direction.y;\r\n            min = (newMinimum.y - this.origin.y) * inv;\r\n            max = (newMaximum.y - this.origin.y) * inv;\r\n\r\n            if (max === -Infinity) {\r\n                max = Infinity;\r\n            }\r\n\r\n            if (min > max) {\r\n                temp = min;\r\n                min = max;\r\n                max = temp;\r\n            }\r\n\r\n            d = Math.max(min, d);\r\n            maxValue = Math.min(max, maxValue);\r\n\r\n            if (d > maxValue) {\r\n                return false;\r\n            }\r\n        }\r\n\r\n        if (Math.abs(this.direction.z) < 0.0000001) {\r\n            if (this.origin.z < newMinimum.z || this.origin.z > newMaximum.z) {\r\n                return false;\r\n            }\r\n        } else {\r\n            inv = 1.0 / this.direction.z;\r\n            min = (newMinimum.z - this.origin.z) * inv;\r\n            max = (newMaximum.z - this.origin.z) * inv;\r\n\r\n            if (max === -Infinity) {\r\n                max = Infinity;\r\n            }\r\n\r\n            if (min > max) {\r\n                temp = min;\r\n                min = max;\r\n                max = temp;\r\n            }\r\n\r\n            d = Math.max(min, d);\r\n            maxValue = Math.min(max, maxValue);\r\n\r\n            if (d > maxValue) {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Checks if the ray intersects a box\r\n     * This does not account for the ray lenght by design to improve perfs.\r\n     * @param box the bounding box to check\r\n     * @param intersectionTreshold extra extend to be added to the BoundingBox in all direction\r\n     * @returns if the box was hit\r\n     */\r\n    public intersectsBox(box: DeepImmutable<BoundingBox>, intersectionTreshold: number = 0): boolean {\r\n        return this.intersectsBoxMinMax(box.minimum, box.maximum, intersectionTreshold);\r\n    }\r\n\r\n    /**\r\n     * If the ray hits a sphere\r\n     * @param sphere the bounding sphere to check\r\n     * @param intersectionTreshold extra extend to be added to the BoundingSphere in all direction\r\n     * @returns true if it hits the sphere\r\n     */\r\n    public intersectsSphere(sphere: DeepImmutable<BoundingSphere>, intersectionTreshold: number = 0): boolean {\r\n        const x = sphere.center.x - this.origin.x;\r\n        const y = sphere.center.y - this.origin.y;\r\n        const z = sphere.center.z - this.origin.z;\r\n        const pyth = x * x + y * y + z * z;\r\n        const radius = sphere.radius + intersectionTreshold;\r\n        const rr = radius * radius;\r\n\r\n        if (pyth <= rr) {\r\n            return true;\r\n        }\r\n\r\n        const dot = x * this.direction.x + y * this.direction.y + z * this.direction.z;\r\n        if (dot < 0.0) {\r\n            return false;\r\n        }\r\n\r\n        const temp = pyth - dot * dot;\r\n\r\n        return temp <= rr;\r\n    }\r\n\r\n    /**\r\n     * If the ray hits a triange\r\n     * @param vertex0 triangle vertex\r\n     * @param vertex1 triangle vertex\r\n     * @param vertex2 triangle vertex\r\n     * @returns intersection information if hit\r\n     */\r\n    public intersectsTriangle(vertex0: DeepImmutable<Vector3>, vertex1: DeepImmutable<Vector3>, vertex2: DeepImmutable<Vector3>): Nullable<IntersectionInfo> {\r\n        const edge1 = Ray._TmpVector3[0];\r\n        const edge2 = Ray._TmpVector3[1];\r\n        const pvec = Ray._TmpVector3[2];\r\n        const tvec = Ray._TmpVector3[3];\r\n        const qvec = Ray._TmpVector3[4];\r\n\r\n        vertex1.subtractToRef(vertex0, edge1);\r\n        vertex2.subtractToRef(vertex0, edge2);\r\n        Vector3.CrossToRef(this.direction, edge2, pvec);\r\n        const det = Vector3.Dot(edge1, pvec);\r\n\r\n        if (det === 0) {\r\n            return null;\r\n        }\r\n\r\n        const invdet = 1 / det;\r\n\r\n        this.origin.subtractToRef(vertex0, tvec);\r\n\r\n        const bv = Vector3.Dot(tvec, pvec) * invdet;\r\n\r\n        if (bv < 0 || bv > 1.0) {\r\n            return null;\r\n        }\r\n\r\n        Vector3.CrossToRef(tvec, edge1, qvec);\r\n\r\n        const bw = Vector3.Dot(this.direction, qvec) * invdet;\r\n\r\n        if (bw < 0 || bv + bw > 1.0) {\r\n            return null;\r\n        }\r\n\r\n        //check if the distance is longer than the predefined length.\r\n        const distance = Vector3.Dot(edge2, qvec) * invdet;\r\n        if (distance > this.length) {\r\n            return null;\r\n        }\r\n\r\n        return new IntersectionInfo(1 - bv - bw, bv, distance);\r\n    }\r\n\r\n    /**\r\n     * Checks if ray intersects a plane\r\n     * @param plane the plane to check\r\n     * @returns the distance away it was hit\r\n     */\r\n    public intersectsPlane(plane: DeepImmutable<Plane>): Nullable<number> {\r\n        let distance: number;\r\n        const result1 = Vector3.Dot(plane.normal, this.direction);\r\n        if (Math.abs(result1) < 9.99999997475243e-7) {\r\n            return null;\r\n        } else {\r\n            const result2 = Vector3.Dot(plane.normal, this.origin);\r\n            distance = (-plane.d - result2) / result1;\r\n            if (distance < 0.0) {\r\n                if (distance < -9.99999997475243e-7) {\r\n                    return null;\r\n                } else {\r\n                    return 0;\r\n                }\r\n            }\r\n\r\n            return distance;\r\n        }\r\n    }\r\n    /**\r\n     * Calculate the intercept of a ray on a given axis\r\n     * @param axis to check 'x' | 'y' | 'z'\r\n     * @param offset from axis interception (i.e. an offset of 1y is intercepted above ground)\r\n     * @returns a vector containing the coordinates where 'axis' is equal to zero (else offset), or null if there is no intercept.\r\n     */\r\n    public intersectsAxis(axis: string, offset: number = 0): Nullable<Vector3> {\r\n        switch (axis) {\r\n            case \"y\": {\r\n                const t = (this.origin.y - offset) / this.direction.y;\r\n                if (t > 0) {\r\n                    return null;\r\n                }\r\n                return new Vector3(this.origin.x + this.direction.x * -t, offset, this.origin.z + this.direction.z * -t);\r\n            }\r\n            case \"x\": {\r\n                const t = (this.origin.x - offset) / this.direction.x;\r\n                if (t > 0) {\r\n                    return null;\r\n                }\r\n                return new Vector3(offset, this.origin.y + this.direction.y * -t, this.origin.z + this.direction.z * -t);\r\n            }\r\n            case \"z\": {\r\n                const t = (this.origin.z - offset) / this.direction.z;\r\n                if (t > 0) {\r\n                    return null;\r\n                }\r\n                return new Vector3(this.origin.x + this.direction.x * -t, this.origin.y + this.direction.y * -t, offset);\r\n            }\r\n            default:\r\n                return null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Checks if ray intersects a mesh\r\n     * @param mesh the mesh to check\r\n     * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n     * @returns picking info of the intersection\r\n     */\r\n    public intersectsMesh(mesh: DeepImmutable<AbstractMesh>, fastCheck?: boolean): PickingInfo {\r\n        const tm = TmpVectors.Matrix[0];\r\n\r\n        mesh.getWorldMatrix().invertToRef(tm);\r\n\r\n        if (this._tmpRay) {\r\n            Ray.TransformToRef(this, tm, this._tmpRay);\r\n        } else {\r\n            this._tmpRay = Ray.Transform(this, tm);\r\n        }\r\n\r\n        return mesh.intersects(this._tmpRay, fastCheck);\r\n    }\r\n\r\n    /**\r\n     * Checks if ray intersects a mesh\r\n     * @param meshes the meshes to check\r\n     * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n     * @param results array to store result in\r\n     * @returns Array of picking infos\r\n     */\r\n    public intersectsMeshes(meshes: Array<DeepImmutable<AbstractMesh>>, fastCheck?: boolean, results?: Array<PickingInfo>): Array<PickingInfo> {\r\n        if (results) {\r\n            results.length = 0;\r\n        } else {\r\n            results = [];\r\n        }\r\n\r\n        for (let i = 0; i < meshes.length; i++) {\r\n            const pickInfo = this.intersectsMesh(meshes[i], fastCheck);\r\n\r\n            if (pickInfo.hit) {\r\n                results.push(pickInfo);\r\n            }\r\n        }\r\n\r\n        results.sort(this._comparePickingInfo);\r\n\r\n        return results;\r\n    }\r\n\r\n    private _comparePickingInfo(pickingInfoA: DeepImmutable<PickingInfo>, pickingInfoB: DeepImmutable<PickingInfo>): number {\r\n        if (pickingInfoA.distance < pickingInfoB.distance) {\r\n            return -1;\r\n        } else if (pickingInfoA.distance > pickingInfoB.distance) {\r\n            return 1;\r\n        } else {\r\n            return 0;\r\n        }\r\n    }\r\n\r\n    private static _Smallnum = 0.00000001;\r\n    private static _Rayl = 10e8;\r\n\r\n    /**\r\n     * Intersection test between the ray and a given segment within a given tolerance (threshold)\r\n     * @param sega the first point of the segment to test the intersection against\r\n     * @param segb the second point of the segment to test the intersection against\r\n     * @param threshold the tolerance margin, if the ray doesn't intersect the segment but is close to the given threshold, the intersection is successful\r\n     * @returns the distance from the ray origin to the intersection point if there's intersection, or -1 if there's no intersection\r\n     */\r\n    intersectionSegment(sega: DeepImmutable<Vector3>, segb: DeepImmutable<Vector3>, threshold: number): number {\r\n        const o = this.origin;\r\n        const u = TmpVectors.Vector3[0];\r\n        const rsegb = TmpVectors.Vector3[1];\r\n        const v = TmpVectors.Vector3[2];\r\n        const w = TmpVectors.Vector3[3];\r\n\r\n        segb.subtractToRef(sega, u);\r\n\r\n        this.direction.scaleToRef(Ray._Rayl, v);\r\n        o.addToRef(v, rsegb);\r\n\r\n        sega.subtractToRef(o, w);\r\n\r\n        const a = Vector3.Dot(u, u); // always >= 0\r\n        const b = Vector3.Dot(u, v);\r\n        const c = Vector3.Dot(v, v); // always >= 0\r\n        const d = Vector3.Dot(u, w);\r\n        const e = Vector3.Dot(v, w);\r\n        const D = a * c - b * b; // always >= 0\r\n        let sN: number,\r\n            sD = D; // sc = sN / sD, default sD = D >= 0\r\n        let tN: number,\r\n            tD = D; // tc = tN / tD, default tD = D >= 0\r\n\r\n        // compute the line parameters of the two closest points\r\n        if (D < Ray._Smallnum) {\r\n            // the lines are almost parallel\r\n            sN = 0.0; // force using point P0 on segment S1\r\n            sD = 1.0; // to prevent possible division by 0.0 later\r\n            tN = e;\r\n            tD = c;\r\n        } else {\r\n            // get the closest points on the infinite lines\r\n            sN = b * e - c * d;\r\n            tN = a * e - b * d;\r\n            if (sN < 0.0) {\r\n                // sc < 0 => the s=0 edge is visible\r\n                sN = 0.0;\r\n                tN = e;\r\n                tD = c;\r\n            } else if (sN > sD) {\r\n                // sc > 1 => the s=1 edge is visible\r\n                sN = sD;\r\n                tN = e + b;\r\n                tD = c;\r\n            }\r\n        }\r\n\r\n        if (tN < 0.0) {\r\n            // tc < 0 => the t=0 edge is visible\r\n            tN = 0.0;\r\n            // recompute sc for this edge\r\n            if (-d < 0.0) {\r\n                sN = 0.0;\r\n            } else if (-d > a) {\r\n                sN = sD;\r\n            } else {\r\n                sN = -d;\r\n                sD = a;\r\n            }\r\n        } else if (tN > tD) {\r\n            // tc > 1 => the t=1 edge is visible\r\n            tN = tD;\r\n            // recompute sc for this edge\r\n            if (-d + b < 0.0) {\r\n                sN = 0;\r\n            } else if (-d + b > a) {\r\n                sN = sD;\r\n            } else {\r\n                sN = -d + b;\r\n                sD = a;\r\n            }\r\n        }\r\n        // finally do the division to get sc and tc\r\n        const sc = Math.abs(sN) < Ray._Smallnum ? 0.0 : sN / sD;\r\n        const tc = Math.abs(tN) < Ray._Smallnum ? 0.0 : tN / tD;\r\n\r\n        // get the difference of the two closest points\r\n        const qtc = TmpVectors.Vector3[4];\r\n        v.scaleToRef(tc, qtc);\r\n        const qsc = TmpVectors.Vector3[5];\r\n        u.scaleToRef(sc, qsc);\r\n        qsc.addInPlace(w);\r\n        const dP = TmpVectors.Vector3[6];\r\n        qsc.subtractToRef(qtc, dP); // = S1(sc) - S2(tc)\r\n\r\n        const isIntersected = tc > 0 && tc <= this.length && dP.lengthSquared() < threshold * threshold; // return intersection result\r\n\r\n        if (isIntersected) {\r\n            return qsc.length();\r\n        }\r\n        return -1;\r\n    }\r\n\r\n    /**\r\n     * Update the ray from viewport position\r\n     * @param x position\r\n     * @param y y position\r\n     * @param viewportWidth viewport width\r\n     * @param viewportHeight viewport height\r\n     * @param world world matrix\r\n     * @param view view matrix\r\n     * @param projection projection matrix\r\n     * @param enableDistantPicking defines if picking should handle large values for mesh position/scaling (false by default)\r\n     * @returns this ray updated\r\n     */\r\n    public update(\r\n        x: number,\r\n        y: number,\r\n        viewportWidth: number,\r\n        viewportHeight: number,\r\n        world: DeepImmutable<Matrix>,\r\n        view: DeepImmutable<Matrix>,\r\n        projection: DeepImmutable<Matrix>,\r\n        enableDistantPicking: boolean = false\r\n    ): Ray {\r\n        if (enableDistantPicking) {\r\n            // With world matrices having great values (like 8000000000 on 1 or more scaling or position axis),\r\n            // multiplying view/projection/world and doing invert will result in loss of float precision in the matrix.\r\n            // One way to fix it is to compute the ray with world at identity then transform the ray in object space.\r\n            // This is slower (2 matrix inverts instead of 1) but precision is preserved.\r\n            // This is hidden behind `EnableDistantPicking` flag (default is false)\r\n            if (!Ray._RayDistant) {\r\n                Ray._RayDistant = Ray.Zero();\r\n            }\r\n\r\n            Ray._RayDistant.unprojectRayToRef(x, y, viewportWidth, viewportHeight, Matrix.IdentityReadOnly, view, projection);\r\n\r\n            const tm = TmpVectors.Matrix[0];\r\n            world.invertToRef(tm);\r\n            Ray.TransformToRef(Ray._RayDistant, tm, this);\r\n        } else {\r\n            this.unprojectRayToRef(x, y, viewportWidth, viewportHeight, world, view, projection);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    // Statics\r\n    /**\r\n     * Creates a ray with origin and direction of 0,0,0\r\n     * @returns the new ray\r\n     */\r\n    public static Zero(): Ray {\r\n        return new Ray(Vector3.Zero(), Vector3.Zero());\r\n    }\r\n\r\n    /**\r\n     * Creates a new ray from screen space and viewport\r\n     * @param x position\r\n     * @param y y position\r\n     * @param viewportWidth viewport width\r\n     * @param viewportHeight viewport height\r\n     * @param world world matrix\r\n     * @param view view matrix\r\n     * @param projection projection matrix\r\n     * @returns new ray\r\n     */\r\n    public static CreateNew(\r\n        x: number,\r\n        y: number,\r\n        viewportWidth: number,\r\n        viewportHeight: number,\r\n        world: DeepImmutable<Matrix>,\r\n        view: DeepImmutable<Matrix>,\r\n        projection: DeepImmutable<Matrix>\r\n    ): Ray {\r\n        const result = Ray.Zero();\r\n\r\n        return result.update(x, y, viewportWidth, viewportHeight, world, view, projection);\r\n    }\r\n\r\n    /**\r\n     * Function will create a new transformed ray starting from origin and ending at the end point. Ray's length will be set, and ray will be\r\n     * transformed to the given world matrix.\r\n     * @param origin The origin point\r\n     * @param end The end point\r\n     * @param world a matrix to transform the ray to. Default is the identity matrix.\r\n     * @returns the new ray\r\n     */\r\n    public static CreateNewFromTo(origin: Vector3, end: Vector3, world: DeepImmutable<Matrix> = Matrix.IdentityReadOnly): Ray {\r\n        const direction = end.subtract(origin);\r\n        const length = Math.sqrt(direction.x * direction.x + direction.y * direction.y + direction.z * direction.z);\r\n        direction.normalize();\r\n\r\n        return Ray.Transform(new Ray(origin, direction, length), world);\r\n    }\r\n\r\n    /**\r\n     * Transforms a ray by a matrix\r\n     * @param ray ray to transform\r\n     * @param matrix matrix to apply\r\n     * @returns the resulting new ray\r\n     */\r\n    public static Transform(ray: DeepImmutable<Ray>, matrix: DeepImmutable<Matrix>): Ray {\r\n        const result = new Ray(new Vector3(0, 0, 0), new Vector3(0, 0, 0));\r\n        Ray.TransformToRef(ray, matrix, result);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Transforms a ray by a matrix\r\n     * @param ray ray to transform\r\n     * @param matrix matrix to apply\r\n     * @param result ray to store result in\r\n     */\r\n    public static TransformToRef(ray: DeepImmutable<Ray>, matrix: DeepImmutable<Matrix>, result: Ray): void {\r\n        Vector3.TransformCoordinatesToRef(ray.origin, matrix, result.origin);\r\n        Vector3.TransformNormalToRef(ray.direction, matrix, result.direction);\r\n        result.length = ray.length;\r\n\r\n        const dir = result.direction;\r\n        const len = dir.length();\r\n\r\n        if (!(len === 0 || len === 1)) {\r\n            const num = 1.0 / len;\r\n            dir.x *= num;\r\n            dir.y *= num;\r\n            dir.z *= num;\r\n            result.length *= len;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Unproject a ray from screen space to object space\r\n     * @param sourceX defines the screen space x coordinate to use\r\n     * @param sourceY defines the screen space y coordinate to use\r\n     * @param viewportWidth defines the current width of the viewport\r\n     * @param viewportHeight defines the current height of the viewport\r\n     * @param world defines the world matrix to use (can be set to Identity to go to world space)\r\n     * @param view defines the view matrix to use\r\n     * @param projection defines the projection matrix to use\r\n     */\r\n    public unprojectRayToRef(\r\n        sourceX: float,\r\n        sourceY: float,\r\n        viewportWidth: number,\r\n        viewportHeight: number,\r\n        world: DeepImmutable<Matrix>,\r\n        view: DeepImmutable<Matrix>,\r\n        projection: DeepImmutable<Matrix>\r\n    ): void {\r\n        const matrix = TmpVectors.Matrix[0];\r\n        world.multiplyToRef(view, matrix);\r\n        matrix.multiplyToRef(projection, matrix);\r\n        matrix.invert();\r\n\r\n        const nearScreenSource = TmpVectors.Vector3[0];\r\n        nearScreenSource.x = (sourceX / viewportWidth) * 2 - 1;\r\n        nearScreenSource.y = -((sourceY / viewportHeight) * 2 - 1);\r\n        nearScreenSource.z = EngineStore.LastCreatedEngine?.isNDCHalfZRange ? 0 : -1;\r\n\r\n        // far Z need to be close but < to 1 or camera projection matrix with maxZ = 0 will NaN\r\n        const farScreenSource = TmpVectors.Vector3[1].copyFromFloats(nearScreenSource.x, nearScreenSource.y, 1.0 - 1e-8);\r\n        const nearVec3 = TmpVectors.Vector3[2];\r\n        const farVec3 = TmpVectors.Vector3[3];\r\n        Vector3._UnprojectFromInvertedMatrixToRef(nearScreenSource, matrix, nearVec3);\r\n        Vector3._UnprojectFromInvertedMatrixToRef(farScreenSource, matrix, farVec3);\r\n\r\n        this.origin.copyFrom(nearVec3);\r\n        farVec3.subtractToRef(nearVec3, this.direction);\r\n        this.direction.normalize();\r\n    }\r\n}\r\n\r\n// Picking\r\n/**\r\n * Type used to define predicate used to select faces when a mesh intersection is detected\r\n */\r\nexport type TrianglePickingPredicate = (p0: Vector3, p1: Vector3, p2: Vector3, ray: Ray, i0: number, i1: number, i2: number) => boolean;\r\n\r\ndeclare module \"../scene\" {\r\n    export interface Scene {\r\n        /** @internal */\r\n        _tempPickingRay: Nullable<Ray>;\r\n\r\n        /** @internal */\r\n        _cachedRayForTransform: Ray;\r\n\r\n        /** @internal */\r\n        _pickWithRayInverseMatrix: Matrix;\r\n\r\n        /** @internal */\r\n        _internalPick(\r\n            rayFunction: (world: Matrix, enableDistantPicking: boolean) => Ray,\r\n            predicate?: (mesh: AbstractMesh) => boolean,\r\n            fastCheck?: boolean,\r\n            onlyBoundingInfo?: boolean,\r\n            trianglePredicate?: TrianglePickingPredicate\r\n        ): PickingInfo;\r\n\r\n        /** @internal */\r\n        _internalMultiPick(\r\n            rayFunction: (world: Matrix, enableDistantPicking: boolean) => Ray,\r\n            predicate?: (mesh: AbstractMesh) => boolean,\r\n            trianglePredicate?: TrianglePickingPredicate\r\n        ): Nullable<PickingInfo[]>;\r\n\r\n        /** @internal */\r\n        _internalPickForMesh(\r\n            pickingInfo: Nullable<PickingInfo>,\r\n            rayFunction: (world: Matrix, enableDistantPicking: boolean) => Ray,\r\n            mesh: AbstractMesh,\r\n            world: Matrix,\r\n            fastCheck?: boolean,\r\n            onlyBoundingInfo?: boolean,\r\n            trianglePredicate?: TrianglePickingPredicate,\r\n            skipBoundingInfo?: boolean\r\n        ): Nullable<PickingInfo>;\r\n    }\r\n}\r\n\r\nScene.prototype.createPickingRay = function (x: number, y: number, world: Nullable<Matrix>, camera: Nullable<Camera>, cameraViewSpace = false): Ray {\r\n    const result = Ray.Zero();\r\n\r\n    this.createPickingRayToRef(x, y, world, result, camera, cameraViewSpace);\r\n\r\n    return result;\r\n};\r\n\r\nScene.prototype.createPickingRayToRef = function (\r\n    x: number,\r\n    y: number,\r\n    world: Nullable<Matrix>,\r\n    result: Ray,\r\n    camera: Nullable<Camera>,\r\n    cameraViewSpace = false,\r\n    enableDistantPicking = false\r\n): Scene {\r\n    const engine = this.getEngine();\r\n\r\n    if (!camera) {\r\n        if (!this.activeCamera) {\r\n            return this;\r\n        }\r\n\r\n        camera = this.activeCamera;\r\n    }\r\n\r\n    const cameraViewport = camera.viewport;\r\n    const viewport = cameraViewport.toGlobal(engine.getRenderWidth(), engine.getRenderHeight());\r\n\r\n    // Moving coordinates to local viewport world\r\n    x = x / engine.getHardwareScalingLevel() - viewport.x;\r\n    y = y / engine.getHardwareScalingLevel() - (engine.getRenderHeight() - viewport.y - viewport.height);\r\n\r\n    result.update(\r\n        x,\r\n        y,\r\n        viewport.width,\r\n        viewport.height,\r\n        world ? world : Matrix.IdentityReadOnly,\r\n        cameraViewSpace ? Matrix.IdentityReadOnly : camera.getViewMatrix(),\r\n        camera.getProjectionMatrix(),\r\n        enableDistantPicking\r\n    );\r\n    return this;\r\n};\r\n\r\nScene.prototype.createPickingRayInCameraSpace = function (x: number, y: number, camera?: Camera): Ray {\r\n    const result = Ray.Zero();\r\n\r\n    this.createPickingRayInCameraSpaceToRef(x, y, result, camera);\r\n\r\n    return result;\r\n};\r\n\r\nScene.prototype.createPickingRayInCameraSpaceToRef = function (x: number, y: number, result: Ray, camera?: Camera): Scene {\r\n    if (!PickingInfo) {\r\n        return this;\r\n    }\r\n\r\n    const engine = this.getEngine();\r\n\r\n    if (!camera) {\r\n        if (!this.activeCamera) {\r\n            throw new Error(\"Active camera not set\");\r\n        }\r\n\r\n        camera = this.activeCamera;\r\n    }\r\n\r\n    const cameraViewport = camera.viewport;\r\n    const viewport = cameraViewport.toGlobal(engine.getRenderWidth(), engine.getRenderHeight());\r\n    const identity = Matrix.Identity();\r\n\r\n    // Moving coordinates to local viewport world\r\n    x = x / engine.getHardwareScalingLevel() - viewport.x;\r\n    y = y / engine.getHardwareScalingLevel() - (engine.getRenderHeight() - viewport.y - viewport.height);\r\n    result.update(x, y, viewport.width, viewport.height, identity, identity, camera.getProjectionMatrix());\r\n    return this;\r\n};\r\n\r\nScene.prototype._internalPickForMesh = function (\r\n    pickingInfo: Nullable<PickingInfo>,\r\n    rayFunction: (world: Matrix, enableDistantPicking: boolean) => Ray,\r\n    mesh: AbstractMesh,\r\n    world: Matrix,\r\n    fastCheck?: boolean,\r\n    onlyBoundingInfo?: boolean,\r\n    trianglePredicate?: TrianglePickingPredicate,\r\n    skipBoundingInfo?: boolean\r\n) {\r\n    const ray = rayFunction(world, mesh.enableDistantPicking);\r\n\r\n    const result = mesh.intersects(ray, fastCheck, trianglePredicate, onlyBoundingInfo, world, skipBoundingInfo);\r\n    if (!result || !result.hit) {\r\n        return null;\r\n    }\r\n\r\n    if (!fastCheck && pickingInfo != null && result.distance >= pickingInfo.distance) {\r\n        return null;\r\n    }\r\n\r\n    return result;\r\n};\r\n\r\nScene.prototype._internalPick = function (\r\n    rayFunction: (world: Matrix, enableDistantPicking: boolean) => Ray,\r\n    predicate?: (mesh: AbstractMesh) => boolean,\r\n    fastCheck?: boolean,\r\n    onlyBoundingInfo?: boolean,\r\n    trianglePredicate?: TrianglePickingPredicate\r\n): PickingInfo {\r\n    let pickingInfo = null;\r\n\r\n    const computeWorldMatrixForCamera = !!(this.activeCameras && this.activeCameras.length > 1 && this.cameraToUseForPointers !== this.activeCamera);\r\n    const currentCamera = this.cameraToUseForPointers || this.activeCamera;\r\n\r\n    for (let meshIndex = 0; meshIndex < this.meshes.length; meshIndex++) {\r\n        const mesh = this.meshes[meshIndex];\r\n\r\n        if (predicate) {\r\n            if (!predicate(mesh)) {\r\n                continue;\r\n            }\r\n        } else if (!mesh.isEnabled() || !mesh.isVisible || !mesh.isPickable) {\r\n            continue;\r\n        }\r\n\r\n        const forceCompute = computeWorldMatrixForCamera && mesh.isWorldMatrixCameraDependent();\r\n        const world = mesh.computeWorldMatrix(forceCompute, currentCamera);\r\n\r\n        if (mesh.hasThinInstances && (mesh as Mesh).thinInstanceEnablePicking) {\r\n            // first check if the ray intersects the whole bounding box/sphere of the mesh\r\n            const result = this._internalPickForMesh(pickingInfo, rayFunction, mesh, world, true, true, trianglePredicate);\r\n            if (result) {\r\n                if (onlyBoundingInfo) {\r\n                    // the user only asked for a bounding info check so we can return\r\n                    return result;\r\n                }\r\n                const tmpMatrix = TmpVectors.Matrix[1];\r\n                const thinMatrices = (mesh as Mesh).thinInstanceGetWorldMatrices();\r\n                for (let index = 0; index < thinMatrices.length; index++) {\r\n                    const thinMatrix = thinMatrices[index];\r\n                    thinMatrix.multiplyToRef(world, tmpMatrix);\r\n                    const result = this._internalPickForMesh(pickingInfo, rayFunction, mesh, tmpMatrix, fastCheck, onlyBoundingInfo, trianglePredicate, true);\r\n\r\n                    if (result) {\r\n                        pickingInfo = result;\r\n                        pickingInfo.thinInstanceIndex = index;\r\n\r\n                        if (fastCheck) {\r\n                            return pickingInfo;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        } else {\r\n            const result = this._internalPickForMesh(pickingInfo, rayFunction, mesh, world, fastCheck, onlyBoundingInfo, trianglePredicate);\r\n\r\n            if (result) {\r\n                pickingInfo = result;\r\n\r\n                if (fastCheck) {\r\n                    return pickingInfo;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    return pickingInfo || new PickingInfo();\r\n};\r\n\r\nScene.prototype._internalMultiPick = function (\r\n    rayFunction: (world: Matrix, enableDistantPicking: boolean) => Ray,\r\n    predicate?: (mesh: AbstractMesh) => boolean,\r\n    trianglePredicate?: TrianglePickingPredicate\r\n): Nullable<PickingInfo[]> {\r\n    if (!PickingInfo) {\r\n        return null;\r\n    }\r\n    const pickingInfos = new Array<PickingInfo>();\r\n    const computeWorldMatrixForCamera = !!(this.activeCameras && this.activeCameras.length > 1 && this.cameraToUseForPointers !== this.activeCamera);\r\n    const currentCamera = this.cameraToUseForPointers || this.activeCamera;\r\n\r\n    for (let meshIndex = 0; meshIndex < this.meshes.length; meshIndex++) {\r\n        const mesh = this.meshes[meshIndex];\r\n\r\n        if (predicate) {\r\n            if (!predicate(mesh)) {\r\n                continue;\r\n            }\r\n        } else if (!mesh.isEnabled() || !mesh.isVisible || !mesh.isPickable) {\r\n            continue;\r\n        }\r\n\r\n        const forceCompute = computeWorldMatrixForCamera && mesh.isWorldMatrixCameraDependent();\r\n        const world = mesh.computeWorldMatrix(forceCompute, currentCamera);\r\n\r\n        if (mesh.hasThinInstances && (mesh as Mesh).thinInstanceEnablePicking) {\r\n            const result = this._internalPickForMesh(null, rayFunction, mesh, world, true, true, trianglePredicate);\r\n            if (result) {\r\n                const tmpMatrix = TmpVectors.Matrix[1];\r\n                const thinMatrices = (mesh as Mesh).thinInstanceGetWorldMatrices();\r\n                for (let index = 0; index < thinMatrices.length; index++) {\r\n                    const thinMatrix = thinMatrices[index];\r\n                    thinMatrix.multiplyToRef(world, tmpMatrix);\r\n                    const result = this._internalPickForMesh(null, rayFunction, mesh, tmpMatrix, false, false, trianglePredicate, true);\r\n\r\n                    if (result) {\r\n                        result.thinInstanceIndex = index;\r\n                        pickingInfos.push(result);\r\n                    }\r\n                }\r\n            }\r\n        } else {\r\n            const result = this._internalPickForMesh(null, rayFunction, mesh, world, false, false, trianglePredicate);\r\n\r\n            if (result) {\r\n                pickingInfos.push(result);\r\n            }\r\n        }\r\n    }\r\n\r\n    return pickingInfos;\r\n};\r\n\r\nScene.prototype.pickWithBoundingInfo = function (\r\n    x: number,\r\n    y: number,\r\n    predicate?: (mesh: AbstractMesh) => boolean,\r\n    fastCheck?: boolean,\r\n    camera?: Nullable<Camera>\r\n): Nullable<PickingInfo> {\r\n    if (!PickingInfo) {\r\n        return null;\r\n    }\r\n    const result = this._internalPick(\r\n        (world) => {\r\n            if (!this._tempPickingRay) {\r\n                this._tempPickingRay = Ray.Zero();\r\n            }\r\n\r\n            this.createPickingRayToRef(x, y, world, this._tempPickingRay, camera || null);\r\n            return this._tempPickingRay;\r\n        },\r\n        predicate,\r\n        fastCheck,\r\n        true\r\n    );\r\n    if (result) {\r\n        result.ray = this.createPickingRay(x, y, Matrix.Identity(), camera || null);\r\n    }\r\n    return result;\r\n};\r\n\r\nObject.defineProperty(Scene.prototype, \"_pickingAvailable\", {\r\n    get: () => true,\r\n    enumerable: false,\r\n    configurable: false,\r\n});\r\n\r\nScene.prototype.pick = function (\r\n    x: number,\r\n    y: number,\r\n    predicate?: (mesh: AbstractMesh) => boolean,\r\n    fastCheck?: boolean,\r\n    camera?: Nullable<Camera>,\r\n    trianglePredicate?: TrianglePickingPredicate,\r\n    _enableDistantPicking = false\r\n): PickingInfo {\r\n    const result = this._internalPick(\r\n        (world, enableDistantPicking) => {\r\n            if (!this._tempPickingRay) {\r\n                this._tempPickingRay = Ray.Zero();\r\n            }\r\n\r\n            this.createPickingRayToRef(x, y, world, this._tempPickingRay, camera || null, false, enableDistantPicking);\r\n            return this._tempPickingRay;\r\n        },\r\n        predicate,\r\n        fastCheck,\r\n        false,\r\n        trianglePredicate\r\n    );\r\n    if (result) {\r\n        result.ray = this.createPickingRay(x, y, Matrix.Identity(), camera || null);\r\n    }\r\n    return result;\r\n};\r\n\r\nScene.prototype.pickWithRay = function (\r\n    ray: Ray,\r\n    predicate?: (mesh: AbstractMesh) => boolean,\r\n    fastCheck?: boolean,\r\n    trianglePredicate?: TrianglePickingPredicate\r\n): Nullable<PickingInfo> {\r\n    const result = this._internalPick(\r\n        (world) => {\r\n            if (!this._pickWithRayInverseMatrix) {\r\n                this._pickWithRayInverseMatrix = Matrix.Identity();\r\n            }\r\n            world.invertToRef(this._pickWithRayInverseMatrix);\r\n\r\n            if (!this._cachedRayForTransform) {\r\n                this._cachedRayForTransform = Ray.Zero();\r\n            }\r\n\r\n            Ray.TransformToRef(ray, this._pickWithRayInverseMatrix, this._cachedRayForTransform);\r\n            return this._cachedRayForTransform;\r\n        },\r\n        predicate,\r\n        fastCheck,\r\n        false,\r\n        trianglePredicate\r\n    );\r\n    if (result) {\r\n        result.ray = ray;\r\n    }\r\n    return result;\r\n};\r\n\r\nScene.prototype.multiPick = function (\r\n    x: number,\r\n    y: number,\r\n    predicate?: (mesh: AbstractMesh) => boolean,\r\n    camera?: Camera,\r\n    trianglePredicate?: TrianglePickingPredicate\r\n): Nullable<PickingInfo[]> {\r\n    return this._internalMultiPick((world) => this.createPickingRay(x, y, world, camera || null), predicate, trianglePredicate);\r\n};\r\n\r\nScene.prototype.multiPickWithRay = function (ray: Ray, predicate?: (mesh: AbstractMesh) => boolean, trianglePredicate?: TrianglePickingPredicate): Nullable<PickingInfo[]> {\r\n    return this._internalMultiPick(\r\n        (world) => {\r\n            if (!this._pickWithRayInverseMatrix) {\r\n                this._pickWithRayInverseMatrix = Matrix.Identity();\r\n            }\r\n            world.invertToRef(this._pickWithRayInverseMatrix);\r\n\r\n            if (!this._cachedRayForTransform) {\r\n                this._cachedRayForTransform = Ray.Zero();\r\n            }\r\n\r\n            Ray.TransformToRef(ray, this._pickWithRayInverseMatrix, this._cachedRayForTransform);\r\n            return this._cachedRayForTransform;\r\n        },\r\n        predicate,\r\n        trianglePredicate\r\n    );\r\n};\r\n\r\nCamera.prototype.getForwardRay = function (length = 100, transform?: Matrix, origin?: Vector3): Ray {\r\n    return this.getForwardRayToRef(new Ray(Vector3.Zero(), Vector3.Zero(), length), length, transform, origin);\r\n};\r\n\r\nCamera.prototype.getForwardRayToRef = function (refRay: Ray, length = 100, transform?: Matrix, origin?: Vector3): Ray {\r\n    if (!transform) {\r\n        transform = this.getWorldMatrix();\r\n    }\r\n    refRay.length = length;\r\n\r\n    if (!origin) {\r\n        refRay.origin.copyFrom(this.position);\r\n    } else {\r\n        refRay.origin.copyFrom(origin);\r\n    }\r\n    TmpVectors.Vector3[2].set(0, 0, this._scene.useRightHandedSystem ? -1 : 1);\r\n    Vector3.TransformNormalToRef(TmpVectors.Vector3[2], transform, TmpVectors.Vector3[3]);\r\n\r\n    Vector3.NormalizeToRef(TmpVectors.Vector3[3], refRay.direction);\r\n\r\n    return refRay;\r\n};\r\n"]}