{"version": 3, "file": "vertexAnimationBaker.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/BakedVertexAnimation/vertexAnimationBaker.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,kCAAkC,CAAC;AAC9D,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AAExD,OAAO,EAAE,yBAAyB,EAAE,oBAAoB,EAAE,MAAM,qBAAqB,CAAC;AAEtF,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAEjD;;;GAGG;AACH,MAAM,OAAO,oBAAoB;IAI7B;;;;OAIG;IACH,YAAY,KAAY,EAAE,IAAU;QAChC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACtB,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,cAAc,CAAC,MAAwB;QAChD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;SAChD;QACD,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC;QAEnD,+CAA+C;QAC/C,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,QAAgB,EAAE,OAAuB,EAAE,EAAE,CAAC,QAAQ,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAE7H,IAAI,KAAK,CAAC,UAAU,CAAC,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;SAChD;QAED,sBAAsB;QACtB,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,MAAM,WAAW,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;QACzD,MAAM,UAAU,GAAG,IAAI,YAAY,CAAC,WAAW,CAAC,CAAC;QACjD,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;QAEnC,oCAAoC;QACpC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YACxB,KAAK,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,EAAE,UAAU,IAAI,KAAK,CAAC,EAAE,EAAE,UAAU,EAAE,EAAE;gBACpE,MAAM,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,CAAC,CAAC;aAC7E;SACJ;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;;;OAMG;IACK,KAAK,CAAC,sBAAsB,CAAC,UAAwB,EAAE,UAAkB,EAAE,YAAoB;QACnG,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;YAC1C,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE;gBACrF,oBAAoB;gBACpB,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,QAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC/E,UAAU,CAAC,GAAG,CAAC,gBAAgB,EAAE,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;gBAEzE,OAAO,EAAE,CAAC;YACd,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IACD;;;;OAIG;IACI,0BAA0B,CAAC,UAAwB;QACtD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;SAChD;QACD,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC;QAEnD,MAAM,OAAO,GAAG,UAAU,CAAC,iBAAiB,CACxC,UAAU,EACV,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,EACnB,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAC7C,IAAI,CAAC,MAAM,EACX,KAAK,EACL,KAAK,EACL,OAAO,CAAC,eAAe,EACvB,SAAS,CAAC,iBAAiB,CAC9B,CAAC;QACF,OAAO,CAAC,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;QAChD,OAAO,OAAO,CAAC;IACnB,CAAC;IACD;;;;OAIG;IACI,gCAAgC,CAAC,UAAwB;QAC5D,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;SAChD;QAED,4EAA4E;QAC5E,qBAAqB;QACrB,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC;QACnD,MAAM,KAAK,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QAClC,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7D,MAAM,IAAI,GAAG;YACT,UAAU,EAAE,yBAAyB,CAAC,UAAU,CAAC;YACjD,KAAK;YACL,MAAM;SACT,CAAC;QACF,OAAO,IAAI,CAAC;IAChB,CAAC;IACD;;;;OAIG;IACI,6BAA6B,CAAC,IAAyB;QAC1D,OAAO,IAAI,YAAY,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;IACnE,CAAC;IACD;;;;;OAKG;IACI,8BAA8B,CAAC,UAAwB;QAC1D,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gCAAgC,CAAC,UAAU,CAAC,CAAC,CAAC;IAC7E,CAAC;IACD;;;;OAIG;IACI,2BAA2B,CAAC,IAAY;QAC3C,OAAO,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;IAChE,CAAC;CACJ", "sourcesContent": ["import type { AnimationRange } from \"../Animations/animationRange\";\r\nimport { RawTexture } from \"../Materials/Textures/rawTexture\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport { EncodeArrayBufferToBase64, DecodeBase64ToBinary } from \"../Misc/stringTools\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Constants } from \"../Engines/constants\";\r\n\r\n/**\r\n * Class to bake vertex animation textures.\r\n * @since 5.0\r\n */\r\nexport class VertexAnimationBaker {\r\n    private _scene: Scene;\r\n    private _mesh: Mesh;\r\n\r\n    /**\r\n     * Create a new VertexAnimationBaker object which can help baking animations into a texture.\r\n     * @param scene Defines the scene the VAT belongs to\r\n     * @param mesh Defines the mesh the VAT belongs to\r\n     */\r\n    constructor(scene: Scene, mesh: Mesh) {\r\n        this._scene = scene;\r\n        this._mesh = mesh;\r\n    }\r\n\r\n    /**\r\n     * Bakes the animation into the texture. This should be called once, when the\r\n     * scene starts, so the VAT is generated and associated to the mesh.\r\n     * @param ranges Defines the ranges in the animation that will be baked.\r\n     * @returns The array of matrix transforms for each vertex (columns) and frame (rows), as a Float32Array.\r\n     */\r\n    public async bakeVertexData(ranges: AnimationRange[]): Promise<Float32Array> {\r\n        if (!this._mesh.skeleton) {\r\n            throw new Error(\"No skeleton in this mesh.\");\r\n        }\r\n        const boneCount = this._mesh.skeleton.bones.length;\r\n\r\n        /** total number of frames in our animations */\r\n        const frameCount = ranges.reduce((previous: number, current: AnimationRange) => previous + current.to - current.from + 1, 0);\r\n\r\n        if (isNaN(frameCount)) {\r\n            throw new Error(\"Invalid animation ranges.\");\r\n        }\r\n\r\n        // reset our loop data\r\n        let textureIndex = 0;\r\n        const textureSize = (boneCount + 1) * 4 * 4 * frameCount;\r\n        const vertexData = new Float32Array(textureSize);\r\n        this._scene.stopAnimation(this._mesh);\r\n        this._mesh.skeleton.returnToRest();\r\n\r\n        // render all frames from our slices\r\n        for (const range of ranges) {\r\n            for (let frameIndex = range.from; frameIndex <= range.to; frameIndex++) {\r\n                await this._executeAnimationFrame(vertexData, frameIndex, textureIndex++);\r\n            }\r\n        }\r\n\r\n        return vertexData;\r\n    }\r\n\r\n    /**\r\n     * Runs an animation frame and stores its vertex data\r\n     *\r\n     * @param vertexData The array to save data to.\r\n     * @param frameIndex Current frame in the skeleton animation to render.\r\n     * @param textureIndex Current index of the texture data.\r\n     */\r\n    private async _executeAnimationFrame(vertexData: Float32Array, frameIndex: number, textureIndex: number): Promise<void> {\r\n        return new Promise<void>((resolve, _reject) => {\r\n            this._scene.beginAnimation(this._mesh.skeleton, frameIndex, frameIndex, false, 1.0, () => {\r\n                // generate matrices\r\n                const skeletonMatrices = this._mesh.skeleton!.getTransformMatrices(this._mesh);\r\n                vertexData.set(skeletonMatrices, textureIndex * skeletonMatrices.length);\r\n\r\n                resolve();\r\n            });\r\n        });\r\n    }\r\n    /**\r\n     * Builds a vertex animation texture given the vertexData in an array.\r\n     * @param vertexData The vertex animation data. You can generate it with bakeVertexData().\r\n     * @returns The vertex animation texture to be used with BakedVertexAnimationManager.\r\n     */\r\n    public textureFromBakedVertexData(vertexData: Float32Array): RawTexture {\r\n        if (!this._mesh.skeleton) {\r\n            throw new Error(\"No skeleton in this mesh.\");\r\n        }\r\n        const boneCount = this._mesh.skeleton.bones.length;\r\n\r\n        const texture = RawTexture.CreateRGBATexture(\r\n            vertexData,\r\n            (boneCount + 1) * 4,\r\n            vertexData.length / ((boneCount + 1) * 4 * 4),\r\n            this._scene,\r\n            false,\r\n            false,\r\n            Texture.NEAREST_NEAREST,\r\n            Constants.TEXTURETYPE_FLOAT\r\n        );\r\n        texture.name = \"VAT\" + this._mesh.skeleton.name;\r\n        return texture;\r\n    }\r\n    /**\r\n     * Serializes our vertexData to an object, with a nice string for the vertexData.\r\n     * @param vertexData The vertex array data.\r\n     * @returns This object serialized to a JS dict.\r\n     */\r\n    public serializeBakedVertexDataToObject(vertexData: Float32Array): Record<string, any> {\r\n        if (!this._mesh.skeleton) {\r\n            throw new Error(\"No skeleton in this mesh.\");\r\n        }\r\n\r\n        // this converts the float array to a serialized base64 string, ~1.3x larger\r\n        // than the original.\r\n        const boneCount = this._mesh.skeleton.bones.length;\r\n        const width = (boneCount + 1) * 4;\r\n        const height = vertexData.length / ((boneCount + 1) * 4 * 4);\r\n        const data = {\r\n            vertexData: EncodeArrayBufferToBase64(vertexData),\r\n            width,\r\n            height,\r\n        };\r\n        return data;\r\n    }\r\n    /**\r\n     * Loads previously baked data.\r\n     * @param data The object as serialized by serializeBakedVertexDataToObject()\r\n     * @returns The array of matrix transforms for each vertex (columns) and frame (rows), as a Float32Array.\r\n     */\r\n    public loadBakedVertexDataFromObject(data: Record<string, any>): Float32Array {\r\n        return new Float32Array(DecodeBase64ToBinary(data.vertexData));\r\n    }\r\n    /**\r\n     * Serializes our vertexData to a JSON string, with a nice string for the vertexData.\r\n     * Should be called right after bakeVertexData().\r\n     * @param vertexData The vertex array data.\r\n     * @returns This object serialized to a safe string.\r\n     */\r\n    public serializeBakedVertexDataToJSON(vertexData: Float32Array): string {\r\n        return JSON.stringify(this.serializeBakedVertexDataToObject(vertexData));\r\n    }\r\n    /**\r\n     * Loads previously baked data in string format.\r\n     * @param json The json string as serialized by serializeBakedVertexDataToJSON().\r\n     * @returns The array of matrix transforms for each vertex (columns) and frame (rows), as a Float32Array.\r\n     */\r\n    public loadBakedVertexDataFromJSON(json: string): Float32Array {\r\n        return this.loadBakedVertexDataFromObject(JSON.parse(json));\r\n    }\r\n}\r\n"]}