{"version": 3, "file": "handConstraintBehavior.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Behaviors/Meshes/handConstraintBehavior.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AAIjE,OAAO,EAAE,cAAc,EAAE,MAAM,qCAAqC,CAAC;AAKrE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAE1E,OAAO,EAAE,KAAK,EAAE,4BAAwB;AAExC;;GAEG;AACH,MAAM,CAAN,IAAY,kBAiBX;AAjBD,WAAY,kBAAkB;IAC1B;;OAEG;IACH,qFAAiB,CAAA;IACjB;;OAEG;IACH,yEAAW,CAAA;IACX;;OAEG;IACH,uEAAU,CAAA;IACV;;OAEG;IACH,yEAAW,CAAA;AACf,CAAC,EAjBW,kBAAkB,KAAlB,kBAAkB,QAiB7B;AAED;;GAEG;AACH,MAAM,CAAN,IAAY,yBASX;AATD,WAAY,yBAAyB;IACjC;;OAEG;IACH,6FAAc,CAAA;IACd;;OAEG;IACH,2FAAa,CAAA;AACjB,CAAC,EATW,yBAAyB,KAAzB,yBAAyB,QASpC;AAED;;GAEG;AACH,MAAM,CAAN,IAAY,wBAkBX;AAlBD,WAAY,wBAAwB;IAChC;;OAEG;IACH,2FAAc,CAAA;IACd;;OAEG;IACH,6EAAO,CAAA;IACP;;;OAGG;IACH,mFAAU,CAAA;IACV;;OAEG;IACH,yFAAa,CAAA;AACjB,CAAC,EAlBW,wBAAwB,KAAxB,wBAAwB,QAkBnC;AAQD;;;GAGG;AACH,MAAM,OAAO,sBAAsB;IAwD/B;;OAEG;IACH;QAtDQ,yBAAoB,GAA8B,IAAI,CAAC;QACvD,cAAS,GAA8B,EAAE,CAAC;QAElD;;WAEG;QACI,6BAAwB,GAA6B,wBAAwB,CAAC,aAAa,CAAC;QAEnG;;;;WAIG;QACI,qBAAgB,GAAW,IAAI,CAAC;QAEvC;;;WAGG;QACI,wBAAmB,GAAW,IAAI,CAAC;QAE1C;;WAEG;QACI,iBAAY,GAAW,GAAG,CAAC;QAElC;;WAEG;QACI,eAAU,GAAuB,kBAAkB,CAAC,UAAU,CAAC;QAEtE;;WAEG;QACI,wBAAmB,GAA8B,yBAAyB,CAAC,aAAa,CAAC;QAChG;;WAEG;QACI,wBAAmB,GAA8B,yBAAyB,CAAC,aAAa,CAAC;QAEhG;;WAEG;QACI,eAAU,GAAiB,MAAM,CAAC;QAEzC;;;WAGG;QACI,aAAQ,GAAG,GAAG,CAAC;QAMlB,mBAAmB;QACnB,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5E,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,WAAW,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACvE,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,UAAU,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrE,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,WAAW,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED,mCAAmC;IACnC,IAAW,IAAI;QACX,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED,0BAA0B;IACnB,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAED,2BAA2B;IACpB,OAAO;QACV,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAEO,YAAY;QAChB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO,IAAI,CAAC;SACf;QAED,oDAAoD;QACpD,IAAI,IAAI,CAAC;QACT,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM,EAAE;YAC5B,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;SAC5G;aAAM;YACH,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SAClE;QAED,IAAI,IAAI,EAAE;YACN,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC;YAClF,MAAM,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,wBAAwB,CAAC,CAAC;YACpF,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAEtD,IAAI,KAAK,IAAI,gBAAgB,IAAI,eAAe,EAAE;gBAC9C,MAAM,QAAQ,GAAiB,EAAE,QAAQ,EAAE,gBAAgB,CAAC,gBAAgB,EAAE,UAAU,EAAE,IAAI,UAAU,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;gBAE7I,eAAe;gBACf,MAAM,EAAE,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACjC,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACtC,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACnC,EAAE,CAAC,QAAQ,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,SAAS,EAAE,CAAC;gBACnG,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,SAAS,EAAE,CAAC;gBAElH,mFAAmF;gBACnF,OAAO,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;gBACzC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;gBAEtC,UAAU,CAAC,wBAAwB,CAAC,OAAO,EAAE,EAAE,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;gBAEtE,OAAO,QAAQ,CAAC;aACnB;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,IAAI,KAAI,CAAC;IAEhB;;;OAGG;IACI,MAAM,CAAC,IAAmB;QAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE;YAChC,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;SACxI;QAED,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;YACtE,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YAEjC,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,IAAI,EAAE,CAAC;YAClE,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,eAAe,IAAI,EAAE,CAAC;YAClG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,eAAe,CAAC,oBAAoB,GAAG,IAAI,CAAC;YAEzE,IAAI,IAAI,EAAE;gBACN,MAAM,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACzC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;gBAExC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;gBAErD,MAAM,sBAAsB,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBACxD,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,mBAAmB,KAAK,yBAAyB,CAAC,cAAc,IAAI,IAAI,CAAC,mBAAmB,KAAK,yBAAyB,CAAC,cAAc,CAAC,EAAE;oBAC5J,MAAM,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oBACvC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,SAAS,EAAE,CAAC;oBAC9E,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;wBAClC,UAAU,CAAC,wBAAwB,CAAC,QAAQ,EAAE,OAAO,CAAC,UAAU,EAAE,sBAAsB,CAAC,CAAC;qBAC7F;yBAAM;wBACH,UAAU,CAAC,wBAAwB,CAAC,QAAQ,EAAE,OAAO,CAAC,UAAU,EAAE,sBAAsB,CAAC,CAAC;qBAC7F;iBACJ;gBAED,IAAI,IAAI,CAAC,mBAAmB,KAAK,yBAAyB,CAAC,aAAa,EAAE;oBACtE,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC1D;qBAAM;oBACH,sBAAsB,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;iBACjE;gBAED,OAAO,CAAC,oBAAoB,CAAC,UAAU,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;gBAC3E,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAE3C,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC7C,MAAM,cAAc,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBAChD,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;gBAE9D,IAAI,IAAI,CAAC,mBAAmB,KAAK,yBAAyB,CAAC,aAAa,EAAE;oBACtE,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;iBAC5C;qBAAM;oBACH,cAAc,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;iBACnD;gBAED,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC;gBAEtC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,cAAc,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBACtG,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAmB,EAAE,cAAc,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAmB,CAAC,CAAC;gBAE/H,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,eAAe,CAAC,oBAAoB,GAAG,IAAI,CAAC,EAAE,CAAC;aAC/E;YAED,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAE1B,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,cAAc,CAAC,IAA4B;QAC/C,IAAI,WAAW,GAAG,IAAI,CAAC;QACvB,IAAI,WAAW,GAAG,IAAI,CAAC;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QAExC,IAAI,MAAM,EAAE;YACR,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;YAE7C,IAAI,IAAI,CAAC,wBAAwB,KAAK,wBAAwB,CAAC,UAAU,IAAI,IAAI,CAAC,wBAAwB,KAAK,wBAAwB,CAAC,aAAa,EAAE;gBACnJ,WAAW,GAAG,KAAK,CAAC;gBACpB,IAAI,IAAqB,CAAC;gBAC1B,IAAI,IAAI,CAAC,YAAY,EAAE;oBACnB,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAG,CAAC;iBAC1C;gBAED,IAAI,GAAG,IAAI,IAAI,aAAa,CAAC;gBAE7B,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC7C,IAAI,IAAI,EAAE;oBACN,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;iBAC5D;qBAAM;oBACH,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;iBAC/E;gBAED,MAAM,iBAAiB,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;gBACtE,MAAM,gBAAgB,GAAG,iBAAiB,GAAG,iBAAiB,CAAC;gBAE/D,IAAI,iBAAiB,GAAG,CAAC,EAAE;oBACvB,MAAM,aAAa,GAAG,cAAc,CAAC,aAAa,EAAE,GAAG,gBAAgB,CAAC;oBACxE,IAAI,aAAa,GAAG,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,EAAE;wBACrE,WAAW,GAAG,IAAI,CAAC;qBACtB;iBACJ;aACJ;YAED,IAAI,IAAI,CAAC,wBAAwB,KAAK,wBAAwB,CAAC,OAAO,IAAI,IAAI,CAAC,wBAAwB,KAAK,wBAAwB,CAAC,aAAa,EAAE;gBAChJ,WAAW,GAAG,KAAK,CAAC;gBAEpB,IAAI,IAAI,EAAE;oBACN,MAAM,aAAa,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oBAC5C,OAAO,CAAC,yBAAyB,CAAC,uBAAuB,CAAC,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;oBAE1F,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,aAAa,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,gBAAgB,GAAG,CAAC,GAAG,CAAC,EAAE;wBACrF,WAAW,GAAG,IAAI,CAAC;qBACtB;iBACJ;aACJ;SACJ;QAED,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,IAAI,WAAW,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAC3E,CAAC;IAED;;;OAGG;IACI,kBAAkB,CAAC,EAAgD;QACtE,MAAM,eAAe,GAA0B,EAA4B,CAAC,eAAe,CAAC,CAAC,CAAE,EAA4B,CAAC,eAAe,CAAC,CAAC,CAAE,EAA2B,CAAC;QAC3K,IAAI,CAAC,eAAe,EAAE;YAClB,KAAK,CAAC,KAAK,CAAC,sFAAsF,CAAC,CAAC;SACvG;aAAM;YACH,IAAI;gBACA,IAAI,CAAC,YAAY,GAAG,eAAe,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,YAAY,CAAqB,CAAC;aAC5G;YAAC,WAAM,GAAE;YAEV,IAAI;gBACA,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,aAAa,CAAsB,CAAC;aAC/G;YAAC,WAAM;gBACJ,KAAK,CAAC,KAAK,CAAC,yDAAyD,CAAC,CAAC;aAC1E;SACJ;IACL,CAAC;CACJ", "sourcesContent": ["import type { TransformNode } from \"../../Meshes/transformNode\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { WebXRFeatureName } from \"../../XR/webXRFeaturesManager\";\r\nimport type { WebXRFeaturesManager } from \"../../XR/webXRFeaturesManager\";\r\nimport type { WebXREyeTracking } from \"../../XR/features/WebXREyeTracking\";\r\nimport type { WebXRHandTracking } from \"../../XR/features/WebXRHandTracking\";\r\nimport { WebXRHandJoint } from \"../../XR/features/WebXRHandTracking\";\r\nimport type { WebXRExperienceHelper } from \"../../XR/webXRExperienceHelper\";\r\nimport type { Behavior } from \"../behavior\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Quaternion, TmpVectors, Vector3 } from \"../../Maths/math.vector\";\r\nimport type { Ray } from \"../../Culling/ray\";\r\nimport { Tools } from \"core/Misc/tools\";\r\n\r\n/**\r\n * Zones around the hand\r\n */\r\nexport enum HandConstraintZone {\r\n    /**\r\n     * Above finger tips\r\n     */\r\n    ABOVE_FINGER_TIPS,\r\n    /**\r\n     * Next to the thumb\r\n     */\r\n    RADIAL_SIDE,\r\n    /**\r\n     * Next to the pinky finger\r\n     */\r\n    ULNAR_SIDE,\r\n    /**\r\n     * Below the wrist\r\n     */\r\n    BELOW_WRIST,\r\n}\r\n\r\n/**\r\n * Orientations for the hand zones and for the attached node\r\n */\r\nexport enum HandConstraintOrientation {\r\n    /**\r\n     * Orientation is towards the camera\r\n     */\r\n    LOOK_AT_CAMERA,\r\n    /**\r\n     * Orientation is determined by the rotation of the palm\r\n     */\r\n    HAND_ROTATION,\r\n}\r\n\r\n/**\r\n * Orientations for the hand zones and for the attached node\r\n */\r\nexport enum HandConstraintVisibility {\r\n    /**\r\n     * Constraint is always visible\r\n     */\r\n    ALWAYS_VISIBLE,\r\n    /**\r\n     * Constraint is only visible when the palm is up\r\n     */\r\n    PALM_UP,\r\n    /**\r\n     * Constraint is only visible when the user is looking at the constraint.\r\n     * Uses XR Eye Tracking if enabled/available, otherwise uses camera direction\r\n     */\r\n    GAZE_FOCUS,\r\n    /**\r\n     * Constraint is only visible when the palm is up and the user is looking at it\r\n     */\r\n    PALM_AND_GAZE,\r\n}\r\n\r\ntype HandPoseInfo = {\r\n    position: Vector3;\r\n    quaternion: Quaternion;\r\n    id: string;\r\n};\r\n\r\n/**\r\n * Hand constraint behavior that makes the attached `TransformNode` follow hands in XR experiences.\r\n * @since 5.0.0\r\n */\r\nexport class HandConstraintBehavior implements Behavior<TransformNode> {\r\n    private _scene: Scene;\r\n    private _node: TransformNode;\r\n    private _eyeTracking: Nullable<WebXREyeTracking>;\r\n    private _handTracking: Nullable<WebXRHandTracking>;\r\n    private _sceneRenderObserver: Nullable<Observer<Scene>> = null;\r\n    private _zoneAxis: { [id: number]: Vector3 } = {};\r\n\r\n    /**\r\n     * Sets the HandConstraintVisibility level for the hand constraint\r\n     */\r\n    public handConstraintVisibility: HandConstraintVisibility = HandConstraintVisibility.PALM_AND_GAZE;\r\n\r\n    /**\r\n     * A number from 0.0 to 1.0, marking how restricted the direction the palm faces is for the attached node to be enabled.\r\n     * A 1 means the palm must be directly facing the user before the node is enabled, a 0 means it is always enabled.\r\n     * Used with HandConstraintVisibility.PALM_UP\r\n     */\r\n    public palmUpStrictness: number = 0.95;\r\n\r\n    /**\r\n     * The radius in meters around the center of the hand that the user must gaze inside for the attached node to be enabled and appear.\r\n     * Used with HandConstraintVisibility.GAZE_FOCUS\r\n     */\r\n    public gazeProximityRadius: number = 0.15;\r\n\r\n    /**\r\n     * Offset distance from the hand in meters\r\n     */\r\n    public targetOffset: number = 0.1;\r\n\r\n    /**\r\n     * Where to place the node regarding the center of the hand.\r\n     */\r\n    public targetZone: HandConstraintZone = HandConstraintZone.ULNAR_SIDE;\r\n\r\n    /**\r\n     * Orientation mode of the 4 zones around the hand\r\n     */\r\n    public zoneOrientationMode: HandConstraintOrientation = HandConstraintOrientation.HAND_ROTATION;\r\n    /**\r\n     * Orientation mode of the node attached to this behavior\r\n     */\r\n    public nodeOrientationMode: HandConstraintOrientation = HandConstraintOrientation.HAND_ROTATION;\r\n\r\n    /**\r\n     * Set the hand this behavior should follow. If set to \"none\", it will follow any visible hand (prioritising the left one).\r\n     */\r\n    public handedness: XRHandedness = \"none\";\r\n\r\n    /**\r\n     * Rate of interpolation of position and rotation of the attached node.\r\n     * Higher values will give a slower interpolation.\r\n     */\r\n    public lerpTime = 100;\r\n\r\n    /**\r\n     * Builds a hand constraint behavior\r\n     */\r\n    constructor() {\r\n        // For a right hand\r\n        this._zoneAxis[HandConstraintZone.ABOVE_FINGER_TIPS] = new Vector3(0, 1, 0);\r\n        this._zoneAxis[HandConstraintZone.RADIAL_SIDE] = new Vector3(-1, 0, 0);\r\n        this._zoneAxis[HandConstraintZone.ULNAR_SIDE] = new Vector3(1, 0, 0);\r\n        this._zoneAxis[HandConstraintZone.BELOW_WRIST] = new Vector3(0, -1, 0);\r\n    }\r\n\r\n    /** gets or sets behavior's name */\r\n    public get name() {\r\n        return \"HandConstraint\";\r\n    }\r\n\r\n    /** Enable the behavior */\r\n    public enable() {\r\n        this._node.setEnabled(true);\r\n    }\r\n\r\n    /** Disable the behavior */\r\n    public disable() {\r\n        this._node.setEnabled(false);\r\n    }\r\n\r\n    private _getHandPose(): Nullable<HandPoseInfo> {\r\n        if (!this._handTracking) {\r\n            return null;\r\n        }\r\n\r\n        // Retrieve any available hand, starting by the left\r\n        let hand;\r\n        if (this.handedness === \"none\") {\r\n            hand = this._handTracking.getHandByHandedness(\"left\") || this._handTracking.getHandByHandedness(\"right\");\r\n        } else {\r\n            hand = this._handTracking.getHandByHandedness(this.handedness);\r\n        }\r\n\r\n        if (hand) {\r\n            const pinkyMetacarpal = hand.getJointMesh(WebXRHandJoint.PINKY_FINGER_METACARPAL);\r\n            const middleMetacarpal = hand.getJointMesh(WebXRHandJoint.MIDDLE_FINGER_METACARPAL);\r\n            const wrist = hand.getJointMesh(WebXRHandJoint.WRIST);\r\n\r\n            if (wrist && middleMetacarpal && pinkyMetacarpal) {\r\n                const handPose: HandPoseInfo = { position: middleMetacarpal.absolutePosition, quaternion: new Quaternion(), id: hand.xrController.uniqueId };\r\n\r\n                // palm forward\r\n                const up = TmpVectors.Vector3[0];\r\n                const forward = TmpVectors.Vector3[1];\r\n                const left = TmpVectors.Vector3[2];\r\n                up.copyFrom(middleMetacarpal.absolutePosition).subtractInPlace(wrist.absolutePosition).normalize();\r\n                forward.copyFrom(pinkyMetacarpal.absolutePosition).subtractInPlace(middleMetacarpal.absolutePosition).normalize();\r\n\r\n                // Create vectors for a rotation quaternion, where forward points out from the palm\r\n                Vector3.CrossToRef(up, forward, forward);\r\n                Vector3.CrossToRef(forward, up, left);\r\n\r\n                Quaternion.FromLookDirectionLHToRef(forward, up, handPose.quaternion);\r\n\r\n                return handPose;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Initializes the hand constraint behavior\r\n     */\r\n    public init() {}\r\n\r\n    /**\r\n     * Attaches the hand constraint to a `TransformNode`\r\n     * @param node defines the node to attach the behavior to\r\n     */\r\n    public attach(node: TransformNode): void {\r\n        this._node = node;\r\n        this._scene = node.getScene();\r\n\r\n        if (!this._node.rotationQuaternion) {\r\n            this._node.rotationQuaternion = Quaternion.RotationYawPitchRoll(this._node.rotation.y, this._node.rotation.x, this._node.rotation.z);\r\n        }\r\n\r\n        let lastTick = Date.now();\r\n        this._sceneRenderObserver = this._scene.onBeforeRenderObservable.add(() => {\r\n            const pose = this._getHandPose();\r\n\r\n            this._node.reservedDataStore = this._node.reservedDataStore || {};\r\n            this._node.reservedDataStore.nearInteraction = this._node.reservedDataStore.nearInteraction || {};\r\n            this._node.reservedDataStore.nearInteraction.excludedControllerId = null;\r\n\r\n            if (pose) {\r\n                const zoneOffset = TmpVectors.Vector3[0];\r\n                const camera = this._scene.activeCamera;\r\n\r\n                zoneOffset.copyFrom(this._zoneAxis[this.targetZone]);\r\n\r\n                const cameraLookAtQuaternion = TmpVectors.Quaternion[0];\r\n                if (camera && (this.zoneOrientationMode === HandConstraintOrientation.LOOK_AT_CAMERA || this.nodeOrientationMode === HandConstraintOrientation.LOOK_AT_CAMERA)) {\r\n                    const toCamera = TmpVectors.Vector3[1];\r\n                    toCamera.copyFrom(camera.position).subtractInPlace(pose.position).normalize();\r\n                    if (this._scene.useRightHandedSystem) {\r\n                        Quaternion.FromLookDirectionRHToRef(toCamera, Vector3.UpReadOnly, cameraLookAtQuaternion);\r\n                    } else {\r\n                        Quaternion.FromLookDirectionLHToRef(toCamera, Vector3.UpReadOnly, cameraLookAtQuaternion);\r\n                    }\r\n                }\r\n\r\n                if (this.zoneOrientationMode === HandConstraintOrientation.HAND_ROTATION) {\r\n                    pose.quaternion.toRotationMatrix(TmpVectors.Matrix[0]);\r\n                } else {\r\n                    cameraLookAtQuaternion.toRotationMatrix(TmpVectors.Matrix[0]);\r\n                }\r\n\r\n                Vector3.TransformNormalToRef(zoneOffset, TmpVectors.Matrix[0], zoneOffset);\r\n                zoneOffset.scaleInPlace(this.targetOffset);\r\n\r\n                const targetPosition = TmpVectors.Vector3[2];\r\n                const targetRotation = TmpVectors.Quaternion[1];\r\n                targetPosition.copyFrom(pose.position).addInPlace(zoneOffset);\r\n\r\n                if (this.nodeOrientationMode === HandConstraintOrientation.HAND_ROTATION) {\r\n                    targetRotation.copyFrom(pose.quaternion);\r\n                } else {\r\n                    targetRotation.copyFrom(cameraLookAtQuaternion);\r\n                }\r\n\r\n                const elapsed = Date.now() - lastTick;\r\n\r\n                Vector3.SmoothToRef(this._node.position, targetPosition, elapsed, this.lerpTime, this._node.position);\r\n                Quaternion.SmoothToRef(this._node.rotationQuaternion!, targetRotation, elapsed, this.lerpTime, this._node.rotationQuaternion!);\r\n\r\n                this._node.reservedDataStore.nearInteraction.excludedControllerId = pose.id;\r\n            }\r\n\r\n            this._setVisibility(pose);\r\n\r\n            lastTick = Date.now();\r\n        });\r\n    }\r\n\r\n    private _setVisibility(pose: Nullable<HandPoseInfo>) {\r\n        let palmVisible = true;\r\n        let gazeVisible = true;\r\n        const camera = this._scene.activeCamera;\r\n\r\n        if (camera) {\r\n            const cameraForward = camera.getForwardRay();\r\n\r\n            if (this.handConstraintVisibility === HandConstraintVisibility.GAZE_FOCUS || this.handConstraintVisibility === HandConstraintVisibility.PALM_AND_GAZE) {\r\n                gazeVisible = false;\r\n                let gaze: Ray | undefined;\r\n                if (this._eyeTracking) {\r\n                    gaze = this._eyeTracking.getEyeGaze()!;\r\n                }\r\n\r\n                gaze = gaze || cameraForward;\r\n\r\n                const gazeToBehavior = TmpVectors.Vector3[0];\r\n                if (pose) {\r\n                    pose.position.subtractToRef(gaze.origin, gazeToBehavior);\r\n                } else {\r\n                    this._node.getAbsolutePosition().subtractToRef(gaze.origin, gazeToBehavior);\r\n                }\r\n\r\n                const projectedDistance = Vector3.Dot(gazeToBehavior, gaze.direction);\r\n                const projectedSquared = projectedDistance * projectedDistance;\r\n\r\n                if (projectedDistance > 0) {\r\n                    const radiusSquared = gazeToBehavior.lengthSquared() - projectedSquared;\r\n                    if (radiusSquared < this.gazeProximityRadius * this.gazeProximityRadius) {\r\n                        gazeVisible = true;\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (this.handConstraintVisibility === HandConstraintVisibility.PALM_UP || this.handConstraintVisibility === HandConstraintVisibility.PALM_AND_GAZE) {\r\n                palmVisible = false;\r\n\r\n                if (pose) {\r\n                    const palmDirection = TmpVectors.Vector3[0];\r\n                    Vector3.LeftHandedForwardReadOnly.rotateByQuaternionToRef(pose.quaternion, palmDirection);\r\n\r\n                    if (Vector3.Dot(palmDirection, cameraForward.direction) > this.palmUpStrictness * 2 - 1) {\r\n                        palmVisible = true;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        this._node.setEnabled(palmVisible && gazeVisible);\r\n    }\r\n\r\n    /**\r\n     * Detaches the behavior from the `TransformNode`\r\n     */\r\n    public detach(): void {\r\n        this._scene.onBeforeRenderObservable.remove(this._sceneRenderObserver);\r\n    }\r\n\r\n    /**\r\n     * Links the behavior to the XR experience in which to retrieve hand transform information.\r\n     * @param xr xr experience\r\n     */\r\n    public linkToXRExperience(xr: WebXRExperienceHelper | WebXRFeaturesManager) {\r\n        const featuresManager: WebXRFeaturesManager = (xr as WebXRExperienceHelper).featuresManager ? (xr as WebXRExperienceHelper).featuresManager : (xr as WebXRFeaturesManager);\r\n        if (!featuresManager) {\r\n            Tools.Error(\"XR features manager must be available or provided directly for the Hand Menu to work\");\r\n        } else {\r\n            try {\r\n                this._eyeTracking = featuresManager.getEnabledFeature(WebXRFeatureName.EYE_TRACKING) as WebXREyeTracking;\r\n            } catch {}\r\n\r\n            try {\r\n                this._handTracking = featuresManager.getEnabledFeature(WebXRFeatureName.HAND_TRACKING) as WebXRHandTracking;\r\n            } catch {\r\n                Tools.Error(\"Hand tracking must be enabled for the Hand Menu to work\");\r\n            }\r\n        }\r\n    }\r\n}\r\n"]}