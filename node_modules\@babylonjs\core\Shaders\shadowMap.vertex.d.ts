import "./ShadersInclude/bonesDeclaration";
import "./ShadersInclude/bakedVertexAnimationDeclaration";
import "./ShadersInclude/morphTargetsVertexGlobalDeclaration";
import "./ShadersInclude/morphTargetsVertexDeclaration";
import "./ShadersInclude/helperFunctions";
import "./ShadersInclude/shadowMapVertexDeclaration";
import "./ShadersInclude/shadowMapUboDeclaration";
import "./ShadersInclude/shadowMapVertexExtraDeclaration";
import "./ShadersInclude/clipPlaneVertexDeclaration";
import "./ShadersInclude/morphTargetsVertexGlobal";
import "./ShadersInclude/morphTargetsVertex";
import "./ShadersInclude/instancesVertex";
import "./ShadersInclude/bonesVertex";
import "./ShadersInclude/bakedVertexAnimation";
import "./ShadersInclude/shadowMapVertexNormalBias";
import "./ShadersInclude/shadowMapVertexMetric";
import "./ShadersInclude/clipPlaneVertex";
/** @internal */
export declare const shadowMapVertexShader: {
    name: string;
    shader: string;
};
