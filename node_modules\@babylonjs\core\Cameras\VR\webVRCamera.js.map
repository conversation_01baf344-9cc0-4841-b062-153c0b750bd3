{"version": 3, "file": "webVRCamera.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Cameras/VR/webVRCamera.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AAEtD,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAE9C,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AACtE,OAAO,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAC;AACjD,OAAO,EAAE,yBAAyB,EAAE,MAAM,kDAAkD,CAAC;AAG7F,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAGlC,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AACjE,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,kCAAkC,EAAE,MAAM,wDAAwD,CAAC;AAC5G,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AACzC,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAE3D,oDAAoD;AACpD,OAAO,uCAAuC,CAAC;AAE/C,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IACvD,OAAO,GAAG,EAAE,CAAC,IAAI,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;AAClE,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IAC1D,OAAO,GAAG,EAAE,CAAC,IAAI,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;AAClE,CAAC,CAAC,CAAC;AA2HH;;;;;GAKG;AACH,MAAM,OAAO,eAAgB,SAAQ,UAAU;IAoE3C;;;;;;OAMG;IACH,YAAY,IAAY,EAAE,QAAiB,EAAE,KAAa,EAAU,gBAA8B,EAAE;QAChG,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QADmC,kBAAa,GAAb,aAAa,CAAmB;QA1EpG;;;WAGG;QACI,cAAS,GAAQ,IAAI,CAAC;QAC7B;;WAEG;QACI,YAAO,GAAyB,IAAI,CAAC;QAEpC,kBAAa,GAAW,KAAK,CAAC;QAC9B,cAAS,GAAY,KAAK,CAAC;QAIzB,iBAAY,GAAgB,EAAE,CAAC;QAEzC,oHAAoH;QAC5G,wBAAmB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC7C,gBAAgB;QACT,kCAA6B,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;QAErD,oBAAe,GAAqB,IAAI,CAAC;QAEjD;;WAEG;QACI,mBAAc,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACvC;;WAEG;QACI,6BAAwB,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;QAExD;;WAEG;QACI,sBAAiB,GAAW,CAAC,CAAC;QAE7B,mBAAc,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QACnC,mBAAc,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QAE3C;;WAEG;QACI,gBAAW,GAA2B,EAAE,CAAC;QAChD;;WAEG;QACI,oCAA+B,GAAG,IAAI,UAAU,EAA0B,CAAC;QAClF;;WAEG;QACI,qCAAgC,GAAG,IAAI,UAAU,EAAmB,CAAC;QAC5E;;WAEG;QACI,sCAAiC,GAAG,IAAI,UAAU,EAAO,CAAC;QACzD,aAAQ,GAAG,KAAK,CAAC;QACzB;;WAEG;QACI,iBAAY,GAAY,IAAI,CAAC;QAI5B,mBAAc,GAAY,SAAS,CAAC;QAmHlC,gBAAW,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QA2JjD,sBAAiB,GAAG,GAAG,EAAE;YAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,WAAW,EAAE,CAAC;YACjD,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE;gBACtC,IAAI,CAAC,aAAa,EAAE,CAAC;aACxB;QACL,CAAC,CAAC;QAyEM,mBAAc,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAChC,eAAU,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;QAC3B,mBAAc,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QA8FnC,eAAU,GAAG,IAAI,MAAM,EAAE,CAAC;QAjb9B,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACtC,IAAI,aAAa,CAAC,aAAa,EAAE;YAC7B,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC,aAAa,CAAC;YAClD,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;SACzC;QAED,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAEhB,wDAAwD;QACxD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,8CAA8C;YAC9C,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;SACrC;QAED,wBAAwB;QACxB,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,IAAI,SAAS,EAAE;YAC/C,IAAI,CAAC,aAAa,CAAC,aAAa,GAAG,IAAI,CAAC;SAC3C;QACD,IAAI,IAAI,CAAC,aAAa,CAAC,gBAAgB,IAAI,SAAS,EAAE;YAClD,IAAI,CAAC,aAAa,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAC9C;QACD,IAAI,IAAI,CAAC,aAAa,CAAC,4BAA4B,IAAI,SAAS,EAAE;YAC9D,IAAI,CAAC,aAAa,CAAC,4BAA4B,GAAG,IAAI,CAAC;SAC1D;QAED,IAAI,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;QAE3C,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE;YACxD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;SAC7D;QAED,WAAW;QACX,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,IAAI,CAAC,YAAY,GAAG,CAAC,OAAgB,EAAE,EAAE;YACrC,IAAI,OAAO,EAAE;gBACT,IAAI,CAAC,eAAe,EAAE,CAAC;aAC1B;QACL,CAAC,CAAC;QACF,MAAM,CAAC,0BAA0B,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACzD,MAAM,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,CAAC,KAA+B,EAAE,EAAE;YACvD,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,SAAS,EAAE;gBACxD,OAAO;aACV;YAED,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;YAEjC,2BAA2B;YAC3B,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;YAEvJ,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,IAAI,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aACjD;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;YACpC,IAAI,CAAC,UAAU,GAAG,IAAI,WAAW,EAAE,CAAC;SACvC;QAED,IAAI,aAAa,CAAC,YAAY,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,SAAS,EAAE;gBAClD,MAAM,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;gBAC9E,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC;aAC1C;iBAAM;gBACH,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;gBACtC,IAAI,CAAC,eAAe,GAAG,IAAI,kCAAkC,CAAC,yBAAyB,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;aACvG;SACJ;QAED;;;;;;;;;;WAUG;QACH,IAAI,CAAC,QAAQ,EAAE,CAAC,8BAA8B,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YAC1D,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE;gBAC7C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE;oBAChD,6CAA6C;oBAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE;wBACtD,OAAO,UAAU,CAAC,KAAK,KAAK,CAAC,CAAC;oBAClC,CAAC,CAAC,CAAC;oBACH,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAS,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;oBAC/D,OAAO,CAAC,YAAY,IAAI,CAAC,WAAW,CAAC;gBACzC,CAAC,CAAC,CAAC;gBACH,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;oBAC/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;gBACzB,CAAC,CAAC,CAAC;aACN;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC,6BAA6B,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YACzD,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE;gBAC7C,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;oBAC/B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;gBACvB,CAAC,CAAC,CAAC;aACN;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAID;;;OAGG;IACI,0BAA0B;QAC7B,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,oEAAoE;YACpE,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;SAC7D;QACD,iGAAiG;QACjG,OAAO,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG;IACH,6DAA6D;IACtD,iBAAiB,CAAC,WAAW,CAAC,IAAa,EAAE,EAAE,GAAE,CAAC;QACrD,mCAAmC;QACnC,IAAI,CAAC,SAAS,EAAE;aACX,cAAc,EAAE;aAChB,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,eAAe,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,0BAA0B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE;gBAC7J,QAAQ,CAAC,KAAK,CAAC,CAAC;aACnB;iBAAM;gBACH,IAAI,CAAC,eAAe,GAAG,IAAI,MAAM,EAAE,CAAC;gBACpC,MAAM,CAAC,2BAA2B,CAAC,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,0BAA0B,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBAC5H,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,EAAE;oBACvC,IAAI,IAAI,CAAC,eAAe,EAAE;wBACtB,IAAI,CAAC,eAAe,CAAC,4BAA4B,EAAE,CAAC;qBACvD;iBACJ;gBACD,QAAQ,CAAC,IAAI,CAAC,CAAC;aAClB;QACL,CAAC,CAAC,CAAC;IACX,CAAC;IAED;;;OAGG;IACI,sBAAsB;QACzB,OAAO,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YACvB,IAAI,CAAC,iBAAiB,CAAC,CAAC,SAAS,EAAE,EAAE;gBACjC,GAAG,CAAC,SAAS,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,SAAS,EAAE,CAAC,0BAA0B,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC9E,IAAI,IAAI,CAAC,wCAAwC,EAAE;YAC/C,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;SAC9F;QACD,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,IAAY;QACnC,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,WAAW,EAAE;YAC/B,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,EAAE;gBAClB,OAAO,EAAE,CAAC;aACb;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAGD;;OAEG;IACH,IAAW,cAAc;QACrB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;SAC3D;QAED,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAGD;;OAEG;IACH,IAAW,eAAe;QACtB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;SAC7D;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,MAAM,GAAG,GAAG;QAC7B,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,oEAAoE;YACpE,OAAO,KAAK,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,kCAAkC;SAC3I;aAAM;YACH,OAAO,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;SACtC;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE;YAC/C,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAE7C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SAC/C;QAED,KAAK,CAAC,YAAY,EAAE,CAAC;IACzB,CAAC;IAED;;;OAGG;IACH,gBAAgB,CAAC,QAAoB;QACjC,IAAI,QAAQ,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;YACvE,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC;YACxB,IAAI,CAAC,6BAA6B,CAAC,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YAExJ,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,EAAE;gBACtC,IAAI,CAAC,6BAA6B,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC3C,IAAI,CAAC,6BAA6B,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;aAC9C;YACD,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;gBAC3D,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvH,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,EAAE;oBACtC,IAAI,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;iBACpC;aACJ;YACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACxB;IACL,CAAC;IASD;;;;;;;OAOG;IACI,aAAa,CAAC,gBAA0B;QAC3C,8CAA8C;QAC9C,gBAAgB,GAAG,KAAK,CAAC,gCAAgC,CAAC,SAAS,CAAC,CAAC;QACrE,KAAK,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;QACtC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,gBAAgB,GAAG,MAAM,CAAC,wCAAwC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,gBAAgB,CAAC;QAE9F,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACjD;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,aAAa,EAAE,CAAC;QAE3D,IAAI,UAAU,EAAE;YACZ,UAAU,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;SACjF;IACL,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,IAAI,CAAC,QAAQ,EAAE,CAAC,cAAc,CAAC,4BAA4B,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACrG,IAAI,CAAC,QAAQ,EAAE,CAAC,cAAc,CAAC,+BAA+B,CAAC,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAE3G,KAAK,CAAC,aAAa,EAAE,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,CAAC;QAC7B,MAAM,CAAC,mBAAmB,CAAC,wBAAwB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;IACjF,CAAC;IAED;;OAEG;IACI,YAAY;QACf,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,sBAAsB;QACzB,qCAAqC;QACrC,mCAAmC;QACnC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACpB,MAAM,OAAO,GAAiB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAClD,MAAM,QAAQ,GAAiB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACnD,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QACxE,QAAQ,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QAEzE,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACpD,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IACzD,CAAC;IAQD,wEAAwE;IAChE,kCAAkC,CAAC,MAAc,EAAE,YAAY,GAAG,KAAK;QAC3E,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE;YAC5E,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACxH,IAAI,CAAC,YAAY,EAAE;gBACf,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;aAC5B;YACD,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;SACjD;IACL,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,iBAA2B;QAC3C,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;YAChH,qFAAqF;YACrF,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBAC1B,yFAAyF;gBACzF,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBAC/B,IAAI,CAAC,MAAM,EAAE,CAAC;aACjB;YAED,sFAAsF;YACtF,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC9D,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YAEtG,oHAAoH;YACpH,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YAC5E,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YAExG,uCAAuC;YACvC,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC7D,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC9C,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC1D,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAExD,6DAA6D;YAC7D,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAErD,+EAA+E;YAC/E,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBACpC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBACxD,IAAI,CAAC,kCAAkC,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;gBACnE,UAAU,CAAC,MAAM,EAAE,CAAC;YACxB,CAAC,CAAC,CAAC;SACN;QAED,IAAI,CAAC,iBAAiB,EAAE;YACpB,KAAK,CAAC,YAAY,EAAE,CAAC;SACxB;QACD,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,sBAAsB;QACzB,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IAC1G,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,+CAA+C;QAC/C,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,6BAA6B,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACpF,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAC5E,UAAU,CAAC,uBAAuB,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAEvF,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,iCAAiC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SAChE;QACD,KAAK,CAAC,MAAM,EAAE,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACI,cAAc;QACjB,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC;IAC7B,CAAC;IAGD;;;;OAIG;IACI,mBAAmB;QACtB,oFAAoF;QACpF,MAAM,YAAY,GAAoB,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;QAC5E,YAAY,CAAC,YAAY,EAAE,CAAC;QAE5B,WAAW;QACX,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,eAAe,CAAC;QAEzJ,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAE3D,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,EAAE;YACvC,IAAI,CAAC,gBAAgB,CAAC,4BAA4B,EAAE,CAAC;SACxD;QAED,oCAAoC;QACpC,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACzE,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAErH,oCAAoC;QACpC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,0BAA0B,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAE7E,oEAAoE;QACpE,IAAI,YAAY,CAAC,iBAAiB,KAAK,CAAC,EAAE;YACtC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;YAC/B,6BAA6B;YAC7B,IAAI,YAAY,CAAC,iBAAiB,EAAE;gBAChC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,EAAE,EAAE,YAAY,CAAC,iBAAiB,CAAC,CAAC;gBAC1E,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,EAAE,EAAE,YAAY,CAAC,iBAAiB,CAAC,CAAC;gBAC1E,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,EAAE,EAAE,YAAY,CAAC,iBAAiB,CAAC,CAAC;aAC7E;YAED,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;SAClC;QAED,wEAAwE;QACxE,YAAY,CAAC,kCAAkC,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;QAE7E,YAAY,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAExF,0BAA0B;QAC1B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;QAC/D,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACvD,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,YAAY,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACtF,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC9D,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED,gBAAgB;IACT,yBAAyB;QAC5B,MAAM,YAAY,GAAoB,IAAI,CAAC,MAAM,CAAC;QAElD,YAAY,CAAC,SAAS,CAAC,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC;QACrD,YAAY,CAAC,SAAS,CAAC,QAAQ,GAAG,YAAY,CAAC,IAAI,CAAC;QAEpD,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,qBAAqB,CAAC;QAC3K,MAAM,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAElE,2BAA2B;QAC3B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,EAAE;YACvC,IAAI,CAAC,iBAAiB,CAAC,iCAAiC,EAAE,CAAC;SAC9D;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAKD;;OAEG;IACI,eAAe;QAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;QAE5B,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,cAAc,CAAC;QAC/C,IAAI,CAAC,8BAA8B,GAAG,OAAO,CAAC,+BAA+B,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YAC1F,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,YAAY,EAAE;gBACvC,MAAM,eAAe,GAAqC,OAAO,CAAC;gBAElE,IAAI,eAAe,CAAC,YAAY,EAAE;oBAC9B,eAAe,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;iBAClD;gBAED,IAAI,eAAe,CAAC,IAAI,KAAK,OAAO,EAAE;oBAClC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;iBAChC;gBACD,IAAI,eAAe,CAAC,IAAI,KAAK,MAAM,EAAE;oBACjC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;iBAC/B;gBACD,MAAM,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;gBAClE,IAAI,eAAe,KAAK,CAAC,CAAC,EAAE;oBACxB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;iBAC/C;aACJ;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,2BAA2B,GAAG,OAAO,CAAC,4BAA4B,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YACpF,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,YAAY,EAAE;gBACvC,MAAM,eAAe,GAAqC,OAAO,CAAC;gBAClE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE;oBACnC,eAAe,CAAC,qBAAqB,CAAC,IAAI,OAAO,CAAC,eAAe,CAAC,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;oBAC9G,0FAA0F;oBAC1F,IAAI,CAAC,IAAI,CAAC,wCAAwC,EAAE;wBAChD,IAAI,CAAC,wCAAwC,GAAG,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;4BAC1F,IAAI,CAAC,YAAY,EAAE,CAAC;wBACxB,CAAC,CAAC,CAAC;qBACN;iBACJ;gBACD,eAAe,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;gBAC3D,eAAe,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC7D,IAAI,CAAC,kCAAkC,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;gBAExE,IAAI,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE;oBACrC,IAAI,eAAe,CAAC,YAAY,EAAE;wBAC9B,eAAe,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;qBACjD;yBAAM;wBACH,kBAAkB;wBAClB,eAAe,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,UAAU,EAAE,EAAE;4BAC/D,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;4BACxD,IAAI,CAAC,gCAAgC,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;4BACvE,IAAI,IAAI,CAAC,aAAa,CAAC,4BAA4B,EAAE;gCACjD,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;oCAC3B,IAAI,CAAC,mBAAmB,GAAG,IAAI,gBAAgB,CAAC,oBAAoB,EAAE,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;iCAChH;gCACD,MAAM,wBAAwB,GAAG,UAAU,IAAkB,EAAE,KAAuB;oCAClF,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;oCACpC,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;wCACnC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;4CACtB,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAe,IAAI,CAAC,CAAC;4CAClD,wBAAwB,CAAe,IAAI,EAAE,KAAK,CAAC,CAAC;wCACxD,CAAC,CAAC,CAAC;qCACN;gCACL,CAAC,CAAC;gCACF,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gCAC7D,wBAAwB,CAAC,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;6BAClE;wBACL,CAAC,CAAC,CAAC;qBACN;iBACJ;gBACD,eAAe,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;gBAEnD,wEAAwE;gBACxE,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE;oBAClD,8BAA8B;oBAC9B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;oBAEvC,kGAAkG;oBAClG,gHAAgH;oBAChH,+DAA+D;oBAC/D,IAAI,qBAAqB,GAAG,KAAK,CAAC;oBAElC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBAC9C,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,KAAK,yBAAyB,CAAC,IAAI,EAAE;4BACvE,IAAI,CAAC,qBAAqB,EAAE;gCACxB,qBAAqB,GAAG,IAAI,CAAC;gCAC7B,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC;6BACrC;iCAAM;gCACH,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC;6BACtC;yBACJ;qBACJ;oBAED,gEAAgE;oBAChE,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,EAAE;wBAC9B,IAAI,CAAC,+BAA+B,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;qBAC1E;iBACJ;aACJ;QACL,CAAC,CAAC,CAAC;IACP,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport type { <PERSON> } from \"../../Misc/observable\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { FreeCamera } from \"../../Cameras/freeCamera\";\r\nimport type { TargetCamera } from \"../../Cameras/targetCamera\";\r\nimport { Camera } from \"../../Cameras/camera\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Quaternion, Matrix, Vector3 } from \"../../Maths/math.vector\";\r\nimport { Gamepad } from \"../../Gamepads/gamepad\";\r\nimport { PoseEnabledControllerType } from \"../../Gamepads/Controllers/poseEnabledController\";\r\nimport type { WebVRController } from \"../../Gamepads/Controllers/webVRController\";\r\nimport type { IDisplayChangedEventArgs } from \"../../Engines/engine\";\r\nimport { Node } from \"../../node\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { <PERSON> } from \"../../Culling/ray\";\r\nimport { HemisphericLight } from \"../../Lights/hemisphericLight\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport { VRMultiviewToSingleviewPostProcess } from \"../../PostProcesses/vrMultiviewToSingleviewPostProcess\";\r\nimport { Tools } from \"../../Misc/tools\";\r\nimport { setWebVRRigMode } from \"../RigModes/webVRRigMode\";\r\n\r\n// Side effect import to add webvr support to engine\r\nimport \"../../Engines/Extensions/engine.webVR\";\r\n\r\nNode.AddNodeConstructor(\"WebVRFreeCamera\", (name, scene) => {\r\n    return () => new WebVRFreeCamera(name, Vector3.Zero(), scene);\r\n});\r\n\r\nNode.AddNodeConstructor(\"WebVRGamepadCamera\", (name, scene) => {\r\n    return () => new WebVRFreeCamera(name, Vector3.Zero(), scene);\r\n});\r\n\r\n/**\r\n * This is a copy of VRPose. See https://developer.mozilla.org/en-US/docs/Web/API/VRPose\r\n * IMPORTANT!! The data is right-hand data.\r\n * @export\r\n * @interface DevicePose\r\n */\r\nexport interface DevicePose {\r\n    /**\r\n     * The position of the device, values in array are [x,y,z].\r\n     */\r\n    readonly position: Nullable<Float32Array>;\r\n    /**\r\n     * The linearVelocity of the device, values in array are [x,y,z].\r\n     */\r\n    readonly linearVelocity: Nullable<Float32Array>;\r\n    /**\r\n     * The linearAcceleration of the device, values in array are [x,y,z].\r\n     */\r\n    readonly linearAcceleration: Nullable<Float32Array>;\r\n\r\n    /**\r\n     * The orientation of the device in a quaternion array, values in array are [x,y,z,w].\r\n     */\r\n    readonly orientation: Nullable<Float32Array>;\r\n    /**\r\n     * The angularVelocity of the device, values in array are [x,y,z].\r\n     */\r\n    readonly angularVelocity: Nullable<Float32Array>;\r\n    /**\r\n     * The angularAcceleration of the device, values in array are [x,y,z].\r\n     */\r\n    readonly angularAcceleration: Nullable<Float32Array>;\r\n}\r\n\r\n/**\r\n * Interface representing a pose controlled object in Babylon.\r\n * A pose controlled object has both regular pose values as well as pose values\r\n * from an external device such as a VR head mounted display\r\n */\r\nexport interface PoseControlled {\r\n    /**\r\n     * The position of the object in babylon space.\r\n     */\r\n    position: Vector3;\r\n    /**\r\n     * The rotation quaternion of the object in babylon space.\r\n     */\r\n    rotationQuaternion: Quaternion;\r\n    /**\r\n     * The position of the device in babylon space.\r\n     */\r\n    devicePosition?: Vector3;\r\n    /**\r\n     * The rotation quaternion of the device in babylon space.\r\n     */\r\n    deviceRotationQuaternion: Quaternion;\r\n    /**\r\n     * The raw pose coming from the device.\r\n     */\r\n    rawPose: Nullable<DevicePose>;\r\n    /**\r\n     * The scale of the device to be used when translating from device space to babylon space.\r\n     */\r\n    deviceScaleFactor: number;\r\n    /**\r\n     * Updates the poseControlled values based on the input device pose.\r\n     * @param poseData the pose data to update the object with\r\n     */\r\n    updateFromDevice(poseData: DevicePose): void;\r\n}\r\n\r\n/**\r\n * Set of options to customize the webVRCamera\r\n */\r\nexport interface WebVROptions {\r\n    /**\r\n     * Sets if the webVR camera should be tracked to the vrDevice. (default: true)\r\n     */\r\n    trackPosition?: boolean;\r\n    /**\r\n     * Sets the scale of the vrDevice in babylon space. (default: 1)\r\n     */\r\n    positionScale?: number;\r\n    /**\r\n     * If there are more than one VRDisplays, this will choose the display matching this name. (default: pick first vrDisplay)\r\n     */\r\n    displayName?: string;\r\n    /**\r\n     * Should the native controller meshes be initialized. (default: true)\r\n     */\r\n    controllerMeshes?: boolean;\r\n    /**\r\n     * Creating a default HemiLight only on controllers. (default: true)\r\n     */\r\n    defaultLightingOnControllers?: boolean;\r\n    /**\r\n     * If you don't want to use the default VR button of the helper. (default: false)\r\n     */\r\n    useCustomVRButton?: boolean;\r\n\r\n    /**\r\n     * If you'd like to provide your own button to the VRHelper. (default: standard babylon vr button)\r\n     */\r\n    customVRButton?: HTMLButtonElement;\r\n\r\n    /**\r\n     * To change the length of the ray for gaze/controllers. Will be scaled by positionScale. (default: 100)\r\n     */\r\n    rayLength?: number;\r\n\r\n    /**\r\n     * To change the default offset from the ground to account for user's height in meters. Will be scaled by positionScale. (default: 1.7)\r\n     */\r\n    defaultHeight?: number;\r\n\r\n    /**\r\n     * If multiview should be used if available (default: false)\r\n     */\r\n    useMultiview?: boolean;\r\n}\r\n\r\n/**\r\n * This represents a WebVR camera.\r\n * The WebVR camera is Babylon's simple interface to interaction with Windows Mixed Reality, HTC Vive and Oculus Rift.\r\n * @deprecated Use WebXR instead - https://doc.babylonjs.com/features/featuresDeepDive/webXR\r\n * @example https://doc.babylonjs.com/features/featuresDeepDive/cameras/webVRCamera\r\n */\r\nexport class WebVRFreeCamera extends FreeCamera implements PoseControlled {\r\n    /**\r\n     * @internal\r\n     * The vrDisplay tied to the camera. See https://developer.mozilla.org/en-US/docs/Web/API/VRDisplay\r\n     */\r\n    public _vrDevice: any = null;\r\n    /**\r\n     * The rawPose of the vrDevice.\r\n     */\r\n    public rawPose: Nullable<DevicePose> = null;\r\n    private _onVREnabled: (success: boolean) => void;\r\n    private _specsVersion: string = \"1.1\";\r\n    private _attached: boolean = false;\r\n\r\n    private _frameData: any;\r\n\r\n    protected _descendants: Array<Node> = [];\r\n\r\n    // Represents device position and rotation in room space. Should only be used to help calculate babylon space values\r\n    private _deviceRoomPosition = Vector3.Zero();\r\n    /** @internal */\r\n    public _deviceRoomRotationQuaternion = Quaternion.Identity();\r\n\r\n    private _standingMatrix: Nullable<Matrix> = null;\r\n\r\n    /**\r\n     * Represents device position in babylon space.\r\n     */\r\n    public devicePosition = Vector3.Zero();\r\n    /**\r\n     * Represents device rotation in babylon space.\r\n     */\r\n    public deviceRotationQuaternion = Quaternion.Identity();\r\n\r\n    /**\r\n     * The scale of the device to be used when translating from device space to babylon space.\r\n     */\r\n    public deviceScaleFactor: number = 1;\r\n\r\n    private _deviceToWorld = Matrix.Identity();\r\n    private _worldToDevice = Matrix.Identity();\r\n\r\n    /**\r\n     * References to the webVR controllers for the vrDevice.\r\n     */\r\n    public controllers: Array<WebVRController> = [];\r\n    /**\r\n     * Emits an event when a controller is attached.\r\n     */\r\n    public onControllersAttachedObservable = new Observable<Array<WebVRController>>();\r\n    /**\r\n     * Emits an event when a controller's mesh has been loaded;\r\n     */\r\n    public onControllerMeshLoadedObservable = new Observable<WebVRController>();\r\n    /**\r\n     * Emits an event when the HMD's pose has been updated.\r\n     */\r\n    public onPoseUpdatedFromDeviceObservable = new Observable<any>();\r\n    private _poseSet = false;\r\n    /**\r\n     * If the rig cameras be used as parent instead of this camera.\r\n     */\r\n    public rigParenting: boolean = true;\r\n\r\n    private _lightOnControllers: HemisphericLight;\r\n\r\n    private _defaultHeight?: number = undefined;\r\n\r\n    /**\r\n     * Instantiates a WebVRFreeCamera.\r\n     * @param name The name of the WebVRFreeCamera\r\n     * @param position The starting anchor position for the camera\r\n     * @param scene The scene the camera belongs to\r\n     * @param _webVROptions a set of customizable options for the webVRCamera\r\n     */\r\n    constructor(name: string, position: Vector3, scene?: Scene, private _webVROptions: WebVROptions = {}) {\r\n        super(name, position, scene);\r\n        this._cache.position = Vector3.Zero();\r\n        if (_webVROptions.defaultHeight) {\r\n            this._defaultHeight = _webVROptions.defaultHeight;\r\n            this.position.y = this._defaultHeight;\r\n        }\r\n\r\n        this.minZ = 0.1;\r\n\r\n        //legacy support - the compensation boolean was removed.\r\n        if (arguments.length === 5) {\r\n            // eslint-disable-next-line prefer-rest-params\r\n            this._webVROptions = arguments[4];\r\n        }\r\n\r\n        // default webVR options\r\n        if (this._webVROptions.trackPosition == undefined) {\r\n            this._webVROptions.trackPosition = true;\r\n        }\r\n        if (this._webVROptions.controllerMeshes == undefined) {\r\n            this._webVROptions.controllerMeshes = true;\r\n        }\r\n        if (this._webVROptions.defaultLightingOnControllers == undefined) {\r\n            this._webVROptions.defaultLightingOnControllers = true;\r\n        }\r\n\r\n        this.rotationQuaternion = new Quaternion();\r\n\r\n        if (this._webVROptions && this._webVROptions.positionScale) {\r\n            this.deviceScaleFactor = this._webVROptions.positionScale;\r\n        }\r\n\r\n        //enable VR\r\n        const engine = this.getEngine();\r\n        this._onVREnabled = (success: boolean) => {\r\n            if (success) {\r\n                this.initControllers();\r\n            }\r\n        };\r\n        engine.onVRRequestPresentComplete.add(this._onVREnabled);\r\n        engine.initWebVR().add((event: IDisplayChangedEventArgs) => {\r\n            if (!event.vrDisplay || this._vrDevice === event.vrDisplay) {\r\n                return;\r\n            }\r\n\r\n            this._vrDevice = event.vrDisplay;\r\n\r\n            //reset the rig parameters.\r\n            this.setCameraRigMode(Camera.RIG_MODE_WEBVR, { parentCamera: this, vrDisplay: this._vrDevice, frameData: this._frameData, specs: this._specsVersion });\r\n\r\n            if (this._attached) {\r\n                this.getEngine().enableVR(this._webVROptions);\r\n            }\r\n        });\r\n\r\n        if (typeof VRFrameData !== \"undefined\") {\r\n            this._frameData = new VRFrameData();\r\n        }\r\n\r\n        if (_webVROptions.useMultiview) {\r\n            if (!this.getScene().getEngine().getCaps().multiview) {\r\n                Logger.Warn(\"Multiview is not supported, falling back to standard rendering\");\r\n                this._useMultiviewToSingleView = false;\r\n            } else {\r\n                this._useMultiviewToSingleView = true;\r\n                this._rigPostProcess = new VRMultiviewToSingleviewPostProcess(\"VRMultiviewToSingleview\", this, 1.0);\r\n            }\r\n        }\r\n\r\n        /**\r\n         * The idea behind the following lines:\r\n         * objects that have the camera as parent should actually have the rig cameras as a parent.\r\n         * BUT, each of those cameras has a different view matrix, which means that if we set the parent to the first rig camera,\r\n         * the second will not show it correctly.\r\n         *\r\n         * To solve this - each object that has the camera as parent will be added to a protected array.\r\n         * When the rig camera renders, it will take this array and set all of those to be its children.\r\n         * This way, the right camera will be used as a parent, and the mesh will be rendered correctly.\r\n         * Amazing!\r\n         */\r\n        this.getScene().onBeforeCameraRenderObservable.add((camera) => {\r\n            if (camera.parent === this && this.rigParenting) {\r\n                this._descendants = this.getDescendants(true, (n) => {\r\n                    // don't take the cameras or the controllers!\r\n                    const isController = this.controllers.some((controller) => {\r\n                        return controller._mesh === n;\r\n                    });\r\n                    const isRigCamera = this._rigCameras.indexOf(<Camera>n) !== -1;\r\n                    return !isController && !isRigCamera;\r\n                });\r\n                this._descendants.forEach((node) => {\r\n                    node.parent = camera;\r\n                });\r\n            }\r\n        });\r\n\r\n        this.getScene().onAfterCameraRenderObservable.add((camera) => {\r\n            if (camera.parent === this && this.rigParenting) {\r\n                this._descendants.forEach((node) => {\r\n                    node.parent = this;\r\n                });\r\n            }\r\n        });\r\n    }\r\n\r\n    protected _setRigMode = setWebVRRigMode.bind(null, this);\r\n\r\n    /**\r\n     * Gets the device distance from the ground in meters.\r\n     * @returns the distance in meters from the vrDevice to ground in device space. If standing matrix is not supported for the vrDevice 0 is returned.\r\n     */\r\n    public deviceDistanceToRoomGround(): number {\r\n        if (this._standingMatrix) {\r\n            // Add standing matrix offset to get real offset from ground in room\r\n            this._standingMatrix.getTranslationToRef(this._workingVector);\r\n            return this._deviceRoomPosition.y + this._workingVector.y;\r\n        }\r\n        //If VRDisplay does not inform stage parameters and no default height is set we fallback to zero.\r\n        return this._defaultHeight || 0;\r\n    }\r\n\r\n    /**\r\n     * Enables the standing matrix when supported. This can be used to position the user's view the correct height from the ground.\r\n     * @param callback will be called when the standing matrix is set. Callback parameter is if the standing matrix is supported.\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public useStandingMatrix(callback = (bool: boolean) => {}) {\r\n        // Use standing matrix if available\r\n        this.getEngine()\r\n            .initWebVRAsync()\r\n            .then((result) => {\r\n                if (!result.vrDisplay || !result.vrDisplay.stageParameters || !result.vrDisplay.stageParameters.sittingToStandingTransform || !this._webVROptions.trackPosition) {\r\n                    callback(false);\r\n                } else {\r\n                    this._standingMatrix = new Matrix();\r\n                    Matrix.FromFloat32ArrayToRefScaled(result.vrDisplay.stageParameters.sittingToStandingTransform, 0, 1, this._standingMatrix);\r\n                    if (!this.getScene().useRightHandedSystem) {\r\n                        if (this._standingMatrix) {\r\n                            this._standingMatrix.toggleModelMatrixHandInPlace();\r\n                        }\r\n                    }\r\n                    callback(true);\r\n                }\r\n            });\r\n    }\r\n\r\n    /**\r\n     * Enables the standing matrix when supported. This can be used to position the user's view the correct height from the ground.\r\n     * @returns A promise with a boolean set to if the standing matrix is supported.\r\n     */\r\n    public useStandingMatrixAsync(): Promise<boolean> {\r\n        return new Promise((res) => {\r\n            this.useStandingMatrix((supported) => {\r\n                res(supported);\r\n            });\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Disposes the camera\r\n     */\r\n    public dispose(): void {\r\n        this._detachIfAttached();\r\n        this.getEngine().onVRRequestPresentComplete.removeCallback(this._onVREnabled);\r\n        if (this._updateCacheWhenTrackingDisabledObserver) {\r\n            this._scene.onBeforeRenderObservable.remove(this._updateCacheWhenTrackingDisabledObserver);\r\n        }\r\n        super.dispose();\r\n    }\r\n\r\n    /**\r\n     * Gets a vrController by name.\r\n     * @param name The name of the controller to retrieve\r\n     * @returns the controller matching the name specified or null if not found\r\n     */\r\n    public getControllerByName(name: string): Nullable<WebVRController> {\r\n        for (const gp of this.controllers) {\r\n            if (gp.hand === name) {\r\n                return gp;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    private _leftController: Nullable<WebVRController>;\r\n    /**\r\n     * The controller corresponding to the users left hand.\r\n     */\r\n    public get leftController(): Nullable<WebVRController> {\r\n        if (!this._leftController) {\r\n            this._leftController = this.getControllerByName(\"left\");\r\n        }\r\n\r\n        return this._leftController;\r\n    }\r\n\r\n    private _rightController: Nullable<WebVRController>;\r\n    /**\r\n     * The controller corresponding to the users right hand.\r\n     */\r\n    public get rightController(): Nullable<WebVRController> {\r\n        if (!this._rightController) {\r\n            this._rightController = this.getControllerByName(\"right\");\r\n        }\r\n\r\n        return this._rightController;\r\n    }\r\n\r\n    /**\r\n     * Casts a ray forward from the vrCamera's gaze.\r\n     * @param length Length of the ray (default: 100)\r\n     * @returns the ray corresponding to the gaze\r\n     */\r\n    public getForwardRay(length = 100): Ray {\r\n        if (this.leftCamera) {\r\n            // Use left eye to avoid computation to compute center on every call\r\n            return super.getForwardRay(length, this.leftCamera.getWorldMatrix(), this.leftCamera.globalPosition); // Need the actual rendered camera\r\n        } else {\r\n            return super.getForwardRay(length);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Updates the camera based on device's frame data\r\n     */\r\n    public _checkInputs(): void {\r\n        if (this._vrDevice && this._vrDevice.isPresenting) {\r\n            this._vrDevice.getFrameData(this._frameData);\r\n\r\n            this.updateFromDevice(this._frameData.pose);\r\n        }\r\n\r\n        super._checkInputs();\r\n    }\r\n\r\n    /**\r\n     * Updates the poseControlled values based on the input device pose.\r\n     * @param poseData Pose coming from the device\r\n     */\r\n    updateFromDevice(poseData: DevicePose) {\r\n        if (poseData && poseData.orientation && poseData.orientation.length === 4) {\r\n            this.rawPose = poseData;\r\n            this._deviceRoomRotationQuaternion.copyFromFloats(poseData.orientation[0], poseData.orientation[1], -poseData.orientation[2], -poseData.orientation[3]);\r\n\r\n            if (this.getScene().useRightHandedSystem) {\r\n                this._deviceRoomRotationQuaternion.z *= -1;\r\n                this._deviceRoomRotationQuaternion.w *= -1;\r\n            }\r\n            if (this._webVROptions.trackPosition && this.rawPose.position) {\r\n                this._deviceRoomPosition.copyFromFloats(this.rawPose.position[0], this.rawPose.position[1], -this.rawPose.position[2]);\r\n                if (this.getScene().useRightHandedSystem) {\r\n                    this._deviceRoomPosition.z *= -1;\r\n                }\r\n            }\r\n            this._poseSet = true;\r\n        }\r\n    }\r\n\r\n    private _detachIfAttached = () => {\r\n        const vrDisplay = this.getEngine().getVRDevice();\r\n        if (vrDisplay && !vrDisplay.isPresenting) {\r\n            this.detachControl();\r\n        }\r\n    };\r\n\r\n    /**\r\n     * WebVR's attach control will start broadcasting frames to the device.\r\n     * Note that in certain browsers (chrome for example) this function must be called\r\n     * within a user-interaction callback. Example:\r\n     * <pre> scene.onPointerDown = function() { camera.attachControl(canvas); }</pre>\r\n     *\r\n     * @param noPreventDefault prevent the default html element operation when attaching the vrDevice\r\n     */\r\n    public attachControl(noPreventDefault?: boolean): void {\r\n        // eslint-disable-next-line prefer-rest-params\r\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(arguments);\r\n        super.attachControl(noPreventDefault);\r\n        this._attached = true;\r\n\r\n        noPreventDefault = Camera.ForceAttachControlToAlwaysPreventDefault ? false : noPreventDefault;\r\n\r\n        if (this._vrDevice) {\r\n            this.getEngine().enableVR(this._webVROptions);\r\n        }\r\n\r\n        const hostWindow = this._scene.getEngine().getHostWindow();\r\n\r\n        if (hostWindow) {\r\n            hostWindow.addEventListener(\"vrdisplaypresentchange\", this._detachIfAttached);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    public detachControl(): void {\r\n        this.getScene().gamepadManager.onGamepadConnectedObservable.remove(this._onGamepadConnectedObserver);\r\n        this.getScene().gamepadManager.onGamepadDisconnectedObservable.remove(this._onGamepadDisconnectedObserver);\r\n\r\n        super.detachControl();\r\n        this._attached = false;\r\n        this.getEngine().disableVR();\r\n        window.removeEventListener(\"vrdisplaypresentchange\", this._detachIfAttached);\r\n    }\r\n\r\n    /**\r\n     * @returns the name of this class\r\n     */\r\n    public getClassName(): string {\r\n        return \"WebVRFreeCamera\";\r\n    }\r\n\r\n    /**\r\n     * Calls resetPose on the vrDisplay\r\n     * See: https://developer.mozilla.org/en-US/docs/Web/API/VRDisplay/resetPose\r\n     */\r\n    public resetToCurrentRotation() {\r\n        //uses the vrDisplay's \"resetPose()\".\r\n        //pitch and roll won't be affected.\r\n        this._vrDevice.resetPose();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Updates the rig cameras (left and right eye)\r\n     */\r\n    public _updateRigCameras() {\r\n        const camLeft = <TargetCamera>this._rigCameras[0];\r\n        const camRight = <TargetCamera>this._rigCameras[1];\r\n        camLeft.rotationQuaternion.copyFrom(this._deviceRoomRotationQuaternion);\r\n        camRight.rotationQuaternion.copyFrom(this._deviceRoomRotationQuaternion);\r\n\r\n        camLeft.position.copyFrom(this._deviceRoomPosition);\r\n        camRight.position.copyFrom(this._deviceRoomPosition);\r\n    }\r\n\r\n    private _workingVector = Vector3.Zero();\r\n    private _oneVector = Vector3.One();\r\n    private _workingMatrix = Matrix.Identity();\r\n\r\n    private _updateCacheCalled: boolean;\r\n\r\n    // Remove translation from 6dof headset if trackposition is set to false\r\n    private _correctPositionIfNotTrackPosition(matrix: Matrix, isViewMatrix = false) {\r\n        if (this.rawPose && this.rawPose.position && !this._webVROptions.trackPosition) {\r\n            Matrix.TranslationToRef(this.rawPose.position[0], this.rawPose.position[1], -this.rawPose.position[2], this._tmpMatrix);\r\n            if (!isViewMatrix) {\r\n                this._tmpMatrix.invert();\r\n            }\r\n            this._tmpMatrix.multiplyToRef(matrix, matrix);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Updates the cached values of the camera\r\n     * @param ignoreParentClass ignores updating the parent class's cache (default: false)\r\n     */\r\n    public _updateCache(ignoreParentClass?: boolean): void {\r\n        if (!this.rotationQuaternion.equals(this._cache.rotationQuaternion) || !this.position.equals(this._cache.position)) {\r\n            // Update to ensure devicePosition is up to date with most recent _deviceRoomPosition\r\n            if (!this._updateCacheCalled) {\r\n                // make sure it is only called once per loop. this.update() might cause an infinite loop.\r\n                this._updateCacheCalled = true;\r\n                this.update();\r\n            }\r\n\r\n            // Set working vector to the device position in room space rotated by the new rotation\r\n            this.rotationQuaternion.toRotationMatrix(this._workingMatrix);\r\n            Vector3.TransformCoordinatesToRef(this._deviceRoomPosition, this._workingMatrix, this._workingVector);\r\n\r\n            // Subtract this vector from the current device position in world to get the translation for the device world matrix\r\n            this.devicePosition.subtractToRef(this._workingVector, this._workingVector);\r\n            Matrix.ComposeToRef(this._oneVector, this.rotationQuaternion, this._workingVector, this._deviceToWorld);\r\n\r\n            // Add translation from anchor position\r\n            this._deviceToWorld.getTranslationToRef(this._workingVector);\r\n            this._workingVector.addInPlace(this.position);\r\n            this._workingVector.subtractInPlace(this._cache.position);\r\n            this._deviceToWorld.setTranslation(this._workingVector);\r\n\r\n            // Set an inverted matrix to be used when updating the camera\r\n            this._deviceToWorld.invertToRef(this._worldToDevice);\r\n\r\n            // Update the gamepad to ensure the mesh is updated on the same frame as camera\r\n            this.controllers.forEach((controller) => {\r\n                controller._deviceToWorld.copyFrom(this._deviceToWorld);\r\n                this._correctPositionIfNotTrackPosition(controller._deviceToWorld);\r\n                controller.update();\r\n            });\r\n        }\r\n\r\n        if (!ignoreParentClass) {\r\n            super._updateCache();\r\n        }\r\n        this._updateCacheCalled = false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Get current device position in babylon world\r\n     */\r\n    public _computeDevicePosition() {\r\n        Vector3.TransformCoordinatesToRef(this._deviceRoomPosition, this._deviceToWorld, this.devicePosition);\r\n    }\r\n\r\n    /**\r\n     * Updates the current device position and rotation in the babylon world\r\n     */\r\n    public update() {\r\n        this._computeDevicePosition();\r\n\r\n        // Get current device rotation in babylon world\r\n        Matrix.FromQuaternionToRef(this._deviceRoomRotationQuaternion, this._workingMatrix);\r\n        this._workingMatrix.multiplyToRef(this._deviceToWorld, this._workingMatrix);\r\n        Quaternion.FromRotationMatrixToRef(this._workingMatrix, this.deviceRotationQuaternion);\r\n\r\n        if (this._poseSet) {\r\n            this.onPoseUpdatedFromDeviceObservable.notifyObservers(null);\r\n        }\r\n        super.update();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Gets the view matrix of this camera (Always set to identity as left and right eye cameras contain the actual view matrix)\r\n     * @returns an identity matrix\r\n     */\r\n    public _getViewMatrix(): Matrix {\r\n        return Matrix.Identity();\r\n    }\r\n\r\n    private _tmpMatrix = new Matrix();\r\n    /**\r\n     * This function is called by the two RIG cameras.\r\n     * 'this' is the left or right camera (and NOT (!!!) the WebVRFreeCamera instance)\r\n     * @internal\r\n     */\r\n    public _getWebVRViewMatrix(): Matrix {\r\n        // Update the parent camera prior to using a child camera to avoid desynchronization\r\n        const parentCamera: WebVRFreeCamera = this._cameraRigParams[\"parentCamera\"];\r\n        parentCamera._updateCache();\r\n\r\n        //WebVR 1.1\r\n        const viewArray = this._cameraRigParams[\"left\"] ? this._cameraRigParams[\"frameData\"].leftViewMatrix : this._cameraRigParams[\"frameData\"].rightViewMatrix;\r\n\r\n        Matrix.FromArrayToRef(viewArray, 0, this._webvrViewMatrix);\r\n\r\n        if (!this.getScene().useRightHandedSystem) {\r\n            this._webvrViewMatrix.toggleModelMatrixHandInPlace();\r\n        }\r\n\r\n        // update the camera rotation matrix\r\n        this._webvrViewMatrix.getRotationMatrixToRef(this._cameraRotationMatrix);\r\n        Vector3.TransformCoordinatesToRef(this._referencePoint, this._cameraRotationMatrix, this._transformedReferencePoint);\r\n\r\n        // Computing target and final matrix\r\n        this.position.addToRef(this._transformedReferencePoint, this._currentTarget);\r\n\r\n        // should the view matrix be updated with scale and position offset?\r\n        if (parentCamera.deviceScaleFactor !== 1) {\r\n            this._webvrViewMatrix.invert();\r\n            // scale the position, if set\r\n            if (parentCamera.deviceScaleFactor) {\r\n                this._webvrViewMatrix.multiplyAtIndex(12, parentCamera.deviceScaleFactor);\r\n                this._webvrViewMatrix.multiplyAtIndex(13, parentCamera.deviceScaleFactor);\r\n                this._webvrViewMatrix.multiplyAtIndex(14, parentCamera.deviceScaleFactor);\r\n            }\r\n\r\n            this._webvrViewMatrix.invert();\r\n        }\r\n\r\n        // Remove translation from 6dof headset if trackposition is set to false\r\n        parentCamera._correctPositionIfNotTrackPosition(this._webvrViewMatrix, true);\r\n\r\n        parentCamera._worldToDevice.multiplyToRef(this._webvrViewMatrix, this._webvrViewMatrix);\r\n\r\n        // Compute global position\r\n        this._workingMatrix = this._workingMatrix || Matrix.Identity();\r\n        this._webvrViewMatrix.invertToRef(this._workingMatrix);\r\n        this._workingMatrix.multiplyToRef(parentCamera.getWorldMatrix(), this._workingMatrix);\r\n        this._workingMatrix.getTranslationToRef(this._globalPosition);\r\n        this._markSyncedWithParent();\r\n\r\n        return this._webvrViewMatrix;\r\n    }\r\n\r\n    /** @internal */\r\n    public _getWebVRProjectionMatrix(): Matrix {\r\n        const parentCamera = <WebVRFreeCamera>this.parent;\r\n\r\n        parentCamera._vrDevice.depthNear = parentCamera.minZ;\r\n        parentCamera._vrDevice.depthFar = parentCamera.maxZ;\r\n\r\n        const projectionArray = this._cameraRigParams[\"left\"] ? this._cameraRigParams[\"frameData\"].leftProjectionMatrix : this._cameraRigParams[\"frameData\"].rightProjectionMatrix;\r\n        Matrix.FromArrayToRef(projectionArray, 0, this._projectionMatrix);\r\n\r\n        //babylon compatible matrix\r\n        if (!this.getScene().useRightHandedSystem) {\r\n            this._projectionMatrix.toggleProjectionMatrixHandInPlace();\r\n        }\r\n\r\n        return this._projectionMatrix;\r\n    }\r\n\r\n    private _onGamepadConnectedObserver: Nullable<Observer<Gamepad>>;\r\n    private _onGamepadDisconnectedObserver: Nullable<Observer<Gamepad>>;\r\n    private _updateCacheWhenTrackingDisabledObserver: Nullable<Observer<Scene>>;\r\n    /**\r\n     * Initializes the controllers and their meshes\r\n     */\r\n    public initControllers() {\r\n        this.controllers.length = 0;\r\n\r\n        const manager = this.getScene().gamepadManager;\r\n        this._onGamepadDisconnectedObserver = manager.onGamepadDisconnectedObservable.add((gamepad) => {\r\n            if (gamepad.type === Gamepad.POSE_ENABLED) {\r\n                const webVrController: WebVRController = <WebVRController>gamepad;\r\n\r\n                if (webVrController.defaultModel) {\r\n                    webVrController.defaultModel.setEnabled(false);\r\n                }\r\n\r\n                if (webVrController.hand === \"right\") {\r\n                    this._rightController = null;\r\n                }\r\n                if (webVrController.hand === \"left\") {\r\n                    this._leftController = null;\r\n                }\r\n                const controllerIndex = this.controllers.indexOf(webVrController);\r\n                if (controllerIndex !== -1) {\r\n                    this.controllers.splice(controllerIndex, 1);\r\n                }\r\n            }\r\n        });\r\n\r\n        this._onGamepadConnectedObserver = manager.onGamepadConnectedObservable.add((gamepad) => {\r\n            if (gamepad.type === Gamepad.POSE_ENABLED) {\r\n                const webVrController: WebVRController = <WebVRController>gamepad;\r\n                if (!this._webVROptions.trackPosition) {\r\n                    webVrController._disableTrackPosition(new Vector3(webVrController.hand == \"left\" ? -0.15 : 0.15, -0.5, 0.25));\r\n                    // Cache must be updated before rendering controllers to avoid them being one frame behind\r\n                    if (!this._updateCacheWhenTrackingDisabledObserver) {\r\n                        this._updateCacheWhenTrackingDisabledObserver = this._scene.onBeforeRenderObservable.add(() => {\r\n                            this._updateCache();\r\n                        });\r\n                    }\r\n                }\r\n                webVrController.deviceScaleFactor = this.deviceScaleFactor;\r\n                webVrController._deviceToWorld.copyFrom(this._deviceToWorld);\r\n                this._correctPositionIfNotTrackPosition(webVrController._deviceToWorld);\r\n\r\n                if (this._webVROptions.controllerMeshes) {\r\n                    if (webVrController.defaultModel) {\r\n                        webVrController.defaultModel.setEnabled(true);\r\n                    } else {\r\n                        // Load the meshes\r\n                        webVrController.initControllerMesh(this.getScene(), (loadedMesh) => {\r\n                            loadedMesh.scaling.scaleInPlace(this.deviceScaleFactor);\r\n                            this.onControllerMeshLoadedObservable.notifyObservers(webVrController);\r\n                            if (this._webVROptions.defaultLightingOnControllers) {\r\n                                if (!this._lightOnControllers) {\r\n                                    this._lightOnControllers = new HemisphericLight(\"vrControllersLight\", new Vector3(0, 1, 0), this.getScene());\r\n                                }\r\n                                const activateLightOnSubMeshes = function (mesh: AbstractMesh, light: HemisphericLight) {\r\n                                    const children = mesh.getChildren();\r\n                                    if (children && children.length !== 0) {\r\n                                        children.forEach((mesh) => {\r\n                                            light.includedOnlyMeshes.push(<AbstractMesh>mesh);\r\n                                            activateLightOnSubMeshes(<AbstractMesh>mesh, light);\r\n                                        });\r\n                                    }\r\n                                };\r\n                                this._lightOnControllers.includedOnlyMeshes.push(loadedMesh);\r\n                                activateLightOnSubMeshes(loadedMesh, this._lightOnControllers);\r\n                            }\r\n                        });\r\n                    }\r\n                }\r\n                webVrController.attachToPoseControlledCamera(this);\r\n\r\n                // since this is async - sanity check. Is the controller already stored?\r\n                if (this.controllers.indexOf(webVrController) === -1) {\r\n                    //add to the controllers array\r\n                    this.controllers.push(webVrController);\r\n\r\n                    // Forced to add some control code for Vive as it doesn't always fill properly the \"hand\" property\r\n                    // Sometimes, both controllers are set correctly (left and right), sometimes none, sometimes only one of them...\r\n                    // So we're overriding setting left & right manually to be sure\r\n                    let firstViveWandDetected = false;\r\n\r\n                    for (let i = 0; i < this.controllers.length; i++) {\r\n                        if (this.controllers[i].controllerType === PoseEnabledControllerType.VIVE) {\r\n                            if (!firstViveWandDetected) {\r\n                                firstViveWandDetected = true;\r\n                                this.controllers[i].hand = \"left\";\r\n                            } else {\r\n                                this.controllers[i].hand = \"right\";\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    //did we find enough controllers? Great! let the developer know.\r\n                    if (this.controllers.length >= 2) {\r\n                        this.onControllersAttachedObservable.notifyObservers(this.controllers);\r\n                    }\r\n                }\r\n            }\r\n        });\r\n    }\r\n}\r\n"]}