{"version": 3, "file": "actionManager.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Actions/actionManager.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACxD,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAExD,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,eAAe,EAAE,MAAM,iBAAiB,CAAC;AAElD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAErD,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAChE,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAEjD;;;;GAIG;AACH,MAAM,OAAO,aAAc,SAAQ,qBAAqB;IAyGpD;;;OAGG;IACH,YAAY,KAAuB;QAC/B,KAAK,EAAE,CAAC;QACR,KAAK,GAAG,KAAK,IAAI,WAAW,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAED,UAAU;IAEV;;OAEG;IACI,OAAO;QACV,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC/B,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;YACzC,IAAI,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC9C,OAAO,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;aACjD;SACJ;QAED,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAC/C;IACL,CAAC;IAED;;;OAGG;IACI,QAAQ;QACX,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,QAAkB;QACzC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACtD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAEnC,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;gBACvC,OAAO,IAAI,CAAC;aACf;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;OAMG;IACI,oBAAoB,CAAC,QAAgB,EAAE,QAAgB;QAC1D,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACtD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAEnC,IAAI,QAAQ,IAAI,MAAM,CAAC,OAAO,IAAI,QAAQ,IAAI,MAAM,CAAC,OAAO,EAAE;gBAC1D,OAAO,IAAI,CAAC;aACf;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACI,kBAAkB,CAAC,OAAe,EAAE,kBAAgD;QACvF,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACtD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAEnC,IAAI,MAAM,CAAC,OAAO,KAAK,OAAO,EAAE;gBAC5B,IAAI,kBAAkB,EAAE;oBACpB,IAAI,kBAAkB,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,EAAE;wBAClD,OAAO,IAAI,CAAC;qBACf;iBACJ;qBAAM;oBACH,OAAO,IAAI,CAAC;iBACf;aACJ;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB;QACzB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACtD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAEnC,IAAI,MAAM,CAAC,OAAO,IAAI,aAAa,CAAC,aAAa,IAAI,MAAM,CAAC,OAAO,IAAI,aAAa,CAAC,mBAAmB,EAAE;gBACtG,OAAO,IAAI,CAAC;aACf;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,IAAW,eAAe;QACtB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACtD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAEnC,IAAI,MAAM,CAAC,OAAO,IAAI,aAAa,CAAC,aAAa,IAAI,MAAM,CAAC,OAAO,IAAI,aAAa,CAAC,eAAe,EAAE;gBAClG,OAAO,IAAI,CAAC;aACf;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,MAAe;QACjC,IAAI,MAAM,CAAC,OAAO,KAAK,aAAa,CAAC,mBAAmB,EAAE;YACtD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,aAAa,KAAK,IAAI,EAAE;gBACxC,MAAM,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;gBAC7E,OAAO,IAAI,CAAC;aACf;SACJ;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1B,IAAI,CAAC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,CAAC;QAErC,IAAI,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;YACxC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;SAC5C;aAAM;YACH,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;SAC9C;QAED,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,MAAM,CAAC,QAAQ,EAAE,CAAC;QAElB,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,MAAe;QACnC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC3C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC9B,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC5C,IAAI,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC9C,OAAO,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;aACjD;YACD,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC;YAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,OAAe,EAAE,GAAkB;QACrD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACtD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAEnC,IAAI,MAAM,CAAC,OAAO,KAAK,OAAO,EAAE;gBAC5B,IAAI,GAAG,EAAE;oBACL,IAAI,OAAO,KAAK,aAAa,CAAC,cAAc,IAAI,OAAO,KAAK,aAAa,CAAC,gBAAgB,EAAE;wBACxF,MAAM,SAAS,GAAG,MAAM,CAAC,mBAAmB,EAAE,CAAC;wBAE/C,IAAI,OAAO,SAAS,KAAK,UAAU,EAAE;4BACjC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;gCACjB,SAAS;6BACZ;yBACJ;6BAAM,IAAI,SAAS,IAAI,SAAS,KAAK,GAAG,CAAC,WAAW,CAAC,OAAO,EAAE;4BAC3D,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;gCACxB,SAAS;6BACZ;4BACD,MAAM,SAAS,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;4BAE1C,IAAI,SAAS,KAAK,GAAG,CAAC,WAAW,CAAC,GAAG,EAAE;gCACnC,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC;gCAC9F,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;gCAC7D,IAAI,SAAS,KAAK,SAAS,EAAE;oCACzB,SAAS;iCACZ;6BACJ;yBACJ;qBACJ;iBACJ;gBAED,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;aAC/B;SACJ;IACL,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,MAAW,EAAE,YAAoB;QACxD,MAAM,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAE3C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,EAAE,EAAE;YACxD,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;SACtC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,YAAoB;QACpC,MAAM,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAE3C,OAAO,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,IAAY;QACzB,MAAM,IAAI,GAAG;YACT,QAAQ,EAAE,IAAI,KAAK,EAAE;YACrB,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,CAAC;YACP,UAAU,EAAE,IAAI,KAAK,EAAE,EAAE,8BAA8B;SAC1D,CAAC;QAEF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC1C,MAAM,aAAa,GAAG;gBAClB,IAAI,EAAE,CAAC;gBACP,QAAQ,EAAE,IAAI,KAAK,EAAE;gBACrB,IAAI,EAAE,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;gBAC3D,UAAU,EAAE,IAAI,KAAK,EAAE;aAC1B,CAAC;YAEF,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;YAEtD,IAAI,cAAc,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE;gBACtD,IAAI,cAAc,CAAC,SAAS,YAAY,IAAI,EAAE;oBAC1C,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC;iBACtF;qBAAM,IAAI,OAAO,cAAc,CAAC,SAAS,KAAK,QAAQ,EAAE;oBACrD,MAAM,SAAS,GAAQ,EAAE,CAAC;oBAC1B,UAAU,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;oBAEnE,IAAI,cAAc,CAAC,SAAS,IAAI,cAAc,CAAC,SAAS,CAAC,IAAI,EAAE;wBAC3D,SAAS,CAAC,OAAO,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;qBACxD;oBAED,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;iBAC5F;qBAAM;oBACH,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,cAAc,CAAC,SAAS,EAAE,CAAC,CAAC;iBAC3G;aACJ;YAED,sCAAsC;YACtC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAEzC,yBAAyB;YACzB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACrC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,aAAkB,EAAE,MAA8B,EAAE,KAAY;QAChF,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC;QAC/C,IAAI,MAAM,KAAK,IAAI,EAAE;YACjB,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;SACvC;aAAM;YACH,MAAM,CAAC,aAAa,GAAG,aAAa,CAAC;SACxC;QAED,2BAA2B;QAC3B,MAAM,WAAW,GAAG,CAAC,IAAY,EAAE,MAAkB,EAAO,EAAE;YAC1D,MAAM,iBAAiB,GAAG,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC;YACtD,OAAO,iBAAiB,IAAI,IAAI,iBAAiB,CAAC,GAAG,MAAM,CAAC,CAAC;QACjE,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,CAAC,IAAY,EAAE,KAAa,EAAE,MAAW,EAAE,YAA8B,EAAO,EAAE;YACrG,IAAI,YAAY,KAAK,IAAI,EAAE;gBACvB,2BAA2B;gBAC3B,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;gBAErC,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,OAAO,EAAE;oBACvC,OAAO,KAAK,KAAK,MAAM,CAAC;iBAC3B;qBAAM;oBACH,OAAO,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC;iBACjD;aACJ;YAED,MAAM,eAAe,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAChD,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAEhC,uBAAuB;YACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC7C,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;aACvC;YAED,yCAAyC;YACzC,IAAI,OAAO,MAAM,KAAK,SAAS,EAAE;gBAC7B,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC;aAC/B;YAED,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;gBAC5B,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;aACpB;YAED,uDAAuD;YACvD,MAAM,KAAK,GAAG,IAAI,KAAK,EAAU,CAAC;YAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACpC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACrC;YAED,IAAI,MAAM,YAAY,OAAO,EAAE;gBAC3B,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;aACnC;YAED,IAAI,MAAM,YAAY,OAAO,EAAE;gBAC3B,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;aACnC;YAED,IAAI,MAAM,YAAY,MAAM,EAAE;gBAC1B,OAAO,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;aAClC;YAED,IAAI,MAAM,YAAY,MAAM,EAAE;gBAC1B,OAAO,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;aAClC;YAED,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC;QAEF,6BAA6B;QAC7B,MAAM,QAAQ,GAAG,CAAC,YAAiB,EAAE,OAAY,EAAE,SAA8B,EAAE,MAAwB,EAAE,eAAwC,IAAI,EAAE,EAAE;YACzJ,IAAI,YAAY,CAAC,QAAQ,EAAE;gBACvB,OAAO;aACV;YAED,MAAM,UAAU,GAAG,IAAI,KAAK,EAAO,CAAC;YACpC,IAAI,MAAM,GAAQ,IAAI,CAAC;YACvB,IAAI,YAAY,GAAqB,IAAI,CAAC;YAC1C,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,IAAI,YAAY,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;YAExE,aAAa;YACb,IAAI,YAAY,CAAC,IAAI,KAAK,CAAC,EAAE;gBACzB,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aAClC;iBAAM;gBACH,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aAC5B;YAED,IAAI,OAAO,EAAE;gBACT,MAAM,OAAO,GAAG,IAAI,KAAK,EAAU,CAAC;gBACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAClD,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,cAAc,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;iBAC/F;gBACD,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aAC5B;iBAAM;gBACH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACrD,IAAI,KAAK,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;oBAC7C,MAAM,IAAI,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;oBAC7C,MAAM,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;oBAEzD,IAAI,IAAI,KAAK,QAAQ,EAAE;wBACnB,IAAI,UAAU,KAAK,iBAAiB,EAAE;4BAClC,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;yBAC1B;6BAAM,IAAI,UAAU,KAAK,oBAAoB,EAAE;4BAC5C,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;yBACnD;6BAAM;4BACH,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;yBAC/C;qBACJ;yBAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;wBAC1B,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;qBACtC;yBAAM,IAAI,IAAI,KAAK,OAAO,EAAE;wBACzB,iFAAiF;wBACjF,IAAI,KAAK,CAAC,cAAc,EAAE;4BACtB,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;yBACvC;qBACJ;yBAAM,IAAI,IAAI,KAAK,cAAc,EAAE;wBAChC,IAAI,YAAY,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,UAAU,EAAE;4BAChD,KAAK,GAAS,cAAe,CAAC,KAAK,CAAC,CAAC;yBACxC;6BAAM;4BACH,KAAK,GAAG,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;yBACvF;qBACJ;yBAAM;wBACH,YAAY,GAAG,KAAK,CAAC;qBACxB;oBAED,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBAC1B;aACJ;YAED,IAAI,YAAY,KAAK,IAAI,EAAE;gBACvB,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAC9B;iBAAM;gBACH,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACzB;YAED,8BAA8B;YAC9B,IAAI,YAAY,CAAC,IAAI,KAAK,wBAAwB,EAAE;gBAChD,MAAM,KAAK,GAAG,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAChD,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;gBAC1C,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC;aACjD;YAED,+CAA+C;YAC/C,IAAI,SAAS,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YAE3D,IAAI,SAAS,YAAY,SAAS,IAAI,SAAS,KAAK,IAAI,EAAE;gBACtD,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;gBAExD,IAAI,MAAM,EAAE;oBACR,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBACxB;qBAAM;oBACH,aAAa,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;iBACzC;gBAED,MAAM,GAAG,OAAO,CAAC;aACpB;YAED,IAAI,YAAY,KAAK,IAAI,EAAE;gBACvB,IAAI,SAAS,YAAY,SAAS,EAAE;oBAChC,SAAS,GAAG,SAAS,CAAC;oBACtB,SAAS,GAAG,MAAM,CAAC;iBACtB;qBAAM;oBACH,SAAS,GAAG,IAAI,CAAC;oBACjB,IAAI,MAAM,EAAE;wBACR,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;qBAC1B;yBAAM;wBACH,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;qBAC3C;iBACJ;aACJ;iBAAM;gBACH,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAChC;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACnD,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;aAC3E;QACL,CAAC,CAAC;QAEF,WAAW;QACX,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpD,IAAI,aAAkB,CAAC;YACvB,MAAM,OAAO,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAE1C,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC/B,MAAM,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;gBAC1C,MAAM,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAE7F,IAAI,KAAK,CAAC,OAAO,EAAE;oBACf,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;iBACjD;gBAED,aAAa,GAAG,EAAE,OAAO,EAAQ,aAAc,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;aACrF;iBAAM;gBACH,aAAa,GAAS,aAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;aACtD;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC9C,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;oBACnB,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;iBAC5D;aACJ;SACJ;IACL,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,cAAc,CAAC,OAAe;QACxC,QAAQ,OAAO,EAAE;YACb,KAAK,SAAS,CAAC,qBAAqB;gBAChC,OAAO,gBAAgB,CAAC;YAC5B,KAAK,SAAS,CAAC,oBAAoB;gBAC/B,OAAO,eAAe,CAAC;YAC3B,KAAK,SAAS,CAAC,wBAAwB;gBACnC,OAAO,mBAAmB,CAAC;YAC/B,KAAK,SAAS,CAAC,yBAAyB;gBACpC,OAAO,oBAAoB,CAAC;YAChC,KAAK,SAAS,CAAC,0BAA0B;gBACrC,OAAO,qBAAqB,CAAC;YACjC,KAAK,SAAS,CAAC,wBAAwB;gBACnC,OAAO,mBAAmB,CAAC;YAC/B,KAAK,SAAS,CAAC,0BAA0B;gBACrC,OAAO,qBAAqB,CAAC,CAAC,SAAS;YAC3C,KAAK,SAAS,CAAC,sBAAsB;gBACjC,OAAO,iBAAiB,CAAC;YAC7B,KAAK,SAAS,CAAC,yBAAyB;gBACpC,OAAO,oBAAoB,CAAC;YAChC,KAAK,SAAS,CAAC,2BAA2B;gBACtC,OAAO,sBAAsB,CAAC;YAClC,KAAK,SAAS,CAAC,0BAA0B;gBACrC,OAAO,qBAAqB,CAAC;YACjC,KAAK,SAAS,CAAC,0BAA0B;gBACrC,OAAO,qBAAqB,CAAC;YACjC,KAAK,SAAS,CAAC,iCAAiC;gBAC5C,OAAO,4BAA4B,CAAC;YACxC,KAAK,SAAS,CAAC,gCAAgC;gBAC3C,OAAO,2BAA2B,CAAC;YACvC,KAAK,SAAS,CAAC,uBAAuB;gBAClC,OAAO,kBAAkB,CAAC;YAC9B,KAAK,SAAS,CAAC,qBAAqB;gBAChC,OAAO,gBAAgB,CAAC;YAC5B,KAAK,SAAS,CAAC,uBAAuB;gBAClC,OAAO,kBAAkB,CAAC;YAC9B;gBACI,OAAO,EAAE,CAAC;SACjB;IACL,CAAC;;AA1oBD;;;GAGG;AACoB,4BAAc,GAAG,SAAS,CAAC,qBAAqB,CAAC;AAExE;;;GAGG;AACoB,2BAAa,GAAG,SAAS,CAAC,oBAAoB,CAAC;AAEtE;;;GAGG;AACoB,+BAAiB,GAAG,SAAS,CAAC,wBAAwB,CAAC;AAE9E;;;GAGG;AACoB,gCAAkB,GAAG,SAAS,CAAC,yBAAyB,CAAC;AAEhF;;;GAGG;AACoB,iCAAmB,GAAG,SAAS,CAAC,0BAA0B,CAAC;AAElF;;;GAGG;AACoB,+BAAiB,GAAG,SAAS,CAAC,wBAAwB,CAAC;AAE9E;;;GAGG;AACoB,iCAAmB,GAAG,SAAS,CAAC,0BAA0B,CAAC;AAElF;;;GAGG;AACoB,6BAAe,GAAG,SAAS,CAAC,sBAAsB,CAAC;AAC1E;;;;GAIG;AACoB,8BAAgB,GAAG,SAAS,CAAC,uBAAuB,CAAC;AAE5E;;;GAGG;AACoB,gCAAkB,GAAG,SAAS,CAAC,yBAAyB,CAAC;AAEhF;;;GAGG;AACoB,kCAAoB,GAAG,SAAS,CAAC,2BAA2B,CAAC;AAEpF;;;GAGG;AACoB,iCAAmB,GAAG,SAAS,CAAC,0BAA0B,CAAC;AAElF;;;GAGG;AACoB,iCAAmB,GAAG,SAAS,CAAC,0BAA0B,CAAC;AAClF;;;GAGG;AACoB,wCAA0B,GAAG,SAAS,CAAC,iCAAiC,CAAC;AAEhG;;;GAGG;AACoB,uCAAyB,GAAG,SAAS,CAAC,gCAAgC,CAAC;AAE9F;;;GAGG;AACoB,8BAAgB,GAAG,SAAS,CAAC,uBAAuB,CAAC;AAE5E;;;GAGG;AACoB,4BAAc,GAAG,SAAS,CAAC,qBAAqB,CAAC", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Vector3, Vector4 } from \"../Maths/math.vector\";\r\nimport { Color3, Color4 } from \"../Maths/math.color\";\r\nimport { Condition, ValueCondition } from \"./condition\";\r\nimport type { IAction } from \"./action\";\r\nimport { Action } from \"./action\";\r\nimport { DoNothingAction } from \"./directActions\";\r\n\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport type { IActionEvent } from \"../Actions/actionEvent\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { DeepCopier } from \"../Misc/deepCopier\";\r\nimport { GetClass } from \"../Misc/typeStore\";\r\nimport { AbstractActionManager } from \"./abstractActionManager\";\r\nimport { Constants } from \"../Engines/constants\";\r\n\r\n/**\r\n * Action Manager manages all events to be triggered on a given mesh or the global scene.\r\n * A single scene can have many Action Managers to handle predefined actions on specific meshes.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions\r\n */\r\nexport class ActionManager extends AbstractActionManager {\r\n    /**\r\n     * Nothing\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\r\n     */\r\n    public static readonly NothingTrigger = Constants.ACTION_NothingTrigger;\r\n\r\n    /**\r\n     * On pick\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\r\n     */\r\n    public static readonly OnPickTrigger = Constants.ACTION_OnPickTrigger;\r\n\r\n    /**\r\n     * On left pick\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\r\n     */\r\n    public static readonly OnLeftPickTrigger = Constants.ACTION_OnLeftPickTrigger;\r\n\r\n    /**\r\n     * On right pick\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\r\n     */\r\n    public static readonly OnRightPickTrigger = Constants.ACTION_OnRightPickTrigger;\r\n\r\n    /**\r\n     * On center pick\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\r\n     */\r\n    public static readonly OnCenterPickTrigger = Constants.ACTION_OnCenterPickTrigger;\r\n\r\n    /**\r\n     * On pick down\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\r\n     */\r\n    public static readonly OnPickDownTrigger = Constants.ACTION_OnPickDownTrigger;\r\n\r\n    /**\r\n     * On double pick\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\r\n     */\r\n    public static readonly OnDoublePickTrigger = Constants.ACTION_OnDoublePickTrigger;\r\n\r\n    /**\r\n     * On pick up\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\r\n     */\r\n    public static readonly OnPickUpTrigger = Constants.ACTION_OnPickUpTrigger;\r\n    /**\r\n     * On pick out.\r\n     * This trigger will only be raised if you also declared a OnPickDown\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\r\n     */\r\n    public static readonly OnPickOutTrigger = Constants.ACTION_OnPickOutTrigger;\r\n\r\n    /**\r\n     * On long press\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\r\n     */\r\n    public static readonly OnLongPressTrigger = Constants.ACTION_OnLongPressTrigger;\r\n\r\n    /**\r\n     * On pointer over\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\r\n     */\r\n    public static readonly OnPointerOverTrigger = Constants.ACTION_OnPointerOverTrigger;\r\n\r\n    /**\r\n     * On pointer out\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\r\n     */\r\n    public static readonly OnPointerOutTrigger = Constants.ACTION_OnPointerOutTrigger;\r\n\r\n    /**\r\n     * On every frame\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\r\n     */\r\n    public static readonly OnEveryFrameTrigger = Constants.ACTION_OnEveryFrameTrigger;\r\n    /**\r\n     * On intersection enter\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\r\n     */\r\n    public static readonly OnIntersectionEnterTrigger = Constants.ACTION_OnIntersectionEnterTrigger;\r\n\r\n    /**\r\n     * On intersection exit\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\r\n     */\r\n    public static readonly OnIntersectionExitTrigger = Constants.ACTION_OnIntersectionExitTrigger;\r\n\r\n    /**\r\n     * On key down\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\r\n     */\r\n    public static readonly OnKeyDownTrigger = Constants.ACTION_OnKeyDownTrigger;\r\n\r\n    /**\r\n     * On key up\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#triggers\r\n     */\r\n    public static readonly OnKeyUpTrigger = Constants.ACTION_OnKeyUpTrigger;\r\n\r\n    // Members\r\n    private _scene: Scene;\r\n\r\n    /**\r\n     * Creates a new action manager\r\n     * @param scene defines the hosting scene\r\n     */\r\n    constructor(scene?: Nullable<Scene>) {\r\n        super();\r\n        scene = scene || EngineStore.LastCreatedScene;\r\n        if (!scene) {\r\n            return;\r\n        }\r\n        this._scene = scene;\r\n\r\n        scene.actionManagers.push(this);\r\n    }\r\n\r\n    // Methods\r\n\r\n    /**\r\n     * Releases all associated resources\r\n     */\r\n    public dispose(): void {\r\n        const index = this._scene.actionManagers.indexOf(this);\r\n\r\n        for (let i = 0; i < this.actions.length; i++) {\r\n            const action = this.actions[i];\r\n            ActionManager.Triggers[action.trigger]--;\r\n            if (ActionManager.Triggers[action.trigger] === 0) {\r\n                delete ActionManager.Triggers[action.trigger];\r\n            }\r\n        }\r\n\r\n        if (index > -1) {\r\n            this._scene.actionManagers.splice(index, 1);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets hosting scene\r\n     * @returns the hosting scene\r\n     */\r\n    public getScene(): Scene {\r\n        return this._scene;\r\n    }\r\n\r\n    /**\r\n     * Does this action manager handles actions of any of the given triggers\r\n     * @param triggers defines the triggers to be tested\r\n     * @returns a boolean indicating whether one (or more) of the triggers is handled\r\n     */\r\n    public hasSpecificTriggers(triggers: number[]): boolean {\r\n        for (let index = 0; index < this.actions.length; index++) {\r\n            const action = this.actions[index];\r\n\r\n            if (triggers.indexOf(action.trigger) > -1) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Does this action manager handles actions of any of the given triggers. This function takes two arguments for\r\n     * speed.\r\n     * @param triggerA defines the trigger to be tested\r\n     * @param triggerB defines the trigger to be tested\r\n     * @returns a boolean indicating whether one (or more) of the triggers is handled\r\n     */\r\n    public hasSpecificTriggers2(triggerA: number, triggerB: number): boolean {\r\n        for (let index = 0; index < this.actions.length; index++) {\r\n            const action = this.actions[index];\r\n\r\n            if (triggerA == action.trigger || triggerB == action.trigger) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Does this action manager handles actions of a given trigger\r\n     * @param trigger defines the trigger to be tested\r\n     * @param parameterPredicate defines an optional predicate to filter triggers by parameter\r\n     * @returns whether the trigger is handled\r\n     */\r\n    public hasSpecificTrigger(trigger: number, parameterPredicate?: (parameter: any) => boolean): boolean {\r\n        for (let index = 0; index < this.actions.length; index++) {\r\n            const action = this.actions[index];\r\n\r\n            if (action.trigger === trigger) {\r\n                if (parameterPredicate) {\r\n                    if (parameterPredicate(action.getTriggerParameter())) {\r\n                        return true;\r\n                    }\r\n                } else {\r\n                    return true;\r\n                }\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Does this action manager has pointer triggers\r\n     */\r\n    public get hasPointerTriggers(): boolean {\r\n        for (let index = 0; index < this.actions.length; index++) {\r\n            const action = this.actions[index];\r\n\r\n            if (action.trigger >= ActionManager.OnPickTrigger && action.trigger <= ActionManager.OnPointerOutTrigger) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Does this action manager has pick triggers\r\n     */\r\n    public get hasPickTriggers(): boolean {\r\n        for (let index = 0; index < this.actions.length; index++) {\r\n            const action = this.actions[index];\r\n\r\n            if (action.trigger >= ActionManager.OnPickTrigger && action.trigger <= ActionManager.OnPickUpTrigger) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Registers an action to this action manager\r\n     * @param action defines the action to be registered\r\n     * @returns the action amended (prepared) after registration\r\n     */\r\n    public registerAction(action: IAction): Nullable<IAction> {\r\n        if (action.trigger === ActionManager.OnEveryFrameTrigger) {\r\n            if (this.getScene().actionManager !== this) {\r\n                Logger.Warn(\"OnEveryFrameTrigger can only be used with scene.actionManager\");\r\n                return null;\r\n            }\r\n        }\r\n\r\n        this.actions.push(action);\r\n        this.getScene()._registeredActions++;\r\n\r\n        if (ActionManager.Triggers[action.trigger]) {\r\n            ActionManager.Triggers[action.trigger]++;\r\n        } else {\r\n            ActionManager.Triggers[action.trigger] = 1;\r\n        }\r\n\r\n        action._actionManager = this;\r\n        action._prepare();\r\n\r\n        return action;\r\n    }\r\n\r\n    /**\r\n     * Unregisters an action to this action manager\r\n     * @param action defines the action to be unregistered\r\n     * @returns a boolean indicating whether the action has been unregistered\r\n     */\r\n    public unregisterAction(action: IAction): Boolean {\r\n        const index = this.actions.indexOf(action);\r\n        if (index !== -1) {\r\n            this.actions.splice(index, 1);\r\n            ActionManager.Triggers[action.trigger] -= 1;\r\n            if (ActionManager.Triggers[action.trigger] === 0) {\r\n                delete ActionManager.Triggers[action.trigger];\r\n            }\r\n            action._actionManager = null;\r\n            this.getScene()._registeredActions--;\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Process a specific trigger\r\n     * @param trigger defines the trigger to process\r\n     * @param evt defines the event details to be processed\r\n     */\r\n    public processTrigger(trigger: number, evt?: IActionEvent): void {\r\n        for (let index = 0; index < this.actions.length; index++) {\r\n            const action = this.actions[index];\r\n\r\n            if (action.trigger === trigger) {\r\n                if (evt) {\r\n                    if (trigger === ActionManager.OnKeyUpTrigger || trigger === ActionManager.OnKeyDownTrigger) {\r\n                        const parameter = action.getTriggerParameter();\r\n\r\n                        if (typeof parameter === \"function\") {\r\n                            if (!parameter(evt)) {\r\n                                continue;\r\n                            }\r\n                        } else if (parameter && parameter !== evt.sourceEvent.keyCode) {\r\n                            if (!parameter.toLowerCase) {\r\n                                continue;\r\n                            }\r\n                            const lowerCase = parameter.toLowerCase();\r\n\r\n                            if (lowerCase !== evt.sourceEvent.key) {\r\n                                const unicode = evt.sourceEvent.charCode ? evt.sourceEvent.charCode : evt.sourceEvent.keyCode;\r\n                                const actualkey = String.fromCharCode(unicode).toLowerCase();\r\n                                if (actualkey !== lowerCase) {\r\n                                    continue;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n                action._executeCurrent(evt);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getEffectiveTarget(target: any, propertyPath: string): any {\r\n        const properties = propertyPath.split(\".\");\r\n\r\n        for (let index = 0; index < properties.length - 1; index++) {\r\n            target = target[properties[index]];\r\n        }\r\n\r\n        return target;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getProperty(propertyPath: string): string {\r\n        const properties = propertyPath.split(\".\");\r\n\r\n        return properties[properties.length - 1];\r\n    }\r\n\r\n    /**\r\n     * Serialize this manager to a JSON object\r\n     * @param name defines the property name to store this manager\r\n     * @returns a JSON representation of this manager\r\n     */\r\n    public serialize(name: string): any {\r\n        const root = {\r\n            children: new Array(),\r\n            name: name,\r\n            type: 3, // Root node\r\n            properties: new Array(), // Empty for root but required\r\n        };\r\n\r\n        for (let i = 0; i < this.actions.length; i++) {\r\n            const triggerObject = {\r\n                type: 0, // Trigger\r\n                children: new Array(),\r\n                name: ActionManager.GetTriggerName(this.actions[i].trigger),\r\n                properties: new Array(),\r\n            };\r\n\r\n            const triggerOptions = this.actions[i].triggerOptions;\r\n\r\n            if (triggerOptions && typeof triggerOptions !== \"number\") {\r\n                if (triggerOptions.parameter instanceof Node) {\r\n                    triggerObject.properties.push(Action._GetTargetProperty(triggerOptions.parameter));\r\n                } else if (typeof triggerOptions.parameter === \"object\") {\r\n                    const parameter = <any>{};\r\n                    DeepCopier.DeepCopy(triggerOptions.parameter, parameter, [\"mesh\"]);\r\n\r\n                    if (triggerOptions.parameter && triggerOptions.parameter.mesh) {\r\n                        parameter._meshId = triggerOptions.parameter.mesh.id;\r\n                    }\r\n\r\n                    triggerObject.properties.push({ name: \"parameter\", targetType: null, value: parameter });\r\n                } else {\r\n                    triggerObject.properties.push({ name: \"parameter\", targetType: null, value: triggerOptions.parameter });\r\n                }\r\n            }\r\n\r\n            // Serialize child action, recursively\r\n            this.actions[i].serialize(triggerObject);\r\n\r\n            // Add serialized trigger\r\n            root.children.push(triggerObject);\r\n        }\r\n\r\n        return root;\r\n    }\r\n\r\n    /**\r\n     * Creates a new ActionManager from a JSON data\r\n     * @param parsedActions defines the JSON data to read from\r\n     * @param object defines the hosting mesh\r\n     * @param scene defines the hosting scene\r\n     */\r\n    public static Parse(parsedActions: any, object: Nullable<AbstractMesh>, scene: Scene): void {\r\n        const actionManager = new ActionManager(scene);\r\n        if (object === null) {\r\n            scene.actionManager = actionManager;\r\n        } else {\r\n            object.actionManager = actionManager;\r\n        }\r\n\r\n        // instantiate a new object\r\n        const instantiate = (name: string, params: Array<any>): any => {\r\n            const internalClassType = GetClass(\"BABYLON.\" + name);\r\n            return internalClassType && new internalClassType(...params);\r\n        };\r\n\r\n        const parseParameter = (name: string, value: string, target: any, propertyPath: Nullable<string>): any => {\r\n            if (propertyPath === null) {\r\n                // String, boolean or float\r\n                const floatValue = parseFloat(value);\r\n\r\n                if (value === \"true\" || value === \"false\") {\r\n                    return value === \"true\";\r\n                } else {\r\n                    return isNaN(floatValue) ? value : floatValue;\r\n                }\r\n            }\r\n\r\n            const effectiveTarget = propertyPath.split(\".\");\r\n            const values = value.split(\",\");\r\n\r\n            // Get effective Target\r\n            for (let i = 0; i < effectiveTarget.length; i++) {\r\n                target = target[effectiveTarget[i]];\r\n            }\r\n\r\n            // Return appropriate value with its type\r\n            if (typeof target === \"boolean\") {\r\n                return values[0] === \"true\";\r\n            }\r\n\r\n            if (typeof target === \"string\") {\r\n                return values[0];\r\n            }\r\n\r\n            // Parameters with multiple values such as Vector3 etc.\r\n            const split = new Array<number>();\r\n            for (let i = 0; i < values.length; i++) {\r\n                split.push(parseFloat(values[i]));\r\n            }\r\n\r\n            if (target instanceof Vector3) {\r\n                return Vector3.FromArray(split);\r\n            }\r\n\r\n            if (target instanceof Vector4) {\r\n                return Vector4.FromArray(split);\r\n            }\r\n\r\n            if (target instanceof Color3) {\r\n                return Color3.FromArray(split);\r\n            }\r\n\r\n            if (target instanceof Color4) {\r\n                return Color4.FromArray(split);\r\n            }\r\n\r\n            return parseFloat(values[0]);\r\n        };\r\n\r\n        // traverse graph per trigger\r\n        const traverse = (parsedAction: any, trigger: any, condition: Nullable<Condition>, action: Nullable<Action>, combineArray: Nullable<Array<Action>> = null) => {\r\n            if (parsedAction.detached) {\r\n                return;\r\n            }\r\n\r\n            const parameters = new Array<any>();\r\n            let target: any = null;\r\n            let propertyPath: Nullable<string> = null;\r\n            const combine = parsedAction.combine && parsedAction.combine.length > 0;\r\n\r\n            // Parameters\r\n            if (parsedAction.type === 2) {\r\n                parameters.push(actionManager);\r\n            } else {\r\n                parameters.push(trigger);\r\n            }\r\n\r\n            if (combine) {\r\n                const actions = new Array<Action>();\r\n                for (let j = 0; j < parsedAction.combine.length; j++) {\r\n                    traverse(parsedAction.combine[j], ActionManager.NothingTrigger, condition, action, actions);\r\n                }\r\n                parameters.push(actions);\r\n            } else {\r\n                for (let i = 0; i < parsedAction.properties.length; i++) {\r\n                    let value = parsedAction.properties[i].value;\r\n                    const name = parsedAction.properties[i].name;\r\n                    const targetType = parsedAction.properties[i].targetType;\r\n\r\n                    if (name === \"target\") {\r\n                        if (targetType === \"SceneProperties\") {\r\n                            value = target = scene;\r\n                        } else if (targetType === \"MaterialProperties\") {\r\n                            value = target = scene.getMaterialByName(value);\r\n                        } else {\r\n                            value = target = scene.getNodeByName(value);\r\n                        }\r\n                    } else if (name === \"parent\") {\r\n                        value = scene.getNodeByName(value);\r\n                    } else if (name === \"sound\") {\r\n                        // Can not externalize to component, so only checks for the presence off the API.\r\n                        if (scene.getSoundByName) {\r\n                            value = scene.getSoundByName(value);\r\n                        }\r\n                    } else if (name !== \"propertyPath\") {\r\n                        if (parsedAction.type === 2 && name === \"operator\") {\r\n                            value = (<any>ValueCondition)[value];\r\n                        } else {\r\n                            value = parseParameter(name, value, target, name === \"value\" ? propertyPath : null);\r\n                        }\r\n                    } else {\r\n                        propertyPath = value;\r\n                    }\r\n\r\n                    parameters.push(value);\r\n                }\r\n            }\r\n\r\n            if (combineArray === null) {\r\n                parameters.push(condition);\r\n            } else {\r\n                parameters.push(null);\r\n            }\r\n\r\n            // If interpolate value action\r\n            if (parsedAction.name === \"InterpolateValueAction\") {\r\n                const param = parameters[parameters.length - 2];\r\n                parameters[parameters.length - 1] = param;\r\n                parameters[parameters.length - 2] = condition;\r\n            }\r\n\r\n            // Action or condition(s) and not CombineAction\r\n            let newAction = instantiate(parsedAction.name, parameters);\r\n\r\n            if (newAction instanceof Condition && condition !== null) {\r\n                const nothing = new DoNothingAction(trigger, condition);\r\n\r\n                if (action) {\r\n                    action.then(nothing);\r\n                } else {\r\n                    actionManager.registerAction(nothing);\r\n                }\r\n\r\n                action = nothing;\r\n            }\r\n\r\n            if (combineArray === null) {\r\n                if (newAction instanceof Condition) {\r\n                    condition = newAction;\r\n                    newAction = action;\r\n                } else {\r\n                    condition = null;\r\n                    if (action) {\r\n                        action.then(newAction);\r\n                    } else {\r\n                        actionManager.registerAction(newAction);\r\n                    }\r\n                }\r\n            } else {\r\n                combineArray.push(newAction);\r\n            }\r\n\r\n            for (let i = 0; i < parsedAction.children.length; i++) {\r\n                traverse(parsedAction.children[i], trigger, condition, newAction, null);\r\n            }\r\n        };\r\n\r\n        // triggers\r\n        for (let i = 0; i < parsedActions.children.length; i++) {\r\n            let triggerParams: any;\r\n            const trigger = parsedActions.children[i];\r\n\r\n            if (trigger.properties.length > 0) {\r\n                const param = trigger.properties[0].value;\r\n                const value = trigger.properties[0].targetType === null ? param : scene.getMeshByName(param);\r\n\r\n                if (value._meshId) {\r\n                    value.mesh = scene.getMeshById(value._meshId);\r\n                }\r\n\r\n                triggerParams = { trigger: (<any>ActionManager)[trigger.name], parameter: value };\r\n            } else {\r\n                triggerParams = (<any>ActionManager)[trigger.name];\r\n            }\r\n\r\n            for (let j = 0; j < trigger.children.length; j++) {\r\n                if (!trigger.detached) {\r\n                    traverse(trigger.children[j], triggerParams, null, null);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get a trigger name by index\r\n     * @param trigger defines the trigger index\r\n     * @returns a trigger name\r\n     */\r\n    public static GetTriggerName(trigger: number): string {\r\n        switch (trigger) {\r\n            case Constants.ACTION_NothingTrigger:\r\n                return \"NothingTrigger\";\r\n            case Constants.ACTION_OnPickTrigger:\r\n                return \"OnPickTrigger\";\r\n            case Constants.ACTION_OnLeftPickTrigger:\r\n                return \"OnLeftPickTrigger\";\r\n            case Constants.ACTION_OnRightPickTrigger:\r\n                return \"OnRightPickTrigger\";\r\n            case Constants.ACTION_OnCenterPickTrigger:\r\n                return \"OnCenterPickTrigger\";\r\n            case Constants.ACTION_OnPickDownTrigger:\r\n                return \"OnPickDownTrigger\";\r\n            case Constants.ACTION_OnDoublePickTrigger:\r\n                return \"OnDoublePickTrigger\"; // start;\r\n            case Constants.ACTION_OnPickUpTrigger:\r\n                return \"OnPickUpTrigger\";\r\n            case Constants.ACTION_OnLongPressTrigger:\r\n                return \"OnLongPressTrigger\";\r\n            case Constants.ACTION_OnPointerOverTrigger:\r\n                return \"OnPointerOverTrigger\";\r\n            case Constants.ACTION_OnPointerOutTrigger:\r\n                return \"OnPointerOutTrigger\";\r\n            case Constants.ACTION_OnEveryFrameTrigger:\r\n                return \"OnEveryFrameTrigger\";\r\n            case Constants.ACTION_OnIntersectionEnterTrigger:\r\n                return \"OnIntersectionEnterTrigger\";\r\n            case Constants.ACTION_OnIntersectionExitTrigger:\r\n                return \"OnIntersectionExitTrigger\";\r\n            case Constants.ACTION_OnKeyDownTrigger:\r\n                return \"OnKeyDownTrigger\";\r\n            case Constants.ACTION_OnKeyUpTrigger:\r\n                return \"OnKeyUpTrigger\";\r\n            case Constants.ACTION_OnPickOutTrigger:\r\n                return \"OnPickOutTrigger\";\r\n            default:\r\n                return \"\";\r\n        }\r\n    }\r\n}\r\n"]}