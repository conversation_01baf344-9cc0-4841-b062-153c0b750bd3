{"version": 3, "file": "animatable.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Animations/animatable.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAGtD,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AACjC,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAC/E,OAAO,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AACtD,OAAO,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AAGrC;;GAEG;AACH,MAAM,OAAO,UAAU;IAmCnB;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACH,IAAW,WAAW;QAClB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE;YACtC,OAAO,CAAC,CAAC;SACZ;QAED,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAW,MAAM,CAAC,KAAa;QAC3B,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,+BAA+B;YAC/B,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;YAClB,OAAO;SACV;QAED,sCAAsC;QACtC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,UAAU,CAAC,KAAa;QAC/B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACjE,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAEjD,SAAS,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC;SAChD;QACD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAEzB,kFAAkF;QAClF,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;YAC1B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SACnC;IACL,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,YACI,KAAY;IACZ,gCAAgC;IACzB,MAAW;IAClB,uDAAuD;IAChD,YAAoB,CAAC;IAC5B,uDAAuD;IAChD,UAAkB,GAAG;IAC5B,6DAA6D;IACtD,gBAAyB,KAAK,EACrC,aAAqB,GAAG;IACxB,0EAA0E;IACnE,cAAqC,EAC5C,UAAwB;IACxB,sDAAsD;IAC/C,eAAsC;IAC7C,mEAAmE;IAC5D,aAAsB,KAAK;QAd3B,WAAM,GAAN,MAAM,CAAK;QAEX,cAAS,GAAT,SAAS,CAAY;QAErB,YAAO,GAAP,OAAO,CAAc;QAErB,kBAAa,GAAb,aAAa,CAAiB;QAG9B,mBAAc,GAAd,cAAc,CAAuB;QAGrC,oBAAe,GAAf,eAAe,CAAuB;QAEtC,eAAU,GAAV,UAAU,CAAiB;QA1H9B,sBAAiB,GAAqB,IAAI,CAAC;QAC3C,iBAAY,GAAqB,IAAI,CAAC;QACtC,qBAAgB,GAAqB,IAAI,CAAC;QAClD,cAAc;QACP,uBAAkB,GAAG,IAAI,KAAK,EAAoB,CAAC;QAClD,YAAO,GAAG,KAAK,CAAC;QAEhB,gBAAW,GAAG,CAAC,CAAC;QAChB,YAAO,GAAG,CAAC,GAAG,CAAC;QACf,cAAS,GAAyB,IAAI,CAAC;QACvC,yBAAoB,GAAqB,IAAI,CAAC;QAC9C,eAAU,GAAqB,IAAI,CAAC;QAE5C;;;WAGG;QACI,iBAAY,GAAG,IAAI,CAAC;QAE3B;;WAEG;QACI,qBAAgB,GAAG,KAAK,CAAC;QAEhC;;WAEG;QACI,6BAAwB,GAAG,IAAI,UAAU,EAAc,CAAC;QAE/D;;WAEG;QACI,8BAAyB,GAAG,IAAI,UAAU,EAAc,CAAC;QA4F5D,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;SAC7C;QAED,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAED,UAAU;IACV;;;;;OAKG;IACI,QAAQ,CAAC,IAA0B;QACtC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,IAAI,EAAE;YACN,wDAAwD;YACxD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC3D,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;gBACZ,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAChD,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC7C;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,MAAW,EAAE,UAAuB;QACxD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACpD,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;YAEpC,MAAM,mBAAmB,GAAG,IAAI,gBAAgB,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YACvF,mBAAmB,CAAC,OAAO,GAAG,GAAG,EAAE;gBAC/B,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACrD,IAAI,IAAI,CAAC,eAAe,EAAE;oBACtB,IAAI,CAAC,eAAe,EAAE,CAAC;iBAC1B;YACL,CAAC,CAAC;YAEF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;SACrD;IACL,CAAC;IAED;;;;OAIG;IACI,4BAA4B,CAAC,QAAgB;QAChD,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAElD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC3D,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,cAAc,KAAK,QAAQ,EAAE;gBAChE,OAAO,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC;aAC7C;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,mCAAmC,CAAC,QAAgB;QACvD,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAElD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC3D,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,cAAc,KAAK,QAAQ,EAAE;gBAChE,OAAO,iBAAiB,CAAC,KAAK,CAAC,CAAC;aACnC;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,KAAK;QACR,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAElD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC3D,iBAAiB,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SACxC;QAED,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,aAAqB;QACvC,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAElD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC3D,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,cAAc,GAAG,IAAI,CAAC;YACzD,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,aAAa,GAAG,aAAa,CAAC;SACpE;IACL,CAAC;IAED;;;OAGG;IACI,eAAe;QAClB,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAElD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC3D,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,cAAc,GAAG,KAAK,CAAC;SAC7D;IACL,CAAC;IAED;;;OAGG;IACI,SAAS,CAAC,KAAa;;QAC1B,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAElD,IAAI,iBAAiB,CAAC,CAAC,CAAC,EAAE;YACtB,MAAM,GAAG,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC;YAC1D,IAAI,CAAC,oBAAoB,GAAG,MAAA,IAAI,CAAC,oBAAoB,mCAAI,iBAAiB,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;YAC3F,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;YACjH,IAAI,CAAC,gBAAgB,GAAG,CAAC,KAAK,CAAC;SAClC;QAED,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC3D,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SAC7C;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IAC5B,CAAC;IAED;;OAEG;IACI,KAAK;QACR,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,OAAO;SACV;QACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACzB,CAAC;IAEO,oBAAoB;QACxB,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,EAAE,CAAC;SACzB;QAED,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACxD,CAAC;IAED;;;;;OAKG;IACI,IAAI,CAAC,aAAsB,EAAE,UAAqC,EAAE,eAAe,GAAG,KAAK;QAC9F,IAAI,aAAa,IAAI,UAAU,EAAE;YAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAEzD,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE;gBACV,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;gBAElD,KAAK,IAAI,KAAK,GAAG,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE;oBAChE,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;oBAClD,IAAI,aAAa,IAAI,gBAAgB,CAAC,SAAS,CAAC,IAAI,IAAI,aAAa,EAAE;wBACnE,SAAS;qBACZ;oBACD,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE;wBACpD,SAAS;qBACZ;oBAED,gBAAgB,CAAC,OAAO,EAAE,CAAC;oBAC3B,iBAAiB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;iBACtC;gBAED,IAAI,iBAAiB,CAAC,MAAM,IAAI,CAAC,EAAE;oBAC/B,IAAI,CAAC,eAAe,EAAE;wBAClB,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;qBACjD;oBACD,IAAI,CAAC,oBAAoB,EAAE,CAAC;iBAC/B;aACJ;SACJ;aAAM;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAE3D,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;gBACZ,IAAI,CAAC,eAAe,EAAE;oBAClB,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;iBACnD;gBACD,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC;gBAEnC,IAAI,CAAC,oBAAoB,EAAE,CAAC;aAC/B;SACJ;IACL,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAC7B,GAAG,EAAE;gBACD,OAAO,CAAC,IAAI,CAAC,CAAC;YAClB,CAAC,EACD,SAAS,EACT,SAAS,EACT,IAAI,EACJ,IAAI,CACP,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,QAAQ,CAAC,KAAa;QACzB,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAC9B,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE;gBAC5B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;aAC7B;YACD,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,EAAE;YACjC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;YAC/B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC5B;aAAM,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE;YACnC,IAAI,CAAC,iBAAiB,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC;YACpD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC5B;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,EAAE;YAChC,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,gBAAgB,CAAC;YAChD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;SACpC;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE;YACpB,yEAAyE;YACzE,OAAO,IAAI,CAAC;SACf;QAED,YAAY;QACZ,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAClD,IAAI,KAAa,CAAC;QAElB,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACvD,MAAM,SAAS,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC3C,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YACtJ,OAAO,GAAG,OAAO,IAAI,SAAS,CAAC;SAClC;QAED,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC;QAEhC,IAAI,CAAC,OAAO,EAAE;YACV,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,iCAAiC;gBACjC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACrD,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAEhD,iCAAiC;gBACjC,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBACvD,iBAAiB,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC;iBACtC;aACJ;YAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAE5B,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC3B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;gBAC5B,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;gBACvC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;aACzC;SACJ;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;CACJ;AAsMD,KAAK,CAAC,SAAS,CAAC,QAAQ,GAAG;IACvB,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;QACzB,OAAO;KACV;IAED,eAAe;IACf,MAAM,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC;IAC9B,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;QAC1B,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9B,OAAO;SACV;QACD,IAAI,CAAC,kBAAkB,GAAG,GAAG,CAAC;KACjC;IAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,6BAA6B,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;IACvH,IAAI,CAAC,kBAAkB,GAAG,GAAG,CAAC;IAE9B,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC;IAC5C,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;QAC1B,OAAO;KACV;IAED,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,SAAS,CAAC;IACtC,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;IAE1C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;QACrD,MAAM,UAAU,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;QAEtC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,YAAY,EAAE;YAChE,KAAK,EAAE,CAAC,CAAC,oBAAoB;SAChC;KACJ;IAED,0BAA0B;IAC1B,IAAI,CAAC,6BAA6B,EAAE,CAAC;AACzC,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,sBAAsB,GAAG,UACrC,MAAW,EACX,IAAY,EACZ,EAAU,EACV,MAAM,GAAG,GAAG,EACZ,IAAc,EACd,aAAqB,GAAG,EACxB,cAA2B,EAC3B,UAAuB,EACvB,UAAqC,EACrC,eAA4B,EAC5B,UAAU,GAAG,KAAK;IAElB,MAAM,kBAAkB,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;IAC/J,kBAAkB,CAAC,MAAM,GAAG,MAAM,CAAC;IAEnC,OAAO,kBAAkB,CAAC;AAC9B,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,cAAc,GAAG,UAC7B,MAAW,EACX,IAAY,EACZ,EAAU,EACV,IAAc,EACd,aAAqB,GAAG,EACxB,cAA2B,EAC3B,UAAuB,EACvB,WAAW,GAAG,IAAI,EAClB,UAAqC,EACrC,eAA4B,EAC5B,UAAU,GAAG,KAAK;IAElB,IAAI,IAAI,GAAG,EAAE,IAAI,UAAU,GAAG,CAAC,EAAE;QAC7B,UAAU,IAAI,CAAC,CAAC,CAAC;KACpB;IAED,IAAI,WAAW,EAAE;QACb,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;KACrD;IAED,IAAI,CAAC,UAAU,EAAE;QACb,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,SAAS,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;KACjI;IAED,MAAM,yBAAyB,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACzE,mBAAmB;IACnB,IAAI,MAAM,CAAC,UAAU,IAAI,yBAAyB,EAAE;QAChD,UAAU,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;KAC1D;IAED,sBAAsB;IACtB,IAAI,MAAM,CAAC,cAAc,EAAE;QACvB,MAAM,WAAW,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;QAC5C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACrD,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,eAAe,CAAC,CAAC;SAC7I;KACJ;IAED,UAAU,CAAC,KAAK,EAAE,CAAC;IAEnB,OAAO,UAAU,CAAC;AACtB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,uBAAuB,GAAG,UACtC,MAAW,EACX,qBAA8B,EAC9B,IAAY,EACZ,EAAU,EACV,IAAc,EACd,aAAqB,GAAG,EACxB,cAA2B,EAC3B,UAAuB,EACvB,WAAW,GAAG,IAAI,EAClB,UAAqC,EACrC,eAA4B,EAC5B,UAAU,GAAG,KAAK;IAElB,MAAM,QAAQ,GAAG,MAAM,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;IAE9D,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;IACjJ,KAAK,MAAM,KAAK,IAAI,QAAQ,EAAE;QAC1B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;KACnJ;IAED,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,oBAAoB,GAAG,UACnC,MAAW,EACX,UAAuB,EACvB,IAAY,EACZ,EAAU,EACV,IAAc,EACd,UAAmB,EACnB,cAA2B,EAC3B,eAA4B,EAC5B,UAAU,GAAG,KAAK;IAElB,IAAI,UAAU,KAAK,SAAS,EAAE;QAC1B,UAAU,GAAG,GAAG,CAAC;KACpB;IAED,IAAI,IAAI,GAAG,EAAE,IAAI,UAAU,GAAG,CAAC,EAAE;QAC7B,UAAU,IAAI,CAAC,CAAC,CAAC;KACpB;SAAM,IAAI,EAAE,GAAG,IAAI,IAAI,UAAU,GAAG,CAAC,EAAE;QACpC,MAAM,IAAI,GAAG,EAAE,CAAC;QAChB,EAAE,GAAG,IAAI,CAAC;QACV,IAAI,GAAG,IAAI,CAAC;KACf;IAED,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;IAErI,OAAO,UAAU,CAAC;AACtB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,6BAA6B,GAAG,UAC5C,MAAY,EACZ,qBAA8B,EAC9B,UAAuB,EACvB,IAAY,EACZ,EAAU,EACV,IAAc,EACd,UAAmB,EACnB,cAA2B,EAC3B,eAA4B,EAC5B,UAAU,GAAG,KAAK;IAElB,MAAM,QAAQ,GAAG,MAAM,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;IAE9D,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC;IACpI,KAAK,MAAM,KAAK,IAAI,QAAQ,EAAE;QAC1B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC;KACtI;IAED,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,qBAAqB,GAAG,UAAU,MAAW;IACzD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;QACjE,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,MAAM,EAAE;YAClD,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;SACzC;KACJ;IAED,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,yBAAyB,GAAG,UAAU,MAAW;IAC7D,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;QACjE,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,MAAM,EAAE;YAClD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;SAC/C;KACJ;IAED,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF;;;;;GAKG;AACH,KAAK,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,MAAW,EAAE,aAAsB,EAAE,UAAqC;IAChH,MAAM,WAAW,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;IAE3D,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;QAClC,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;KAC9C;AACL,CAAC,CAAC;AAEF;;GAEG;AACH,KAAK,CAAC,SAAS,CAAC,iBAAiB,GAAG;IAChC,IAAI,IAAI,CAAC,kBAAkB,EAAE;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrD,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;SAC/D;QACD,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC;KACtC;IAED,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,eAAe,EAAE;QACtC,KAAK,CAAC,IAAI,EAAE,CAAC;KAChB;AACL,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,sCAAsC,GAAG,UAAU,gBAAkC,EAAE,aAAkB;IACrH,MAAM,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC;IACvC,IAAI,CAAC,mCAAmC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAEjE,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE;QAC/B,MAAM,CAAC,qBAAqB,GAAG,EAAE,CAAC;KACrC;IAED,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE;QAC5D,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,GAAG;YACxD,WAAW,EAAE,CAAC;YACd,mBAAmB,EAAE,CAAC;YACtB,UAAU,EAAE,EAAE;YACd,kBAAkB,EAAE,EAAE;YACtB,aAAa,EAAE,aAAa;SAC/B,CAAC;KACL;IAED,IAAI,gBAAgB,CAAC,UAAU,EAAE;QAC7B,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACpG,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,mBAAmB,IAAI,gBAAgB,CAAC,MAAM,CAAC;KAC5G;SAAM;QACH,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC5F,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,WAAW,IAAI,gBAAgB,CAAC,MAAM,CAAC;KACpG;AACL,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,wCAAwC,GAAG,UAAU,MAMpE;IACG,IAAI,MAAM,CAAC,WAAW,KAAK,CAAC,IAAI,MAAM,CAAC,mBAAmB,KAAK,CAAC,EAAE;QAC9D,OAAO,MAAM,CAAC,aAAa,CAAC;KAC/B;IAED,IAAI,UAAU,GAAG,GAAG,CAAC;IACrB,MAAM,aAAa,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC5C,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3C,MAAM,eAAe,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACjD,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,MAAM,iBAAiB,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAC/C,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;IAE3C,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,YAAY,GAAG,KAAK,CAAC;IACzB,IAAI,MAAM,CAAC,WAAW,GAAG,GAAG,EAAE;QAC1B,uCAAuC;QACvC,KAAK,GAAG,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC;QACjC,aAAa,CAAC,SAAS,CAAC,YAAY,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;KACzE;SAAM;QACH,UAAU,GAAG,CAAC,CAAC;QACf,mCAAmC;QACnC,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC;QAChC,KAAK,GAAG,iBAAiB,CAAC,MAAM,GAAG,UAAU,CAAC;QAC9C,IAAI,KAAK,IAAI,CAAC,EAAE;YACZ,IAAI,MAAM,CAAC,mBAAmB,EAAE;gBAC5B,YAAY,GAAG,IAAI,CAAC;aACvB;iBAAM;gBACH,OAAO,iBAAiB,CAAC,YAAY,CAAC;aACzC;SACJ;QAED,iBAAiB,CAAC,YAAY,CAAC,SAAS,CAAC,YAAY,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;KAC1F;IAED,iCAAiC;IACjC,IAAI,CAAC,YAAY,EAAE;QACf,YAAY,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACjC,aAAa,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAClC,eAAe,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAEpC,KAAK,IAAI,SAAS,GAAG,UAAU,EAAE,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;YAChF,MAAM,gBAAgB,GAAG,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACtD,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC/B,SAAS;aACZ;YAED,KAAK,GAAG,gBAAgB,CAAC,MAAM,GAAG,UAAU,CAAC;YAC7C,MAAM,eAAe,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC9C,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,iBAAiB,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAEnD,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,cAAc,EAAE,iBAAiB,EAAE,eAAe,CAAC,CAAC;YAE5F,cAAc,CAAC,gBAAgB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;YACrD,iBAAiB,CAAC,gBAAgB,CAAC,UAAU,CAAC,GAAG,CAAC,eAAe,EAAE,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;YAC7H,eAAe,CAAC,gBAAgB,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;SAC1D;QAED,eAAe,CAAC,SAAS,EAAE,CAAC;KAC/B;IAED,iCAAiC;IACjC,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC,kBAAkB,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;QAC/E,MAAM,gBAAgB,GAAG,MAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAC9D,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/B,SAAS;SACZ;QAED,MAAM,eAAe,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC9C,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,iBAAiB,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAEnD,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,cAAc,EAAE,iBAAiB,EAAE,eAAe,CAAC,CAAC;QAC5F,cAAc,CAAC,aAAa,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;QAC3D,OAAO,CAAC,SAAS,CAAC,YAAY,EAAE,cAAc,EAAE,gBAAgB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QACvF,eAAe,CAAC,aAAa,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;QACpE,UAAU,CAAC,UAAU,CAAC,eAAe,EAAE,iBAAiB,EAAE,gBAAgB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;QACpG,eAAe,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;KAC5E;IAED,MAAM,SAAS,GAAG,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;IACjH,MAAM,CAAC,YAAY,CAAC,YAAY,EAAE,eAAe,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC;IAC7E,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,2CAA2C,GAAG,UAC1D,MAMC,EACD,aAAyB;IAEzB,IAAI,MAAM,CAAC,WAAW,KAAK,CAAC,IAAI,MAAM,CAAC,mBAAmB,KAAK,CAAC,EAAE;QAC9D,OAAO,aAAa,CAAC;KACxB;IAED,MAAM,iBAAiB,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAC/C,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;IAC3C,IAAI,oBAAoB,GAAG,aAAa,CAAC;IAEzC,IAAI,MAAM,CAAC,WAAW,KAAK,CAAC,IAAI,MAAM,CAAC,mBAAmB,GAAG,CAAC,EAAE;QAC5D,oBAAoB,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;KAChD;SAAM,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;QACvC,UAAU,CAAC,UAAU,CAAC,aAAa,EAAE,iBAAiB,CAAC,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,WAAW,CAAC,EAAE,oBAAoB,CAAC,CAAC;QAE9H,IAAI,MAAM,CAAC,mBAAmB,KAAK,CAAC,EAAE;YAClC,OAAO,oBAAoB,CAAC;SAC/B;KACJ;SAAM,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;QACrC,iCAAiC;QACjC,IAAI,UAAU,GAAG,GAAG,CAAC;QACrB,IAAI,WAA8B,CAAC;QACnC,IAAI,OAAsB,CAAC;QAE3B,IAAI,MAAM,CAAC,WAAW,GAAG,GAAG,EAAE;YAC1B,MAAM,KAAK,GAAG,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC;YAEvC,WAAW,GAAG,EAAE,CAAC;YACjB,OAAO,GAAG,EAAE,CAAC;YAEb,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACvB;aAAM;YACH,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;gBAChC,0BAA0B;gBAC1B,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;gBAE7J,IAAI,MAAM,CAAC,mBAAmB,KAAK,CAAC,EAAE;oBAClC,OAAO,aAAa,CAAC;iBACxB;aACJ;YAED,WAAW,GAAG,EAAE,CAAC;YACjB,OAAO,GAAG,EAAE,CAAC;YACb,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC;SACnC;QAED,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;YACvE,MAAM,gBAAgB,GAAG,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACtD,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;YAChD,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC;SACtD;QAED,mGAAmG;QAEnG,IAAI,gBAAgB,GAAG,CAAC,CAAC;QACzB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,WAAW,CAAC,MAAM,GAAI;YAC9C,IAAI,CAAC,KAAK,EAAE;gBACR,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;gBAC7I,oBAAoB,GAAG,aAAa,CAAC;gBACrC,gBAAgB,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBACvD,KAAK,IAAI,CAAC,CAAC;gBACX,SAAS;aACZ;YACD,gBAAgB,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC;YACnC,UAAU,CAAC,UAAU,CAAC,oBAAoB,EAAE,WAAW,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,gBAAgB,EAAE,oBAAoB,CAAC,CAAC;YACzH,KAAK,EAAE,CAAC;SACX;KACJ;IAED,iCAAiC;IACjC,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC,kBAAkB,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;QAC/E,MAAM,gBAAgB,GAAG,MAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAC9D,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/B,SAAS;SACZ;QAED,oBAAoB,CAAC,aAAa,CAAC,gBAAgB,CAAC,YAAY,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5F,UAAU,CAAC,UAAU,CAAC,oBAAoB,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAAC;KACxH;IAED,OAAO,oBAAqB,CAAC;AACjC,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,6BAA6B,GAAG;IAC5C,IAAI,CAAC,IAAI,CAAC,mCAAmC,CAAC,MAAM,EAAE;QAClD,OAAO;KACV;IACD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,mCAAmC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;QAClF,MAAM,MAAM,GAAG,IAAI,CAAC,mCAAmC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEpE,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,qBAAqB,EAAE;YAC7C,MAAM,MAAM,GAAG,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,iBAAiB,GAAqB,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACjE,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;YAC3C,IAAI,aAAa,KAAK,SAAS,IAAI,aAAa,KAAK,IAAI,EAAE;gBACvD,SAAS;aACZ;YACD,MAAM,mBAAmB,GAAG,SAAS,CAAC,oCAAoC,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,qBAAqB;YAEpH,IAAI,UAAU,GAAQ,MAAM,CAAC,IAAI,CAAC,CAAC;YACnC,IAAI,mBAAmB,EAAE;gBACrB,UAAU,GAAG,IAAI,CAAC,wCAAwC,CAAC,MAAM,CAAC,CAAC;aACtE;iBAAM;gBACH,MAAM,cAAc,GAAG,aAAa,CAAC,CAAC,KAAK,SAAS,CAAC;gBACrD,IAAI,cAAc,EAAE;oBAChB,UAAU,GAAG,IAAI,CAAC,2CAA2C,CAAC,MAAM,EAAE,UAAU,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;iBAC9G;qBAAM;oBACH,IAAI,UAAU,GAAG,CAAC,CAAC;oBACnB,IAAI,UAAU,GAAG,GAAG,CAAC;oBAErB,IAAI,MAAM,CAAC,WAAW,GAAG,GAAG,EAAE;wBAC1B,uCAAuC;wBACvC,IAAI,iBAAiB,IAAI,aAAa,CAAC,KAAK,EAAE;4BAC1C,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;yBAC9D;6BAAM,IAAI,iBAAiB,EAAE;4BAC1B,UAAU,GAAG,aAAa,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;yBAC3D;6BAAM,IAAI,aAAa,CAAC,KAAK,EAAE;4BAC5B,UAAU,GAAG,aAAa,CAAC,KAAK,EAAE,CAAC;yBACtC;6BAAM;4BACH,UAAU,GAAG,aAAa,CAAC;yBAC9B;qBACJ;yBAAM,IAAI,iBAAiB,EAAE;wBAC1B,mCAAmC;wBACnC,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC;wBAChC,MAAM,KAAK,GAAG,iBAAiB,CAAC,MAAM,GAAG,UAAU,CAAC;wBACpD,IAAI,KAAK,KAAK,CAAC,EAAE;4BACb,IAAI,iBAAiB,CAAC,YAAY,CAAC,KAAK,EAAE;gCACtC,UAAU,GAAG,iBAAiB,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;6BAC5D;iCAAM;gCACH,UAAU,GAAG,iBAAiB,CAAC,YAAY,GAAG,KAAK,CAAC;6BACvD;yBACJ;6BAAM;4BACH,UAAU,GAAG,iBAAiB,CAAC,YAAY,CAAC;yBAC/C;wBAED,UAAU,GAAG,CAAC,CAAC;qBAClB;oBAED,iCAAiC;oBACjC,KAAK,IAAI,SAAS,GAAG,UAAU,EAAE,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;wBAChF,MAAM,gBAAgB,GAAG,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;wBACtD,MAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,GAAG,UAAU,CAAC;wBAEnD,IAAI,CAAC,KAAK,EAAE;4BACR,SAAS;yBACZ;6BAAM,IAAI,gBAAgB,CAAC,YAAY,CAAC,gBAAgB,EAAE;4BACvD,gBAAgB,CAAC,YAAY,CAAC,gBAAgB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;yBACrE;6BAAM;4BACH,UAAU,IAAI,gBAAgB,CAAC,YAAY,GAAG,KAAK,CAAC;yBACvD;qBACJ;oBAED,iCAAiC;oBACjC,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC,kBAAkB,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;wBAC/E,MAAM,gBAAgB,GAAG,MAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;wBAC9D,MAAM,KAAK,GAAW,gBAAgB,CAAC,MAAM,CAAC;wBAE9C,IAAI,CAAC,KAAK,EAAE;4BACR,SAAS;yBACZ;6BAAM,IAAI,gBAAgB,CAAC,YAAY,CAAC,gBAAgB,EAAE;4BACvD,gBAAgB,CAAC,YAAY,CAAC,gBAAgB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;yBACrE;6BAAM;4BACH,UAAU,IAAI,gBAAgB,CAAC,YAAY,GAAG,KAAK,CAAC;yBACvD;qBACJ;iBACJ;aACJ;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;SAC7B;QAED,MAAM,CAAC,qBAAqB,GAAG,EAAE,CAAC;KACrC;IACD,IAAI,CAAC,mCAAmC,CAAC,KAAK,EAAE,CAAC;AACrD,CAAC,CAAC;AAiBF,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAChC,MAAY,EACZ,SAAiB,EACjB,WAAmB,EACnB,iBAAiB,GAAG,KAAK,EACzB,sBAAyC,IAAI;IAE7C,uFAAuF;IACvF,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;QAC9B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC,CAAC;QAClI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;KAClC;IAED,yEAAyE;IACzE,MAAM,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAC7D,IAAI,CAAC,WAAW,EAAE;QACd,OAAO,KAAK,CAAC;KAChB;IACD,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;IAC9B,MAAM,EAAE,GAAG,WAAW,CAAC,EAAE,CAAC;IAC1B,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;IAElD,iBAAiB;IACjB,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC;IACvC,MAAM,YAAY,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;IACxC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;IAChC,MAAM,iBAAiB,GAAG,iBAAiB,IAAI,YAAY,IAAI,gBAAgB,IAAI,IAAI,CAAC,MAAM,IAAI,gBAAgB,KAAK,IAAI,CAAC,MAAM,CAAC;IACnI,MAAM,WAAW,GAAG,iBAAiB,IAAI,MAAM,IAAI,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAE1G,MAAM,qBAAqB,GACvB,iBAAiB,IAAI,CAAC,MAAM,IAAI,mBAAmB,IAAI,CAAC,mBAAmB,CAAC,CAAC,KAAK,CAAC,IAAI,mBAAmB,CAAC,CAAC,KAAK,CAAC,IAAI,mBAAmB,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IAEvJ,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;IAE9C,wBAAwB;IACxB,IAAI,IAAsC,CAAC;IAC3C,IAAI,eAAwB,CAAC;IAC7B,IAAI,GAAW,CAAC;IAEhB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,GAAG,KAAK,EAAE,GAAG,EAAE,EAAE;QAC7D,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;QACvB,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,EAAE;YACxC,IAAI,iBAAiB,EAAE;gBACnB,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;gBAEzB,oDAAoD;gBACpD,IAAI,iBAAiB,EAAE;oBACnB,eAAe,GAAG,GAAG,CAAC,cAAc,EAAE,CAAC;oBACvC,GAAG,CAAC,cAAc,CAAC,eAAe,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC;oBAE9D,8EAA8E;iBACjF;qBAAM,IAAI,qBAAqB,IAAI,mBAAmB,EAAE;oBACrD,eAAe,GAAG,GAAG,CAAC,cAAc,EAAE,CAAC;oBACvC,GAAG,CAAC,cAAc,CAAC,eAAe,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,CAAC;oBAEzE,mEAAmE;iBACtE;qBAAM;oBACH,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;iBACpB;aACJ;iBAAM;gBACH,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;aACpB;YACD,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;SAClE;KACJ;IACD,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,GAAG,WAAW,EAAE,EAAE,GAAG,WAAW,CAAC,CAAC;IAChF,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC", "sourcesContent": ["import { Animation } from \"./animation\";\r\nimport { RuntimeAnimation } from \"./runtimeAnimation\";\r\n\r\nimport type { Nullable } from \"../types\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { Scene } from \"../scene\";\r\nimport { Matrix, Quaternion, Vector3, TmpVectors } from \"../Maths/math.vector\";\r\nimport { PrecisionDate } from \"../Misc/precisionDate\";\r\nimport { Bone } from \"../Bones/bone\";\r\nimport type { Node } from \"../node\";\r\n\r\n/**\r\n * Class used to store an actual running animation\r\n */\r\nexport class Animatable {\r\n    private _localDelayOffset: Nullable<number> = null;\r\n    private _pausedDelay: Nullable<number> = null;\r\n    private _manualJumpDelay: Nullable<number> = null;\r\n    /** @hidden */\r\n    public _runtimeAnimations = new Array<RuntimeAnimation>();\r\n    private _paused = false;\r\n    private _scene: Scene;\r\n    private _speedRatio = 1;\r\n    private _weight = -1.0;\r\n    private _syncRoot: Nullable<Animatable> = null;\r\n    private _frameToSyncFromJump: Nullable<number> = null;\r\n    private _goToFrame: Nullable<number> = null;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the animatable must be disposed and removed at the end of the animation.\r\n     * This will only apply for non looping animation (default is true)\r\n     */\r\n    public disposeOnEnd = true;\r\n\r\n    /**\r\n     * Gets a boolean indicating if the animation has started\r\n     */\r\n    public animationStarted = false;\r\n\r\n    /**\r\n     * Observer raised when the animation ends\r\n     */\r\n    public onAnimationEndObservable = new Observable<Animatable>();\r\n\r\n    /**\r\n     * Observer raised when the animation loops\r\n     */\r\n    public onAnimationLoopObservable = new Observable<Animatable>();\r\n\r\n    /**\r\n     * Gets the root Animatable used to synchronize and normalize animations\r\n     */\r\n    public get syncRoot(): Nullable<Animatable> {\r\n        return this._syncRoot;\r\n    }\r\n\r\n    /**\r\n     * Gets the current frame of the first RuntimeAnimation\r\n     * Used to synchronize Animatables\r\n     */\r\n    public get masterFrame(): number {\r\n        if (this._runtimeAnimations.length === 0) {\r\n            return 0;\r\n        }\r\n\r\n        return this._runtimeAnimations[0].currentFrame;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the animatable weight (-1.0 by default meaning not weighted)\r\n     */\r\n    public get weight(): number {\r\n        return this._weight;\r\n    }\r\n\r\n    public set weight(value: number) {\r\n        if (value === -1) {\r\n            // -1 is ok and means no weight\r\n            this._weight = -1;\r\n            return;\r\n        }\r\n\r\n        // Else weight must be in [0, 1] range\r\n        this._weight = Math.min(Math.max(value, 0), 1.0);\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the speed ratio to apply to the animatable (1.0 by default)\r\n     */\r\n    public get speedRatio(): number {\r\n        return this._speedRatio;\r\n    }\r\n\r\n    public set speedRatio(value: number) {\r\n        for (let index = 0; index < this._runtimeAnimations.length; index++) {\r\n            const animation = this._runtimeAnimations[index];\r\n\r\n            animation._prepareForSpeedRatioChange(value);\r\n        }\r\n        this._speedRatio = value;\r\n\r\n        // Resync _manualJumpDelay in case goToFrame was called before speedRatio was set.\r\n        if (this._goToFrame !== null) {\r\n            this.goToFrame(this._goToFrame);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Creates a new Animatable\r\n     * @param scene defines the hosting scene\r\n     * @param target defines the target object\r\n     * @param fromFrame defines the starting frame number (default is 0)\r\n     * @param toFrame defines the ending frame number (default is 100)\r\n     * @param loopAnimation defines if the animation must loop (default is false)\r\n     * @param speedRatio defines the factor to apply to animation speed (default is 1)\r\n     * @param onAnimationEnd defines a callback to call when animation ends if it is not looping\r\n     * @param animations defines a group of animation to add to the new Animatable\r\n     * @param onAnimationLoop defines a callback to call when animation loops\r\n     * @param isAdditive defines whether the animation should be evaluated additively\r\n     */\r\n    constructor(\r\n        scene: Scene,\r\n        /** defines the target object */\r\n        public target: any,\r\n        /** defines the starting frame number (default is 0) */\r\n        public fromFrame: number = 0,\r\n        /** defines the ending frame number (default is 100) */\r\n        public toFrame: number = 100,\r\n        /** defines if the animation must loop (default is false)  */\r\n        public loopAnimation: boolean = false,\r\n        speedRatio: number = 1.0,\r\n        /** defines a callback to call when animation ends if it is not looping */\r\n        public onAnimationEnd?: Nullable<() => void>,\r\n        animations?: Animation[],\r\n        /** defines a callback to call when animation loops */\r\n        public onAnimationLoop?: Nullable<() => void>,\r\n        /** defines whether the animation should be evaluated additively */\r\n        public isAdditive: boolean = false\r\n    ) {\r\n        this._scene = scene;\r\n        if (animations) {\r\n            this.appendAnimations(target, animations);\r\n        }\r\n\r\n        this._speedRatio = speedRatio;\r\n        scene._activeAnimatables.push(this);\r\n    }\r\n\r\n    // Methods\r\n    /**\r\n     * Synchronize and normalize current Animatable with a source Animatable\r\n     * This is useful when using animation weights and when animations are not of the same length\r\n     * @param root defines the root Animatable to synchronize with (null to stop synchronizing)\r\n     * @returns the current Animatable\r\n     */\r\n    public syncWith(root: Nullable<Animatable>): Animatable {\r\n        this._syncRoot = root;\r\n\r\n        if (root) {\r\n            // Make sure this animatable will animate after the root\r\n            const index = this._scene._activeAnimatables.indexOf(this);\r\n            if (index > -1) {\r\n                this._scene._activeAnimatables.splice(index, 1);\r\n                this._scene._activeAnimatables.push(this);\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of runtime animations\r\n     * @returns an array of RuntimeAnimation\r\n     */\r\n    public getAnimations(): RuntimeAnimation[] {\r\n        return this._runtimeAnimations;\r\n    }\r\n\r\n    /**\r\n     * Adds more animations to the current animatable\r\n     * @param target defines the target of the animations\r\n     * @param animations defines the new animations to add\r\n     */\r\n    public appendAnimations(target: any, animations: Animation[]): void {\r\n        for (let index = 0; index < animations.length; index++) {\r\n            const animation = animations[index];\r\n\r\n            const newRuntimeAnimation = new RuntimeAnimation(target, animation, this._scene, this);\r\n            newRuntimeAnimation._onLoop = () => {\r\n                this.onAnimationLoopObservable.notifyObservers(this);\r\n                if (this.onAnimationLoop) {\r\n                    this.onAnimationLoop();\r\n                }\r\n            };\r\n\r\n            this._runtimeAnimations.push(newRuntimeAnimation);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the source animation for a specific property\r\n     * @param property defines the property to look for\r\n     * @returns null or the source animation for the given property\r\n     */\r\n    public getAnimationByTargetProperty(property: string): Nullable<Animation> {\r\n        const runtimeAnimations = this._runtimeAnimations;\r\n\r\n        for (let index = 0; index < runtimeAnimations.length; index++) {\r\n            if (runtimeAnimations[index].animation.targetProperty === property) {\r\n                return runtimeAnimations[index].animation;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets the runtime animation for a specific property\r\n     * @param property defines the property to look for\r\n     * @returns null or the runtime animation for the given property\r\n     */\r\n    public getRuntimeAnimationByTargetProperty(property: string): Nullable<RuntimeAnimation> {\r\n        const runtimeAnimations = this._runtimeAnimations;\r\n\r\n        for (let index = 0; index < runtimeAnimations.length; index++) {\r\n            if (runtimeAnimations[index].animation.targetProperty === property) {\r\n                return runtimeAnimations[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Resets the animatable to its original state\r\n     */\r\n    public reset(): void {\r\n        const runtimeAnimations = this._runtimeAnimations;\r\n\r\n        for (let index = 0; index < runtimeAnimations.length; index++) {\r\n            runtimeAnimations[index].reset(true);\r\n        }\r\n\r\n        this._localDelayOffset = null;\r\n        this._pausedDelay = null;\r\n    }\r\n\r\n    /**\r\n     * Allows the animatable to blend with current running animations\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-blending\r\n     * @param blendingSpeed defines the blending speed to use\r\n     */\r\n    public enableBlending(blendingSpeed: number): void {\r\n        const runtimeAnimations = this._runtimeAnimations;\r\n\r\n        for (let index = 0; index < runtimeAnimations.length; index++) {\r\n            runtimeAnimations[index].animation.enableBlending = true;\r\n            runtimeAnimations[index].animation.blendingSpeed = blendingSpeed;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disable animation blending\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-blending\r\n     */\r\n    public disableBlending(): void {\r\n        const runtimeAnimations = this._runtimeAnimations;\r\n\r\n        for (let index = 0; index < runtimeAnimations.length; index++) {\r\n            runtimeAnimations[index].animation.enableBlending = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Jump directly to a given frame\r\n     * @param frame defines the frame to jump to\r\n     */\r\n    public goToFrame(frame: number): void {\r\n        const runtimeAnimations = this._runtimeAnimations;\r\n\r\n        if (runtimeAnimations[0]) {\r\n            const fps = runtimeAnimations[0].animation.framePerSecond;\r\n            this._frameToSyncFromJump = this._frameToSyncFromJump ?? runtimeAnimations[0].currentFrame;\r\n            const delay = this.speedRatio === 0 ? 0 : (((frame - this._frameToSyncFromJump) / fps) * 1000) / this.speedRatio;\r\n            this._manualJumpDelay = -delay;\r\n        }\r\n\r\n        for (let index = 0; index < runtimeAnimations.length; index++) {\r\n            runtimeAnimations[index].goToFrame(frame);\r\n        }\r\n\r\n        this._goToFrame = frame;\r\n    }\r\n\r\n    /**\r\n     * Pause the animation\r\n     */\r\n    public pause(): void {\r\n        if (this._paused) {\r\n            return;\r\n        }\r\n        this._paused = true;\r\n    }\r\n\r\n    /**\r\n     * Restart the animation\r\n     */\r\n    public restart(): void {\r\n        this._paused = false;\r\n    }\r\n\r\n    private _raiseOnAnimationEnd() {\r\n        if (this.onAnimationEnd) {\r\n            this.onAnimationEnd();\r\n        }\r\n\r\n        this.onAnimationEndObservable.notifyObservers(this);\r\n    }\r\n\r\n    /**\r\n     * Stop and delete the current animation\r\n     * @param animationName defines a string used to only stop some of the runtime animations instead of all\r\n     * @param targetMask a function that determines if the animation should be stopped based on its target (all animations will be stopped if both this and animationName are empty)\r\n     * @param useGlobalSplice if true, the animatables will be removed by the caller of this function (false by default)\r\n     */\r\n    public stop(animationName?: string, targetMask?: (target: any) => boolean, useGlobalSplice = false): void {\r\n        if (animationName || targetMask) {\r\n            const idx = this._scene._activeAnimatables.indexOf(this);\r\n\r\n            if (idx > -1) {\r\n                const runtimeAnimations = this._runtimeAnimations;\r\n\r\n                for (let index = runtimeAnimations.length - 1; index >= 0; index--) {\r\n                    const runtimeAnimation = runtimeAnimations[index];\r\n                    if (animationName && runtimeAnimation.animation.name != animationName) {\r\n                        continue;\r\n                    }\r\n                    if (targetMask && !targetMask(runtimeAnimation.target)) {\r\n                        continue;\r\n                    }\r\n\r\n                    runtimeAnimation.dispose();\r\n                    runtimeAnimations.splice(index, 1);\r\n                }\r\n\r\n                if (runtimeAnimations.length == 0) {\r\n                    if (!useGlobalSplice) {\r\n                        this._scene._activeAnimatables.splice(idx, 1);\r\n                    }\r\n                    this._raiseOnAnimationEnd();\r\n                }\r\n            }\r\n        } else {\r\n            const index = this._scene._activeAnimatables.indexOf(this);\r\n\r\n            if (index > -1) {\r\n                if (!useGlobalSplice) {\r\n                    this._scene._activeAnimatables.splice(index, 1);\r\n                }\r\n                this._runtimeAnimations.length = 0;\r\n\r\n                this._raiseOnAnimationEnd();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Wait asynchronously for the animation to end\r\n     * @returns a promise which will be fulfilled when the animation ends\r\n     */\r\n    public waitAsync(): Promise<Animatable> {\r\n        return new Promise((resolve) => {\r\n            this.onAnimationEndObservable.add(\r\n                () => {\r\n                    resolve(this);\r\n                },\r\n                undefined,\r\n                undefined,\r\n                this,\r\n                true\r\n            );\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _animate(delay: number): boolean {\r\n        if (this._paused) {\r\n            this.animationStarted = false;\r\n            if (this._pausedDelay === null) {\r\n                this._pausedDelay = delay;\r\n            }\r\n            return true;\r\n        }\r\n\r\n        if (this._localDelayOffset === null) {\r\n            this._localDelayOffset = delay;\r\n            this._pausedDelay = null;\r\n        } else if (this._pausedDelay !== null) {\r\n            this._localDelayOffset += delay - this._pausedDelay;\r\n            this._pausedDelay = null;\r\n        }\r\n\r\n        if (this._manualJumpDelay !== null) {\r\n            this._localDelayOffset += this._manualJumpDelay;\r\n            this._manualJumpDelay = null;\r\n            this._frameToSyncFromJump = null;\r\n        }\r\n\r\n        this._goToFrame = null;\r\n\r\n        if (this._weight === 0) {\r\n            // We consider that an animation with a weight === 0 is \"actively\" paused\r\n            return true;\r\n        }\r\n\r\n        // Animating\r\n        let running = false;\r\n        const runtimeAnimations = this._runtimeAnimations;\r\n        let index: number;\r\n\r\n        for (index = 0; index < runtimeAnimations.length; index++) {\r\n            const animation = runtimeAnimations[index];\r\n            const isRunning = animation.animate(delay - this._localDelayOffset, this.fromFrame, this.toFrame, this.loopAnimation, this._speedRatio, this._weight);\r\n            running = running || isRunning;\r\n        }\r\n\r\n        this.animationStarted = running;\r\n\r\n        if (!running) {\r\n            if (this.disposeOnEnd) {\r\n                // Remove from active animatables\r\n                index = this._scene._activeAnimatables.indexOf(this);\r\n                this._scene._activeAnimatables.splice(index, 1);\r\n\r\n                // Dispose all runtime animations\r\n                for (index = 0; index < runtimeAnimations.length; index++) {\r\n                    runtimeAnimations[index].dispose();\r\n                }\r\n            }\r\n\r\n            this._raiseOnAnimationEnd();\r\n\r\n            if (this.disposeOnEnd) {\r\n                this.onAnimationEnd = null;\r\n                this.onAnimationLoop = null;\r\n                this.onAnimationLoopObservable.clear();\r\n                this.onAnimationEndObservable.clear();\r\n            }\r\n        }\r\n\r\n        return running;\r\n    }\r\n}\r\n\r\ndeclare module \"../scene\" {\r\n    export interface Scene {\r\n        /** @internal */\r\n        _registerTargetForLateAnimationBinding(runtimeAnimation: RuntimeAnimation, originalValue: any): void;\r\n\r\n        /** @internal */\r\n        _processLateAnimationBindingsForMatrices(holder: {\r\n            totalWeight: number;\r\n            totalAdditiveWeight: number;\r\n            animations: RuntimeAnimation[];\r\n            additiveAnimations: RuntimeAnimation[];\r\n            originalValue: Matrix;\r\n        }): any;\r\n\r\n        /** @internal */\r\n        _processLateAnimationBindingsForQuaternions(\r\n            holder: {\r\n                totalWeight: number;\r\n                totalAdditiveWeight: number;\r\n                animations: RuntimeAnimation[];\r\n                additiveAnimations: RuntimeAnimation[];\r\n                originalValue: Quaternion;\r\n            },\r\n            refQuaternion: Quaternion\r\n        ): Quaternion;\r\n\r\n        /** @internal */\r\n        _processLateAnimationBindings(): void;\r\n\r\n        /**\r\n         * Will start the animation sequence of a given target\r\n         * @param target defines the target\r\n         * @param from defines from which frame should animation start\r\n         * @param to defines until which frame should animation run.\r\n         * @param weight defines the weight to apply to the animation (1.0 by default)\r\n         * @param loop defines if the animation loops\r\n         * @param speedRatio defines the speed in which to run the animation (1.0 by default)\r\n         * @param onAnimationEnd defines the function to be executed when the animation ends\r\n         * @param animatable defines an animatable object. If not provided a new one will be created from the given params\r\n         * @param targetMask defines if the target should be animated if animations are present (this is called recursively on descendant animatables regardless of return value)\r\n         * @param onAnimationLoop defines the callback to call when an animation loops\r\n         * @param isAdditive defines whether the animation should be evaluated additively (false by default)\r\n         * @returns the animatable object created for this animation\r\n         */\r\n        beginWeightedAnimation(\r\n            target: any,\r\n            from: number,\r\n            to: number,\r\n            weight: number,\r\n            loop?: boolean,\r\n            speedRatio?: number,\r\n            onAnimationEnd?: () => void,\r\n            animatable?: Animatable,\r\n            targetMask?: (target: any) => boolean,\r\n            onAnimationLoop?: () => void,\r\n            isAdditive?: boolean\r\n        ): Animatable;\r\n\r\n        /**\r\n         * Will start the animation sequence of a given target\r\n         * @param target defines the target\r\n         * @param from defines from which frame should animation start\r\n         * @param to defines until which frame should animation run.\r\n         * @param loop defines if the animation loops\r\n         * @param speedRatio defines the speed in which to run the animation (1.0 by default)\r\n         * @param onAnimationEnd defines the function to be executed when the animation ends\r\n         * @param animatable defines an animatable object. If not provided a new one will be created from the given params\r\n         * @param stopCurrent defines if the current animations must be stopped first (true by default)\r\n         * @param targetMask defines if the target should be animate if animations are present (this is called recursively on descendant animatables regardless of return value)\r\n         * @param onAnimationLoop defines the callback to call when an animation loops\r\n         * @param isAdditive defines whether the animation should be evaluated additively (false by default)\r\n         * @returns the animatable object created for this animation\r\n         */\r\n        beginAnimation(\r\n            target: any,\r\n            from: number,\r\n            to: number,\r\n            loop?: boolean,\r\n            speedRatio?: number,\r\n            onAnimationEnd?: () => void,\r\n            animatable?: Animatable,\r\n            stopCurrent?: boolean,\r\n            targetMask?: (target: any) => boolean,\r\n            onAnimationLoop?: () => void,\r\n            isAdditive?: boolean\r\n        ): Animatable;\r\n\r\n        /**\r\n         * Will start the animation sequence of a given target and its hierarchy\r\n         * @param target defines the target\r\n         * @param directDescendantsOnly if true only direct descendants will be used, if false direct and also indirect (children of children, an so on in a recursive manner) descendants will be used.\r\n         * @param from defines from which frame should animation start\r\n         * @param to defines until which frame should animation run.\r\n         * @param loop defines if the animation loops\r\n         * @param speedRatio defines the speed in which to run the animation (1.0 by default)\r\n         * @param onAnimationEnd defines the function to be executed when the animation ends\r\n         * @param animatable defines an animatable object. If not provided a new one will be created from the given params\r\n         * @param stopCurrent defines if the current animations must be stopped first (true by default)\r\n         * @param targetMask defines if the target should be animated if animations are present (this is called recursively on descendant animatables regardless of return value)\r\n         * @param onAnimationLoop defines the callback to call when an animation loops\r\n         * @param isAdditive defines whether the animation should be evaluated additively (false by default)\r\n         * @returns the list of created animatables\r\n         */\r\n        beginHierarchyAnimation(\r\n            target: any,\r\n            directDescendantsOnly: boolean,\r\n            from: number,\r\n            to: number,\r\n            loop?: boolean,\r\n            speedRatio?: number,\r\n            onAnimationEnd?: () => void,\r\n            animatable?: Animatable,\r\n            stopCurrent?: boolean,\r\n            targetMask?: (target: any) => boolean,\r\n            onAnimationLoop?: () => void,\r\n            isAdditive?: boolean\r\n        ): Animatable[];\r\n\r\n        /**\r\n         * Begin a new animation on a given node\r\n         * @param target defines the target where the animation will take place\r\n         * @param animations defines the list of animations to start\r\n         * @param from defines the initial value\r\n         * @param to defines the final value\r\n         * @param loop defines if you want animation to loop (off by default)\r\n         * @param speedRatio defines the speed ratio to apply to all animations\r\n         * @param onAnimationEnd defines the callback to call when an animation ends (will be called once per node)\r\n         * @param onAnimationLoop defines the callback to call when an animation loops\r\n         * @param isAdditive defines whether the animation should be evaluated additively (false by default)\r\n         * @returns the list of created animatables\r\n         */\r\n        beginDirectAnimation(\r\n            target: any,\r\n            animations: Animation[],\r\n            from: number,\r\n            to: number,\r\n            loop?: boolean,\r\n            speedRatio?: number,\r\n            onAnimationEnd?: () => void,\r\n            onAnimationLoop?: () => void,\r\n            isAdditive?: boolean\r\n        ): Animatable;\r\n\r\n        /**\r\n         * Begin a new animation on a given node and its hierarchy\r\n         * @param target defines the root node where the animation will take place\r\n         * @param directDescendantsOnly if true only direct descendants will be used, if false direct and also indirect (children of children, an so on in a recursive manner) descendants will be used.\r\n         * @param animations defines the list of animations to start\r\n         * @param from defines the initial value\r\n         * @param to defines the final value\r\n         * @param loop defines if you want animation to loop (off by default)\r\n         * @param speedRatio defines the speed ratio to apply to all animations\r\n         * @param onAnimationEnd defines the callback to call when an animation ends (will be called once per node)\r\n         * @param onAnimationLoop defines the callback to call when an animation loops\r\n         * @param isAdditive defines whether the animation should be evaluated additively (false by default)\r\n         * @returns the list of animatables created for all nodes\r\n         */\r\n        beginDirectHierarchyAnimation(\r\n            target: Node,\r\n            directDescendantsOnly: boolean,\r\n            animations: Animation[],\r\n            from: number,\r\n            to: number,\r\n            loop?: boolean,\r\n            speedRatio?: number,\r\n            onAnimationEnd?: () => void,\r\n            onAnimationLoop?: () => void,\r\n            isAdditive?: boolean\r\n        ): Animatable[];\r\n\r\n        /**\r\n         * Gets the animatable associated with a specific target\r\n         * @param target defines the target of the animatable\r\n         * @returns the required animatable if found\r\n         */\r\n        getAnimatableByTarget(target: any): Nullable<Animatable>;\r\n\r\n        /**\r\n         * Gets all animatables associated with a given target\r\n         * @param target defines the target to look animatables for\r\n         * @returns an array of Animatables\r\n         */\r\n        getAllAnimatablesByTarget(target: any): Array<Animatable>;\r\n\r\n        /**\r\n         * Stops and removes all animations that have been applied to the scene\r\n         */\r\n        stopAllAnimations(): void;\r\n\r\n        /**\r\n         * Gets the current delta time used by animation engine\r\n         */\r\n        deltaTime: number;\r\n    }\r\n}\r\n\r\nScene.prototype._animate = function (): void {\r\n    if (!this.animationsEnabled) {\r\n        return;\r\n    }\r\n\r\n    // Getting time\r\n    const now = PrecisionDate.Now;\r\n    if (!this._animationTimeLast) {\r\n        if (this._pendingData.length > 0) {\r\n            return;\r\n        }\r\n        this._animationTimeLast = now;\r\n    }\r\n\r\n    this.deltaTime = this.useConstantAnimationDeltaTime ? 16.0 : (now - this._animationTimeLast) * this.animationTimeScale;\r\n    this._animationTimeLast = now;\r\n\r\n    const animatables = this._activeAnimatables;\r\n    if (animatables.length === 0) {\r\n        return;\r\n    }\r\n\r\n    this._animationTime += this.deltaTime;\r\n    const animationTime = this._animationTime;\r\n\r\n    for (let index = 0; index < animatables.length; index++) {\r\n        const animatable = animatables[index];\r\n\r\n        if (!animatable._animate(animationTime) && animatable.disposeOnEnd) {\r\n            index--; // Array was updated\r\n        }\r\n    }\r\n\r\n    // Late animation bindings\r\n    this._processLateAnimationBindings();\r\n};\r\n\r\nScene.prototype.beginWeightedAnimation = function (\r\n    target: any,\r\n    from: number,\r\n    to: number,\r\n    weight = 1.0,\r\n    loop?: boolean,\r\n    speedRatio: number = 1.0,\r\n    onAnimationEnd?: () => void,\r\n    animatable?: Animatable,\r\n    targetMask?: (target: any) => boolean,\r\n    onAnimationLoop?: () => void,\r\n    isAdditive = false\r\n): Animatable {\r\n    const returnedAnimatable = this.beginAnimation(target, from, to, loop, speedRatio, onAnimationEnd, animatable, false, targetMask, onAnimationLoop, isAdditive);\r\n    returnedAnimatable.weight = weight;\r\n\r\n    return returnedAnimatable;\r\n};\r\n\r\nScene.prototype.beginAnimation = function (\r\n    target: any,\r\n    from: number,\r\n    to: number,\r\n    loop?: boolean,\r\n    speedRatio: number = 1.0,\r\n    onAnimationEnd?: () => void,\r\n    animatable?: Animatable,\r\n    stopCurrent = true,\r\n    targetMask?: (target: any) => boolean,\r\n    onAnimationLoop?: () => void,\r\n    isAdditive = false\r\n): Animatable {\r\n    if (from > to && speedRatio > 0) {\r\n        speedRatio *= -1;\r\n    }\r\n\r\n    if (stopCurrent) {\r\n        this.stopAnimation(target, undefined, targetMask);\r\n    }\r\n\r\n    if (!animatable) {\r\n        animatable = new Animatable(this, target, from, to, loop, speedRatio, onAnimationEnd, undefined, onAnimationLoop, isAdditive);\r\n    }\r\n\r\n    const shouldRunTargetAnimations = targetMask ? targetMask(target) : true;\r\n    // Local animations\r\n    if (target.animations && shouldRunTargetAnimations) {\r\n        animatable.appendAnimations(target, target.animations);\r\n    }\r\n\r\n    // Children animations\r\n    if (target.getAnimatables) {\r\n        const animatables = target.getAnimatables();\r\n        for (let index = 0; index < animatables.length; index++) {\r\n            this.beginAnimation(animatables[index], from, to, loop, speedRatio, onAnimationEnd, animatable, stopCurrent, targetMask, onAnimationLoop);\r\n        }\r\n    }\r\n\r\n    animatable.reset();\r\n\r\n    return animatable;\r\n};\r\n\r\nScene.prototype.beginHierarchyAnimation = function (\r\n    target: any,\r\n    directDescendantsOnly: boolean,\r\n    from: number,\r\n    to: number,\r\n    loop?: boolean,\r\n    speedRatio: number = 1.0,\r\n    onAnimationEnd?: () => void,\r\n    animatable?: Animatable,\r\n    stopCurrent = true,\r\n    targetMask?: (target: any) => boolean,\r\n    onAnimationLoop?: () => void,\r\n    isAdditive = false\r\n): Animatable[] {\r\n    const children = target.getDescendants(directDescendantsOnly);\r\n\r\n    const result = [];\r\n    result.push(this.beginAnimation(target, from, to, loop, speedRatio, onAnimationEnd, animatable, stopCurrent, targetMask, undefined, isAdditive));\r\n    for (const child of children) {\r\n        result.push(this.beginAnimation(child, from, to, loop, speedRatio, onAnimationEnd, animatable, stopCurrent, targetMask, undefined, isAdditive));\r\n    }\r\n\r\n    return result;\r\n};\r\n\r\nScene.prototype.beginDirectAnimation = function (\r\n    target: any,\r\n    animations: Animation[],\r\n    from: number,\r\n    to: number,\r\n    loop?: boolean,\r\n    speedRatio?: number,\r\n    onAnimationEnd?: () => void,\r\n    onAnimationLoop?: () => void,\r\n    isAdditive = false\r\n): Animatable {\r\n    if (speedRatio === undefined) {\r\n        speedRatio = 1.0;\r\n    }\r\n\r\n    if (from > to && speedRatio > 0) {\r\n        speedRatio *= -1;\r\n    } else if (to > from && speedRatio < 0) {\r\n        const temp = to;\r\n        to = from;\r\n        from = temp;\r\n    }\r\n\r\n    const animatable = new Animatable(this, target, from, to, loop, speedRatio, onAnimationEnd, animations, onAnimationLoop, isAdditive);\r\n\r\n    return animatable;\r\n};\r\n\r\nScene.prototype.beginDirectHierarchyAnimation = function (\r\n    target: Node,\r\n    directDescendantsOnly: boolean,\r\n    animations: Animation[],\r\n    from: number,\r\n    to: number,\r\n    loop?: boolean,\r\n    speedRatio?: number,\r\n    onAnimationEnd?: () => void,\r\n    onAnimationLoop?: () => void,\r\n    isAdditive = false\r\n): Animatable[] {\r\n    const children = target.getDescendants(directDescendantsOnly);\r\n\r\n    const result = [];\r\n    result.push(this.beginDirectAnimation(target, animations, from, to, loop, speedRatio, onAnimationEnd, onAnimationLoop, isAdditive));\r\n    for (const child of children) {\r\n        result.push(this.beginDirectAnimation(child, animations, from, to, loop, speedRatio, onAnimationEnd, onAnimationLoop, isAdditive));\r\n    }\r\n\r\n    return result;\r\n};\r\n\r\nScene.prototype.getAnimatableByTarget = function (target: any): Nullable<Animatable> {\r\n    for (let index = 0; index < this._activeAnimatables.length; index++) {\r\n        if (this._activeAnimatables[index].target === target) {\r\n            return this._activeAnimatables[index];\r\n        }\r\n    }\r\n\r\n    return null;\r\n};\r\n\r\nScene.prototype.getAllAnimatablesByTarget = function (target: any): Array<Animatable> {\r\n    const result = [];\r\n    for (let index = 0; index < this._activeAnimatables.length; index++) {\r\n        if (this._activeAnimatables[index].target === target) {\r\n            result.push(this._activeAnimatables[index]);\r\n        }\r\n    }\r\n\r\n    return result;\r\n};\r\n\r\n/**\r\n * Will stop the animation of the given target\r\n * @param target - the target\r\n * @param animationName - the name of the animation to stop (all animations will be stopped if both this and targetMask are empty)\r\n * @param targetMask - a function that determines if the animation should be stopped based on its target (all animations will be stopped if both this and animationName are empty)\r\n */\r\nScene.prototype.stopAnimation = function (target: any, animationName?: string, targetMask?: (target: any) => boolean): void {\r\n    const animatables = this.getAllAnimatablesByTarget(target);\r\n\r\n    for (const animatable of animatables) {\r\n        animatable.stop(animationName, targetMask);\r\n    }\r\n};\r\n\r\n/**\r\n * Stops and removes all animations that have been applied to the scene\r\n */\r\nScene.prototype.stopAllAnimations = function (): void {\r\n    if (this._activeAnimatables) {\r\n        for (let i = 0; i < this._activeAnimatables.length; i++) {\r\n            this._activeAnimatables[i].stop(undefined, undefined, true);\r\n        }\r\n        this._activeAnimatables.length = 0;\r\n    }\r\n\r\n    for (const group of this.animationGroups) {\r\n        group.stop();\r\n    }\r\n};\r\n\r\nScene.prototype._registerTargetForLateAnimationBinding = function (runtimeAnimation: RuntimeAnimation, originalValue: any): void {\r\n    const target = runtimeAnimation.target;\r\n    this._registeredForLateAnimationBindings.pushNoDuplicate(target);\r\n\r\n    if (!target._lateAnimationHolders) {\r\n        target._lateAnimationHolders = {};\r\n    }\r\n\r\n    if (!target._lateAnimationHolders[runtimeAnimation.targetPath]) {\r\n        target._lateAnimationHolders[runtimeAnimation.targetPath] = {\r\n            totalWeight: 0,\r\n            totalAdditiveWeight: 0,\r\n            animations: [],\r\n            additiveAnimations: [],\r\n            originalValue: originalValue,\r\n        };\r\n    }\r\n\r\n    if (runtimeAnimation.isAdditive) {\r\n        target._lateAnimationHolders[runtimeAnimation.targetPath].additiveAnimations.push(runtimeAnimation);\r\n        target._lateAnimationHolders[runtimeAnimation.targetPath].totalAdditiveWeight += runtimeAnimation.weight;\r\n    } else {\r\n        target._lateAnimationHolders[runtimeAnimation.targetPath].animations.push(runtimeAnimation);\r\n        target._lateAnimationHolders[runtimeAnimation.targetPath].totalWeight += runtimeAnimation.weight;\r\n    }\r\n};\r\n\r\nScene.prototype._processLateAnimationBindingsForMatrices = function (holder: {\r\n    totalWeight: number;\r\n    totalAdditiveWeight: number;\r\n    animations: RuntimeAnimation[];\r\n    additiveAnimations: RuntimeAnimation[];\r\n    originalValue: Matrix;\r\n}): any {\r\n    if (holder.totalWeight === 0 && holder.totalAdditiveWeight === 0) {\r\n        return holder.originalValue;\r\n    }\r\n\r\n    let normalizer = 1.0;\r\n    const finalPosition = TmpVectors.Vector3[0];\r\n    const finalScaling = TmpVectors.Vector3[1];\r\n    const finalQuaternion = TmpVectors.Quaternion[0];\r\n    let startIndex = 0;\r\n    const originalAnimation = holder.animations[0];\r\n    const originalValue = holder.originalValue;\r\n\r\n    let scale = 1;\r\n    let skipOverride = false;\r\n    if (holder.totalWeight < 1.0) {\r\n        // We need to mix the original value in\r\n        scale = 1.0 - holder.totalWeight;\r\n        originalValue.decompose(finalScaling, finalQuaternion, finalPosition);\r\n    } else {\r\n        startIndex = 1;\r\n        // We need to normalize the weights\r\n        normalizer = holder.totalWeight;\r\n        scale = originalAnimation.weight / normalizer;\r\n        if (scale == 1) {\r\n            if (holder.totalAdditiveWeight) {\r\n                skipOverride = true;\r\n            } else {\r\n                return originalAnimation.currentValue;\r\n            }\r\n        }\r\n\r\n        originalAnimation.currentValue.decompose(finalScaling, finalQuaternion, finalPosition);\r\n    }\r\n\r\n    // Add up the override animations\r\n    if (!skipOverride) {\r\n        finalScaling.scaleInPlace(scale);\r\n        finalPosition.scaleInPlace(scale);\r\n        finalQuaternion.scaleInPlace(scale);\r\n\r\n        for (let animIndex = startIndex; animIndex < holder.animations.length; animIndex++) {\r\n            const runtimeAnimation = holder.animations[animIndex];\r\n            if (runtimeAnimation.weight === 0) {\r\n                continue;\r\n            }\r\n\r\n            scale = runtimeAnimation.weight / normalizer;\r\n            const currentPosition = TmpVectors.Vector3[2];\r\n            const currentScaling = TmpVectors.Vector3[3];\r\n            const currentQuaternion = TmpVectors.Quaternion[1];\r\n\r\n            runtimeAnimation.currentValue.decompose(currentScaling, currentQuaternion, currentPosition);\r\n\r\n            currentScaling.scaleAndAddToRef(scale, finalScaling);\r\n            currentQuaternion.scaleAndAddToRef(Quaternion.Dot(finalQuaternion, currentQuaternion) > 0 ? scale : -scale, finalQuaternion);\r\n            currentPosition.scaleAndAddToRef(scale, finalPosition);\r\n        }\r\n\r\n        finalQuaternion.normalize();\r\n    }\r\n\r\n    // Add up the additive animations\r\n    for (let animIndex = 0; animIndex < holder.additiveAnimations.length; animIndex++) {\r\n        const runtimeAnimation = holder.additiveAnimations[animIndex];\r\n        if (runtimeAnimation.weight === 0) {\r\n            continue;\r\n        }\r\n\r\n        const currentPosition = TmpVectors.Vector3[2];\r\n        const currentScaling = TmpVectors.Vector3[3];\r\n        const currentQuaternion = TmpVectors.Quaternion[1];\r\n\r\n        runtimeAnimation.currentValue.decompose(currentScaling, currentQuaternion, currentPosition);\r\n        currentScaling.multiplyToRef(finalScaling, currentScaling);\r\n        Vector3.LerpToRef(finalScaling, currentScaling, runtimeAnimation.weight, finalScaling);\r\n        finalQuaternion.multiplyToRef(currentQuaternion, currentQuaternion);\r\n        Quaternion.SlerpToRef(finalQuaternion, currentQuaternion, runtimeAnimation.weight, finalQuaternion);\r\n        currentPosition.scaleAndAddToRef(runtimeAnimation.weight, finalPosition);\r\n    }\r\n\r\n    const workValue = originalAnimation ? originalAnimation._animationState.workValue : TmpVectors.Matrix[0].clone();\r\n    Matrix.ComposeToRef(finalScaling, finalQuaternion, finalPosition, workValue);\r\n    return workValue;\r\n};\r\n\r\nScene.prototype._processLateAnimationBindingsForQuaternions = function (\r\n    holder: {\r\n        totalWeight: number;\r\n        totalAdditiveWeight: number;\r\n        animations: RuntimeAnimation[];\r\n        additiveAnimations: RuntimeAnimation[];\r\n        originalValue: Quaternion;\r\n    },\r\n    refQuaternion: Quaternion\r\n): Quaternion {\r\n    if (holder.totalWeight === 0 && holder.totalAdditiveWeight === 0) {\r\n        return refQuaternion;\r\n    }\r\n\r\n    const originalAnimation = holder.animations[0];\r\n    const originalValue = holder.originalValue;\r\n    let cumulativeQuaternion = refQuaternion;\r\n\r\n    if (holder.totalWeight === 0 && holder.totalAdditiveWeight > 0) {\r\n        cumulativeQuaternion.copyFrom(originalValue);\r\n    } else if (holder.animations.length === 1) {\r\n        Quaternion.SlerpToRef(originalValue, originalAnimation.currentValue, Math.min(1.0, holder.totalWeight), cumulativeQuaternion);\r\n\r\n        if (holder.totalAdditiveWeight === 0) {\r\n            return cumulativeQuaternion;\r\n        }\r\n    } else if (holder.animations.length > 1) {\r\n        // Add up the override animations\r\n        let normalizer = 1.0;\r\n        let quaternions: Array<Quaternion>;\r\n        let weights: Array<number>;\r\n\r\n        if (holder.totalWeight < 1.0) {\r\n            const scale = 1.0 - holder.totalWeight;\r\n\r\n            quaternions = [];\r\n            weights = [];\r\n\r\n            quaternions.push(originalValue);\r\n            weights.push(scale);\r\n        } else {\r\n            if (holder.animations.length === 2) {\r\n                // Slerp as soon as we can\r\n                Quaternion.SlerpToRef(holder.animations[0].currentValue, holder.animations[1].currentValue, holder.animations[1].weight / holder.totalWeight, refQuaternion);\r\n\r\n                if (holder.totalAdditiveWeight === 0) {\r\n                    return refQuaternion;\r\n                }\r\n            }\r\n\r\n            quaternions = [];\r\n            weights = [];\r\n            normalizer = holder.totalWeight;\r\n        }\r\n\r\n        for (let animIndex = 0; animIndex < holder.animations.length; animIndex++) {\r\n            const runtimeAnimation = holder.animations[animIndex];\r\n            quaternions.push(runtimeAnimation.currentValue);\r\n            weights.push(runtimeAnimation.weight / normalizer);\r\n        }\r\n\r\n        // https://gamedev.stackexchange.com/questions/62354/method-for-interpolation-between-3-quaternions\r\n\r\n        let cumulativeAmount = 0;\r\n        for (let index = 0; index < quaternions.length; ) {\r\n            if (!index) {\r\n                Quaternion.SlerpToRef(quaternions[index], quaternions[index + 1], weights[index + 1] / (weights[index] + weights[index + 1]), refQuaternion);\r\n                cumulativeQuaternion = refQuaternion;\r\n                cumulativeAmount = weights[index] + weights[index + 1];\r\n                index += 2;\r\n                continue;\r\n            }\r\n            cumulativeAmount += weights[index];\r\n            Quaternion.SlerpToRef(cumulativeQuaternion, quaternions[index], weights[index] / cumulativeAmount, cumulativeQuaternion);\r\n            index++;\r\n        }\r\n    }\r\n\r\n    // Add up the additive animations\r\n    for (let animIndex = 0; animIndex < holder.additiveAnimations.length; animIndex++) {\r\n        const runtimeAnimation = holder.additiveAnimations[animIndex];\r\n        if (runtimeAnimation.weight === 0) {\r\n            continue;\r\n        }\r\n\r\n        cumulativeQuaternion.multiplyToRef(runtimeAnimation.currentValue, TmpVectors.Quaternion[0]);\r\n        Quaternion.SlerpToRef(cumulativeQuaternion, TmpVectors.Quaternion[0], runtimeAnimation.weight, cumulativeQuaternion);\r\n    }\r\n\r\n    return cumulativeQuaternion!;\r\n};\r\n\r\nScene.prototype._processLateAnimationBindings = function (): void {\r\n    if (!this._registeredForLateAnimationBindings.length) {\r\n        return;\r\n    }\r\n    for (let index = 0; index < this._registeredForLateAnimationBindings.length; index++) {\r\n        const target = this._registeredForLateAnimationBindings.data[index];\r\n\r\n        for (const path in target._lateAnimationHolders) {\r\n            const holder = target._lateAnimationHolders[path];\r\n            const originalAnimation: RuntimeAnimation = holder.animations[0];\r\n            const originalValue = holder.originalValue;\r\n            if (originalValue === undefined || originalValue === null) {\r\n                continue;\r\n            }\r\n            const matrixDecomposeMode = Animation.AllowMatrixDecomposeForInterpolation && originalValue.m; // ie. data is matrix\r\n\r\n            let finalValue: any = target[path];\r\n            if (matrixDecomposeMode) {\r\n                finalValue = this._processLateAnimationBindingsForMatrices(holder);\r\n            } else {\r\n                const quaternionMode = originalValue.w !== undefined;\r\n                if (quaternionMode) {\r\n                    finalValue = this._processLateAnimationBindingsForQuaternions(holder, finalValue || Quaternion.Identity());\r\n                } else {\r\n                    let startIndex = 0;\r\n                    let normalizer = 1.0;\r\n\r\n                    if (holder.totalWeight < 1.0) {\r\n                        // We need to mix the original value in\r\n                        if (originalAnimation && originalValue.scale) {\r\n                            finalValue = originalValue.scale(1.0 - holder.totalWeight);\r\n                        } else if (originalAnimation) {\r\n                            finalValue = originalValue * (1.0 - holder.totalWeight);\r\n                        } else if (originalValue.clone) {\r\n                            finalValue = originalValue.clone();\r\n                        } else {\r\n                            finalValue = originalValue;\r\n                        }\r\n                    } else if (originalAnimation) {\r\n                        // We need to normalize the weights\r\n                        normalizer = holder.totalWeight;\r\n                        const scale = originalAnimation.weight / normalizer;\r\n                        if (scale !== 1) {\r\n                            if (originalAnimation.currentValue.scale) {\r\n                                finalValue = originalAnimation.currentValue.scale(scale);\r\n                            } else {\r\n                                finalValue = originalAnimation.currentValue * scale;\r\n                            }\r\n                        } else {\r\n                            finalValue = originalAnimation.currentValue;\r\n                        }\r\n\r\n                        startIndex = 1;\r\n                    }\r\n\r\n                    // Add up the override animations\r\n                    for (let animIndex = startIndex; animIndex < holder.animations.length; animIndex++) {\r\n                        const runtimeAnimation = holder.animations[animIndex];\r\n                        const scale = runtimeAnimation.weight / normalizer;\r\n\r\n                        if (!scale) {\r\n                            continue;\r\n                        } else if (runtimeAnimation.currentValue.scaleAndAddToRef) {\r\n                            runtimeAnimation.currentValue.scaleAndAddToRef(scale, finalValue);\r\n                        } else {\r\n                            finalValue += runtimeAnimation.currentValue * scale;\r\n                        }\r\n                    }\r\n\r\n                    // Add up the additive animations\r\n                    for (let animIndex = 0; animIndex < holder.additiveAnimations.length; animIndex++) {\r\n                        const runtimeAnimation = holder.additiveAnimations[animIndex];\r\n                        const scale: number = runtimeAnimation.weight;\r\n\r\n                        if (!scale) {\r\n                            continue;\r\n                        } else if (runtimeAnimation.currentValue.scaleAndAddToRef) {\r\n                            runtimeAnimation.currentValue.scaleAndAddToRef(scale, finalValue);\r\n                        } else {\r\n                            finalValue += runtimeAnimation.currentValue * scale;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            target[path] = finalValue;\r\n        }\r\n\r\n        target._lateAnimationHolders = {};\r\n    }\r\n    this._registeredForLateAnimationBindings.reset();\r\n};\r\n\r\ndeclare module \"../Bones/bone\" {\r\n    export interface Bone {\r\n        /**\r\n         * Copy an animation range from another bone\r\n         * @param source defines the source bone\r\n         * @param rangeName defines the range name to copy\r\n         * @param frameOffset defines the frame offset\r\n         * @param rescaleAsRequired defines if rescaling must be applied if required\r\n         * @param skelDimensionsRatio defines the scaling ratio\r\n         * @returns true if operation was successful\r\n         */\r\n        copyAnimationRange(source: Bone, rangeName: string, frameOffset: number, rescaleAsRequired: boolean, skelDimensionsRatio: Nullable<Vector3>): boolean;\r\n    }\r\n}\r\n\r\nBone.prototype.copyAnimationRange = function (\r\n    source: Bone,\r\n    rangeName: string,\r\n    frameOffset: number,\r\n    rescaleAsRequired = false,\r\n    skelDimensionsRatio: Nullable<Vector3> = null\r\n): boolean {\r\n    // all animation may be coming from a library skeleton, so may need to create animation\r\n    if (this.animations.length === 0) {\r\n        this.animations.push(new Animation(this.name, \"_matrix\", source.animations[0].framePerSecond, Animation.ANIMATIONTYPE_MATRIX, 0));\r\n        this.animations[0].setKeys([]);\r\n    }\r\n\r\n    // get animation info / verify there is such a range from the source bone\r\n    const sourceRange = source.animations[0].getRange(rangeName);\r\n    if (!sourceRange) {\r\n        return false;\r\n    }\r\n    const from = sourceRange.from;\r\n    const to = sourceRange.to;\r\n    const sourceKeys = source.animations[0].getKeys();\r\n\r\n    // rescaling prep\r\n    const sourceBoneLength = source.length;\r\n    const sourceParent = source.getParent();\r\n    const parent = this.getParent();\r\n    const parentScalingReqd = rescaleAsRequired && sourceParent && sourceBoneLength && this.length && sourceBoneLength !== this.length;\r\n    const parentRatio = parentScalingReqd && parent && sourceParent ? parent.length / sourceParent.length : 1;\r\n\r\n    const dimensionsScalingReqd =\r\n        rescaleAsRequired && !parent && skelDimensionsRatio && (skelDimensionsRatio.x !== 1 || skelDimensionsRatio.y !== 1 || skelDimensionsRatio.z !== 1);\r\n\r\n    const destKeys = this.animations[0].getKeys();\r\n\r\n    // loop vars declaration\r\n    let orig: { frame: number; value: Matrix };\r\n    let origTranslation: Vector3;\r\n    let mat: Matrix;\r\n\r\n    for (let key = 0, nKeys = sourceKeys.length; key < nKeys; key++) {\r\n        orig = sourceKeys[key];\r\n        if (orig.frame >= from && orig.frame <= to) {\r\n            if (rescaleAsRequired) {\r\n                mat = orig.value.clone();\r\n\r\n                // scale based on parent ratio, when bone has parent\r\n                if (parentScalingReqd) {\r\n                    origTranslation = mat.getTranslation();\r\n                    mat.setTranslation(origTranslation.scaleInPlace(parentRatio));\r\n\r\n                    // scale based on skeleton dimension ratio when root bone, and value is passed\r\n                } else if (dimensionsScalingReqd && skelDimensionsRatio) {\r\n                    origTranslation = mat.getTranslation();\r\n                    mat.setTranslation(origTranslation.multiplyInPlace(skelDimensionsRatio));\r\n\r\n                    // use original when root bone, and no data for skelDimensionsRatio\r\n                } else {\r\n                    mat = orig.value;\r\n                }\r\n            } else {\r\n                mat = orig.value;\r\n            }\r\n            destKeys.push({ frame: orig.frame + frameOffset, value: mat });\r\n        }\r\n    }\r\n    this.animations[0].createRange(rangeName, from + frameOffset, to + frameOffset);\r\n    return true;\r\n};\r\n"]}