{"version": 3, "file": "sysroot-fetcher.js", "sourceRoot": "", "sources": ["../src/sysroot-fetcher.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qEAAoD;AACpD,+CAAiC;AACjC,kDAA0B;AAC1B,6CAA+B;AAC/B,2CAA6B;AAE7B,2CAA+C;AAC/C,uCAAkC;AAElC,MAAM,CAAC,GAAG,IAAA,eAAK,EAAC,kBAAkB,CAAC,CAAC;AAEpC,MAAM,kBAAkB,GAAG;IACzB,GAAG,EAAE,OAAO;IACZ,IAAI,EAAE,MAAM;CACb,CAAC;AAEF,MAAM,gBAAgB,GAAG,+CAA+C,CAAC;AAElE,KAAK,UAAU,oBAAoB,CAAC,eAAuB,EAAE,UAAkB;IACpF,CAAC,CAAC,gCAAgC,EAAE,eAAe,CAAC,CAAC;IACrD,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,4BAAgB,EAAE,GAAG,eAAe,UAAU,CAAC,CAAC;IAChF,IAAI,MAAM,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAAE,OAAO,UAAU,CAAC;IAC5E,IAAI,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;QAAE,MAAM,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IAElE,MAAM,SAAS,GAAG,kBAAkB,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC;IAC/D,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,IAAA,eAAK,EAAC,wDAAwD,eAAe,uBAAuB,EAAE,MAAM,CAAC,CAAC,CAAC;IAEzJ,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC,OAAO,SAAS,EAAE,CAAC,IAAI,gBAAgB,CAAC,YAAY,SAAS,EAAE,CAAC,CAAC;IAC9H,MAAM,UAAU,GAAG,GAAG,gBAAgB,IAAI,GAAG,IAAI,QAAQ,EAAE,CAAC;IAC5D,MAAM,aAAa,GAAG,MAAM,IAAA,eAAK,EAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IAExD,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAChF,CAAC,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;IACxB,CAAC,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;IAC5B,IAAI,GAAG,KAAK,SAAS;QAAE,MAAM,IAAI,KAAK,CAAC,+CAA+C,eAAe,qCAAqC,CAAC,CAAC;IAE5I,CAAC,CAAC,yBAAyB,CAAC,CAAC;IAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,4BAAgB,EAAE,GAAG,eAAe,IAAI,QAAQ,EAAE,CAAC,CAAC;IACpF,IAAI,MAAM,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;QAAE,MAAM,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IACjE,MAAM,EAAE,CAAC,SAAS,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;IAE9C,CAAC,CAAC,uBAAuB,CAAC,CAAC;IAC3B,MAAM,IAAA,2BAAK,EAAC,KAAK,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,CAAC,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;IAE/E,OAAO,UAAU,CAAC;AACpB,CAAC;AA3BD,oDA2BC"}