import { SpriteManager } from "./spriteManager";
import type { Scene } from "../scene";
/**
 * Class used to manage multiple sprites of different sizes on the same spritesheet
 * @see https://doc.babylonjs.com/features/featuresDeepDive/sprites
 */
export declare class SpritePackedManager extends SpriteManager {
    /** defines the packed manager's name */
    name: string;
    /**
     * Creates a new sprite manager from a packed sprite sheet
     * @param name defines the manager's name
     * @param imgUrl defines the sprite sheet url
     * @param capacity defines the maximum allowed number of sprites
     * @param scene defines the hosting scene
     * @param spriteJSON null otherwise a JSON object defining sprite sheet data
     * @param epsilon defines the epsilon value to align texture (0.01 by default)
     * @param samplingMode defines the sampling mode to use with spritesheet
     * @param fromPacked set to true; do not alter
     */
    constructor(
    /** defines the packed manager's name */
    name: string, imgUrl: string, capacity: number, scene: Scene, spriteJSON?: string | null, epsilon?: number, samplingMode?: number);
}
