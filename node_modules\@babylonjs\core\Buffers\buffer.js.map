{"version": 3, "file": "buffer.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Buffers/buffer.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C;;GAEG;AACH,MAAM,OAAO,MAAM;IAef;;;;;;;;;;OAUG;IACH,YACI,MAAW,EACX,IAA4B,EAC5B,SAAkB,EAClB,MAAM,GAAG,CAAC,EACV,wBAAwB,GAAG,KAAK,EAChC,SAAS,GAAG,KAAK,EACjB,QAAQ,GAAG,KAAK,EAChB,OAAgB;QA1BZ,oBAAe,GAAG,KAAK,CAAC;QA4B5B,IAAI,MAAM,CAAC,QAAQ,EAAE;YACjB,mEAAmE;YACnE,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC;SAChD;aAAM;YACH,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;SACzB;QAED,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,QAAQ,GAAG,OAAO,IAAI,CAAC,CAAC;QAE7B,IAAI,IAAI,YAAY,UAAU,EAAE;YAC5B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;SACvB;aAAM;YACH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;SACvB;QAED,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,YAAY,CAAC,iBAAiB,CAAC;QAE9E,IAAI,CAAC,wBAAwB,EAAE;YAC3B,aAAa;YACb,IAAI,CAAC,MAAM,EAAE,CAAC;SACjB;IACL,CAAC;IAED;;;;;;;;;;OAUG;IACI,kBAAkB,CAAC,IAAY,EAAE,MAAc,EAAE,IAAY,EAAE,MAAe,EAAE,SAAmB,EAAE,QAAQ,GAAG,KAAK,EAAE,OAAgB;QAC1I,MAAM,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,YAAY,CAAC,iBAAiB,CAAC;QAC/E,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,YAAY,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;QAE5G,6EAA6E;QAC7E,OAAO,IAAI,YAAY,CACnB,IAAI,CAAC,OAAO,EACZ,IAAI,EACJ,IAAI,EACJ,IAAI,CAAC,UAAU,EACf,IAAI,EACJ,UAAU,EACV,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,EACrD,UAAU,EACV,IAAI,EACJ,SAAS,EACT,SAAS,EACT,IAAI,EACJ,IAAI,CAAC,QAAQ,IAAI,OAAO,CAC3B,CAAC;IACN,CAAC;IAED,aAAa;IAEb;;;OAGG;IACI,WAAW;QACd,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;;;;OAKG;IACI,aAAa;QAChB,OAAO,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC,iBAAiB,CAAC;IAC5D,CAAC;IAED,UAAU;IAEV;;;;OAIG;IACI,MAAM,CAAC,OAA4B,IAAI;QAC1C,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;YACvB,OAAO,CAAC,gBAAgB;SAC3B;QAED,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC;QAE1B,IAAI,CAAC,IAAI,EAAE;YACP,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,gBAAgB;YAChB,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;gBAC5D,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;aACrB;iBAAM;gBACH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;aACxD;SACJ;aAAM,IAAI,IAAI,CAAC,UAAU,EAAE;YACxB,gBAAgB;YAChB,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAC3D,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;SACrB;IACL,CAAC;IAED,gBAAgB;IACT,QAAQ;QACX,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,IAAe;QACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC;IAED;;;;;;OAMG;IACI,cAAc,CAAC,IAAe,EAAE,MAAc,EAAE,WAAoB,EAAE,WAAoB,KAAK;QAClG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO;SACV;QAED,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,gBAAgB;YAChB,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAClC,IAAI,CAAC,OAAO,EACZ,IAAI,EACJ,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,YAAY,CAAC,iBAAiB,EAC3D,WAAW,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAC1D,CAAC;YACF,IAAI,MAAM,KAAK,CAAC,IAAI,WAAW,KAAK,SAAS,EAAE;gBAC3C,iCAAiC;gBACjC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;aACrB;iBAAM;gBACH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;aACrB;SACJ;IACL,CAAC;IAED,gBAAgB;IACT,mBAAmB;QACtB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,OAAO;SACV;QAED,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO;SACV;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YAC3C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;SACrB;IACL,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,YAAY;IAgDrB;;OAEG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED,IAAW,eAAe,CAAC,KAAa;QACpC,MAAM,WAAW,GAAG,KAAK,IAAI,CAAC,CAAC;QAC/B,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAE9B,IAAI,WAAW,KAAK,IAAI,CAAC,UAAU,EAAE;YACjC,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC;YAC9B,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC3B;IACL,CAAC;IAiCD;;;;;;;;;;;;;;;;OAgBG;IACH,YACI,MAAW,EACX,IAAqC,EACrC,IAAY,EACZ,SAAkB,EAClB,wBAAkC,EAClC,MAAe,EACf,SAAmB,EACnB,MAAe,EACf,IAAa,EACb,IAAa,EACb,UAAU,GAAG,KAAK,EAClB,QAAQ,GAAG,KAAK,EAChB,OAAO,GAAG,CAAC,EACX,mBAAmB,GAAG,KAAK;QAE3B,IAAI,IAAI,YAAY,MAAM,EAAE;YACxB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,IAAI,CAAC,WAAW,GAAG,mBAAmB,CAAC;SAC1C;aAAM;YACH,IAAI,CAAC,OAAO,GAAG,IAAI,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,wBAAwB,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;YAC1G,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;SAC3B;QAED,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;QACxC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAElB,IAAI,IAAI,IAAI,SAAS,EAAE;YACnB,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAClC,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,KAAK,CAAC;YAC/B,IAAI,UAAU,YAAY,SAAS,EAAE;gBACjC,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;aACjC;iBAAM,IAAI,UAAU,YAAY,UAAU,EAAE;gBACzC,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,aAAa,CAAC;aAC1C;iBAAM,IAAI,UAAU,YAAY,UAAU,EAAE;gBACzC,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,KAAK,CAAC;aAClC;iBAAM,IAAI,UAAU,YAAY,WAAW,EAAE;gBAC1C,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,cAAc,CAAC;aAC3C;iBAAM,IAAI,UAAU,YAAY,UAAU,EAAE;gBACzC,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,GAAG,CAAC;aAChC;iBAAM,IAAI,UAAU,YAAY,WAAW,EAAE;gBAC1C,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,YAAY,CAAC;aACzC;SACJ;aAAM;YACH,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;SACpB;QAED,MAAM,cAAc,GAAG,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEjE,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;YAC1F,IAAI,CAAC,UAAU,GAAG,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC;YACnF,IAAI,CAAC,UAAU,GAAG,MAAM,IAAI,CAAC,CAAC;SACjC;aAAM;YACH,IAAI,CAAC,KAAK,GAAG,IAAI,IAAI,MAAM,IAAI,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAC/D,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC;YAC5G,IAAI,CAAC,UAAU,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC,GAAG,cAAc,CAAC;SACpD;QAED,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,IAAI,CAAC,UAAU,GAAG,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC;QAC9D,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhD,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAEO,gBAAgB;QACpB,8DAA8D;QAC7D,IAAI,CAAC,QAAgB;YAClB,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBAChC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;gBACjB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBAChC,sBAAsB;gBACtB,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC;IAChC,CAAC;IAED,gBAAgB;IACT,QAAQ;QACX,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO;SACV;QAED,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,aAAa;IAEb;;;OAGG;IACI,WAAW;QACd,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;IACtC,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;IAClC,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,aAAqB,EAAE,SAAmB;QAC1D,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC5B,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,IAAI,CAAC;SACf;QAED,MAAM,uBAAuB,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3F,MAAM,KAAK,GAAG,aAAa,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAE7C,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,KAAK,IAAI,IAAI,CAAC,UAAU,KAAK,uBAAuB,EAAE;YACjF,MAAM,IAAI,GAAG,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC;YACrC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;YAC7D,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,CAAC,IAAI,YAAY,KAAK,IAAI,IAAI,YAAY,YAAY,CAAC,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE;YAC5G,IAAI,IAAI,YAAY,KAAK,EAAE;gBACvB,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;gBACnC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC;aAC7C;iBAAM,IAAI,IAAI,YAAY,WAAW,EAAE;gBACpC,OAAO,IAAI,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;aACzD;iBAAM;gBACH,IAAI,MAAM,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;gBAC/C,IAAI,SAAS,EAAE;oBACX,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC;oBACvC,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;oBAE5D,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBAEnB,OAAO,MAAM,CAAC;iBACjB;gBAED,2BAA2B;gBAC3B,MAAM,SAAS,GAAG,MAAM,GAAG,CAAC,CAAC;gBAE7B,IAAI,SAAS,EAAE;oBACX,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC,CAAC;iBAC5C;gBAED,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;aACvD;SACJ;QAED,IAAI,SAAS,EAAE;YACX,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;SACvB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;IACpC,CAAC;IAED;;;;;OAKG;IACI,aAAa;QAChB,OAAO,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvE,CAAC;IAED;;;;OAIG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvE,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,WAAW,GAAG,KAAK;QAC9B,OAAO,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;IAC7F,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED,UAAU;IAEV;;;OAGG;IACI,MAAM,CAAC,IAAgB;QAC1B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,IAAe;QACzB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;;;;;OAMG;IACI,cAAc,CAAC,IAAe,EAAE,MAAc,EAAE,WAAoB,KAAK;QAC5E,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;SAC1B;IACL,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,KAAa,EAAE,QAAgD;QAC1E,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAG,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IAC7I,CAAC;IAgED;;;;OAIG;IACI,MAAM,CAAC,YAAY,CAAC,IAAY;QACnC,QAAQ,IAAI,EAAE;YACV,KAAK,YAAY,CAAC,MAAM,CAAC;YACzB,KAAK,YAAY,CAAC,OAAO,CAAC;YAC1B,KAAK,YAAY,CAAC,OAAO,CAAC;YAC1B,KAAK,YAAY,CAAC,OAAO,CAAC;YAC1B,KAAK,YAAY,CAAC,OAAO,CAAC;YAC1B,KAAK,YAAY,CAAC,OAAO;gBACrB,OAAO,CAAC,CAAC;YACb,KAAK,YAAY,CAAC,UAAU,CAAC;YAC7B,KAAK,YAAY,CAAC,YAAY;gBAC1B,OAAO,CAAC,CAAC;YACb,KAAK,YAAY,CAAC,SAAS,CAAC;YAC5B,KAAK,YAAY,CAAC,mBAAmB,CAAC;YACtC,KAAK,YAAY,CAAC,wBAAwB,CAAC;YAC3C,KAAK,YAAY,CAAC,mBAAmB,CAAC;YACtC,KAAK,YAAY,CAAC,wBAAwB,CAAC;YAC3C,KAAK,YAAY,CAAC,WAAW;gBACzB,OAAO,CAAC,CAAC;YACb;gBACI,MAAM,IAAI,KAAK,CAAC,gBAAgB,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;SACtD;IACL,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,iBAAiB,CAAC,IAAY;QACxC,QAAQ,IAAI,EAAE;YACV,KAAK,YAAY,CAAC,IAAI,CAAC;YACvB,KAAK,YAAY,CAAC,aAAa;gBAC3B,OAAO,CAAC,CAAC;YACb,KAAK,YAAY,CAAC,KAAK,CAAC;YACxB,KAAK,YAAY,CAAC,cAAc;gBAC5B,OAAO,CAAC,CAAC;YACb,KAAK,YAAY,CAAC,GAAG,CAAC;YACtB,KAAK,YAAY,CAAC,YAAY,CAAC;YAC/B,KAAK,YAAY,CAAC,KAAK;gBACnB,OAAO,CAAC,CAAC;YACb;gBACI,MAAM,IAAI,KAAK,CAAC,iBAAiB,IAAI,GAAG,CAAC,CAAC;SACjD;IACL,CAAC;IAED;;;;;;;;;;OAUG;IACI,MAAM,CAAC,OAAO,CACjB,IAAe,EACf,UAAkB,EAClB,UAAkB,EAClB,cAAsB,EACtB,aAAqB,EACrB,KAAa,EACb,UAAmB,EACnB,QAAgD;QAEhD,IAAI,IAAI,YAAY,KAAK,EAAE;YACvB,IAAI,MAAM,GAAG,UAAU,GAAG,CAAC,CAAC;YAC5B,MAAM,MAAM,GAAG,UAAU,GAAG,CAAC,CAAC;YAC9B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,IAAI,cAAc,EAAE;gBACxD,KAAK,IAAI,cAAc,GAAG,CAAC,EAAE,cAAc,GAAG,cAAc,EAAE,cAAc,EAAE,EAAE;oBAC5E,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC,EAAE,KAAK,GAAG,cAAc,CAAC,CAAC;iBACnE;gBACD,MAAM,IAAI,MAAM,CAAC;aACpB;SACJ;aAAM;YACH,MAAM,QAAQ,GAAG,IAAI,YAAY,WAAW,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAChI,MAAM,mBAAmB,GAAG,YAAY,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAC1E,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,IAAI,cAAc,EAAE;gBACxD,IAAI,mBAAmB,GAAG,UAAU,CAAC;gBACrC,KAAK,IAAI,cAAc,GAAG,CAAC,EAAE,cAAc,GAAG,cAAc,EAAE,cAAc,EAAE,EAAE;oBAC5E,MAAM,KAAK,GAAG,YAAY,CAAC,cAAc,CAAC,QAAQ,EAAE,aAAa,EAAE,mBAAmB,EAAE,UAAU,CAAC,CAAC;oBACpG,QAAQ,CAAC,KAAK,EAAE,KAAK,GAAG,cAAc,CAAC,CAAC;oBACxC,mBAAmB,IAAI,mBAAmB,CAAC;iBAC9C;gBACD,UAAU,IAAI,UAAU,CAAC;aAC5B;SACJ;IACL,CAAC;IAEO,MAAM,CAAC,cAAc,CAAC,QAAkB,EAAE,IAAY,EAAE,UAAkB,EAAE,UAAmB;QACnG,QAAQ,IAAI,EAAE;YACV,KAAK,YAAY,CAAC,IAAI,CAAC,CAAC;gBACpB,IAAI,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBACzC,IAAI,UAAU,EAAE;oBACZ,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;iBACrC;gBACD,OAAO,KAAK,CAAC;aAChB;YACD,KAAK,YAAY,CAAC,aAAa,CAAC,CAAC;gBAC7B,IAAI,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;gBAC1C,IAAI,UAAU,EAAE;oBACZ,KAAK,GAAG,KAAK,GAAG,GAAG,CAAC;iBACvB;gBACD,OAAO,KAAK,CAAC;aAChB;YACD,KAAK,YAAY,CAAC,KAAK,CAAC,CAAC;gBACrB,IAAI,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;gBAChD,IAAI,UAAU,EAAE;oBACZ,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;iBACvC;gBACD,OAAO,KAAK,CAAC;aAChB;YACD,KAAK,YAAY,CAAC,cAAc,CAAC,CAAC;gBAC9B,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;gBACjD,IAAI,UAAU,EAAE;oBACZ,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;iBACzB;gBACD,OAAO,KAAK,CAAC;aAChB;YACD,KAAK,YAAY,CAAC,GAAG,CAAC,CAAC;gBACnB,OAAO,QAAQ,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;aAC9C;YACD,KAAK,YAAY,CAAC,YAAY,CAAC,CAAC;gBAC5B,OAAO,QAAQ,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;aAC/C;YACD,KAAK,YAAY,CAAC,KAAK,CAAC,CAAC;gBACrB,OAAO,QAAQ,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;aAChD;YACD,OAAO,CAAC,CAAC;gBACL,MAAM,IAAI,KAAK,CAAC,0BAA0B,IAAI,EAAE,CAAC,CAAC;aACrD;SACJ;IACL,CAAC;;AAtkBc,qBAAQ,GAAG,CAAC,CAAC;AAY5B;;GAEG;AACoB,iBAAI,GAAG,IAAI,CAAC;AAEnC;;GAEG;AACoB,0BAAa,GAAG,IAAI,CAAC;AAE5C;;GAEG;AACoB,kBAAK,GAAG,IAAI,CAAC;AAEpC;;GAEG;AACoB,2BAAc,GAAG,IAAI,CAAC;AAE7C;;GAEG;AACoB,gBAAG,GAAG,IAAI,CAAC;AAElC;;GAEG;AACoB,yBAAY,GAAG,IAAI,CAAC;AAE3C;;GAEG;AACoB,kBAAK,GAAG,IAAI,CAAC;AAgVpC,QAAQ;AACR;;GAEG;AACoB,yBAAY,GAAG,UAAU,CAAC;AACjD;;GAEG;AACoB,uBAAU,GAAG,QAAQ,CAAC;AAC7C;;GAEG;AACoB,wBAAW,GAAG,SAAS,CAAC;AAC/C;;GAEG;AACoB,mBAAM,GAAG,IAAI,CAAC;AACrC;;GAEG;AACoB,oBAAO,GAAG,KAAK,CAAC;AACvC;;GAEG;AACoB,oBAAO,GAAG,KAAK,CAAC;AACvC;;GAEG;AACoB,oBAAO,GAAG,KAAK,CAAC;AACvC;;GAEG;AACoB,oBAAO,GAAG,KAAK,CAAC;AACvC;;GAEG;AACoB,oBAAO,GAAG,KAAK,CAAC;AACvC;;GAEG;AACoB,sBAAS,GAAG,OAAO,CAAC;AAC3C;;GAEG;AACoB,8BAAiB,GAAG,eAAe,CAAC;AAC3D;;GAEG;AACoB,gCAAmB,GAAG,iBAAiB,CAAC;AAC/D;;GAEG;AACoB,gCAAmB,GAAG,iBAAiB,CAAC;AAC/D;;GAEG;AACoB,qCAAwB,GAAG,sBAAsB,CAAC;AACzE;;GAEG;AACoB,qCAAwB,GAAG,sBAAsB,CAAC", "sourcesContent": ["import type { Nullable, DataArray, FloatArray } from \"../types\";\r\nimport type { ThinEngine } from \"../Engines/thinEngine\";\r\nimport { DataBuffer } from \"./dataBuffer\";\r\n\r\n/**\r\n * Class used to store data that will be store in GPU memory\r\n */\r\nexport class Buffer {\r\n    private _engine: ThinEngine;\r\n    private _buffer: Nullable<DataBuffer>;\r\n    /** @internal */\r\n    public _data: Nullable<DataArray>;\r\n    private _updatable: boolean;\r\n    private _instanced: boolean;\r\n    private _divisor: number;\r\n    private _isAlreadyOwned = false;\r\n\r\n    /**\r\n     * Gets the byte stride.\r\n     */\r\n    public readonly byteStride: number;\r\n\r\n    /**\r\n     * Constructor\r\n     * @param engine the engine\r\n     * @param data the data to use for this buffer\r\n     * @param updatable whether the data is updatable\r\n     * @param stride the stride (optional)\r\n     * @param postponeInternalCreation whether to postpone creating the internal WebGL buffer (optional)\r\n     * @param instanced whether the buffer is instanced (optional)\r\n     * @param useBytes set to true if the stride in in bytes (optional)\r\n     * @param divisor sets an optional divisor for instances (1 by default)\r\n     */\r\n    constructor(\r\n        engine: any,\r\n        data: DataArray | DataBuffer,\r\n        updatable: boolean,\r\n        stride = 0,\r\n        postponeInternalCreation = false,\r\n        instanced = false,\r\n        useBytes = false,\r\n        divisor?: number\r\n    ) {\r\n        if (engine.getScene) {\r\n            // old versions of VertexBuffer accepted 'mesh' instead of 'engine'\r\n            this._engine = engine.getScene().getEngine();\r\n        } else {\r\n            this._engine = engine;\r\n        }\r\n\r\n        this._updatable = updatable;\r\n        this._instanced = instanced;\r\n        this._divisor = divisor || 1;\r\n\r\n        if (data instanceof DataBuffer) {\r\n            this._data = null;\r\n            this._buffer = data;\r\n        } else {\r\n            this._data = data;\r\n            this._buffer = null;\r\n        }\r\n\r\n        this.byteStride = useBytes ? stride : stride * Float32Array.BYTES_PER_ELEMENT;\r\n\r\n        if (!postponeInternalCreation) {\r\n            // by default\r\n            this.create();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Create a new VertexBuffer based on the current buffer\r\n     * @param kind defines the vertex buffer kind (position, normal, etc.)\r\n     * @param offset defines offset in the buffer (0 by default)\r\n     * @param size defines the size in floats of attributes (position is 3 for instance)\r\n     * @param stride defines the stride size in floats in the buffer (the offset to apply to reach next value when data is interleaved)\r\n     * @param instanced defines if the vertex buffer contains indexed data\r\n     * @param useBytes defines if the offset and stride are in bytes     *\r\n     * @param divisor sets an optional divisor for instances (1 by default)\r\n     * @returns the new vertex buffer\r\n     */\r\n    public createVertexBuffer(kind: string, offset: number, size: number, stride?: number, instanced?: boolean, useBytes = false, divisor?: number): VertexBuffer {\r\n        const byteOffset = useBytes ? offset : offset * Float32Array.BYTES_PER_ELEMENT;\r\n        const byteStride = stride ? (useBytes ? stride : stride * Float32Array.BYTES_PER_ELEMENT) : this.byteStride;\r\n\r\n        // a lot of these parameters are ignored as they are overridden by the buffer\r\n        return new VertexBuffer(\r\n            this._engine,\r\n            this,\r\n            kind,\r\n            this._updatable,\r\n            true,\r\n            byteStride,\r\n            instanced === undefined ? this._instanced : instanced,\r\n            byteOffset,\r\n            size,\r\n            undefined,\r\n            undefined,\r\n            true,\r\n            this._divisor || divisor\r\n        );\r\n    }\r\n\r\n    // Properties\r\n\r\n    /**\r\n     * Gets a boolean indicating if the Buffer is updatable?\r\n     * @returns true if the buffer is updatable\r\n     */\r\n    public isUpdatable(): boolean {\r\n        return this._updatable;\r\n    }\r\n\r\n    /**\r\n     * Gets current buffer's data\r\n     * @returns a DataArray or null\r\n     */\r\n    public getData(): Nullable<DataArray> {\r\n        return this._data;\r\n    }\r\n\r\n    /**\r\n     * Gets underlying native buffer\r\n     * @returns underlying native buffer\r\n     */\r\n    public getBuffer(): Nullable<DataBuffer> {\r\n        return this._buffer;\r\n    }\r\n\r\n    /**\r\n     * Gets the stride in float32 units (i.e. byte stride / 4).\r\n     * May not be an integer if the byte stride is not divisible by 4.\r\n     * @returns the stride in float32 units\r\n     * @deprecated Please use byteStride instead.\r\n     */\r\n    public getStrideSize(): number {\r\n        return this.byteStride / Float32Array.BYTES_PER_ELEMENT;\r\n    }\r\n\r\n    // Methods\r\n\r\n    /**\r\n     * Store data into the buffer. Creates the buffer if not used already.\r\n     * If the buffer was already used, it will be updated only if it is updatable, otherwise it will do nothing.\r\n     * @param data defines the data to store\r\n     */\r\n    public create(data: Nullable<DataArray> = null): void {\r\n        if (!data && this._buffer) {\r\n            return; // nothing to do\r\n        }\r\n\r\n        data = data || this._data;\r\n\r\n        if (!data) {\r\n            return;\r\n        }\r\n\r\n        if (!this._buffer) {\r\n            // create buffer\r\n            if (this._updatable) {\r\n                this._buffer = this._engine.createDynamicVertexBuffer(data);\r\n                this._data = data;\r\n            } else {\r\n                this._buffer = this._engine.createVertexBuffer(data);\r\n            }\r\n        } else if (this._updatable) {\r\n            // update buffer\r\n            this._engine.updateDynamicVertexBuffer(this._buffer, data);\r\n            this._data = data;\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _rebuild(): void {\r\n        this._buffer = null;\r\n        this.create(this._data);\r\n    }\r\n\r\n    /**\r\n     * Update current buffer data\r\n     * @param data defines the data to store\r\n     */\r\n    public update(data: DataArray): void {\r\n        this.create(data);\r\n    }\r\n\r\n    /**\r\n     * Updates the data directly.\r\n     * @param data the new data\r\n     * @param offset the new offset\r\n     * @param vertexCount the vertex count (optional)\r\n     * @param useBytes set to true if the offset is in bytes\r\n     */\r\n    public updateDirectly(data: DataArray, offset: number, vertexCount?: number, useBytes: boolean = false): void {\r\n        if (!this._buffer) {\r\n            return;\r\n        }\r\n\r\n        if (this._updatable) {\r\n            // update buffer\r\n            this._engine.updateDynamicVertexBuffer(\r\n                this._buffer,\r\n                data,\r\n                useBytes ? offset : offset * Float32Array.BYTES_PER_ELEMENT,\r\n                vertexCount ? vertexCount * this.byteStride : undefined\r\n            );\r\n            if (offset === 0 && vertexCount === undefined) {\r\n                // Keep the data if we easily can\r\n                this._data = data;\r\n            } else {\r\n                this._data = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _increaseReferences() {\r\n        if (!this._buffer) {\r\n            return;\r\n        }\r\n\r\n        if (!this._isAlreadyOwned) {\r\n            this._isAlreadyOwned = true;\r\n            return;\r\n        }\r\n\r\n        this._buffer.references++;\r\n    }\r\n\r\n    /**\r\n     * Release all resources\r\n     */\r\n    public dispose(): void {\r\n        if (!this._buffer) {\r\n            return;\r\n        }\r\n        if (this._engine._releaseBuffer(this._buffer)) {\r\n            this._buffer = null;\r\n            this._data = null;\r\n        }\r\n    }\r\n}\r\n\r\n/**\r\n * Specialized buffer used to store vertex data\r\n */\r\nexport class VertexBuffer {\r\n    private static _Counter = 0;\r\n\r\n    /** @internal */\r\n    public _buffer: Buffer;\r\n    /** @internal */\r\n    public _validOffsetRange: boolean; // used internally by the engine\r\n    private _kind: string;\r\n    private _size: number;\r\n    private _ownsBuffer: boolean;\r\n    private _instanced: boolean;\r\n    private _instanceDivisor: number;\r\n\r\n    /**\r\n     * The byte type.\r\n     */\r\n    public static readonly BYTE = 5120;\r\n\r\n    /**\r\n     * The unsigned byte type.\r\n     */\r\n    public static readonly UNSIGNED_BYTE = 5121;\r\n\r\n    /**\r\n     * The short type.\r\n     */\r\n    public static readonly SHORT = 5122;\r\n\r\n    /**\r\n     * The unsigned short type.\r\n     */\r\n    public static readonly UNSIGNED_SHORT = 5123;\r\n\r\n    /**\r\n     * The integer type.\r\n     */\r\n    public static readonly INT = 5124;\r\n\r\n    /**\r\n     * The unsigned integer type.\r\n     */\r\n    public static readonly UNSIGNED_INT = 5125;\r\n\r\n    /**\r\n     * The float type.\r\n     */\r\n    public static readonly FLOAT = 5126;\r\n\r\n    /**\r\n     * Gets or sets the instance divisor when in instanced mode\r\n     */\r\n    public get instanceDivisor(): number {\r\n        return this._instanceDivisor;\r\n    }\r\n\r\n    public set instanceDivisor(value: number) {\r\n        const isInstanced = value != 0;\r\n        this._instanceDivisor = value;\r\n\r\n        if (isInstanced !== this._instanced) {\r\n            this._instanced = isInstanced;\r\n            this._computeHashCode();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the byte stride.\r\n     */\r\n    public readonly byteStride: number;\r\n\r\n    /**\r\n     * Gets the byte offset.\r\n     */\r\n    public readonly byteOffset: number;\r\n\r\n    /**\r\n     * Gets whether integer data values should be normalized into a certain range when being casted to a float.\r\n     */\r\n    public readonly normalized: boolean;\r\n\r\n    /**\r\n     * Gets the data type of each component in the array.\r\n     */\r\n    public readonly type: number;\r\n\r\n    /**\r\n     * Gets the unique id of this vertex buffer\r\n     */\r\n    public readonly uniqueId: number;\r\n\r\n    /**\r\n     * Gets a hash code representing the format (type, normalized, size, instanced, stride) of this buffer\r\n     * All buffers with the same format will have the same hash code\r\n     */\r\n    public readonly hashCode: number;\r\n\r\n    /**\r\n     * Constructor\r\n     * @param engine the engine\r\n     * @param data the data to use for this vertex buffer\r\n     * @param kind the vertex buffer kind\r\n     * @param updatable whether the data is updatable\r\n     * @param postponeInternalCreation whether to postpone creating the internal WebGL buffer (optional)\r\n     * @param stride the stride (optional)\r\n     * @param instanced whether the buffer is instanced (optional)\r\n     * @param offset the offset of the data (optional)\r\n     * @param size the number of components (optional)\r\n     * @param type the type of the component (optional)\r\n     * @param normalized whether the data contains normalized data (optional)\r\n     * @param useBytes set to true if stride and offset are in bytes (optional)\r\n     * @param divisor defines the instance divisor to use (1 by default)\r\n     * @param takeBufferOwnership defines if the buffer should be released when the vertex buffer is disposed\r\n     */\r\n    constructor(\r\n        engine: any,\r\n        data: DataArray | Buffer | DataBuffer,\r\n        kind: string,\r\n        updatable: boolean,\r\n        postponeInternalCreation?: boolean,\r\n        stride?: number,\r\n        instanced?: boolean,\r\n        offset?: number,\r\n        size?: number,\r\n        type?: number,\r\n        normalized = false,\r\n        useBytes = false,\r\n        divisor = 1,\r\n        takeBufferOwnership = false\r\n    ) {\r\n        if (data instanceof Buffer) {\r\n            this._buffer = data;\r\n            this._ownsBuffer = takeBufferOwnership;\r\n        } else {\r\n            this._buffer = new Buffer(engine, data, updatable, stride, postponeInternalCreation, instanced, useBytes);\r\n            this._ownsBuffer = true;\r\n        }\r\n\r\n        this.uniqueId = VertexBuffer._Counter++;\r\n        this._kind = kind;\r\n\r\n        if (type == undefined) {\r\n            const vertexData = this.getData();\r\n            this.type = VertexBuffer.FLOAT;\r\n            if (vertexData instanceof Int8Array) {\r\n                this.type = VertexBuffer.BYTE;\r\n            } else if (vertexData instanceof Uint8Array) {\r\n                this.type = VertexBuffer.UNSIGNED_BYTE;\r\n            } else if (vertexData instanceof Int16Array) {\r\n                this.type = VertexBuffer.SHORT;\r\n            } else if (vertexData instanceof Uint16Array) {\r\n                this.type = VertexBuffer.UNSIGNED_SHORT;\r\n            } else if (vertexData instanceof Int32Array) {\r\n                this.type = VertexBuffer.INT;\r\n            } else if (vertexData instanceof Uint32Array) {\r\n                this.type = VertexBuffer.UNSIGNED_INT;\r\n            }\r\n        } else {\r\n            this.type = type;\r\n        }\r\n\r\n        const typeByteLength = VertexBuffer.GetTypeByteLength(this.type);\r\n\r\n        if (useBytes) {\r\n            this._size = size || (stride ? stride / typeByteLength : VertexBuffer.DeduceStride(kind));\r\n            this.byteStride = stride || this._buffer.byteStride || this._size * typeByteLength;\r\n            this.byteOffset = offset || 0;\r\n        } else {\r\n            this._size = size || stride || VertexBuffer.DeduceStride(kind);\r\n            this.byteStride = stride ? stride * typeByteLength : this._buffer.byteStride || this._size * typeByteLength;\r\n            this.byteOffset = (offset || 0) * typeByteLength;\r\n        }\r\n\r\n        this.normalized = normalized;\r\n\r\n        this._instanced = instanced !== undefined ? instanced : false;\r\n        this._instanceDivisor = instanced ? divisor : 0;\r\n\r\n        this._computeHashCode();\r\n    }\r\n\r\n    private _computeHashCode(): void {\r\n        // note: cast to any because the property is declared readonly\r\n        (this.hashCode as any) =\r\n            ((this.type - 5120) << 0) +\r\n            ((this.normalized ? 1 : 0) << 3) +\r\n            (this._size << 4) +\r\n            ((this._instanced ? 1 : 0) << 6) +\r\n            /* keep 5 bits free */\r\n            (this.byteStride << 12);\r\n    }\r\n\r\n    /** @internal */\r\n    public _rebuild(): void {\r\n        if (!this._buffer) {\r\n            return;\r\n        }\r\n\r\n        this._buffer._rebuild();\r\n    }\r\n\r\n    /**\r\n     * Returns the kind of the VertexBuffer (string)\r\n     * @returns a string\r\n     */\r\n    public getKind(): string {\r\n        return this._kind;\r\n    }\r\n\r\n    // Properties\r\n\r\n    /**\r\n     * Gets a boolean indicating if the VertexBuffer is updatable?\r\n     * @returns true if the buffer is updatable\r\n     */\r\n    public isUpdatable(): boolean {\r\n        return this._buffer.isUpdatable();\r\n    }\r\n\r\n    /**\r\n     * Gets current buffer's data\r\n     * @returns a DataArray or null\r\n     */\r\n    public getData(): Nullable<DataArray> {\r\n        return this._buffer.getData();\r\n    }\r\n\r\n    /**\r\n     * Gets current buffer's data as a float array. Float data is constructed if the vertex buffer data cannot be returned directly.\r\n     * @param totalVertices number of vertices in the buffer to take into account\r\n     * @param forceCopy defines a boolean indicating that the returned array must be cloned upon returning it\r\n     * @returns a float array containing vertex data\r\n     */\r\n    public getFloatData(totalVertices: number, forceCopy?: boolean): Nullable<FloatArray> {\r\n        const data = this.getData();\r\n        if (!data) {\r\n            return null;\r\n        }\r\n\r\n        const tightlyPackedByteStride = this.getSize() * VertexBuffer.GetTypeByteLength(this.type);\r\n        const count = totalVertices * this.getSize();\r\n\r\n        if (this.type !== VertexBuffer.FLOAT || this.byteStride !== tightlyPackedByteStride) {\r\n            const copy = new Float32Array(count);\r\n            this.forEach(count, (value, index) => (copy[index] = value));\r\n            return copy;\r\n        }\r\n\r\n        if (!(data instanceof Array || data instanceof Float32Array) || this.byteOffset !== 0 || data.length !== count) {\r\n            if (data instanceof Array) {\r\n                const offset = this.byteOffset / 4;\r\n                return data.slice(offset, offset + count);\r\n            } else if (data instanceof ArrayBuffer) {\r\n                return new Float32Array(data, this.byteOffset, count);\r\n            } else {\r\n                let offset = data.byteOffset + this.byteOffset;\r\n                if (forceCopy) {\r\n                    const result = new Float32Array(count);\r\n                    const source = new Float32Array(data.buffer, offset, count);\r\n\r\n                    result.set(source);\r\n\r\n                    return result;\r\n                }\r\n\r\n                // Protect against bad data\r\n                const remainder = offset % 4;\r\n\r\n                if (remainder) {\r\n                    offset = Math.max(0, offset - remainder);\r\n                }\r\n\r\n                return new Float32Array(data.buffer, offset, count);\r\n            }\r\n        }\r\n\r\n        if (forceCopy) {\r\n            return data.slice();\r\n        }\r\n\r\n        return data;\r\n    }\r\n\r\n    /**\r\n     * Gets underlying native buffer\r\n     * @returns underlying native buffer\r\n     */\r\n    public getBuffer(): Nullable<DataBuffer> {\r\n        return this._buffer.getBuffer();\r\n    }\r\n\r\n    /**\r\n     * Gets the stride in float32 units (i.e. byte stride / 4).\r\n     * May not be an integer if the byte stride is not divisible by 4.\r\n     * @returns the stride in float32 units\r\n     * @deprecated Please use byteStride instead.\r\n     */\r\n    public getStrideSize(): number {\r\n        return this.byteStride / VertexBuffer.GetTypeByteLength(this.type);\r\n    }\r\n\r\n    /**\r\n     * Returns the offset as a multiple of the type byte length.\r\n     * @returns the offset in bytes\r\n     * @deprecated Please use byteOffset instead.\r\n     */\r\n    public getOffset(): number {\r\n        return this.byteOffset / VertexBuffer.GetTypeByteLength(this.type);\r\n    }\r\n\r\n    /**\r\n     * Returns the number of components or the byte size per vertex attribute\r\n     * @param sizeInBytes If true, returns the size in bytes or else the size in number of components of the vertex attribute (default: false)\r\n     * @returns the number of components\r\n     */\r\n    public getSize(sizeInBytes = false): number {\r\n        return sizeInBytes ? this._size * VertexBuffer.GetTypeByteLength(this.type) : this._size;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating is the internal buffer of the VertexBuffer is instanced\r\n     * @returns true if this buffer is instanced\r\n     */\r\n    public getIsInstanced(): boolean {\r\n        return this._instanced;\r\n    }\r\n\r\n    /**\r\n     * Returns the instancing divisor, zero for non-instanced (integer).\r\n     * @returns a number\r\n     */\r\n    public getInstanceDivisor(): number {\r\n        return this._instanceDivisor;\r\n    }\r\n\r\n    // Methods\r\n\r\n    /**\r\n     * Store data into the buffer. If the buffer was already used it will be either recreated or updated depending on isUpdatable property\r\n     * @param data defines the data to store\r\n     */\r\n    public create(data?: DataArray): void {\r\n        this._buffer.create(data);\r\n    }\r\n\r\n    /**\r\n     * Updates the underlying buffer according to the passed numeric array or Float32Array.\r\n     * This function will create a new buffer if the current one is not updatable\r\n     * @param data defines the data to store\r\n     */\r\n    public update(data: DataArray): void {\r\n        this._buffer.update(data);\r\n    }\r\n\r\n    /**\r\n     * Updates directly the underlying WebGLBuffer according to the passed numeric array or Float32Array.\r\n     * Returns the directly updated WebGLBuffer.\r\n     * @param data the new data\r\n     * @param offset the new offset\r\n     * @param useBytes set to true if the offset is in bytes\r\n     */\r\n    public updateDirectly(data: DataArray, offset: number, useBytes: boolean = false): void {\r\n        this._buffer.updateDirectly(data, offset, undefined, useBytes);\r\n    }\r\n\r\n    /**\r\n     * Disposes the VertexBuffer and the underlying WebGLBuffer.\r\n     */\r\n    public dispose(): void {\r\n        if (this._ownsBuffer) {\r\n            this._buffer.dispose();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Enumerates each value of this vertex buffer as numbers.\r\n     * @param count the number of values to enumerate\r\n     * @param callback the callback function called for each value\r\n     */\r\n    public forEach(count: number, callback: (value: number, index: number) => void): void {\r\n        VertexBuffer.ForEach(this._buffer.getData()!, this.byteOffset, this.byteStride, this._size, this.type, count, this.normalized, callback);\r\n    }\r\n\r\n    // Enums\r\n    /**\r\n     * Positions\r\n     */\r\n    public static readonly PositionKind = \"position\";\r\n    /**\r\n     * Normals\r\n     */\r\n    public static readonly NormalKind = \"normal\";\r\n    /**\r\n     * Tangents\r\n     */\r\n    public static readonly TangentKind = \"tangent\";\r\n    /**\r\n     * Texture coordinates\r\n     */\r\n    public static readonly UVKind = \"uv\";\r\n    /**\r\n     * Texture coordinates 2\r\n     */\r\n    public static readonly UV2Kind = \"uv2\";\r\n    /**\r\n     * Texture coordinates 3\r\n     */\r\n    public static readonly UV3Kind = \"uv3\";\r\n    /**\r\n     * Texture coordinates 4\r\n     */\r\n    public static readonly UV4Kind = \"uv4\";\r\n    /**\r\n     * Texture coordinates 5\r\n     */\r\n    public static readonly UV5Kind = \"uv5\";\r\n    /**\r\n     * Texture coordinates 6\r\n     */\r\n    public static readonly UV6Kind = \"uv6\";\r\n    /**\r\n     * Colors\r\n     */\r\n    public static readonly ColorKind = \"color\";\r\n    /**\r\n     * Instance Colors\r\n     */\r\n    public static readonly ColorInstanceKind = \"instanceColor\";\r\n    /**\r\n     * Matrix indices (for bones)\r\n     */\r\n    public static readonly MatricesIndicesKind = \"matricesIndices\";\r\n    /**\r\n     * Matrix weights (for bones)\r\n     */\r\n    public static readonly MatricesWeightsKind = \"matricesWeights\";\r\n    /**\r\n     * Additional matrix indices (for bones)\r\n     */\r\n    public static readonly MatricesIndicesExtraKind = \"matricesIndicesExtra\";\r\n    /**\r\n     * Additional matrix weights (for bones)\r\n     */\r\n    public static readonly MatricesWeightsExtraKind = \"matricesWeightsExtra\";\r\n\r\n    /**\r\n     * Deduces the stride given a kind.\r\n     * @param kind The kind string to deduce\r\n     * @returns The deduced stride\r\n     */\r\n    public static DeduceStride(kind: string): number {\r\n        switch (kind) {\r\n            case VertexBuffer.UVKind:\r\n            case VertexBuffer.UV2Kind:\r\n            case VertexBuffer.UV3Kind:\r\n            case VertexBuffer.UV4Kind:\r\n            case VertexBuffer.UV5Kind:\r\n            case VertexBuffer.UV6Kind:\r\n                return 2;\r\n            case VertexBuffer.NormalKind:\r\n            case VertexBuffer.PositionKind:\r\n                return 3;\r\n            case VertexBuffer.ColorKind:\r\n            case VertexBuffer.MatricesIndicesKind:\r\n            case VertexBuffer.MatricesIndicesExtraKind:\r\n            case VertexBuffer.MatricesWeightsKind:\r\n            case VertexBuffer.MatricesWeightsExtraKind:\r\n            case VertexBuffer.TangentKind:\r\n                return 4;\r\n            default:\r\n                throw new Error(\"Invalid kind '\" + kind + \"'\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the byte length of the given type.\r\n     * @param type the type\r\n     * @returns the number of bytes\r\n     */\r\n    public static GetTypeByteLength(type: number): number {\r\n        switch (type) {\r\n            case VertexBuffer.BYTE:\r\n            case VertexBuffer.UNSIGNED_BYTE:\r\n                return 1;\r\n            case VertexBuffer.SHORT:\r\n            case VertexBuffer.UNSIGNED_SHORT:\r\n                return 2;\r\n            case VertexBuffer.INT:\r\n            case VertexBuffer.UNSIGNED_INT:\r\n            case VertexBuffer.FLOAT:\r\n                return 4;\r\n            default:\r\n                throw new Error(`Invalid type '${type}'`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Enumerates each value of the given parameters as numbers.\r\n     * @param data the data to enumerate\r\n     * @param byteOffset the byte offset of the data\r\n     * @param byteStride the byte stride of the data\r\n     * @param componentCount the number of components per element\r\n     * @param componentType the type of the component\r\n     * @param count the number of values to enumerate\r\n     * @param normalized whether the data is normalized\r\n     * @param callback the callback function called for each value\r\n     */\r\n    public static ForEach(\r\n        data: DataArray,\r\n        byteOffset: number,\r\n        byteStride: number,\r\n        componentCount: number,\r\n        componentType: number,\r\n        count: number,\r\n        normalized: boolean,\r\n        callback: (value: number, index: number) => void\r\n    ): void {\r\n        if (data instanceof Array) {\r\n            let offset = byteOffset / 4;\r\n            const stride = byteStride / 4;\r\n            for (let index = 0; index < count; index += componentCount) {\r\n                for (let componentIndex = 0; componentIndex < componentCount; componentIndex++) {\r\n                    callback(data[offset + componentIndex], index + componentIndex);\r\n                }\r\n                offset += stride;\r\n            }\r\n        } else {\r\n            const dataView = data instanceof ArrayBuffer ? new DataView(data) : new DataView(data.buffer, data.byteOffset, data.byteLength);\r\n            const componentByteLength = VertexBuffer.GetTypeByteLength(componentType);\r\n            for (let index = 0; index < count; index += componentCount) {\r\n                let componentByteOffset = byteOffset;\r\n                for (let componentIndex = 0; componentIndex < componentCount; componentIndex++) {\r\n                    const value = VertexBuffer._GetFloatValue(dataView, componentType, componentByteOffset, normalized);\r\n                    callback(value, index + componentIndex);\r\n                    componentByteOffset += componentByteLength;\r\n                }\r\n                byteOffset += byteStride;\r\n            }\r\n        }\r\n    }\r\n\r\n    private static _GetFloatValue(dataView: DataView, type: number, byteOffset: number, normalized: boolean): number {\r\n        switch (type) {\r\n            case VertexBuffer.BYTE: {\r\n                let value = dataView.getInt8(byteOffset);\r\n                if (normalized) {\r\n                    value = Math.max(value / 127, -1);\r\n                }\r\n                return value;\r\n            }\r\n            case VertexBuffer.UNSIGNED_BYTE: {\r\n                let value = dataView.getUint8(byteOffset);\r\n                if (normalized) {\r\n                    value = value / 255;\r\n                }\r\n                return value;\r\n            }\r\n            case VertexBuffer.SHORT: {\r\n                let value = dataView.getInt16(byteOffset, true);\r\n                if (normalized) {\r\n                    value = Math.max(value / 32767, -1);\r\n                }\r\n                return value;\r\n            }\r\n            case VertexBuffer.UNSIGNED_SHORT: {\r\n                let value = dataView.getUint16(byteOffset, true);\r\n                if (normalized) {\r\n                    value = value / 65535;\r\n                }\r\n                return value;\r\n            }\r\n            case VertexBuffer.INT: {\r\n                return dataView.getInt32(byteOffset, true);\r\n            }\r\n            case VertexBuffer.UNSIGNED_INT: {\r\n                return dataView.getUint32(byteOffset, true);\r\n            }\r\n            case VertexBuffer.FLOAT: {\r\n                return dataView.getFloat32(byteOffset, true);\r\n            }\r\n            default: {\r\n                throw new Error(`Invalid component type ${type}`);\r\n            }\r\n        }\r\n    }\r\n}\r\n"]}