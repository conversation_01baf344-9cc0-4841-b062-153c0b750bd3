{"version": 3, "file": "bakedVertexAnimationManager.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/BakedVertexAnimation/bakedVertexAnimationManager.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,MAAM,oBAAoB,CAAC;AAE1G,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAE/C,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAwDrD;;;;GAIG;AACH,MAAM,OAAO,2BAA2B;IA+BpC;;;OAGG;IACH,YAAY,KAAuB;QAhC3B,aAAQ,GAA0B,IAAI,CAAC;QAQvC,eAAU,GAAG,IAAI,CAAC;QAC1B;;WAEG;QAGI,cAAS,GAAG,IAAI,CAAC;QAQxB;;WAEG;QAEI,SAAI,GAAG,CAAC,CAAC;QAOZ,KAAK,GAAG,KAAK,IAAI,WAAW,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,mBAAmB,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;IACxD,CAAC;IAED,gBAAgB;IACT,+BAA+B;QAClC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YACnC,IAAU,IAAK,CAAC,2BAA2B,KAAK,IAAI,EAAE;gBAClD,IAAI,CAAC,+BAA+B,EAAE,CAAC;aAC1C;SACJ;IACL,CAAC;IAED;;;;OAIG;IACI,IAAI,CAAC,MAAc,EAAE,YAAY,GAAG,KAAK;QAC5C,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpC,OAAO;SACV;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;QACrC,MAAM,CAAC,SAAS,CAAC,yCAAyC,EAAE,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QACjG,MAAM,CAAC,QAAQ,CAAC,0BAA0B,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAEvD,IAAI,CAAC,YAAY,EAAE;YACf,MAAM,CAAC,UAAU,CAAC,8BAA8B,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;SAC/E;QAED,MAAM,CAAC,UAAU,CAAC,6BAA6B,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACpE,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,MAAM,IAAI,GAAG,IAAI,2BAA2B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAClB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,sBAAsB,CAAC,UAAkB,EAAE,QAAgB,EAAE,SAAiB,CAAC,EAAE,uBAA+B,EAAE;QACrH,IAAI,CAAC,mBAAmB,GAAG,IAAI,OAAO,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,oBAAoB,CAAC,CAAC;IAC/F,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,oBAA8B;;QACzC,IAAI,oBAAoB,EAAE;YACtB,MAAA,IAAI,CAAC,QAAQ,0CAAE,OAAO,EAAE,CAAC;SAC5B;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,6BAA6B,CAAC;IACzC,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,MAAmC;QAC7C,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAClD,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe;QACnD,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAClE,CAAC;CACJ;AAjIG;IAFC,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,iCAAiC,CAAC;4DACd;AAQtC;IAFC,SAAS,EAAE;IACX,gBAAgB,CAAC,iCAAiC,CAAC;8DAC5B;AAMxB;IADC,SAAS,EAAE;wEACwB;AAMpC;IADC,SAAS,EAAE;yDACI", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport { serialize, expandToProperty, serializeAsTexture, SerializationHelper } from \"../Misc/decorators\";\r\nimport type { BaseTexture } from \"../Materials/Textures/baseTexture\";\r\nimport { Vector4 } from \"../Maths/math.vector\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\n\r\n/**\r\n * Interface for baked vertex animation texture, see BakedVertexAnimationManager\r\n * @since 5.0\r\n */\r\nexport interface IBakedVertexAnimationManager {\r\n    /**\r\n     * The vertex animation texture\r\n     */\r\n    texture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the edges<PERSON>enderer is active\r\n     */\r\n    isEnabled: boolean;\r\n\r\n    /**\r\n     * The animation parameters for the mesh. See setAnimationParameters()\r\n     */\r\n    animationParameters: Vector4;\r\n\r\n    /**\r\n     * The time counter, to pick the correct animation frame.\r\n     */\r\n    time: number;\r\n\r\n    /**\r\n     * Binds to the effect.\r\n     * @param effect The effect to bind to.\r\n     * @param useInstances True when it's an instance.\r\n     */\r\n    bind(effect: Effect, useInstances: boolean): void;\r\n\r\n    /**\r\n     * Sets animation parameters.\r\n     * @param startFrame The first frame of the animation.\r\n     * @param endFrame The last frame of the animation.\r\n     * @param offset The offset when starting the animation.\r\n     * @param speedFramesPerSecond The frame rate.\r\n     */\r\n    setAnimationParameters(startFrame: number, endFrame: number, offset: number, speedFramesPerSecond: number): void;\r\n\r\n    /**\r\n     * Disposes the resources of the manager.\r\n     * @param forceDisposeTextures - Forces the disposal of all textures.\r\n     */\r\n    dispose(forceDisposeTextures?: boolean): void;\r\n\r\n    /**\r\n     * Get the current class name useful for serialization or dynamic coding.\r\n     * @returns \"BakedVertexAnimationManager\"\r\n     */\r\n    getClassName(): string;\r\n}\r\n\r\n/**\r\n * This class is used to animate meshes using a baked vertex animation texture\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/baked_texture_animations\r\n * @since 5.0\r\n */\r\nexport class BakedVertexAnimationManager implements IBakedVertexAnimationManager {\r\n    private _scene: Scene;\r\n\r\n    private _texture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * The vertex animation texture\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markSubMeshesAsAttributesDirty\")\r\n    public texture: Nullable<BaseTexture>;\r\n\r\n    private _isEnabled = true;\r\n    /**\r\n     * Enable or disable the vertex animation manager\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markSubMeshesAsAttributesDirty\")\r\n    public isEnabled = true;\r\n\r\n    /**\r\n     * The animation parameters for the mesh. See setAnimationParameters()\r\n     */\r\n    @serialize()\r\n    public animationParameters: Vector4;\r\n\r\n    /**\r\n     * The time counter, to pick the correct animation frame.\r\n     */\r\n    @serialize()\r\n    public time = 0;\r\n\r\n    /**\r\n     * Creates a new BakedVertexAnimationManager\r\n     * @param scene defines the current scene\r\n     */\r\n    constructor(scene?: Nullable<Scene>) {\r\n        scene = scene || EngineStore.LastCreatedScene;\r\n        if (!scene) {\r\n            return;\r\n        }\r\n        this._scene = scene;\r\n        this.animationParameters = new Vector4(0, 0, 0, 30);\r\n    }\r\n\r\n    /** @internal */\r\n    public _markSubMeshesAsAttributesDirty(): void {\r\n        for (const mesh of this._scene.meshes) {\r\n            if ((<any>mesh).bakedVertexAnimationManager === this) {\r\n                mesh._markSubMeshesAsAttributesDirty();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Binds to the effect.\r\n     * @param effect The effect to bind to.\r\n     * @param useInstances True when it's an instance.\r\n     */\r\n    public bind(effect: Effect, useInstances = false): void {\r\n        if (!this._texture || !this._isEnabled) {\r\n            return;\r\n        }\r\n\r\n        const size = this._texture.getSize();\r\n        effect.setFloat2(\"bakedVertexAnimationTextureSizeInverted\", 1.0 / size.width, 1.0 / size.height);\r\n        effect.setFloat(\"bakedVertexAnimationTime\", this.time);\r\n\r\n        if (!useInstances) {\r\n            effect.setVector4(\"bakedVertexAnimationSettings\", this.animationParameters);\r\n        }\r\n\r\n        effect.setTexture(\"bakedVertexAnimationTexture\", this._texture);\r\n    }\r\n\r\n    /**\r\n     * Clone the current manager\r\n     * @returns a new BakedVertexAnimationManager\r\n     */\r\n    public clone(): BakedVertexAnimationManager {\r\n        const copy = new BakedVertexAnimationManager(this._scene);\r\n        this.copyTo(copy);\r\n        return copy;\r\n    }\r\n\r\n    /**\r\n     * Sets animation parameters.\r\n     * @param startFrame The first frame of the animation.\r\n     * @param endFrame The last frame of the animation.\r\n     * @param offset The offset when starting the animation.\r\n     * @param speedFramesPerSecond The frame rate.\r\n     */\r\n    public setAnimationParameters(startFrame: number, endFrame: number, offset: number = 0, speedFramesPerSecond: number = 30): void {\r\n        this.animationParameters = new Vector4(startFrame, endFrame, offset, speedFramesPerSecond);\r\n    }\r\n\r\n    /**\r\n     * Disposes the resources of the manager.\r\n     * @param forceDisposeTextures - Forces the disposal of all textures.\r\n     */\r\n    public dispose(forceDisposeTextures?: boolean): void {\r\n        if (forceDisposeTextures) {\r\n            this._texture?.dispose();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the current class name useful for serialization or dynamic coding.\r\n     * @returns \"BakedVertexAnimationManager\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"BakedVertexAnimationManager\";\r\n    }\r\n\r\n    /**\r\n     * Makes a duplicate of the current instance into another one.\r\n     * @param vatMap define the instance where to copy the info\r\n     */\r\n    public copyTo(vatMap: BakedVertexAnimationManager): void {\r\n        SerializationHelper.Clone(() => vatMap, this);\r\n    }\r\n\r\n    /**\r\n     * Serializes this vertex animation instance\r\n     * @returns - An object with the serialized instance.\r\n     */\r\n    public serialize(): any {\r\n        return SerializationHelper.Serialize(this);\r\n    }\r\n\r\n    /**\r\n     * Parses a vertex animation setting from a serialized object.\r\n     * @param source - Serialized object.\r\n     * @param scene Defines the scene we are parsing for\r\n     * @param rootUrl Defines the rootUrl to load from\r\n     */\r\n    public parse(source: any, scene: Scene, rootUrl: string): void {\r\n        SerializationHelper.Parse(() => this, source, scene, rootUrl);\r\n    }\r\n}\r\n"]}