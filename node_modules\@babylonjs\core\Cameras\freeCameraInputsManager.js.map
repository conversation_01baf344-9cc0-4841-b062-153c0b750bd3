{"version": 3, "file": "freeCameraInputsManager.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Cameras/freeCameraInputsManager.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EAAE,2BAA2B,EAAE,MAAM,+CAA+C,CAAC;AAC5F,OAAO,EAAE,oBAAoB,EAAE,MAAM,wCAAwC,CAAC;AAC9E,OAAO,EAAE,yBAAyB,EAAE,MAAM,6CAA6C,CAAC;AACxF,OAAO,EAAE,oBAAoB,EAAE,MAAM,wCAAwC,CAAC;AAG9E;;;;GAIG;AACH,MAAM,OAAO,uBAAwB,SAAQ,mBAA+B;IASxE;;;OAGG;IACH,YAAY,MAAkB;QAC1B,KAAK,CAAC,MAAM,CAAC,CAAC;QAblB;;WAEG;QACI,gBAAW,GAAmC,IAAI,CAAC;QAC1D;;WAEG;QACI,qBAAgB,GAAwC,IAAI,CAAC;IAOpE,CAAC;IAED;;;OAGG;IACH,WAAW;QACP,IAAI,CAAC,GAAG,CAAC,IAAI,2BAA2B,EAAE,CAAC,CAAC;QAC5C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACH,QAAQ,CAAC,YAAY,GAAG,IAAI;QACxB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,IAAI,CAAC,WAAW,GAAG,IAAI,oBAAoB,CAAC,YAAY,CAAC,CAAC;YAC1D,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAC9B;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACH,WAAW;QACP,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SACjC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACH,aAAa;QACT,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,IAAI,CAAC,gBAAgB,GAAG,IAAI,yBAAyB,EAAE,CAAC;YACxD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACnC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACH,gBAAgB;QACZ,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACtC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACH,QAAQ;QACJ,IAAI,CAAC,GAAG,CAAC,IAAI,oBAAoB,EAAE,CAAC,CAAC;QACrC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,KAAK;QACR,KAAK,CAAC,KAAK,EAAE,CAAC;QACd,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC5B,CAAC;CACJ", "sourcesContent": ["import type { <PERSON>Camera } from \"./freeCamera\";\r\nimport { CameraInputsManager } from \"./cameraInputsManager\";\r\nimport { FreeCameraKeyboardMoveInput } from \"../Cameras/Inputs/freeCameraKeyboardMoveInput\";\r\nimport { FreeCameraMouseInput } from \"../Cameras/Inputs/freeCameraMouseInput\";\r\nimport { FreeCameraMouseWheelInput } from \"../Cameras/Inputs/freeCameraMouseWheelInput\";\r\nimport { FreeCameraTouchInput } from \"../Cameras/Inputs/freeCameraTouchInput\";\r\nimport type { Nullable } from \"../types\";\r\n\r\n/**\r\n * Default Inputs manager for the FreeCamera.\r\n * It groups all the default supported inputs for ease of use.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n */\r\nexport class FreeCameraInputsManager extends CameraInputsManager<FreeCamera> {\r\n    /**\r\n     * @internal\r\n     */\r\n    public _mouseInput: Nullable<FreeCameraMouseInput> = null;\r\n    /**\r\n     * @internal\r\n     */\r\n    public _mouseWheelInput: Nullable<FreeCameraMouseWheelInput> = null;\r\n    /**\r\n     * Instantiates a new FreeCameraInputsManager.\r\n     * @param camera Defines the camera the inputs belong to\r\n     */\r\n    constructor(camera: FreeCamera) {\r\n        super(camera);\r\n    }\r\n\r\n    /**\r\n     * Add keyboard input support to the input manager.\r\n     * @returns the current input manager\r\n     */\r\n    addKeyboard(): FreeCameraInputsManager {\r\n        this.add(new FreeCameraKeyboardMoveInput());\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Add mouse input support to the input manager.\r\n     * @param touchEnabled if the FreeCameraMouseInput should support touch (default: true)\r\n     * @returns the current input manager\r\n     */\r\n    addMouse(touchEnabled = true): FreeCameraInputsManager {\r\n        if (!this._mouseInput) {\r\n            this._mouseInput = new FreeCameraMouseInput(touchEnabled);\r\n            this.add(this._mouseInput);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Removes the mouse input support from the manager\r\n     * @returns the current input manager\r\n     */\r\n    removeMouse(): FreeCameraInputsManager {\r\n        if (this._mouseInput) {\r\n            this.remove(this._mouseInput);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Add mouse wheel input support to the input manager.\r\n     * @returns the current input manager\r\n     */\r\n    addMouseWheel(): FreeCameraInputsManager {\r\n        if (!this._mouseWheelInput) {\r\n            this._mouseWheelInput = new FreeCameraMouseWheelInput();\r\n            this.add(this._mouseWheelInput);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Removes the mouse wheel input support from the manager\r\n     * @returns the current input manager\r\n     */\r\n    removeMouseWheel(): FreeCameraInputsManager {\r\n        if (this._mouseWheelInput) {\r\n            this.remove(this._mouseWheelInput);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Add touch input support to the input manager.\r\n     * @returns the current input manager\r\n     */\r\n    addTouch(): FreeCameraInputsManager {\r\n        this.add(new FreeCameraTouchInput());\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Remove all attached input methods from a camera\r\n     */\r\n    public clear(): void {\r\n        super.clear();\r\n        this._mouseInput = null;\r\n    }\r\n}\r\n"]}