{"version": 3, "file": "meshCollisionData.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Collisions/meshCollisionData.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAM/C;;GAEG;AACH,gEAAgE;AAChE,MAAM,OAAO,kBAAkB;IAA/B;QACW,qBAAgB,GAAG,KAAK,CAAC;QACzB,mBAAc,GAAG,CAAC,CAAC,CAAC;QACpB,oBAAe,GAAG,CAAC,CAAC,CAAC;QACrB,uBAAkB,GAA6B,IAAI,CAAC;QACpD,cAAS,GAAuB,IAAI,CAAC;QACrC,8BAAyB,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACjD,+BAA0B,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAGlD,uBAAkB,GAAG,IAAI,CAAC;IACrC,CAAC;CAAA", "sourcesContent": ["import type { Collide<PERSON> } from \"./collider\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Observer } from \"../Misc/observable\";\r\n\r\ndeclare type AbstractMesh = import(\"../Meshes/abstractMesh\").AbstractMesh;\r\n\r\n/**\r\n * @internal\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class _MeshCollisionData {\r\n    public _checkCollisions = false;\r\n    public _collisionMask = -1;\r\n    public _collisionGroup = -1;\r\n    public _surroundingMeshes: Nullable<AbstractMesh[]> = null;\r\n    public _collider: Nullable<Collider> = null;\r\n    public _oldPositionForCollisions = new Vector3(0, 0, 0);\r\n    public _diffPositionForCollisions = new Vector3(0, 0, 0);\r\n    public _onCollideObserver: Nullable<Observer<AbstractMesh>>;\r\n    public _onCollisionPositionChangeObserver: Nullable<Observer<Vector3>>;\r\n    public _collisionResponse = true;\r\n}\r\n"]}