{"version": 3, "file": "vrDeviceOrientationGamepadCamera.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Cameras/VR/vrDeviceOrientationGamepadCamera.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,6BAA6B,EAAE,MAAM,iCAAiC,CAAC;AAChF,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEpD,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAC;AAErD,OAAO,sCAAsC,CAAC;AAE9C,IAAI,CAAC,kBAAkB,CAAC,kCAAkC,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IACxE,OAAO,GAAG,EAAE,CAAC,IAAI,gCAAgC,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;AACnF,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACH,MAAM,OAAO,gCAAiC,SAAQ,6BAA6B;IAC/E;;;;;;;OAOG;IACH,YAAY,IAAY,EAAE,QAAiB,EAAE,KAAa,EAAE,oBAAoB,GAAG,IAAI,EAAE,kBAAmC,eAAe,CAAC,UAAU,EAAE;QACpJ,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,oBAAoB,EAAE,eAAe,CAAC,CAAC;QAa9D,gBAAW,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAXlD,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,kCAAkC,CAAC;IAC9C,CAAC;CAGJ", "sourcesContent": ["import { VRDeviceOrientationFreeCamera } from \"./vrDeviceOrientationFreeCamera\";\r\nimport { VRCameraMetrics } from \"./vrCameraMetrics\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport { Node } from \"../../node\";\r\nimport { setVRRigMode } from \"../RigModes/vrRigMode\";\r\n\r\nimport \"../../Gamepads/gamepadSceneComponent\";\r\n\r\nNode.AddNodeConstructor(\"VRDeviceOrientationGamepadCamera\", (name, scene) => {\r\n    return () => new VRDeviceOrientationGamepadCamera(name, Vector3.Zero(), scene);\r\n});\r\n\r\n/**\r\n * Camera used to simulate VR rendering (based on VRDeviceOrientationFreeCamera)\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#vr-device-orientation-cameras\r\n */\r\nexport class VRDeviceOrientationGamepadCamera extends VRDeviceOrientationFreeCamera {\r\n    /**\r\n     * Creates a new VRDeviceOrientationGamepadCamera\r\n     * @param name defines camera name\r\n     * @param position defines the start position of the camera\r\n     * @param scene defines the scene the camera belongs to\r\n     * @param compensateDistortion defines if the camera needs to compensate the lens distortion\r\n     * @param vrCameraMetrics defines the vr metrics associated to the camera\r\n     */\r\n    constructor(name: string, position: Vector3, scene?: Scene, compensateDistortion = true, vrCameraMetrics: VRCameraMetrics = VRCameraMetrics.GetDefault()) {\r\n        super(name, position, scene, compensateDistortion, vrCameraMetrics);\r\n\r\n        this.inputs.addGamepad();\r\n    }\r\n\r\n    /**\r\n     * Gets camera class name\r\n     * @returns VRDeviceOrientationGamepadCamera\r\n     */\r\n    public getClassName(): string {\r\n        return \"VRDeviceOrientationGamepadCamera\";\r\n    }\r\n\r\n    protected _setRigMode = setVRRigMode.bind(null, this);\r\n}\r\n"]}