{"version": 3, "file": "animatable.interface.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Animations/animatable.interface.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { Nullable } from \"../types\";\r\n\r\ndeclare type Animation = import(\"./animation\").Animation;\r\n\r\n/**\r\n * Interface containing an array of animations\r\n */\r\nexport interface IAnimatable {\r\n    /**\r\n     * Array of animations\r\n     */\r\n    animations: Nullable<Array<Animation>>;\r\n}\r\n"]}