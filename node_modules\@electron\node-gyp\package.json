{"name": "@electron/node-gyp", "description": "Node.js native addon build tool", "license": "MIT", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "gyp"], "version": "10.2.0-electron.1", "installVersion": 11, "author": "<PERSON> <<EMAIL>> (http://tootallnate.net)", "repository": {"type": "git", "url": "git://github.com/nodejs/node-gyp.git"}, "preferGlobal": true, "bin": "./bin/node-gyp.js", "main": "./lib/node-gyp.js", "dependencies": {"env-paths": "^2.2.0", "exponential-backoff": "^3.1.1", "glob": "^8.1.0", "graceful-fs": "^4.2.6", "make-fetch-happen": "^10.2.1", "nopt": "^6.0.0", "proc-log": "^2.0.1", "semver": "^7.3.5", "tar": "^6.2.1", "which": "^2.0.2"}, "engines": {"node": ">=12.13.0"}, "devDependencies": {"bindings": "^1.5.0", "cross-env": "^7.0.3", "mocha": "^10.2.0", "nan": "^2.14.2", "require-inject": "^1.4.4", "standard": "^17.0.0"}, "scripts": {"lint": "standard --write \"*/*.js\" \"test/**/*.js\" \".github/**/*.js\"", "test": "cross-env NODE_GYP_NULL_LOGGER=true mocha --timeout 15000 test/test-download.js test/test-*"}}