'use strict'

var listenerCount = require('events').listenerCount
// listenerCount isn't in node 0.10, so here's a basic polyfill
listenerCount = listenerCount || function (ee, event) {
  var listeners = ee && ee._events && ee._events[event]
  if (Array.isArray(listeners)) {
    return listeners.length
  } else if (typeof listeners === 'function') {
    return 1
  } else {
    return 0
  }
}

module.exports = listenerCount
