import { Bone } from "./bone";
import { Observable } from "../Misc/observable";
import { Vector3, Matrix } from "../Maths/math.vector";
import type { Scene } from "../scene";
import type { Nullable } from "../types";
import type { AbstractMesh } from "../Meshes/abstractMesh";
import { RawTexture } from "../Materials/Textures/rawTexture";
import type { Animatable } from "../Animations/animatable";
import type { AnimationPropertiesOverride } from "../Animations/animationPropertiesOverride";
import { Animation } from "../Animations/animation";
import { AnimationRange } from "../Animations/animationRange";
import type { IInspectable } from "../Misc/iInspectable";
import type { IAnimatable } from "../Animations/animatable.interface";
import type { AbstractScene } from "../abstractScene";
/**
 * Class used to handle skinning animations
 * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/bonesSkeletons
 */
export declare class Skeleton implements IAnimatable {
    /** defines the skeleton name */
    name: string;
    /** defines the skeleton Id */
    id: string;
    /**
     * Defines the list of child bones
     */
    bones: Bone[];
    /**
     * Defines an estimate of the dimension of the skeleton at rest
     */
    dimensionsAtRest: Vector3;
    /**
     * Defines a boolean indicating if the root matrix is provided by meshes or by the current skeleton (this is the default value)
     */
    needInitialSkinMatrix: boolean;
    /**
     * Gets the list of animations attached to this skeleton
     */
    animations: Array<Animation>;
    private _scene;
    private _isDirty;
    private _transformMatrices;
    private _transformMatrixTexture;
    private _meshesWithPoseMatrix;
    private _animatables;
    private _identity;
    private _synchronizedWithMesh;
    private _ranges;
    private _absoluteTransformIsDirty;
    private _canUseTextureForBones;
    private _uniqueId;
    /** @internal */
    _numBonesWithLinkedTransformNode: number;
    /** @internal */
    _hasWaitingData: Nullable<boolean>;
    /** @internal */
    _parentContainer: Nullable<AbstractScene>;
    /**
     * Specifies if the skeleton should be serialized
     */
    doNotSerialize: boolean;
    private _useTextureToStoreBoneMatrices;
    /**
     * Gets or sets a boolean indicating that bone matrices should be stored as a texture instead of using shader uniforms (default is true).
     * Please note that this option is not available if the hardware does not support it
     */
    get useTextureToStoreBoneMatrices(): boolean;
    set useTextureToStoreBoneMatrices(value: boolean);
    private _animationPropertiesOverride;
    /**
     * Gets or sets the animation properties override
     */
    get animationPropertiesOverride(): Nullable<AnimationPropertiesOverride>;
    set animationPropertiesOverride(value: Nullable<AnimationPropertiesOverride>);
    /**
     * List of inspectable custom properties (used by the Inspector)
     * @see https://doc.babylonjs.com/toolsAndResources/inspector#extensibility
     */
    inspectableCustomProperties: IInspectable[];
    /**
     * An observable triggered before computing the skeleton's matrices
     */
    onBeforeComputeObservable: Observable<Skeleton>;
    /**
     * Gets a boolean indicating that the skeleton effectively stores matrices into a texture
     */
    get isUsingTextureForMatrices(): boolean;
    /**
     * Gets the unique ID of this skeleton
     */
    get uniqueId(): number;
    /**
     * Creates a new skeleton
     * @param name defines the skeleton name
     * @param id defines the skeleton Id
     * @param scene defines the hosting scene
     */
    constructor(
    /** defines the skeleton name */
    name: string, 
    /** defines the skeleton Id */
    id: string, scene: Scene);
    /**
     * Gets the current object class name.
     * @returns the class name
     */
    getClassName(): string;
    /**
     * Returns an array containing the root bones
     * @returns an array containing the root bones
     */
    getChildren(): Array<Bone>;
    /**
     * Gets the list of transform matrices to send to shaders (one matrix per bone)
     * @param mesh defines the mesh to use to get the root matrix (if needInitialSkinMatrix === true)
     * @returns a Float32Array containing matrices data
     */
    getTransformMatrices(mesh: AbstractMesh): Float32Array;
    /**
     * Gets the list of transform matrices to send to shaders inside a texture (one matrix per bone)
     * @param mesh defines the mesh to use to get the root matrix (if needInitialSkinMatrix === true)
     * @returns a raw texture containing the data
     */
    getTransformMatrixTexture(mesh: AbstractMesh): Nullable<RawTexture>;
    /**
     * Gets the current hosting scene
     * @returns a scene object
     */
    getScene(): Scene;
    /**
     * Gets a string representing the current skeleton data
     * @param fullDetails defines a boolean indicating if we want a verbose version
     * @returns a string representing the current skeleton data
     */
    toString(fullDetails?: boolean): string;
    /**
     * Get bone's index searching by name
     * @param name defines bone's name to search for
     * @returns the indice of the bone. Returns -1 if not found
     */
    getBoneIndexByName(name: string): number;
    /**
     * Create a new animation range
     * @param name defines the name of the range
     * @param from defines the start key
     * @param to defines the end key
     */
    createAnimationRange(name: string, from: number, to: number): void;
    /**
     * Delete a specific animation range
     * @param name defines the name of the range
     * @param deleteFrames defines if frames must be removed as well
     */
    deleteAnimationRange(name: string, deleteFrames?: boolean): void;
    /**
     * Gets a specific animation range
     * @param name defines the name of the range to look for
     * @returns the requested animation range or null if not found
     */
    getAnimationRange(name: string): Nullable<AnimationRange>;
    /**
     * Gets the list of all animation ranges defined on this skeleton
     * @returns an array
     */
    getAnimationRanges(): Nullable<AnimationRange>[];
    /**
     * Copy animation range from a source skeleton.
     * This is not for a complete retargeting, only between very similar skeleton's with only possible bone length differences
     * @param source defines the source skeleton
     * @param name defines the name of the range to copy
     * @param rescaleAsRequired defines if rescaling must be applied if required
     * @returns true if operation was successful
     */
    copyAnimationRange(source: Skeleton, name: string, rescaleAsRequired?: boolean): boolean;
    /**
     * Forces the skeleton to go to rest pose
     */
    returnToRest(): void;
    private _getHighestAnimationFrame;
    /**
     * Begin a specific animation range
     * @param name defines the name of the range to start
     * @param loop defines if looping must be turned on (false by default)
     * @param speedRatio defines the speed ratio to apply (1 by default)
     * @param onAnimationEnd defines a callback which will be called when animation will end
     * @returns a new animatable
     */
    beginAnimation(name: string, loop?: boolean, speedRatio?: number, onAnimationEnd?: () => void): Nullable<Animatable>;
    /**
     * Convert the keyframes for a range of animation on a skeleton to be relative to a given reference frame.
     * @param skeleton defines the Skeleton containing the animation range to convert
     * @param referenceFrame defines the frame that keyframes in the range will be relative to
     * @param range defines the name of the AnimationRange belonging to the Skeleton to convert
     * @returns the original skeleton
     */
    static MakeAnimationAdditive(skeleton: Skeleton, referenceFrame: number | undefined, range: string): Nullable<Skeleton>;
    /** @internal */
    _markAsDirty(): void;
    /**
     * @internal
     */
    _registerMeshWithPoseMatrix(mesh: AbstractMesh): void;
    /**
     * @internal
     */
    _unregisterMeshWithPoseMatrix(mesh: AbstractMesh): void;
    private _computeTransformMatrices;
    /**
     * Build all resources required to render a skeleton
     */
    prepare(): void;
    /**
     * Gets the list of animatables currently running for this skeleton
     * @returns an array of animatables
     */
    getAnimatables(): IAnimatable[];
    /**
     * Clone the current skeleton
     * @param name defines the name of the new skeleton
     * @param id defines the id of the new skeleton
     * @returns the new skeleton
     */
    clone(name: string, id?: string): Skeleton;
    /**
     * Enable animation blending for this skeleton
     * @param blendingSpeed defines the blending speed to apply
     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-blending
     */
    enableBlending(blendingSpeed?: number): void;
    /**
     * Releases all resources associated with the current skeleton
     */
    dispose(): void;
    /**
     * Serialize the skeleton in a JSON object
     * @returns a JSON object
     */
    serialize(): any;
    /**
     * Creates a new skeleton from serialized data
     * @param parsedSkeleton defines the serialized data
     * @param scene defines the hosting scene
     * @returns a new skeleton
     */
    static Parse(parsedSkeleton: any, scene: Scene): Skeleton;
    /**
     * Compute all node absolute transforms
     * @param forceUpdate defines if computation must be done even if cache is up to date
     */
    computeAbsoluteTransforms(forceUpdate?: boolean): void;
    /**
     * Gets the root pose matrix
     * @returns a matrix
     */
    getPoseMatrix(): Nullable<Matrix>;
    /**
     * Sorts bones per internal index
     */
    sortBones(): void;
    private _sortBones;
    /**
     * Set the current local matrix as the restPose for all bones in the skeleton.
     */
    setCurrentPoseAsRest(): void;
}
