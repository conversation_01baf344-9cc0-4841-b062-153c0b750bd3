{"version": 3, "file": "BaseCameraMouseWheelInput.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Cameras/Inputs/BaseCameraMouseWheelInput.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAElD,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAInD,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAE/D,OAAO,EAAE,cAAc,EAAE,MAAM,gCAAgC,CAAC;AAChE,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEzC;;;;GAIG;AACH,MAAM,OAAgB,yBAAyB;IAA/C;QAMI;;;WAGG;QAEI,oBAAe,GAAG,GAAG,CAAC;QAE7B;;;WAGG;QAEI,oBAAe,GAAG,GAAG,CAAC;QAE7B;;;WAGG;QAEI,oBAAe,GAAG,GAAG,CAAC;QAE7B;;WAEG;QACI,wBAAmB,GAAG,IAAI,UAAU,EAAqE,CAAC;QAoFjH;;;WAGG;QACO,iBAAY,GAAW,CAAC,CAAC;QAEnC;;;WAGG;QACO,iBAAY,GAAW,CAAC,CAAC;QAEnC;;;WAGG;QACO,iBAAY,GAAW,CAAC,CAAC;QAEnC;;;;;;WAMG;QACc,kBAAa,GAAG,EAAE,CAAC;QAEpC;;;;WAIG;QACc,eAAU,GAAG,GAAG,CAAC;IACtC,CAAC;IAhHG;;;;;OAKG;IACI,aAAa,CAAC,gBAA0B;QAC3C,gBAAgB,GAAG,KAAK,CAAC,gCAAgC,CAAC,SAAS,CAAC,CAAC;QAErE,IAAI,CAAC,MAAM,GAAG,CAAC,OAAO,EAAE,EAAE;YACtB,sDAAsD;YACtD,IAAI,OAAO,CAAC,IAAI,KAAK,iBAAiB,CAAC,YAAY,EAAE;gBACjD,OAAO;aACV;YAED,MAAM,KAAK,GAAgB,OAAO,CAAC,KAAK,CAAC;YAEzC,MAAM,aAAa,GAAG,KAAK,CAAC,SAAS,KAAK,cAAc,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kEAAkE;YAEpK,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;YAC7F,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;YAC7F,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;YAE7F,IAAI,KAAK,CAAC,cAAc,EAAE;gBACtB,IAAI,CAAC,gBAAgB,EAAE;oBACnB,KAAK,CAAC,cAAc,EAAE,CAAC;iBAC1B;aACJ;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,yBAAyB,CAAC,IAAI,CAAC,MAAM,EAAE,iBAAiB,CAAC,YAAY,CAAC,CAAC;IACjI,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,4BAA4B,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAClF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;SACtB;QACD,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;SACpC;IACL,CAAC;IAED;;OAEG;IACI,WAAW;QACd,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC;YACrC,WAAW,EAAE,IAAI,CAAC,YAAY;YAC9B,WAAW,EAAE,IAAI,CAAC,YAAY;YAC9B,WAAW,EAAE,IAAI,CAAC,YAAY;SACjC,CAAC,CAAC;QAEH,gBAAgB;QAChB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,2BAA2B,CAAC;IACvC,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,YAAY,CAAC;IACxB,CAAC;CAmCJ;AAxIG;IADC,SAAS,EAAE;kEACiB;AAO7B;IADC,SAAS,EAAE;kEACiB;AAO7B;IADC,SAAS,EAAE;kEACiB", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { serialize } from \"../../Misc/decorators\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport type { Camera } from \"../../Cameras/camera\";\r\nimport type { ICameraInput } from \"../../Cameras/cameraInputsManager\";\r\nimport type { PointerInfo } from \"../../Events/pointerEvents\";\r\nimport { PointerEventTypes } from \"../../Events/pointerEvents\";\r\nimport type { IWheelEvent } from \"../../Events/deviceInputEvents\";\r\nimport { EventConstants } from \"../../Events/deviceInputEvents\";\r\nimport { Tools } from \"../../Misc/tools\";\r\n\r\n/**\r\n * Base class for mouse wheel input..\r\n * See FollowCameraMouseWheelInput in src/Cameras/Inputs/freeCameraMouseWheelInput.ts\r\n * for example usage.\r\n */\r\nexport abstract class BaseCameraMouseWheelInput implements ICameraInput<Camera> {\r\n    /**\r\n     * Defines the camera the input is attached to.\r\n     */\r\n    public abstract camera: Camera;\r\n\r\n    /**\r\n     * How fast is the camera moves in relation to X axis mouseWheel events.\r\n     * Use negative value to reverse direction.\r\n     */\r\n    @serialize()\r\n    public wheelPrecisionX = 3.0;\r\n\r\n    /**\r\n     * How fast is the camera moves in relation to Y axis mouseWheel events.\r\n     * Use negative value to reverse direction.\r\n     */\r\n    @serialize()\r\n    public wheelPrecisionY = 3.0;\r\n\r\n    /**\r\n     * How fast is the camera moves in relation to Z axis mouseWheel events.\r\n     * Use negative value to reverse direction.\r\n     */\r\n    @serialize()\r\n    public wheelPrecisionZ = 3.0;\r\n\r\n    /**\r\n     * Observable for when a mouse wheel move event occurs.\r\n     */\r\n    public onChangedObservable = new Observable<{ wheelDeltaX: number; wheelDeltaY: number; wheelDeltaZ: number }>();\r\n\r\n    private _wheel: Nullable<(pointer: PointerInfo) => void>;\r\n    private _observer: Nullable<Observer<PointerInfo>>;\r\n\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * @param noPreventDefault Defines whether event caught by the controls\r\n     *   should call preventdefault().\r\n     *   (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public attachControl(noPreventDefault?: boolean): void {\r\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(arguments);\r\n\r\n        this._wheel = (pointer) => {\r\n            // sanity check - this should be a PointerWheel event.\r\n            if (pointer.type !== PointerEventTypes.POINTERWHEEL) {\r\n                return;\r\n            }\r\n\r\n            const event = <IWheelEvent>pointer.event;\r\n\r\n            const platformScale = event.deltaMode === EventConstants.DOM_DELTA_LINE ? this._ffMultiplier : 1; // If this happens to be set to DOM_DELTA_LINE, adjust accordingly\r\n\r\n            this._wheelDeltaX += (this.wheelPrecisionX * platformScale * event.deltaX) / this._normalize;\r\n            this._wheelDeltaY -= (this.wheelPrecisionY * platformScale * event.deltaY) / this._normalize;\r\n            this._wheelDeltaZ += (this.wheelPrecisionZ * platformScale * event.deltaZ) / this._normalize;\r\n\r\n            if (event.preventDefault) {\r\n                if (!noPreventDefault) {\r\n                    event.preventDefault();\r\n                }\r\n            }\r\n        };\r\n\r\n        this._observer = this.camera.getScene()._inputManager._addCameraPointerObserver(this._wheel, PointerEventTypes.POINTERWHEEL);\r\n    }\r\n\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    public detachControl(): void {\r\n        if (this._observer) {\r\n            this.camera.getScene()._inputManager._removeCameraPointerObserver(this._observer);\r\n            this._observer = null;\r\n            this._wheel = null;\r\n        }\r\n        if (this.onChangedObservable) {\r\n            this.onChangedObservable.clear();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Called for each rendered frame.\r\n     */\r\n    public checkInputs(): void {\r\n        this.onChangedObservable.notifyObservers({\r\n            wheelDeltaX: this._wheelDeltaX,\r\n            wheelDeltaY: this._wheelDeltaY,\r\n            wheelDeltaZ: this._wheelDeltaZ,\r\n        });\r\n\r\n        // Clear deltas.\r\n        this._wheelDeltaX = 0;\r\n        this._wheelDeltaY = 0;\r\n        this._wheelDeltaZ = 0;\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of the current input.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"BaseCameraMouseWheelInput\";\r\n    }\r\n\r\n    /**\r\n     * Get the friendly name associated with the input class.\r\n     * @returns the input friendly name\r\n     */\r\n    public getSimpleName(): string {\r\n        return \"mousewheel\";\r\n    }\r\n\r\n    /**\r\n     * Incremental value of multiple mouse wheel movements of the X axis.\r\n     * Should be zero-ed when read.\r\n     */\r\n    protected _wheelDeltaX: number = 0;\r\n\r\n    /**\r\n     * Incremental value of multiple mouse wheel movements of the Y axis.\r\n     * Should be zero-ed when read.\r\n     */\r\n    protected _wheelDeltaY: number = 0;\r\n\r\n    /**\r\n     * Incremental value of multiple mouse wheel movements of the Z axis.\r\n     * Should be zero-ed when read.\r\n     */\r\n    protected _wheelDeltaZ: number = 0;\r\n\r\n    /**\r\n     * Firefox uses a different scheme to report scroll distances to other\r\n     * browsers. Rather than use complicated methods to calculate the exact\r\n     * multiple we need to apply, let's just cheat and use a constant.\r\n     * https://developer.mozilla.org/en-US/docs/Web/API/WheelEvent/deltaMode\r\n     * https://stackoverflow.com/questions/20110224/what-is-the-height-of-a-line-in-a-wheel-event-deltamode-dom-delta-line\r\n     */\r\n    private readonly _ffMultiplier = 12;\r\n\r\n    /**\r\n     * Different event attributes for wheel data fall into a few set ranges.\r\n     * Some relevant but dated date here:\r\n     * https://stackoverflow.com/questions/5527601/normalizing-mousewheel-speed-across-browsers\r\n     */\r\n    private readonly _normalize = 120;\r\n}\r\n"]}