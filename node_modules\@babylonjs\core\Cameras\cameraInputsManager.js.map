{"version": 3, "file": "cameraInputsManager.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Cameras/cameraInputsManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,mBAAmB,EAAE,MAAM,oBAAoB,CAAC;AAEzD,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC;;;;GAIG;AACH,wEAAwE;AACxE,MAAM,CAAC,IAAI,gBAAgB,GAAG,EAAE,CAAC;AAmDjC;;;;GAIG;AACH,MAAM,OAAO,mBAAmB;IA4B5B;;;OAGG;IACH,YAAY,MAAe;QA1B3B;;;WAGG;QACI,sBAAiB,GAAY,KAAK,CAAC;QAuBtC,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,WAAW,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACI,GAAG,CAAC,KAA4B;QACnC,MAAM,IAAI,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;QACnC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACrB,MAAM,CAAC,IAAI,CAAC,uBAAuB,GAAG,IAAI,GAAG,2BAA2B,CAAC,CAAC;YAC1E,OAAO;SACV;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAE5B,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAE3B,0DAA0D;QAC1D,wFAAwF;QACxF,IAAI,KAAK,CAAC,WAAW,EAAE;YACnB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;SAC1E;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC9C;IACL,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,aAAoC;QAC9C,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACjC,IAAI,KAAK,KAAK,aAAa,EAAE;gBACzB,KAAK,CAAC,aAAa,EAAE,CAAC;gBACtB,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;gBACpB,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;gBAC1B,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAEzB,OAAO;aACV;SACJ;IACL,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,SAAiB;QACjC,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACjC,IAAI,KAAK,CAAC,YAAY,EAAE,KAAK,SAAS,EAAE;gBACpC,KAAK,CAAC,aAAa,EAAE,CAAC;gBACtB,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;gBACpB,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;gBAC1B,IAAI,CAAC,iBAAiB,EAAE,CAAC;aAC5B;SACJ;IACL,CAAC;IAEO,eAAe,CAAC,EAAc;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;QACjC,OAAO,GAAG,EAAE;YACR,OAAO,EAAE,CAAC;YACV,EAAE,EAAE,CAAC;QACT,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,KAA4B;QAC3C,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC9C;IACL,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,mBAA4B,KAAK;QAClD,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,OAAO;SACV;QAED,gBAAgB,GAAG,MAAM,CAAC,wCAAwC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,gBAAgB,CAAC;QAC9F,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QAEzC,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC7B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;SACtD;IACL,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,UAAU,GAAG,KAAK;QACnC,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC7B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,CAAC;YAEnC,IAAI,UAAU,EAAE;gBACZ,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC;aACpC;SACJ;QACD,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;IACnC,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACpB,IAAI,CAAC,WAAW,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;QAE5B,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACjC,IAAI,KAAK,CAAC,WAAW,EAAE;gBACnB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;aAC1E;SACJ;IACL,CAAC;IAED;;OAEG;IACI,KAAK;QACR,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;SAC5B;QACD,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;IAChC,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,gBAAqB;QAClC,MAAM,MAAM,GAA2B,EAAE,CAAC;QAC1C,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACjC,MAAM,GAAG,GAAG,mBAAmB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,CAAC;SACtC;QAED,gBAAgB,CAAC,SAAS,GAAG,MAAM,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,YAAiB;QAC1B,MAAM,YAAY,GAAG,YAAY,CAAC,SAAS,CAAC;QAC5C,IAAI,YAAY,EAAE;YACd,IAAI,CAAC,KAAK,EAAE,CAAC;YAEb,KAAK,MAAM,CAAC,IAAI,YAAY,EAAE;gBAC1B,MAAM,SAAS,GAAS,gBAAiB,CAAC,CAAC,CAAC,CAAC;gBAC7C,IAAI,SAAS,EAAE;oBACX,MAAM,WAAW,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;oBACpC,MAAM,KAAK,GAAG,mBAAmB,CAAC,KAAK,CACnC,GAAG,EAAE;wBACD,OAAO,IAAI,SAAS,EAAE,CAAC;oBAC3B,CAAC,EACD,WAAW,EACX,IAAI,CACP,CAAC;oBACF,IAAI,CAAC,GAAG,CAAC,KAAY,CAAC,CAAC;iBAC1B;aACJ;SACJ;aAAM;YACH,6DAA6D;YAC7D,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE;gBAC3B,MAAM,SAAS,GAAS,gBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC;gBAC3E,IAAI,SAAS,EAAE;oBACX,MAAM,KAAK,GAAG,mBAAmB,CAAC,KAAK,CACnC,GAAG,EAAE;wBACD,OAAO,IAAI,SAAS,EAAE,CAAC;oBAC3B,CAAC,EACD,YAAY,EACZ,IAAI,CACP,CAAC;oBACF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9B,IAAI,CAAC,GAAG,CAAC,KAAY,CAAC,CAAC;iBAC1B;aACJ;SACJ;IACL,CAAC;CACJ", "sourcesContent": ["import { Logger } from \"../Misc/logger\";\r\nimport { Serial<PERSON>Helper } from \"../Misc/decorators\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Camera } from \"./camera\";\r\n/**\r\n * @ignore\r\n * This is a list of all the different input types that are available in the application.\r\n * Fo instance: ArcRotateCameraGamepadInput...\r\n */\r\n// eslint-disable-next-line no-var, @typescript-eslint/naming-convention\r\nexport var CameraInputTypes = {};\r\n\r\n/**\r\n * This is the contract to implement in order to create a new input class.\r\n * Inputs are dealing with listening to user actions and moving the camera accordingly.\r\n */\r\nexport interface ICameraInput<TCamera extends Camera> {\r\n    /**\r\n     * Defines the camera the input is attached to.\r\n     */\r\n    camera: Nullable<TCamera>;\r\n    /**\r\n     * Gets the class name of the current input.\r\n     * @returns the class name\r\n     */\r\n    getClassName(): string;\r\n    /**\r\n     * Get the friendly name associated with the input class.\r\n     * @returns the input friendly name\r\n     */\r\n    getSimpleName(): string;\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    attachControl(noPreventDefault?: boolean): void;\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    detachControl(): void;\r\n    /**\r\n     * Update the current camera state depending on the inputs that have been used this frame.\r\n     * This is a dynamically created lambda to avoid the performance penalty of looping for inputs in the render loop.\r\n     */\r\n    checkInputs?: () => void;\r\n}\r\n\r\n/**\r\n * Represents a map of input types to input instance or input index to input instance.\r\n */\r\nexport interface CameraInputsMap<TCamera extends Camera> {\r\n    /**\r\n     * Accessor to the input by input type.\r\n     */\r\n    [name: string]: ICameraInput<TCamera>;\r\n    /**\r\n     * Accessor to the input by input index.\r\n     */\r\n    [idx: number]: ICameraInput<TCamera>;\r\n}\r\n\r\n/**\r\n * This represents the input manager used within a camera.\r\n * It helps dealing with all the different kind of input attached to a camera.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n */\r\nexport class CameraInputsManager<TCamera extends Camera> {\r\n    /**\r\n     * Defines the list of inputs attached to the camera.\r\n     */\r\n    public attached: CameraInputsMap<TCamera>;\r\n\r\n    /**\r\n     * Defines the dom element the camera is collecting inputs from.\r\n     * This is null if the controls have not been attached.\r\n     */\r\n    public attachedToElement: boolean = false;\r\n\r\n    /**\r\n     * Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public noPreventDefault: boolean;\r\n\r\n    /**\r\n     * Defined the camera the input manager belongs to.\r\n     */\r\n    public camera: TCamera;\r\n\r\n    /**\r\n     * Update the current camera state depending on the inputs that have been used this frame.\r\n     * This is a dynamically created lambda to avoid the performance penalty of looping for inputs in the render loop.\r\n     */\r\n    public checkInputs: () => void;\r\n\r\n    /**\r\n     * Instantiate a new Camera Input Manager.\r\n     * @param camera Defines the camera the input manager belongs to\r\n     */\r\n    constructor(camera: TCamera) {\r\n        this.attached = {};\r\n        this.camera = camera;\r\n        this.checkInputs = () => {};\r\n    }\r\n\r\n    /**\r\n     * Add an input method to a camera\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n     * @param input Camera input method\r\n     */\r\n    public add(input: ICameraInput<TCamera>): void {\r\n        const type = input.getSimpleName();\r\n        if (this.attached[type]) {\r\n            Logger.Warn(\"camera input of type \" + type + \" already exists on camera\");\r\n            return;\r\n        }\r\n\r\n        this.attached[type] = input;\r\n\r\n        input.camera = this.camera;\r\n\r\n        // for checkInputs, we are dynamically creating a function\r\n        // the goal is to avoid the performance penalty of looping for inputs in the render loop\r\n        if (input.checkInputs) {\r\n            this.checkInputs = this._addCheckInputs(input.checkInputs.bind(input));\r\n        }\r\n\r\n        if (this.attachedToElement) {\r\n            input.attachControl(this.noPreventDefault);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Remove a specific input method from a camera\r\n     * example: camera.inputs.remove(camera.inputs.attached.mouse);\r\n     * @param inputToRemove camera input method\r\n     */\r\n    public remove(inputToRemove: ICameraInput<TCamera>): void {\r\n        for (const cam in this.attached) {\r\n            const input = this.attached[cam];\r\n            if (input === inputToRemove) {\r\n                input.detachControl();\r\n                input.camera = null;\r\n                delete this.attached[cam];\r\n                this.rebuildInputCheck();\r\n\r\n                return;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Remove a specific input type from a camera\r\n     * example: camera.inputs.remove(\"ArcRotateCameraGamepadInput\");\r\n     * @param inputType the type of the input to remove\r\n     */\r\n    public removeByType(inputType: string): void {\r\n        for (const cam in this.attached) {\r\n            const input = this.attached[cam];\r\n            if (input.getClassName() === inputType) {\r\n                input.detachControl();\r\n                input.camera = null;\r\n                delete this.attached[cam];\r\n                this.rebuildInputCheck();\r\n            }\r\n        }\r\n    }\r\n\r\n    private _addCheckInputs(fn: () => void) {\r\n        const current = this.checkInputs;\r\n        return () => {\r\n            current();\r\n            fn();\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Attach the input controls to the currently attached dom element to listen the events from.\r\n     * @param input Defines the input to attach\r\n     */\r\n    public attachInput(input: ICameraInput<TCamera>): void {\r\n        if (this.attachedToElement) {\r\n            input.attachControl(this.noPreventDefault);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Attach the current manager inputs controls to a specific dom element to listen the events from.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public attachElement(noPreventDefault: boolean = false): void {\r\n        if (this.attachedToElement) {\r\n            return;\r\n        }\r\n\r\n        noPreventDefault = Camera.ForceAttachControlToAlwaysPreventDefault ? false : noPreventDefault;\r\n        this.attachedToElement = true;\r\n        this.noPreventDefault = noPreventDefault;\r\n\r\n        for (const cam in this.attached) {\r\n            this.attached[cam].attachControl(noPreventDefault);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Detach the current manager inputs controls from a specific dom element.\r\n     * @param disconnect Defines whether the input should be removed from the current list of attached inputs\r\n     */\r\n    public detachElement(disconnect = false): void {\r\n        for (const cam in this.attached) {\r\n            this.attached[cam].detachControl();\r\n\r\n            if (disconnect) {\r\n                this.attached[cam].camera = null;\r\n            }\r\n        }\r\n        this.attachedToElement = false;\r\n    }\r\n\r\n    /**\r\n     * Rebuild the dynamic inputCheck function from the current list of\r\n     * defined inputs in the manager.\r\n     */\r\n    public rebuildInputCheck(): void {\r\n        this.checkInputs = () => {};\r\n\r\n        for (const cam in this.attached) {\r\n            const input = this.attached[cam];\r\n            if (input.checkInputs) {\r\n                this.checkInputs = this._addCheckInputs(input.checkInputs.bind(input));\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Remove all attached input methods from a camera\r\n     */\r\n    public clear(): void {\r\n        if (this.attachedToElement) {\r\n            this.detachElement(true);\r\n        }\r\n        this.attached = {};\r\n        this.attachedToElement = false;\r\n        this.checkInputs = () => {};\r\n    }\r\n\r\n    /**\r\n     * Serialize the current input manager attached to a camera.\r\n     * This ensures than once parsed,\r\n     * the input associated to the camera will be identical to the current ones\r\n     * @param serializedCamera Defines the camera serialization JSON the input serialization should write to\r\n     */\r\n    public serialize(serializedCamera: any): void {\r\n        const inputs: { [key: string]: any } = {};\r\n        for (const cam in this.attached) {\r\n            const input = this.attached[cam];\r\n            const res = SerializationHelper.Serialize(input);\r\n            inputs[input.getClassName()] = res;\r\n        }\r\n\r\n        serializedCamera.inputsmgr = inputs;\r\n    }\r\n\r\n    /**\r\n     * Parses an input manager serialized JSON to restore the previous list of inputs\r\n     * and states associated to a camera.\r\n     * @param parsedCamera Defines the JSON to parse\r\n     */\r\n    public parse(parsedCamera: any): void {\r\n        const parsedInputs = parsedCamera.inputsmgr;\r\n        if (parsedInputs) {\r\n            this.clear();\r\n\r\n            for (const n in parsedInputs) {\r\n                const construct = (<any>CameraInputTypes)[n];\r\n                if (construct) {\r\n                    const parsedinput = parsedInputs[n];\r\n                    const input = SerializationHelper.Parse(\r\n                        () => {\r\n                            return new construct();\r\n                        },\r\n                        parsedinput,\r\n                        null\r\n                    );\r\n                    this.add(input as any);\r\n                }\r\n            }\r\n        } else {\r\n            //2016-03-08 this part is for managing backward compatibility\r\n            for (const n in this.attached) {\r\n                const construct = (<any>CameraInputTypes)[this.attached[n].getClassName()];\r\n                if (construct) {\r\n                    const input = SerializationHelper.Parse(\r\n                        () => {\r\n                            return new construct();\r\n                        },\r\n                        parsedCamera,\r\n                        null\r\n                    );\r\n                    this.remove(this.attached[n]);\r\n                    this.add(input as any);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n"]}