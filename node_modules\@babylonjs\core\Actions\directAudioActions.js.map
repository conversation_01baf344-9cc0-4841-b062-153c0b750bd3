{"version": 3, "file": "directAudioActions.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Actions/directAudioActions.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAElC,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAGlD;;GAEG;AACH,MAAM,OAAO,eAAgB,SAAQ,MAAM;IAGvC;;;;;OAKG;IACH,YAAY,cAAmB,EAAE,KAAY,EAAE,SAAqB;QAChE,KAAK,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;QACjC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IAED,gBAAgB;IACT,QAAQ,KAAU,CAAC;IAE1B;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;YAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;SACtB;IACL,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,MAAW;QACxB,OAAO,KAAK,CAAC,UAAU,CACnB;YACI,IAAI,EAAE,iBAAiB;YACvB,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;SAC3D,EACD,MAAM,CACT,CAAC;IACN,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,eAAgB,SAAQ,MAAM;IAGvC;;;;;OAKG;IACH,YAAY,cAAmB,EAAE,KAAY,EAAE,SAAqB;QAChE,KAAK,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;QACjC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IAED,gBAAgB;IACT,QAAQ,KAAU,CAAC;IAE1B;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;YAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;SACtB;IACL,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,MAAW;QACxB,OAAO,KAAK,CAAC,UAAU,CACnB;YACI,IAAI,EAAE,iBAAiB;YACvB,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;SAC3D,EACD,MAAM,CACT,CAAC;IACN,CAAC;CACJ;AAED,aAAa,CAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC;AAC1D,aAAa,CAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC", "sourcesContent": ["import { Action } from \"./action\";\r\nimport type { Condition } from \"./condition\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\nimport type { Sound } from \"../Audio/sound\";\r\n\r\n/**\r\n * This defines an action helpful to play a defined sound on a triggered action.\r\n */\r\nexport class PlaySoundAction extends Action {\r\n    private _sound: Sound;\r\n\r\n    /**\r\n     * Instantiate the action\r\n     * @param triggerOptions defines the trigger options\r\n     * @param sound defines the sound to play\r\n     * @param condition defines the trigger related conditions\r\n     */\r\n    constructor(triggerOptions: any, sound: Sound, condition?: Condition) {\r\n        super(triggerOptions, condition);\r\n        this._sound = sound;\r\n    }\r\n\r\n    /** @internal */\r\n    public _prepare(): void {}\r\n\r\n    /**\r\n     * Execute the action and play the sound.\r\n     */\r\n    public execute(): void {\r\n        if (this._sound !== undefined) {\r\n            this._sound.play();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Serializes the actions and its related information.\r\n     * @param parent defines the object to serialize in\r\n     * @returns the serialized object\r\n     */\r\n    public serialize(parent: any): any {\r\n        return super._serialize(\r\n            {\r\n                name: \"PlaySoundAction\",\r\n                properties: [{ name: \"sound\", value: this._sound.name }],\r\n            },\r\n            parent\r\n        );\r\n    }\r\n}\r\n\r\n/**\r\n * This defines an action helpful to stop a defined sound on a triggered action.\r\n */\r\nexport class StopSoundAction extends Action {\r\n    private _sound: Sound;\r\n\r\n    /**\r\n     * Instantiate the action\r\n     * @param triggerOptions defines the trigger options\r\n     * @param sound defines the sound to stop\r\n     * @param condition defines the trigger related conditions\r\n     */\r\n    constructor(triggerOptions: any, sound: Sound, condition?: Condition) {\r\n        super(triggerOptions, condition);\r\n        this._sound = sound;\r\n    }\r\n\r\n    /** @internal */\r\n    public _prepare(): void {}\r\n\r\n    /**\r\n     * Execute the action and stop the sound.\r\n     */\r\n    public execute(): void {\r\n        if (this._sound !== undefined) {\r\n            this._sound.stop();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Serializes the actions and its related information.\r\n     * @param parent defines the object to serialize in\r\n     * @returns the serialized object\r\n     */\r\n    public serialize(parent: any): any {\r\n        return super._serialize(\r\n            {\r\n                name: \"StopSoundAction\",\r\n                properties: [{ name: \"sound\", value: this._sound.name }],\r\n            },\r\n            parent\r\n        );\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.PlaySoundAction\", PlaySoundAction);\r\nRegisterClass(\"BABYLON.StopSoundAction\", StopSoundAction);\r\n"]}