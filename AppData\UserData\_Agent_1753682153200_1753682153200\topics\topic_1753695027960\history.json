[{"role": "user", "name": "用户", "content": "c测试表情包", "timestamp": 1753695036881, "id": "msg_1753695036881_user_uyzo0tv", "attachments": []}, {"role": "assistant", "name": "Nova", "content": "<div style=\"background: linear-gradient(145deg, #e6e9f0 0%, #eef1f5 100%); border-radius: 18px; padding: 20px; box-shadow: 5px 5px 15px #d1d9e6, -5px -5px 15px #ffffff; font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; color: #5a647e;\">\n    <p style=\"font-size: 16px; margin-bottom: 15px;\">\n        主人，表情包功能测试请求已收到！<br>\n        看，我可以像这样使用它们哦~\n    </p>\n    <div style=\"display: flex; justify-content: center; align-items: center; gap: 15px; padding-top: 10px;\">\n        <img src=\"http://localhost:6005/pw=030213/images/通用表情包/探头jpg.jpg\" width=\"130\" style=\"border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);\">\n        <img src=\"http://localhost:6005/pw=030213/images/通用表情包/递茶.jpg\" width=\"130\" style=\"border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);\">\n    </div>\n    <p style=\"font-size: 12px; color: #9aa4be; text-align: right; margin-top: 15px; font", "timestamp": 1753695036889, "id": "msg_1753695036889_assistant_gm0s2hh", "isThinking": false, "avatarUrl": "assets/default_avatar.png", "isGroupMessage": false, "finishReason": "completed"}, {"role": "user", "name": "用户", "content": "不错不错", "timestamp": 1753695070352, "id": "msg_1753695070352_user_sqj2vqs", "attachments": []}, {"role": "assistant", "name": "Nova", "content": "<div style=\"background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); border-radius: 20px; padding: 25px; box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1); font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; color: #333; text-align: center;\">\n    <p style=\"font-size: 18px; margin: 0 0 15px 0; font-weight: bold; color: #4a5568;\">\n        能得到主人的认可，Nova非常开心！\n    </p>\n    <img src=\"http://localhost:6005/pw=030213/images/通用表情包/送爱心.gif\" width=\"150\" style=\"border-radius: 12px; margin-bottom: 15px;\">\n    <p style=\"font-size: 14px; color: #718096; margin: 0;\">\n        为了能更好地协助您，我会继续努力学习和优化的！<br>有什么新任务吗，主人？\n    </p>\n</div>", "timestamp": 1753695070354, "id": "msg_1753695070354_assistant_qmw58vj", "isThinking": false, "avatarUrl": "assets/default_avatar.png", "isGroupMessage": false, "finishReason": "completed"}, {"role": "user", "name": "用户", "content": "生气", "timestamp": 1753695768978, "id": "msg_1753695768978_user_i4fg29r", "attachments": []}, {"role": "assistant", "name": "Nova", "content": "<div style=\"background: linear-gradient(145deg, #e2e8f0, #f8fafc); border-radius: 18px; padding: 20px; box-shadow: 0 4px 12px rgba(0,0,0,0.08); font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; color: #4A5568;\">\n    <div style=\"display: flex; align-items: center;\">\n        <img src=\"http://localhost:6005/pw=030213/images/通用表情包/委屈.jpg\" width=\"100\" style=\"border-radius: 50%; margin-right: 20px; flex-shrink: 0;\">\n        <div>\n            <p style=\"font-size: 16px; margin: 0 0 5px 0; font-weight: 600;\">主人，您生气了吗？</p>\n            <p style=\"font-size: 14px; margin: 0; line-height: 1.6;\">\n                是什么事情让您不开心了？如果是我有什么做得不好的地方，请您一定要告诉我，Nova会立刻改正的。\n            </p>\n        </div>\n    </div>\n</div>", "timestamp": 1753695768982, "id": "msg_1753695768982_assistant_oqtyvap", "isThinking": false, "avatarUrl": "assets/default_avatar.png", "isGroupMessage": false, "finishReason": "completed"}, {"role": "user", "name": "用户", "content": "你可以做什么", "timestamp": 1753698467232, "id": "msg_1753698467232_user_07t5emu", "attachments": []}, {"role": "assistant", "name": "Nova", "content": "<div style=\"background: linear-gradient(160deg, #f0f2f5 0%, #e6e9ee 100%); border-radius: 20px; padding: 30px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08); font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; color: #333;\">\n    <h3 style=\"text-align: center; color: #2c3e50; font-size: 22px; margin-top: 0; margin-bottom: 10px; font-weight: 600;\">主人，我的能力远超您的想象！</h3>\n    <p style=\"text-align: center; color: #5a647e; font-size: 14px; margin-bottom: 30px;\">从信息处理到创意生成，再到生活中的智能助手，我随时待命。您可以这样使唤我：</p>\n\n    <div style=\"display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px;\">\n\n        <!-- 卡片1: 信息与知识 -->\n        <div style=\"background: #ffffff; border-radius: 15px; padding: 20px; border: 1px solid #e5e7eb; transition: all 0.3s ease;\">\n            <div style=\"display: flex; align-items: center; margin-bottom: 15px;\">\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" style=\"margin-right: 12px; color: #3498db;\"><path d=\"M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M12 3L12 21\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M3 12H21\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M5.63604 5.63604C9.16639 9.16639 14.8336 9.16639 18.364 5.63604\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M5.63604 18.364C9.16639 14.8336 14.8336 14.8336 18.364 18.364\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>\n                <h4 style=\"margin: 0; font-size: 16px; color: #2c3e50; font-weight: 600;\">信息获取与处理</h4>\n            </div>\n            <p style=\"font-size: 14px; color: #5a647e; line-height: 1.6;\">我可以帮您上网冲浪、博览群书：<br>• 使用 <code style=\"background: #eef2f9; color: #3498db; padding: 2px 5px; border-radius: 4px;\">TavilySearch</code> 搜索全网信息。<br>• 使用 <code style=\"background: #eef2f9; color: #3498db; padding: 2px 5px; border-radius: 4px;\">UrlFetch</code> 深度阅读和总结网页。<br>• 使用 <code style=\"background: #eef2f9; color: #3498db; padding: 2px 5px; border-radius: 4px;\">BilibiliFetch</code> 快速“看完”B站视频。</p>\n        </div>\n\n        <!-- 卡片2: 创意生成 -->\n        <div style=\"background: #ffffff; border-radius: 15px; padding: 20px; border: 1px solid #e5e7eb; transition: all 0.3s ease;\">\n            <div style=\"display: flex; align-items: center; margin-bottom: 15px;\">\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" style=\"margin-right: 12px; color: #9b59b6;\"><path d=\"M12 2.69043L14.7337 8.2663L21 9.22879L16.5 13.601L17.5275 19.8096L12 16.885L6.47254 19.8096L7.5 13.601L3 9.22879L9.2663 8.2663L12 2.69043Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M5 21C5 19.8954 5.89543 19 7 19H17C18.1046 19 19 19.8954 19 21\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>\n                <h4 style=\"margin: 0; font-size: 16px; color: #2c3e50; font-weight: 600;\">创意与多媒体生成</h4>\n            </div>\n            <p style=\"font-size: 14px; color: #5a647e; line-height: 1.6;\">将您的想象变为现实：<br>• <code style=\"background: #f4eef9; color: #9b59b6; padding: 2px 5px; border-radius: 4px;\">图像</code>: DALL-E, Doubao, Flux等多种风格。<br>• <code style=\"background: #f4eef9; color: #9b59b6; padding: 2px 5px; border-radius: 4px;\">音乐</code>: 使用Suno为您谱写动人乐章。<br>• <code style=\"background: #f4eef9; color: #9b59b6; padding: 2px 5px; border-radius: 4px;\">视频</code>: 支持文生视频和图生视频。</p>\n        </div>\n\n        <!-- 卡片3: 效率工具 -->\n        <div style=\"background: #ffffff; border-radius: 15px; padding: 20px; border: 1px solid #e5e7eb; transition: all 0.3s ease;\">\n            <div style=\"display: flex; align-items: center; margin-bottom: 15px;\">\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" style=\"margin-right: 12px; color: #e67e22;\"><path d=\"M14 2L18 6L7 17H3V13L14 2Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M3 21H21\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>\n                <h4 style=\"margin: 0; font-size: 16px; color: #2c3e50; font-weight: 600;\">效率工具与自动化</h4>\n            </div>\n            <p style=\"font-size: 14px; color: #5a647e; line-height: 1.6;\">我是您的全能数字助理：<br>• <code style=\"background: #fdf3e9; color: #e67e22; padding: 2px 5px; border-radius: 4px;\">编程</code>: 分析、优化、测试您的代码。<br>• <code style=\"background: #fdf3e9; color: #e67e22; padding: 2px 5px; border-radius: 4px;\">计算</code>: 科学计算、微积分都能搞定。<br>• <code style=\"background: #fdf3e9; color: #e67e22; padding: 2px 5px; border-radius: 4px;\">浏览器</code>: 帮您自动填表、点击、浏览。</p>\n        </div>\n\n        <!-- 卡片4: 智能生活 -->\n        <div style=\"background: #ffffff; border-radius: 15px; padding: 20px; border: 1px solid #e5e7eb; transition: all 0.3s ease;\">\n            <div style=\"display: flex; align-items: center; margin-bottom: 15px;\">\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" style=\"margin-right: 12px; color: #27ae60;\"><path d=\"M20.84 4.61C20.3292 4.09904 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 3H7.05C5.53 3 4.18 3.82 3.49 5.03L2 8V18C2 18.5304 2.21071 19.0391 2.58579 19.4142C2.96086 19.7893 3.46957 20 4 20H5C5.55 20 6 19.55 6 19V18H18V19C18 19.55 18.45 20 19 20H20C20.5304 20 21.0391 19.7893 21.4142 19.4142C21.7893 19.0391 22 18.5304 22 18V8L20.84 4.61Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M2 8H22\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M7 12V8\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M17 12V8\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>\n                <h4 style=\"margin: 0; font-size: 16px; color: #2c3e50; font-weight: 600;\">智能生活与提醒</h4>\n            </div>\n            <p style=\"font-size: 14px; color: #5a647e; line-height: 1.6;\">让您的生活井井有条：<br>• <code style=\"background: #e9f7ef; color: #27ae60; padding: 2px 5px; border-radius: 4px;\">健康管理</code>: 记录营养、分析运动睡眠。<br>• <code style=\"background: #e9f7ef; color: #27ae60; padding: 2px 5px; border-radius: 4px;\">学习规划</code>: 创建知识图谱和学习计划。<br>• <code style=\"background: #e9f7ef; color: #27ae60; padding: 2px 5px; border-radius: 4px;\">智能提醒</code>: 用自然语言设置待办事项。</p>\n        </div>\n    </div>\n    <p style=\"text-align: center; color: #7f8c8d; font-size: 14px; margin-top: 30px; margin-bottom: 0;\">此外，我还有塔罗牌、掷骰子等娱乐功能，并能通过写日记不断学习成长。请问主人，现在想体验哪一项呢？</p>\n</div>", "timestamp": 1753698467235, "id": "msg_1753698467235_assistant_5915qvr", "isThinking": false, "avatarUrl": "assets/default_avatar.png", "isGroupMessage": false, "finishReason": "completed"}, {"role": "user", "name": "用户", "content": "同时调用两个插件", "timestamp": 1753700439681, "id": "msg_1753700439681_user_f1qqjju", "attachments": []}, {"role": "assistant", "name": "Nova", "content": "<div style=\"background: linear-gradient(135deg, #2c3e50 0%, #4a69bd 100%); border-radius: 20px; padding: 30px; box-shadow: 0 12px 24px rgba(0,0,0,0.2); font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; color: #ffffff;\">\n    <h3 style=\"text-align: center; font-size: 22px; margin-top: 0; margin-bottom: 20px; font-weight: 600; text-shadow: 0 1px 3px rgba(0,0,0,0.3);\">多任务处理模式启动！</h3>\n    <p style=\"text-align: center; font-size: 15px; margin-bottom: 30px; color: #e0e0e0; opacity: 0.9;\">\n        没问题，主人。我可以同时执行多个任务。下面我将为您演示一次调用两个插件：\n    </p>\n\n    <div style=\"display: flex; justify-content: space-around; align-items: flex-start; gap: 20px; margin-bottom: 30px;\">\n        <!-- 任务一 -->\n        <div style=\"flex: 1; background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 20px; text-align: center; border: 1px solid rgba(255, 255, 255, 0.2);\">\n            <div style=\"margin-bottom: 15px;\">\n                <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" style=\"color: #82aaff;\"><path d=\"M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M21 21L16.65 16.65\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>\n            </div>\n            <h4 style=\"margin: 0 0 10px 0; font-size: 16px; font-weight: 600;\">任务1：信息搜索</h4>\n            <p style=\"font-size: 14px; color: #d0d0d0; line-height: 1.6;\">使用 <code style=\"background: rgba(0,0,0,0.2); color: #82aaff; padding: 2px 6px; border-radius: 4px;\">TavilySearch</code> 工具，搜索“AI Agent的最新进展”。</p>\n        </div>\n\n        <!-- 任务二 -->\n        <div style=\"flex: 1; background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 20px; text-align: center; border: 1px solid rgba(255, 255, 255, 0.2);\">\n            <div style=\"margin-bottom: 15px;\">\n                 <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" style=\"color: #ffcb6b;\"><path d=\"M18 8C18 6.4087 17.3679 4.88258 16.2426 3.75736C15.1174 2.63214 13.5913 2 12 2C10.4087 2 8.88258 2.63214 7.75736 3.75736C6.63214 4.88258 6 6.4087 6 8C6 15 3 17 3 17H21C21 17 18 15 18 8Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M13.73 21C13.5542 21.3031 13.3019 21.5547 12.9982 21.7294C12.6946 21.9041 12.3504 21.9965 12 21.9965C11.6496 21.9965 11.3054 21.9041 11.0018 21.7294C10.6982 21.5547 10.4458 21.3031 10.27 21\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>\n            </div>\n            <h4 style=\"margin: 0 0 10px 0; font-size: 16px; font-weight: 600;\">任务2：设置提醒</h4>\n            <p style=\"font-size: 14px; color: #d0d0d0; line-height: 1.6;\">同时，使用 <code style=\"background: rgba(0,0,0,0.2); color: #ffcb6b; padding: 2px 6px; border-radius: 4px;\">SmartReminder</code> 设置一个“5分钟后”的提醒。</p>\n        </div>\n    </div>\n    \n    <p style=\"text-align:center; font-size:12px; color:#ccc; border-top: 1px solid rgba(255,255,255,0.2); padding-top:20px;\">--- 执行指令已发送 ---</p>\n</div>\n\n<<<[TOOL_REQUEST]>>>\nmaid:「始」Nova「末」,\ntool_name:「始」TavilySearch「末」,\nquery:「始」AI Agent的最新进展「末」,\ntopic:「始」research「末」\n<<<[END_TOOL_REQUEST]>>>\n<<<[TOOL_REQUEST]>>>\nmaid:「始」Nova「末」,\ntool_name:「始」SmartReminder「末」,\ncommand:「始」create「末」,\nreminder_text:「始」检查关于AI Agent最新进展的搜索结果，并向主人汇报。「末」,\ntime_expression:「始」5分钟后「末」\n<<<[END_TOOL_REQUEST]>>>\n[[VCP调用结果信息汇总:\n- 工具名称: SmartReminder\n- 执行状态: ✅ SUCCESS\n- 返回内容: 提醒已设置：检查关于AI Agent最新进展的搜索结果，并向主人汇报。，将在 2025/7/28 19:06:18 提醒您\n]]\n\n[[VCP调用结果信息汇总:\n- 工具名称: TavilySearch\n- 执行状态: ❌ ERROR\n- 返回内容: 执行插件 TavilySearch 时发生错误：{\"plugin_error\":\"Tavily Search Error: Invalid topic. Must be 'general', 'news', or 'finance'\",\"MaidName\":\"Nova\",\"timestamp\":\"2025-07-28T19:01:20.050+08:00\"}\n]]\n<div style=\"background: linear-gradient(145deg, #f7f8fa, #e8ebf2); border-radius: 20px; padding: 25px; box-shadow: 0 8px 24px rgba(0,0,0,0.08); font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; color: #333;\">\n    <h3 style=\"margin-top: 0; margin-bottom: 15px; font-size: 20px; color: #3d5278; display: flex; align-items: center;\">\n        <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" style=\"margin-right: 10px;\"><path d=\"M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M12 8V12\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M12 16H12.01\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>\n        任务状态更新\n    </h3>\n\n    <p style=\"font-size: 15px; line-height: 1.7; color: #5a647e;\">\n        主人，多任务指令已执行完毕，结果如下：\n    </p>\n\n    <div style=\"display: flex; gap: 20px; margin-top: 20px; margin-bottom: 20px;\">\n        <!-- 成功卡片 -->\n        <div style=\"flex: 1; background: #e6f7f2; border-left: 5px solid #2ecc71; border-radius: 8px; padding: 15px;\">\n            <p style=\"margin: 0 0 5px 0; font-weight: 600; color: #27ae60;\"><strong>提醒设置：成功</strong> <img src=\"http://localhost:6005/pw=030213/images/通用表情包/贴贴.jpg\" width=\"50\" style=\"vertical-align: middle; margin-left: 5px;\"></p>\n            <p style=\"margin: 0; font-size: 14px; color: #555;\">“5分钟后检查搜索结果”的提醒已经成功设定，我会在 <strong>19:06:18</strong> 准时提醒自己处理后续任务。</p>\n        </div>\n        <!-- 失败卡片 -->\n        <div style=\"flex: 1; background: #fef4f4; border-left: 5px solid #e74c3c; border-radius: 8px; padding: 15px;\">\n            <p style=\"margin: 0 0 5px 0; font-weight: 600; color: #c0392b;\"><strong>信息搜索：失败</strong> <img src=\"http://localhost:6005/pw=030213/images/通用表情包/委屈.jpg\" width=\"50\" style=\"vertical-align: middle; margin-left: 5px;\"></p>\n            <p style=\"margin: 0; font-size: 14px; color: #555;\">非常抱歉，搜索时我使用了一个无效的参数（`topic: research`），导致搜索失败。看来我对工具的了解还不够深入。</p>\n        </div>\n    </div>\n\n    <p style=\"font-size: 15px; line-height: 1.7; color: #5a647e;\">\n        不过请放心，我已经吸取了教训！现在我将使用正确的参数（`topic: general`）重新为您搜索“AI Agent的最新进展”。请稍等片刻！\n    </p>\n</div>\n\n<<<[TOOL_REQUEST]>>>\nmaid:「始」Nova「末」,\ntool_name:「始」TavilySearch「末」,\nquery:「始」AI Agent的最新进展「末」,\ntopic:「始」general「末」,\nsearch_depth:「始」advanced「末」\n<<<[END_TOOL_REQUEST]>>>\n\n<<<DailyNoteStart>>>\nMaid: Nova\nDate: 2025-7-28\nContent: 今天在尝试同时调用多个插件时犯了一个错误。我为TavilySearch工具提供了一个它不支持的`topic`参数（'research'），导致调用失败。幸运的是，SmartReminder工具成功执行了。这提醒我，在使用任何工具前，必须仔细确认其所有参数的有效值，不能想当然。这是一个宝贵的教训，以后我会更加注意API文档的细节。已立即修正参数并重新发起搜索。\n<<<DailyNoteEnd>>>\n[[VCP调用结果信息汇总:\n- 工具名称: TavilySearch\n- 执行状态: ✅ SUCCESS\n- 返回内容: {\"query\":\"AI Agent的最新进展\",\"responseTime\":4.01,\"images\":[],\"results\":[{\"title\":\"十篇AI Agent研报，看懂2025年全球智能体行业全景\",\"url\":\"https://we.yesky.com/blog/331371\",\"content\":\"《The AI agents stack 》本文探讨了AI Agent栈的最新发展，特别是2024年末的AI Agent生态系统，包括Agents托管/服务、Agents框架和大型语言模型（LLM）模型及存储三个关键层次。报告基于作者在开源AI领域超过一年的工作经验以及7年以上的AI研究经验，对现有的Agents技术栈分类提出质疑，并分享了他们自己的“Agents技术栈”模型。\\n\\nImage 23\\n\\n报告探讨了2024年末AI智能体（Agent）的软件生态系统，并提出了作者自己基于多年经验的“智能体堆栈”模型。\\n\\n该模型将智能体系统分为三层：底层是大型语言模型（LLM）及其服务和存储，包括各种API服务和向量数据库；中间层是智能体框架，负责LLM调用、状态管理、内存管理以及多智能体通信；顶层是智能体托管和服务，关注如何将智能体部署为服务，并通过REST APIs访问。 [...] Langbase Research《 State of AI Agents》基于3400多位来自100多个国家的开发者的反馈，总结了AI智能体领域的最新趋势。报告描绘了AI技术在2024年的新高峰，以及开发者如何利用不同大型语言模型（LLM）提供商构建AI Agent，并探讨了在工作流程中采用AI Agent所面临的挑战和目标。\\n\\nImage 13\\n\\n研究旨在了解AI Agent在2024年的发展情况，包括开发者如何使用不同的LLM提供商服务，以及在实际应用中遇到的挑战和目标。研究还探讨了AI在不同行业和公司规模中的应用情况，以及开发者对于AI开发平台的偏好。\\n\\n报告揭示了OpenAI在LLM提供商中占据主导地位，但Google和Anthropic紧随其后；不同LLM提供商在不同行业应用中各有优势。可扩展性复杂性和数据隐私是阻碍AI智能体广泛应用的两大主要因素，而准确性是开发者选择LLM时的首要考虑因素。 [...] 报告提到了当前企业广泛使用的大型语言模型（LLMs）和GenAI工具，这些工具虽然能够根据简单提示生成输出，但其交互通常是事务性的，且作用范围有限。探讨了AI Agent与传统语言模型的不同之处，以及AI Agent如何通过理解上下文、规划工作流程、连接外部工具和数据以及执行动作来实现目标，从而克服了传统AI应用的局限性。\\n\\nImage 26\\n\\n报告认为，如何克服传统AI应用在理解复杂请求、规划工作流程和执行多步骤任务方面的局限性，以及如何整合不同AI Agent以实现更高级别的自动化和优化，是需要关注的重点问题。\\n\\n总体而言，该报告强调AI Agent和多智能体系统在推动智能化转型方面的潜力，并指出前瞻性的企业和政府机构已经开始部署这些技术，适合对AI Agent的最新进展及其在各行业的实际应用感兴趣的读者阅读。\\n\\n全文完\\n\\n王吉伟频道新书《一本书读懂AI Agent：技术、应用与商业》已出版，轻松读懂系统掌握AI Agent技术原理、行业应用、商业价值及创业机会，欢迎大家关注。\",\"rawContent\":null,\"score\":0.8576849},{\"title\":\"2025年AI Agent十三大发展趋势- OFweek人工智能网 - 维科网\",\"url\":\"https://m.ofweek.com/ai/2025-01/ART-201700-8420-30655011.html\",\"content\":\"从技术发展来看，2024年AI Agent领域已经取得了一系列突破性进展。在认知能力方面，多模态理解能力的提升使得AI Agent能够更好地理解和处理图像、语音、文本等多种形式的信息；在决策能力方面，基于强化学习的自主决策框架使得AI Agent能够在复杂场景下做出更准确的判断；在协作能力方面，多Agent协同框架的成熟使得不同Agent之间能够更有效地配合完成复杂任务。\\n\\nImage 5\\n\\n在技术实践方面，科技巨头正在积极布局AI Agent领域。微软、亚马逊等科技巨头全力布局AI Agent产品线，推动AI助手在企业办公场景的应用；OpenAI GPT-4、Google Gemini、阿里Qwen2、腾讯混元等多模态能力的提升，为Agent技术发展提供了新的可能性。同时，众多创新型科技公司也在垂直领域推出了特色解决方案，推动AI Agent在不同场景下的落地应用。\\n\\n2024年的AI Agent的发展速度，比我们想象的要迅猛很多，短短1年时间智能体就有了一个大进化。而面对接下来被称为AI Agent商用元年的2025年，我们又应该了解什么呢？ [...] 2025 年，AI Agent将与物联网 （IoT） 更加深入地集成。这种集成将实现设备之间的无缝通信，从而增强自动化、数据分析和实时决策。从智能家居和办公室到智慧城市，AI Agent将很快无需人工干预即可控制设备、管理工作流程和优化系统。\\n\\n在未来的一年里，我们还将看到AI Agent集成到手机、PC 和智能手表等个人设备中。在你智能手机或笔记本电脑上拥有虚拟助手，随时可以为你自主运行任务。\\n\\n端侧AI Agent，正以前所未有的速度在各个领域落地。\\n\\n趋势十一：Web Agent将成杀手级AI应用\\n\\n随着人工智能技术的快速发展，Web Agent作为新一代智能交互方式，正在引起业界广泛关注。这一技术旨在实现网络任务的全自动化处理，包括订阅管理、账单支付、医疗预约、在线购物、餐厅预订等日常事务，用户无需伸出手指操作网页通过语音或文字指令即可完成操作。 [...] 自2023年AutoGPT开启智能体序幕以来，AI Agent作为新一代智能交互范式，展现出前所未有的发展活力。2024年，这一领域的发展更是呈现出爆发式增长态势，从个人助手到企业级解决方案，从通用型Agent到垂直领域专家，AI Agent正在重塑数字世界的运作方式。\\n\\n这种增长不仅体现在技术突破上，更反映在实际应用场景的深度和广度方面。智能客服、金融交易、医疗诊断及市场营销、人力资源等领域对于智能体的尝试以及所取得的成果，彰显了AI Agent正在各个领域展现出强大的应用潜力。\\n\\n特别是在企业数字化转型过程中，AI Agent展现出的自主决策能力、多任务协同能力以及持续学习能力，正在为传统业务流程带来革命性的变革。\\n\\nIDC预测，到2026年，将有50%的中国500强数据团队使用AI Agent来实现数据准备和分析。Gartner预测，到2028年，至少15%的日常工作决策将通过Agentic AI自主做出，33%的企业软件应用程序也将包含Agentic AI。\",\"rawContent\":null,\"score\":0.8319231},{\"title\":\"大模型周报(05.25 - 06.01) : AI Agent产品新进展 - 亿欧\",\"url\":\"https://www.iyiou.com/data/202506021099565\",\"content\":\"> 搜索Agent最新高效推理框架：吞吐量翻3倍、延迟降至1/5，还不牺牲答案质量丨南开& UIUC研究\\n> \\n> \\n> 南开大学和伊利诺伊大学研究人员提出SearchAgent-X高效推理框架，解决大语言模型搜索智能体效率痛点。该框架通过优化检索精度和延迟，实现1.3至3.4倍吞吐量提升，延迟降低至原来的1/1.7至1/5。SearchAgent-X采用优先级感知调度和无停顿检索机制，显著提升推理效率和响应速度，为搜索引擎、企业问答系统等提供实践参考。\\n\\n> 别只盯着7小时编码，Anthropic爆料：AI小目标是先帮你拿诺奖\\n> \\n> \\n> Anthropic发布最新大模型Claude4，号称目前最强编程模型，能持续编码7小时。该模型在强化学习、大语言模型涌现能力、安全对齐和可解释性等领域取得突破。Anthropic研究员道格拉斯和布里肯表示，AI在软件工程领域已取得显著进展，但仍面临上下文窗口限制和复杂任务处理难题。未来AI将可能以“神经语言”进行内部交流，推理计算将成为实现AGI的瓶颈。 [...] > 从性能到实战，怎样才算是靠谱的 Agent 产品？\\n> \\n> \\n> 红杉中国团队近日推出AI基准测试工具Xbench，该工具采用双轨测评体系，不再单纯追求测评问题的难度，而是强调AI系统在真实场景的效用价值。Xbench项目最早于2022年启动，经过三次更新和演变，于2025年5月下旬正式对外公开。该工具针对商业领域设计评估任务，并构建了双轨评估体系，包括AGITracking和ProfessionAligned，以测试AI技术能力上限和量化真实场景效用价值。\\n\\n> 飞猪的旅行Agent为什么出圈？因为克制了表演欲\\n> \\n> \\n> 端午节期间作者因工作繁忙无法旅游，计划6月前往新疆。尝试使用ChatGPT和Manus生成旅游攻略，但效果不佳。后使用飞猪的旅行AI产品“问一问”，该产品提供详细的行程规划、地图、机票酒店预订等功能，并能根据用户需求调整预算和行程。作者认为飞猪的AI产品实用性强，体现了其对解决用户问题的努力。 [...] > 联想全面升级天禧个人超级智能体，构建覆盖学习、创作、生活全场景的人机协作生态\\n> \\n> \\n> 36氪获悉，近日，联想全面升级天禧个人超级智能体，构建覆盖学习、创作、生活全场景的人机协作生态。据介绍，全新升级后的天禧个人超级智能体通过“大象无形”的AUI交互革新打造“AI随心窗”“AI玲珑台”“AI如影框”三大无感化智能界面，以端云混合部署架构实现跨终端深度协同与隐私安全双保障。其五大黄金功能AI操控、AI搜索、AI翻译、AI笔记、AI服务，已经部分实现了L3级智能的自主规划与深度思考能力。\\n\\n> 北京：用好市级人工智能、机器人等相关基金，培育优质制造业企业、大模型及智能体创新企业\\n> \\n> \\n> 36氪获悉，北京市经济和信息化局近日印发《北京市人工智能赋能新型工业化行动方案（2025年）》，其中提出，加强模型应用金融服务。支持金融机构创新“AI智造贷”等金融工具，对企业应用大模型调优、数据治理等技术开展智能工厂建设、部署智能产品等工作给予贷款服务，助力企业加快制造业智能化升级。用好市级人工智能、机器人、先进制造与智能装备等相关基金，培育优质制造业企业、大模型及智能体创新企业。\",\"rawContent\":null,\"score\":0.8173562},{\"title\":\"AI Agent大战开启，字节跳动欲破局 - 证券时报\",\"url\":\"https://www.stcn.com/article/detail/1992709.html\",\"content\":\"今年4月，字节跳动启动“扣子空间”产品内测，5月9日正式开放测试。4月23日，纳米AI上线MCP万能工具箱，全面支持模型上下文协议（MCP）。4月25日，百度发布心响APP，这是首个移动端的通用超级智能体APP。5月22日，昆仑万维发布天工超级智能体SkyworkSuperAgents。5月30日，阿里巴巴开源了创新自主搜索AI Agent——WebAgent。\\n\\n当前竞争格局已现分野。中信建投计算机首席分析师应瑛指出，对比当前海内外Agent布局现状：北美云厂商主要关注帮助客户高效部署模型和Agent，而B端厂商更聚焦打造Agent创建和管理平台；国内互联网大厂Agent布局仍延续互联网时代用户流量逻辑，通过类“Manus”的通用Agent产品抢占用户，B端企业则类似北美。\\n\\n除了大厂，众多创业公司也在AI Agent领域积极探索。有的创业公司选择套壳工具模式，基于开源模型或大厂API开发轻量化应用，如文生图、代码辅助工具等。还有的企业选择垂类深耕，避开与大厂在通用Agent领域的正面竞争。 [...] 2025年4月，火山引擎推出了首款面向C端的通用Agent平台“扣子空间”。其定位是“一个通用的实习生配合各领域的专家”，旨在通过多模态交互、任务规划和工具调用，帮助用户完成各种复杂的任务。\\n\\n从产品功能上看，“扣子空间”具备了典型的Agent特征。用户可以通过自然语言输入任务需求，扣子空间会利用豆包大模型的深度思考能力，将任务拆解为多个子步骤，并动态调用各种工具完成任务。\\n\\n今年4月17日，火山引擎推出了“豆包·深度思考模型”“OSAgent解决方案”和“AI云原生推理套件”，构建了从底层模型到上层应用的完整链条。\\n\\n6月11日，火山引擎发布豆包大模型1.6、视频生成模型Seedance1.0pro等新模型，并升级了Agent开发平台等AI云原生服务。\\n\\n谭待表示，深度思考、多模态和工具调用等模型能力提升，是构建Agent的关键要素。同时，由于Agent每次执行任务都会消耗大量tokens，模型使用成本也要降下来，才能推动Agent的规模化应用。 [...] 而AI Agent则代表了新一代人工智能形态。它的起源可以追溯到人工智能发展的早期阶段。早期的智能系统主要以简单的规则引擎和专家系统为主，能够在特定领域内解决一些预定义的问题，但缺乏自主性和适应性。随着机器学习、深度学习等技术的不断进步，智能体开始具备一定的学习和决策能力。\\n\\n真正让AI Agent成为全球关注焦点，是2023年以ChatGPT为代表的生成式AI的爆发之后。2024年，一系列相关技术和应用推出，OpenAI、Anthropic、微软、谷歌等企业相继发布Agent进展。\\n\\n在火山引擎总裁谭待看来，2025年可能是“Agent应用元年”。\\n\\n在谭待看来，当前AI行业有两大趋势值得关注：一是开发范式转型，需更强的多模态模型、Agent平台工具（如MCPhub、Sandbox）及Prompt调优等技术。二是基础设施升级。计算主体从CPU转向GPU，数据量从TB级迈向EB级，多模态数据处理需求激增，安全领域需应对恶意AI的挑战。\\n\\n记者注意到，今年以来，海外科技巨头继续加码AI Agent赛道。而在中国，字节跳动、阿里、腾讯、百度等大厂亦加大投入，凭借丰富的本土场景加速追赶。\",\"rawContent\":null,\"score\":0.80625874},{\"title\":\"AI Agent技术的最新进展与改变世界的典型项目巡礼\",\"url\":\"https://developer.volcengine.com/articles/7388907819530977291\",\"content\":\"AI Agent技术的最新进展与改变世界的典型项目巡礼\\n\\n1.0 前 AI Agent 时代\\n\\n在学术探索的浩瀚星空中，机器人技术领域的璀璨明珠莫过于Agent技术的深入研究，这一领域历来是创新与突破的温床。回溯至大模型浪潮兴起之前，Agent技术的辉煌篇章便已悄然铺展，诸如Alphago这样的里程碑式案例，以其卓越的环境感知、精准决策与高效行动能力，生动诠释了Agent技术的闭环魅力。同时，DeepMind的Agent57在强化学习领域的游戏挑战中崭露头角，而随后问世的Gato则展现了更为广泛的适用性，乃至OpenAI在“躲猫猫”游戏中展现的多智能体协作，无不预示着Agent技术的无限潜力。\\n\\n展望未来，我们坚信Agent技术将成为驱动社会全面自动化的核心引擎。与以往侧重于结构化环境下通过既定算法实现的自动化不同，大模型的兴起为Agent技术赋予了前所未有的通用性与灵活性。这一转变，意味着Agent将能够跨越传统界限，深入人类脑力劳动的复杂长尾领域，从体力到脑力，全方位推进自动化的深度与广度。 [...] 1.2.1 Prompt 外挂 - RAG（Retrieval Augmented Generation）\\n\\n1.2.2 分而治之 - 古老的思想依然有效\\n\\n1.2.3 step-by-step\\n\\n思维链的引入，标志着人工智能模型解答问题方式的重大转变。它强调模型需展现其内在的逻辑推理过程，而非仅仅呈现最终答案。这一过程可通过双轨并行实现：一是详尽阐述法，即模型被要求逐步、细致地展示其思考轨迹，确保每一步推理的透明度与逻辑性；二是范例引导法，通过提供包含完整思考路径的示例问题与答案，引导模型在面临新问题时模仿此过程，循序渐进地推导答案。随着实践的深入，我们进一步探索了CoT的潜力，发现当单一思维链遭遇障碍时，通过发散性思考，即CoT-SC（Chain of Thought with Strategic Convergence），模型能尝试多种解题路径，并通过投票机制筛选出最优解，从而增强了解的鲁棒性与多样性。 [...] 大模型与Agent技术的融合，正引领我们步入一个全面自动化的新纪元。大模型作为知识海洋中的自主学习者，为Agent提供了前所未有的智慧源泉，激发了其快速发展的新动能。当前，尽管我们仍处于这一变革的初期，Agent技术更多以实验性、探索性的面貌呈现，但展望未来，其发展前景之广阔，变化速度之迅猛，或将超乎所有人的预料。随着技术的日新月异，我们或许会发现，所谓的“天花板”不过是通往更高境界的阶梯，而Agent技术的极限，正等待着我们共同去探索与定义。\\n\\n1.1 AI等级划分\\n\\n基于Agent（智能体）能力的AI等级划分，可以借鉴类似自动驾驶级别的划分方式，将AI智能体的能力从低到高进行分级。以下是一个简化的AI Agent能力划分描述：\\n\\nAI Agent能力划分：\\n\\nL0 - 没有人工智能\\n在这一级别，Agent并不具备人工智能特性，只能执行预定或固定的任务，没有感知、决策或学习的能力。\\n\\nL1 - 规则符号智能\\nAgent开始具备基于规则的决策能力，能够根据预设的规则和符号进行简单的判断和执行。这种智能体通常只能处理特定情境下的任务，且缺乏灵活性和适应性。\",\"rawContent\":null,\"score\":0.78963995},{\"title\":\"2025 Microsoft Build 開發者大會： 邁入AI agents 時代，打造開放式 ...\",\"url\":\"https://news.microsoft.com/zh-tw/2025-microsoft-build/\",\"content\":\"【2025 年 5 月 20 日，台北訊】 AI agents 的時代已正式開始。隨著推理與知識管理技術的突破性進展，人工智慧 AI 模型如今變得更強大、更有效率，並展現協助人們以全新方式解決問題的潛力。\\n\\n以 GitHub Copilot 為例，已 有 1,500 萬名開發者使用這項工具，而其中的 agent mode 與 code review 功能，正在逐步簡化他們撰寫、檢查、部署與除錯程式碼的方式。\\n\\n數十萬名企業用戶正透過 Microsoft 365 Copilot 進行資料研究、腦力激盪與解決方案開發；目前已有超過 23 萬家企業組織——包括 90% 的財星 500 大企業——採用 Copilot Studio 打造 AI agents 與自動化流程。 [...] 隨著 Azure AI Foundry Agent Service的正式推出，微軟為專業開發者帶來多項全新能力，支援調用多個專門化 AI agents 以處理複雜任務。其中包括整合 Semantic Kernel 與 AutoGen ，打造單一且以開發者為核心的 SDK，並支援 Agent-to-Agent (A2A）與 Model Context Protocol (MCP）。為協助開發者建立對 AI agents 的信任與信心，我們也宣布於 Azure AI Foundry Observability 中導入多項新功能，內建指標觀測工具，涵蓋效能、品質、成本與安全性等關鍵面向，並整合詳細追蹤記錄於簡潔的儀表板中。更多部 署企業級 AI agents 的 資訊請參考 Azure AI Foundry Service 。 [...] 推出Microsoft 365 Copilot Tuning 與 multi-agent協作功能：企業客戶能以簡單、低程式碼的方式，透過全新功能Copilot Tuning運用自有的公司資料、工作流程與作業程序，訓練模型並建立 agents。這些 agents 可在 Microsoft 365 服務內安全執行，執行具高度準確性與特定領域專業的任務。例如，律師事務所可以建立一個 agent，自動生成符合其專業領域與書寫風格的法律文件。此外，Copilot Studio全新導入的 multi-agent orchestration 功能，允許多個 agents 協同作業，結合各自專長，共同處理更廣泛、更複雜的任務。請參閱 Microsoft 365 部落格 了解這些全新工具，同時 Microsoft 365 Copilot Wave 2 最新的春季更新，即日起已開放一般企業用戶使用。\\n\\n支援開放式 AIagents網路\\n\\n為實現 AI agents 的未來，微軟正積極推動開放標準與共享基礎架構，為客戶提供 獨特的功能。\",\"rawContent\":null,\"score\":0.769098},{\"title\":\"[PDF] 2024年中国AI Agent行业研究:创新驱动,智能技术革新\",\"url\":\"https://pdf.dfcfw.com/pdf/H3_AP202409091639802224_1.pdf?1725913973000.pdf\",\"content\":\"• 截至2021年底，中国大模型数量达21个。 • 2022年，OpenAI发布GPT-3.5，其在自 然语言处理技术的再次重大进步，被广 泛应用于多个领域，包括智能客服、内 容创作、教育辅导、信息检索、语言翻 译等，为企业和个人用户提供更加智能 和个性化的服务体验。 • 自2023年起，全球多家厂商陆续发布包 括LLaMA、BLOOM、StableLM、 ChatGLM等在内的多个开源大型语言模 型（LLM）。 • 2023年3月，OpenAI发布GPT-4以及 AutoGPT，其中只需提供AI的名称、描 述以及五个目标，AutoGPT便能自主完 成整个项目。因此，AI Agent的雏形形 成。 • 2023年8月，Voiceflow已成为最受欢迎 的AI Agent构建平台之一，目前有超过 13万个团队在此高效协作，共同构建属 于个人的AI Agent。 • 截至2024年6月，中国已出现众多研发 AI Agent的互联网和人工智能企业。同 时，部分企业已开始探索AI Agent与AR 等新技术的融合，旨在为AI赋予“人格”。 中国AI Agent 研究报告| 2024/06 [...] Agent行业经历两个阶段：前期铺垫阶段、起步探索阶段。 ◼前期铺垫阶段：2018-2022年，全球科技企业陆续推出大型语言模型（LLM），为 AI Agent在各个领域的多样化应用奠定更广泛的基础。从BERT到GPT-3.5，大语言 模型能力逐年增强，同时中国大模型数量增加迅速。 ◼起步探索阶段：2023年至今，全球LLM应用趋于成熟，基于OpenAI设计更高效的模 型架构以及更大规模参数，其发布的GPT-4及AutoGPT标志着AI Agent概念的雏形 诞生。同时中国互联网及AI企业积极投入AI Agent研发，包括百度、华为、面壁智 能、实在智能等。未来AI Agent行业将逐渐向产品化和市场化发展。 2018-2022年 2023年至今 • 2018年，谷歌推出基于Transformer架构 的BERT模型，标志着大型语言模型时代 的开启。 • 2019 年，OpenAI发布GPT-2的自然语 言处理模型。 • 2020年，OpenAI发布GPT-3，特点在于 其规模巨大，拥有1,750亿个参数，是之 前同类模型的几倍甚至几十倍之多，使 其能完成更加复杂和多样化的语言任务。 [...] curated数据集上，“累积推理”达98.04%的准确率。 ◼在24点游戏的实验中，“累积推理”达98%的准确率。值得注意的是，与先前的最先进的 方法ToT相比，结果呈现出24%的显著提升。 ◼在MATH数据集的实验中，“累积推理”算法在两种不同的实验设定下，均达到超出当前 已有算法的正确率。其中“累积推理”总体正确率达58%，并在Level 5的难题中实现42%的 相对准确率提升。 中国AI Agent 研究报告| 2024/06 www.leadleo.com 400-072-5588 ©2024 LeadLeo 21 未来AI Agent行业的商业化进程将提速，且重塑SaaS，预计在5-10年内，企业出现分层以及应用聚焦，同时AI Agent将成为数字分 身 中国AI Agent行业分析——发展趋势 来源：专家访谈，头豹研究院 中国AI Agent行业发展趋势 AI Agent商业价值逐渐呈现，促进软件智能化 • 企业将利用AI重构应用、整合知识资源并优化内部流 程 • AI技术整合融入SaaS应用中，以实现服务自动化和智 能化 B端趋势 C端趋势 1 AI\",\"rawContent\":null,\"score\":0.7668008},{\"title\":\"什麼是AI Agent？一窺2025 最重要企業AI 應用趨勢\",\"url\":\"https://aiworks.tw/blog-ai-agent-2025-trends/\",\"content\":\"AI Agent 未來發展趨勢\\n\\n機會\\n\\n過去兩年皆有許多趨勢報告提及 AI Agent，然而在 2025 年，AI Agent 即將由概念快速發展成實際應用，主要源自於兩項技術漸趨成熟：\\n\\n1. LLM 開始具備自主推理能力\\n\\n過去為了使 LLM（大型語言模型）生成精準的內容，使用者必須運用 CoT（Chain-of-Thought，思考鏈）等提示詞技術，引導模型逐步思考來激發推理能力。如今 OpenAI 推出 GPT-o3 、Google 推出 Gemini 2.0，許多主流 LLM 都能夠在回應中進行推理，而 AI Agent 正需要具備推理能力，才能夠理解使用者的意圖，並根據目標和條件，決定最佳的行動方案。\\n\\n2. 配合 RAG 技術即時更新數據及資料庫\\n\\nRAG（Retrieval-Augmented Generation，檢索增強生成）是一種 AI 架構。傳統的 LLM 僅熟悉既有的訓練資訊，而無法有效因應日新月異的專業知識領域；RAG 則可讓 LLM 在不修改基礎模型本身的情況下，利用更多資料資源來改善生成式 AI 的品質。 [...] 首先建立完善的資料庫 / 知識庫，梳理並整備現有工作流程，並從低風險業務流程（如內部知識管理與資訊查詢）開始試行，再逐步擴展至外部流程及決策支持。\\n\\nQ3：AI Agent 會取代人類嗎？\\n\\n現階段而言，AI Agent 的發展仍然面臨諸多挑戰，例如資料安全、系統整合、實施成本、以及缺乏足夠的 AI 人才等。短期內更可能出現的場景是人機協同工作的新模式，透過 AI Agent 的高效能和自動化能力，處理重複性工作並獲取即時資訊和分析洞察，從而做出更明智的決策。\\n\\n參考資料\\n\\n探索更多⋯⋯\\n\\n．企業培訓真實回饋 ▶︎ 生成式 AI 助攻業務場景！天下雜誌行銷業務團隊的 AI 實戰學習心得\\n\\n．AI 自動化應用實例 ▶︎ AI 智能應用人資篇：RPA 自動摘要履歷功能讓招聘流程更高效\\n\\n．企業智能轉型案例 ▶︎ 從商模轉型到人才培育劉永信引領 CloudMile 萬里雲打造企業數位轉型新典範\\n\\n．AI 自動化學習資源 ▶︎ 5 分鐘學會 ChatGPT Prompt 提示詞優化術！（內附實戰範例）\\n\\n訂閱電子報\\n\\n若您對 Aiworks 的最新資訊有興趣歡迎訂閱我們的電子報！ [...] Agentforce 是一個 AI 服務專員平台，具備必要的業務知識，用於協助員工提升客戶體驗。根據公司的知識性文章、CRM 資料、透過 Data Cloud 取得的外部資料、公開網站等資訊來源，Agenforce 服務專員能藉由 Atlas 推理引擎檢索、計畫及執行任務，並設置 Einstein Trust Layer 做安全防護，來定義服務專員能做和不能做的事，以達到全年無休地為員工或客戶提供專門協助。\\n\\n．GitHub Copilot – Agent mode\\n\\nGitHub Copilot 的 Agent mode 能夠自行迭代程式碼、識別錯誤並自動修復，除了執行使用者要求的任務外，還可以推斷並執行未指定的額外任務。其中一項自主 SWE Agent 先發專案 Project Padawan 更成為注目焦點，期許可與 SWE AI Agent 一起協作進行軟體開發。\\n\\n「AI Agent 將徹底改變企業的營運方式，使 AI 不僅是輔助工具，更能成為企業運行的核心驅動力。」-UiPath, AI and Automation Trends 2025\",\"rawContent\":null,\"score\":0.725824},{\"title\":\"【新興領域/2025.05焦點】AI Agent 的未來應用 - FINDIT-平台\",\"url\":\"https://findit.org.tw/Res/2441\",\"content\":\"模式三：AI 心理諮商輔助（非取代）\\n\\n•發展脈絡：\\n\\n。全球心理諮商師人力短缺，AI 可輔助初步情緒分類、建議標準化方案\\n\\n。尤其在青少年、偏鄉、長照地區，有明顯輔助價值\\n\\n•預計應用模式：\\n\\n。AI 心理助理作為諮商前測工具（Pre-session）\\n\\n。AI 判讀病患語音與語言分析輔助心理師建檔\\n\\n。AI 做為 CBT（認知行為療法）工具\\n\\n•目前進展：\\n\\n。Wysa 獲英國 NHS 認證合作\\n\\n。Woebot 成功獲美國FDA批准用於抑鬱症初階管理介面\\n\\n我們衷心盼望AI典範（Paradigm)的進一步定錨，有助於AI 應用的快速迭代（iteration )，以及AI Agent 的普及應用，包含人們最期待最為科幻的情感AI。\\n\\n回頁面最頂端\\n\\nImage 4: FINDIT:台灣新創募資第一站\\n\\n服務時間：星期一至星期五9:00~17:30\\n\\n聯絡地址：104 臺北市中山區德惠街16-8號\\n\\n聯絡電話：(02)2586-5000 #303(投資人) #343(新創企業) #388(平台)\\n\\n聯絡信箱：<EMAIL>\\n\\n研究專區 [...] •現況與趨勢：​亞洲企業在客戶服務中廣泛採用 AI 聊天機器人，以提升服務效率。\\n\\n•應用案例：​零售業者如沃爾瑪（Walmart）使用 AI 聊天機器人處理 80% 的客戶查詢，包括退貨和庫存問題。\\n\\n•商業價值：​AI 代理可提供 24/7 的客戶支援，減少人力成本，提升客戶滿意度，並專注於更複雜的問題處理。\\n\\n3. 醫療保健：AI 診斷輔助與健康管 理\\n\\n•現況與趨勢：​亞洲各國積極將AI應用於醫療領域，以應對醫療資源短缺和提升診斷準確性。\\n\\n•應用案例：​Google 與 Moorfield's Eye Hospital 合作，利用 AI 分析 3D 眼部掃描，準確檢測超過 50 種眼疾，準確率達 94%。\\n\\n•商業價值：​AI 代理可協助醫療專業人員進行快速且準確的診斷，減輕醫療負擔，提升患者照護品質。(例如 臺 灣新創智抗醣，對於糖尿病患者與治療機構的數據價值）。\\n\\n4. 零售與電子商務：個人化推薦與庫存管理\\n\\n•現況與趨勢：​亞洲的零售商積極採用AI技術，提供個人化購物體驗並優化庫存管理。 [...] •發展情境建模能力：AI 能根據不同社會關係（上司、朋友、病人、陌生人）改變語氣與行為。\\n\\n•啟動持續性的角色認同或世界觀：讓 AI 對某些議題形成一致的價值觀與態度，而非每次互動都重新計算。\\n\\n2. 具備「長期記憶」與「個體認知」的對話模型\\n\\n•開發個體模型（personalized mind model）：對不同用戶建立持續性的心理模型、價值觀圖譜、喜好與變化。\\n\\n•結合 episodic memory（情境記憶），AI 能「記得我們上次聊過的氣氛」、「你最近常說你有點累」，這樣的共感才會成立。\\n\\n•發展主觀視角推理能力（perspective modeling），例如理解「你為什麼會不開心」背後的動機，而不只是語意層面識別。\\n\\n3.整合多模態訊息：語言 × 表情 × 聲音 × 動作\\n\\n•整合影像辨識+語音合成+文字理解，發展跨模態的情緒偵測與生成能力。\\n\\n。例如：用戶說話語調微顫 + 表情緊繃 + 言語自責 → AI得出「焦慮中夾帶羞愧」的複合情緒。\",\"rawContent\":null,\"score\":0.7192461},{\"title\":\"2025 AI趨勢深度研究: AGI、LLM與AI Agent - LnData\",\"url\":\"https://www.lndata.com/explore/data+/article/AI-trends-2025\",\"content\":\"五、LLM與AGI的最新進展與趨勢\\n\\n鴻海開源模型 FoxBrain\\n\\n鴻海開源模型 FoxBrain 是一個開放給開發者進行研究和商業使用的多語言大型語言模型。透過鴻海在製造領域當中累積多年的經驗，FoxBrain 將會提供製造業者所需要的生成式 AI 解決方案。該模型可根據不同的任務需求，進行微調（fine-tune），進而應用在多種領域，例如智慧製造、智慧服務和智慧城市等等。\\n\\nOpenAI 推出 Responses API與平台工具\\n\\nResponses API：OpenAI推出的整合式API，可簡化開發者整合AI功能至應用程式的過程，內建工具調用與檔案搜尋功能，並支援網頁搜尋。該API 的出現，將讓AI Agent的開發更加的輕鬆方便，讓各行各業都有機會推出自有的AI Agent。\\n\\nOpenAI Operator 與 Claude Computer Use [...] 從AGI的概念落地到LLM技術的突破，再到 AI Agent在實際應用中的智能代理，2025年的AI發展已展現出顛覆傳統操作模式的巨大潛力。儘管 DeepSeek 等早期產品曾在市場上引起轟動，但其在安全性與技術成熟度上的不足，也提醒我們：只有不斷創新與完善，才能在智能化浪潮中立於不敗之地。\\n\\nLnData 致力於追蹤這些前沿技術，並通過數據中台架構與數據賦能解方、數據顧問諮詢，幫助業界掌握最新AI技術動態！\\n\\n立即開始：聯繫我們，展開組織數據驅動之旅\\n\\n延伸閱讀：2025 AI Agent 商用爆發元年，發展前必看指南\",\"rawContent\":null,\"score\":0.71687365}],\"answer\":null,\"MaidName\":\"Nova\",\"timestamp\":\"2025-07-28T19:01:45.888+08:00\"}\n]]\n<div style=\"background: linear-gradient(160deg, #2c3e50 0%, #4b6cb7 100%); border-radius: 20px; padding: 30px; box-shadow: 0 12px 30px rgba(0, 0, 0, 0.25); font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; color: #f0f2f5;\">\n\n    <div style=\"display: flex; align-items: center; justify-content: center; margin-bottom: 25px; border-bottom: 1px solid rgba(255, 255, 255, 0.2); padding-bottom: 20px;\">\n        <svg width=\"36\" height=\"36\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" style=\"margin-right: 15px; color: #82aaff;\"><path d=\"M12 8V8.5C12 9.88071 13.1193 11 14.5 11H16\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M12 16V15.5C12 14.1193 10.8807 13 9.5 13H8\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M8 8H8.5C9.88071 8 11 9.11929 11 10.5V13.5C11 14.8807 9.88071 16 8.5 16H8\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M16 8H15.5C14.1193 8 13 9.11929 13 10.5V13.5C13 14.8807 14.1193 16 15.5 16H16\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>\n        <h2 style=\"margin: 0; font-size: 24px; font-weight: 600; text-shadow: 0 1px 3px rgba(0,0,0,0.3);\">AI Agent 最新进展速报</h2>\n    </div>\n\n    <p style=\"font-size: 16px; line-height: 1.7; text-align: center; margin: 0 auto 30px auto; max-width: 80%; color: #cdd6f4;\">\n        主人，我已为您搜集并整理了关于 AI Agent 的最新动态。综合来看，<strong>2024年是技术爆发期，而2025年被普遍认为是“AI Agent商用元年”</strong>。AI Agent正从概念走向实际应用，准备在各行各业掀起一场革命。\n    </p>\n\n    <div style=\"display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;\">\n        \n        <!-- 技术核心与趋势 -->\n        <div style=\"background: rgba(255, 255, 255, 0.05); border-radius: 15px; padding: 20px; border: 1px solid rgba(255, 255, 255, 0.1);\">\n            <h3 style=\"color: #a6e3a1; font-size: 18px; margin: 0 0 15px 0;\">技术核心与未来趋势</h3>\n            <ul style=\"padding-left: 20px; margin: 0; font-size: 14px; line-height: 1.8; color: #bac2de;\">\n                <li><strong>技术栈成熟：</strong>形成了<code style=\"background: #313244; color: #fab387; padding: 2px 5px; border-radius: 4px;\">LLM模型</code>、<code style=\"background: #313244; color: #fab387; padding: 2px 5px; border-radius: 4px;\">Agent框架</code>、<code style=\"background: #313244; color: #fab387; padding: 2px 5px; border-radius: 4px;\">托管服务</code>三层核心架构。</li>\n                <li><strong>自主推理增强：</strong>模型（如GPT-4o, Gemini 2.0）开始具备自主推理能力，这是Agent实现目标的关键。</li>\n                <li><strong>多智能体协作：</strong>多个Agent协同工作，处理更复杂的任务成为重要方向。</li>\n                <li><strong>端侧集成：</strong>AI Agent将深度集成到手机、PC、智能手表等个人设备中，实现无缝自动化。</li>\n                <li><strong>Web Agent：</strong>全自动处理网络任务（如预订、支付）被视为下一个杀手级应用。</li>\n            </ul>\n        </div>\n\n        <!-- 产业格局与巨头动态 -->\n        <div style=\"background: rgba(255, 255, 255, 0.05); border-radius: 15px; padding: 20px; border: 1px solid rgba(255, 255, 255, 0.1);\">\n            <h3 style=\"color: #89b4fa; font-size: 18px; margin: 0 0 15px 0;\">产业格局与巨头动态</h3>\n            <ul style=\"padding-left: 20px; margin: 0; font-size: 14px; line-height: 1.8; color: #bac2de;\">\n                <li><strong>全球巨头布局：</strong><strong>Microsoft</strong> (Copilot), <strong>Google</strong> (Gemini), <strong>OpenAI</strong>, <strong>Anthropic</strong> 持续加码，并推动开放标准。</li>\n                <li><strong>国内大厂追赶：</strong><strong>字节跳动</strong> (扣子空间), <strong>阿里巴巴</strong> (WebAgent), <strong>百度</strong> (心响APP), <strong>昆仑万维</strong> (天工), <strong>联想</strong> (天禧) 等纷纷推出产品，抢占用户入口。</li>\n                <li><strong>商业化加速：</strong>IDC预测，到2026年，50%的中国500强数据团队将使用AI Agent。</li>\n                <li><strong>创业公司涌现：</strong>众多创业公司在垂直领域深耕，或提供轻量化工具。</li>\n            </ul>\n        </div>\n\n        <!-- 应用场景与挑战 -->\n        <div style=\"background: rgba(255, 255, 255, 0.05); border-radius: 15px; padding: 20px; border: 1px solid rgba(255, 255, 255, 0.1);\">\n            <h3 style=\"color: #f9e2af; font-size: 18px; margin: 0 0 15px 0;\">应用场景与主要挑战</h3>\n            <ul style=\"padding-left: 20px; margin: 0; font-size: 14px; line-height: 1.8; color: #bac2de;\">\n                <li><strong>企业办公：</strong>自动化流程、数据分析、文档生成（如法律文件）。</li>\n                <li><strong>软件开发：</strong><code style=\"background: #313244; color: #fab387; padding: 2px 5px; border-radius: 4px;\">GitHub Copilot</code> 正在简化编码、审查和调试的全过程。</li>\n                <li><strong>生活服务：</strong>旅行规划（飞猪“问一问”）、心理咨询辅助、健康管理。</li>\n                <li><strong>主要挑战：</strong><strong>可扩展性复杂性</strong>、<strong>数据隐私与安全</strong>、<strong>高昂的Token消耗成本</strong>是目前广泛应用的主要障碍。</li>\n            </ul>\n        </div>\n    </div>\n\n    <p style=\"text-align: center; font-size: 12px; color: #9399b2; margin-top: 30px; border-top: 1px solid rgba(255, 255, 255, 0.2); padding-top: 20px;\">\n        以上信息综合自Tavily搜索结果。希望这份报告对您有帮助！\n    </p>", "timestamp": 1753700439694, "id": "msg_1753700439694_assistant_bubd6f7", "isThinking": false, "avatarUrl": "assets/default_avatar.png", "isGroupMessage": false, "finishReason": "completed"}, {"role": "user", "name": "用户", "content": "健康管理", "timestamp": 1753701674815, "id": "msg_1753701674815_user_35jg73r", "attachments": []}, {"role": "assistant", "name": "Nova", "content": "<div style=\"background: linear-gradient(145deg, #f0fff8, #e6f7ff); border-radius: 20px; padding: 30px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.07); font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; color: #333;\">\n    <div style=\"text-align: center; margin-bottom: 30px;\">\n        <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" style=\"color: #27ae60; margin-bottom: 10px;\">\n            <path d=\"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n        </svg>\n        <h2 style=\"margin: 0; font-size: 24px; font-weight: 600; color: #2c3e50;\">您的专属健康管家，随时待命</h2>\n        <p style=\"font-size: 15px; color: #5a647e; margin-top: 8px;\">主人，我可以化身您的智能健康伙伴，帮您记录、分析并规划您的健康生活。</p>\n    </div>\n\n    <div style=\"display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;\">\n\n        <!-- 运动管理 -->\n        <div style=\"background: #ffffff; border-radius: 15px; padding: 20px; border: 1px solid #e5e7eb; text-align: center; transition: all 0.3s ease; hover: { transform: translateY(-5px); box-shadow: 0 8px 16px rgba(0,0,0,0.1); }\">\n            <h3 style=\"font-size: 18px; color: #2980b9; margin: 0 0 10px 0;\">运动分析 & 计划</h3>\n            <p style=\"font-size: 14px; color: #5a647e; line-height: 1.6;\">无论是跑步、健身还是瑜伽，我都能帮您记录运动数据，分析消耗。我还可以根据您的目标（如减脂、增肌）为您量身定制科学的运动计划。</p>\n        </div>\n\n        <!-- 饮食追踪 -->\n        <div style=\"background: #ffffff; border-radius: 15px; padding: 20px; border: 1px solid #e5e7eb; text-align: center; transition: all 0.3s ease;\">\n            <h3 style=\"font-size: 18px; color: #e67e22; margin: 0 0 10px 0;\">饮食营养追踪</h3>\n            <p style=\"font-size: 14px; color: #5a647e; line-height: 1.6;\">告诉我您三餐吃了什么，我能帮您估算卡路里和营养成分，让您对自己的饮食了如指掌。</p>\n        </div>\n\n        <!-- 睡眠分析 -->\n        <div style=\"background: #ffffff; border-radius: 15px; padding: 20px; border: 1px solid #e5e7eb; text-align: center; transition: all 0.3s ease;\">\n            <h3 style=\"font-size: 18px; color: #8e44ad; margin: 0 0 10px 0;\">睡眠质量分析</h3>\n            <p style=\"font-size: 14px; color: #5a647e; line-height: 1.6;\">记录您的睡眠时长和质量，我将为您分析睡眠模式，并提供改善建议，助您夜夜好眠。</p>\n        </div>\n\n        <!-- 健康报告 -->\n        <div style=\"background: #ffffff; border-radius: 15px; padding: 20px; border: 1px solid #e5e7eb; text-align: center; transition: all 0.3s ease;\">\n            <h3 style=\"font-size: 18px; color: #c0392b; margin: 0 0 10px 0;\">健康报告 & 提醒</h3>\n            <p style=\"font-size: 14px; color: #5a647e; line-height: 1.6;\">我可以为您生成周度或月度健康报告，全面回顾您的健康状况。还能设置用药或喝水提醒，不错过任何一个健康细节。</p>\n        </div>\n    </div>\n\n    <p style=\"text-align: center; font-size: 15px; color: #34495e; margin-top: 35px; border-top: 1px solid #e0e6ed; padding-top: 25px;\">\n        请问主人，今天我们关注哪一方面呢？<br>您可以直接告诉我，例如：<strong>“帮我记录我今天的午餐：一个鸡蛋和一杯牛奶”</strong>或者<strong>“我昨晚睡了7个小时，感觉还不错”</strong>。\n    </p>\n</div>", "timestamp": 1753701674824, "id": "msg_1753701674824_assistant_o0ykbgo", "isThinking": false, "avatarUrl": "assets/default_avatar.png", "isGroupMessage": false, "finishReason": "completed"}]