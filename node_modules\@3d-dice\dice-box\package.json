{"name": "@3d-dice/dice-box", "author": {"name": "<PERSON>"}, "description": "A 3D environment for rolling game dice", "version": "1.1.4", "keywords": ["3D", "dice", "roll", "roller", "javascript", "rpg", "dnd", "d&d", "tabletop"], "license": "MIT", "homepage": "https://fantasticdice.games/", "repository": {"type": "git", "url": "https://github.com/3d-dice/dice-box"}, "bugs": {"url": "https://github.com/3d-dice/dice-box/issues"}, "type": "module", "files": ["dist", "copyAssets.js"], "main": "./dist/dice-box.es.js", "files-dev": ["src"], "main-dev": "./src/index", "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview", "postinstall": "node copyAssets.js"}, "dependencies": {"@babylonjs/core": "5.57.1", "@babylonjs/loaders": "5.57.1", "@babylonjs/materials": "5.57.1", "copy-dir": "^1.3.0", "node-abort-controller": "^3.1.1"}, "devDependencies": {"rollup-plugin-copy": "^3.5.0", "rollup-plugin-delete": "^2.0.0", "rollup-plugin-visualizer": "^5.12.0", "vite": "^4.5.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": [">0.2%", "not dead", "not op_mini all"]}}