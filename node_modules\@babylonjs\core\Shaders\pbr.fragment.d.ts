import "./ShadersInclude/prePassDeclaration";
import "./ShadersInclude/oitDeclaration";
import "./ShadersInclude/pbrFragmentDeclaration";
import "./ShadersInclude/pbrUboDeclaration";
import "./ShadersInclude/pbrFragmentExtraDeclaration";
import "./ShadersInclude/lightFragmentDeclaration";
import "./ShadersInclude/lightUboDeclaration";
import "./ShadersInclude/pbrFragmentSamplersDeclaration";
import "./ShadersInclude/imageProcessingDeclaration";
import "./ShadersInclude/clipPlaneFragmentDeclaration";
import "./ShadersInclude/logDepthDeclaration";
import "./ShadersInclude/fogFragmentDeclaration";
import "./ShadersInclude/helperFunctions";
import "./ShadersInclude/subSurfaceScatteringFunctions";
import "./ShadersInclude/importanceSampling";
import "./ShadersInclude/pbrHelperFunctions";
import "./ShadersInclude/imageProcessingFunctions";
import "./ShadersInclude/shadowsFragmentFunctions";
import "./ShadersInclude/harmonicsFunctions";
import "./ShadersInclude/pbrDirectLightingSetupFunctions";
import "./ShadersInclude/pbrDirectLightingFalloffFunctions";
import "./ShadersInclude/pbrBRDFFunctions";
import "./ShadersInclude/hdrFilteringFunctions";
import "./ShadersInclude/pbrDirectLightingFunctions";
import "./ShadersInclude/pbrIBLFunctions";
import "./ShadersInclude/bumpFragmentMainFunctions";
import "./ShadersInclude/bumpFragmentFunctions";
import "./ShadersInclude/reflectionFunction";
import "./ShadersInclude/pbrBlockAlbedoOpacity";
import "./ShadersInclude/pbrBlockReflectivity";
import "./ShadersInclude/pbrBlockAmbientOcclusion";
import "./ShadersInclude/pbrBlockAlphaFresnel";
import "./ShadersInclude/pbrBlockAnisotropic";
import "./ShadersInclude/pbrBlockReflection";
import "./ShadersInclude/pbrBlockSheen";
import "./ShadersInclude/pbrBlockClearcoat";
import "./ShadersInclude/pbrBlockIridescence";
import "./ShadersInclude/pbrBlockSubSurface";
import "./ShadersInclude/clipPlaneFragment";
import "./ShadersInclude/pbrBlockNormalGeometric";
import "./ShadersInclude/bumpFragment";
import "./ShadersInclude/pbrBlockNormalFinal";
import "./ShadersInclude/depthPrePass";
import "./ShadersInclude/pbrBlockLightmapInit";
import "./ShadersInclude/pbrBlockGeometryInfo";
import "./ShadersInclude/pbrBlockReflectance0";
import "./ShadersInclude/pbrBlockReflectance";
import "./ShadersInclude/pbrBlockDirectLighting";
import "./ShadersInclude/lightFragment";
import "./ShadersInclude/pbrBlockFinalLitComponents";
import "./ShadersInclude/pbrBlockFinalUnlitComponents";
import "./ShadersInclude/pbrBlockFinalColorComposition";
import "./ShadersInclude/logDepthFragment";
import "./ShadersInclude/fogFragment";
import "./ShadersInclude/pbrBlockImageProcessing";
import "./ShadersInclude/oitFragment";
import "./ShadersInclude/pbrDebug";
/** @internal */
export declare const pbrPixelShader: {
    name: string;
    shader: string;
};
