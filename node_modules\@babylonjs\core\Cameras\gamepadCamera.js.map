{"version": 3, "file": "gamepadCamera.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Cameras/gamepadCamera.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEpD,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAC/B,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IACrD,OAAO,GAAG,EAAE,CAAC,IAAI,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;AAChE,CAAC,CAAC,CAAC;AAEH;;;;GAIG;AACH,MAAM,OAAO,aAAc,SAAQ,eAAe;IAC9C;;;;;;;;OAQG;IACH,YAAY,IAAY,EAAE,QAAiB,EAAE,KAAa;QACtD,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,eAAe,CAAC;IAC3B,CAAC;CACJ", "sourcesContent": ["import { UniversalCamera } from \"./universalCamera\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport { Node } from \"../node\";\r\nNode.AddNodeConstructor(\"GamepadCamera\", (name, scene) => {\r\n    return () => new GamepadCamera(name, Vector3.Zero(), scene);\r\n});\r\n\r\n/**\r\n * This represents a FPS type of camera. This is only here for back compat purpose.\r\n * Please use the UniversalCamera instead as both are identical.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#universal-camera\r\n */\r\nexport class GamepadCamera extends UniversalCamera {\r\n    /**\r\n     * Instantiates a new Gamepad Camera\r\n     * This represents a FPS type of camera. This is only here for back compat purpose.\r\n     * Please use the UniversalCamera instead as both are identical.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#universal-camera\r\n     * @param name Define the name of the camera in the scene\r\n     * @param position Define the start position of the camera in the scene\r\n     * @param scene Define the scene the camera belongs to\r\n     */\r\n    constructor(name: string, position: Vector3, scene?: Scene) {\r\n        super(name, position, scene);\r\n    }\r\n\r\n    /**\r\n     * Gets the current object class name.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"GamepadCamera\";\r\n    }\r\n}\r\n"]}