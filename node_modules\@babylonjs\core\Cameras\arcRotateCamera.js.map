{"version": 3, "file": "arcRotateCamera.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Cameras/arcRotateCamera.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,wBAAwB,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AACjH,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAGhD,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAE/B,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AACtC,OAAO,EAAE,oBAAoB,EAAE,MAAM,2CAA2C,CAAC;AACjF,OAAO,EAAE,gBAAgB,EAAE,MAAM,uCAAuC,CAAC;AACzE,OAAO,EAAE,eAAe,EAAE,MAAM,sCAAsC,CAAC;AACvE,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAI9C,OAAO,EAAE,4BAA4B,EAAE,MAAM,yCAAyC,CAAC;AACvF,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAItC,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IACvD,OAAO,GAAG,EAAE,CAAC,IAAI,eAAe,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;AAC7E,CAAC,CAAC,CAAC;AAEH;;;;;;GAMG;AACH,MAAM,OAAO,eAAgB,SAAQ,YAAY;IA+B7C;;;OAGG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IACD,IAAW,MAAM,CAAC,KAAc;QAC5B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IACD,IAAW,UAAU,CAAC,KAA6B;QAC/C,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SACzB;IACL,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,QAAQ,CAAC,WAAoB;QACpC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IAClC,CAAC;IAKD;;;;OAIG;IACH,IAAI,QAAQ,CAAC,GAAY;QACrB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,IAAI,CAAC,YAAY,GAAG,IAAI,MAAM,EAAE,CAAC;YACjC,IAAI,CAAC,YAAY,GAAG,IAAI,MAAM,EAAE,CAAC;YAEjC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;SACnC;QAED,GAAG,CAAC,SAAS,EAAE,CAAC;QAChB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC;IACpB,CAAC;IAED,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,QAAQ;QACX,kDAAkD;QAClD,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAEjF,0DAA0D;QAC1D,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;IACrF,CAAC;IAyGD,2DAA2D;IAE3D;;OAEG;IACH,IAAW,mBAAmB;QAC1B,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,mBAAmB,CAAC;SACvC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAW,mBAAmB,CAAC,KAAa;QACxC,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,mBAAmB,GAAG,KAAK,CAAC;SACxC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,mBAAmB;QAC1B,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,mBAAmB,CAAC;SACvC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAW,mBAAmB,CAAC,KAAa;QACxC,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,mBAAmB,GAAG,KAAK,CAAC;SACxC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,cAAc,CAAC;SAClC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAW,cAAc,CAAC,KAAa;QACnC,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,cAAc,GAAG,KAAK,CAAC;SACnC;IACL,CAAC;IAED;;;;OAIG;IACH,IAAW,oBAAoB;QAC3B,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,oBAAoB,CAAC;SACxC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAW,oBAAoB,CAAC,KAAa;QACzC,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,oBAAoB,GAAG,KAAK,CAAC;SACzC;IACL,CAAC;IAED;;;;;;OAMG;IACH,IAAW,mBAAmB;QAC1B,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,mBAAmB,CAAC;SACvC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,IAAW,mBAAmB,CAAC,KAAc;QACzC,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,mBAAmB,GAAG,KAAK,CAAC;SACxC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB;QACzB,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,kBAAkB,CAAC;SACtC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAW,kBAAkB,CAAC,KAAa;QACvC,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,kBAAkB,GAAG,KAAK,CAAC;SACvC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,MAAM,QAAQ,GAAqC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACpF,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,MAAM,CAAC;SAC1B;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAW,MAAM,CAAC,KAAe;QAC7B,MAAM,QAAQ,GAAqC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACpF,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;SAC3B;IACL,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,MAAM,QAAQ,GAAqC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACpF,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,QAAQ,CAAC;SAC5B;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAW,QAAQ,CAAC,KAAe;QAC/B,MAAM,QAAQ,GAAqC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACpF,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC;SAC7B;IACL,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,MAAM,QAAQ,GAAqC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACpF,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,QAAQ,CAAC;SAC5B;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAW,QAAQ,CAAC,KAAe;QAC/B,MAAM,QAAQ,GAAqC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACpF,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC;SAC7B;IACL,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,MAAM,QAAQ,GAAqC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACpF,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,SAAS,CAAC;SAC7B;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAW,SAAS,CAAC,KAAe;QAChC,MAAM,QAAQ,GAAqC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACpF,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;SAC9B;IACL,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,MAAM,UAAU,GAAmC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACtF,IAAI,UAAU,EAAE;YACZ,OAAO,UAAU,CAAC,cAAc,CAAC;SACpC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAW,cAAc,CAAC,KAAa;QACnC,MAAM,UAAU,GAAmC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACtF,IAAI,UAAU,EAAE;YACZ,UAAU,CAAC,cAAc,GAAG,KAAK,CAAC;SACrC;IACL,CAAC;IAED;;;OAGG;IAEH,IAAW,mBAAmB;QAC1B,MAAM,UAAU,GAAmC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACtF,IAAI,UAAU,EAAE;YACZ,OAAO,UAAU,CAAC,mBAAmB,CAAC;SACzC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,IAAW,mBAAmB,CAAC,KAAc;QACzC,MAAM,UAAU,GAAmC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACtF,IAAI,UAAU,EAAE;YACZ,UAAU,CAAC,mBAAmB,GAAG,KAAK,CAAC;SAC1C;IACL,CAAC;IAED;;;;OAIG;IACH,IAAW,oBAAoB;QAC3B,MAAM,UAAU,GAAmC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACtF,IAAI,UAAU,EAAE;YACZ,OAAO,UAAU,CAAC,oBAAoB,CAAC;SAC1C;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAW,oBAAoB,CAAC,KAAa;QACzC,MAAM,UAAU,GAAmC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACtF,IAAI,UAAU,EAAE;YACZ,UAAU,CAAC,oBAAoB,GAAG,KAAK,CAAC;SAC3C;IACL,CAAC;IA0DD;;;OAGG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;OAGG;IACH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC;IAC1C,CAAC;IAED,IAAW,mBAAmB,CAAC,KAAc;QACzC,IAAI,KAAK,KAAK,IAAI,CAAC,mBAAmB,EAAE;YACpC,OAAO;SACV;QAED,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,iBAAiB,GAAG,IAAI,gBAAgB,EAAE,CAAC;YAChD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SAC5C;aAAM,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC/B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC5C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;SACjC;IACL,CAAC;IAID;;;OAGG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;;OAGG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC;IACzC,CAAC;IAED,IAAW,kBAAkB,CAAC,KAAc;QACxC,IAAI,KAAK,KAAK,IAAI,CAAC,kBAAkB,EAAE;YACnC,OAAO;SACV;QAED,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,gBAAgB,GAAG,IAAI,eAAe,EAAE,CAAC;YAC9C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC3C;aAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC9B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC3C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAChC;IACL,CAAC;IAID;;;OAGG;IACH,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IAED;;;OAGG;IACH,IAAW,uBAAuB;QAC9B,OAAO,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC;IAC9C,CAAC;IAED,IAAW,uBAAuB,CAAC,KAAc;QAC7C,IAAI,KAAK,KAAK,IAAI,CAAC,uBAAuB,EAAE;YACxC,OAAO;SACV;QAED,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,qBAAqB,GAAG,IAAI,oBAAoB,EAAE,CAAC;YACxD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;SAChD;aAAM,IAAI,IAAI,CAAC,qBAAqB,EAAE;YACnC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAChD,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;SACrC;IACL,CAAC;IAuCD;;;;;;;;;OASG;IACH,YAAY,IAAY,EAAE,KAAa,EAAE,IAAY,EAAE,MAAc,EAAE,MAAe,EAAE,KAAa,EAAE,4BAA4B,GAAG,IAAI;QACtI,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,4BAA4B,CAAC,CAAC;QAhjBrE;;;WAGG;QAEI,wBAAmB,GAAG,CAAC,CAAC;QAE/B;;;WAGG;QAEI,uBAAkB,GAAG,CAAC,CAAC;QAE9B;;;WAGG;QAEI,yBAAoB,GAAG,CAAC,CAAC;QAEhC;;;WAGG;QAEI,oBAAe,GAAqB,IAAI,CAAC;QAEhD;;;WAGG;QAEI,oBAAe,GAAqB,IAAI,CAAC;QAEhD;;;WAGG;QAEI,mBAAc,GAAqB,IAAI,CAAC;QAE/C;;;WAGG;QAEI,mBAAc,GAAqB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;QAEzD;;;WAGG;QAEI,qBAAgB,GAAqB,IAAI,CAAC;QAEjD;;;WAGG;QAEI,qBAAgB,GAAqB,IAAI,CAAC;QAEjD;;WAEG;QAEI,qBAAgB,GAAW,CAAC,CAAC;QAEpC;;WAEG;QAEI,qBAAgB,GAAW,CAAC,CAAC;QAEpC;;;;WAIG;QAEI,0BAAqB,GAAW,EAAE,CAAC;QAE1C;;;WAGG;QAEI,yBAAoB,GAAqB,IAAI,CAAC;QAErD;;WAEG;QAEI,wBAAmB,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAErD;;;WAGG;QAEI,mBAAc,GAAG,GAAG,CAAC;QAqQ5B,yDAAyD;QAEzD;;WAEG;QAEI,iBAAY,GAAG,CAAC,CAAC;QAExB;;WAEG;QAEI,uBAAkB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE3C;;;WAGG;QAEI,oBAAe,GAAG,IAAI,CAAC;QAE9B;;WAEG;QAEI,2BAAsB,GAAG,IAAI,CAAC;QAErC,gBAAgB;QACT,gBAAW,GAAG,IAAI,MAAM,EAAE,CAAC;QAclC;;WAEG;QACI,gBAAW,GAAY,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzC,0BAAqB,GAAY,IAAI,OAAO,EAAE,CAAC;QAEzD;;WAEG;QACI,eAAU,GAAY,KAAK,CAAC;QAmGnC;;WAEG;QACI,kCAA6B,GAAG,IAAI,UAAU,EAA0B,CAAC;QAOhF;;;WAGG;QACI,oBAAe,GAAG,KAAK,CAAC;QAE/B;;;;WAIG;QACI,oBAAe,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAG1C,sBAAiB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACnC,uBAAkB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACpC,iBAAY,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAShC,uBAAkB,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QA0d3C,+BAA0B,GAAG,CAAC,WAAmB,EAAE,WAAoB,EAAE,eAAuC,IAAI,EAAE,EAAE;YAC9H,IAAI,CAAC,YAAY,EAAE;gBACf,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aACnD;iBAAM;gBACH,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;gBAE9B,IAAI,IAAI,CAAC,SAAS,EAAE;oBAChB,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;iBAChC;aACJ;YAED,mCAAmC;YACnC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjC,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE/B,IAAI,IAAI,KAAK,CAAC,EAAE;gBACZ,IAAI,GAAG,MAAM,CAAC;aACjB;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACzC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,IAAI,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;YACjH,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAC5D,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAE3C,IAAI,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;YACvB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE;gBACvC,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;gBAChB,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;aACpB;YAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;YACpD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;YAC3D,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;YAE3D,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACrC,CAAC,CAAC;QAhfE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC9B,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;SAC1B;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,IAAI,4BAA4B,CAAC,IAAI,CAAC,CAAC;QACrD,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,aAAa,EAAE,CAAC,WAAW,EAAE,CAAC;IAC5D,CAAC;IAED,QAAQ;IACR,gBAAgB;IACT,UAAU;QACb,KAAK,CAAC,UAAU,EAAE,CAAC;QACnB,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QACxF,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,SAAS,CAAC;QAC7B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC;QAC/B,IAAI,CAAC,MAAM,CAAC,kBAAkB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;IACpD,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,iBAA2B;QAC3C,IAAI,CAAC,iBAAiB,EAAE;YACpB,KAAK,CAAC,YAAY,EAAE,CAAC;SACxB;QAED,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;QACxD,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAC/B,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAC7B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IACrE,CAAC;IAES,kBAAkB;QACxB,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE;YAC1D,MAAM,GAAG,GAAY,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC;YAC5D,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;aAC1D;iBAAM;gBACH,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;aAC9B;SACJ;QAED,MAAM,oBAAoB,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAE7D,IAAI,oBAAoB,EAAE;YACtB,OAAO,oBAAoB,CAAC;SAC/B;QAED,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAQD;;;OAGG;IACI,UAAU;QACb,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC,KAAK,EAAE,CAAC;QACvD,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAEjE,OAAO,KAAK,CAAC,UAAU,EAAE,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,mBAAmB;QACtB,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,EAAE;YAC9B,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC;QACjC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QAEjE,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAE1B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,eAAe;IACf,gBAAgB;IACT,yBAAyB;QAC5B,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE;YACpC,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,CACH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACrD,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK;YAChC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;YAC9B,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM;YAClC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CACjE,CAAC;IACN,CAAC;IAiCD;;;;;;OAMG;IACI,aAAa,CAAC,OAAY,EAAE,gBAA0B,EAAE,oBAAsC,IAAI,EAAE,qBAA6B,CAAC;QACrI,8CAA8C;QAC9C,MAAM,IAAI,GAAG,SAAS,CAAC;QAEvB,gBAAgB,GAAG,KAAK,CAAC,gCAAgC,CAAC,IAAI,CAAC,CAAC;QAChE,IAAI,CAAC,kBAAkB,GAAG,iBAA4B,CAAC;QACvD,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;QAC9C,0BAA0B;QAC1B,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;YAC9B,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;aACrC;YACD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;aACtC;SACJ;QAED,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;QAE5C,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE;YACf,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;YAC7B,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;YAC5B,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;YAC9B,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;YAC1B,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAC9B,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;QAE5B,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,EAAE,CAAC;SACjB;IACL,CAAC;IAED,gBAAgB;IACT,YAAY;QACf,8HAA8H;QAC9H,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,OAAO;SACV;QAED,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAC1B,UAAU;QACV,IAAI,IAAI,CAAC,mBAAmB,KAAK,CAAC,IAAI,IAAI,CAAC,kBAAkB,KAAK,CAAC,IAAI,IAAI,CAAC,oBAAoB,KAAK,CAAC,EAAE;YACpG,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,IAAI,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;YACnD,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE;gBAChB,mBAAmB,IAAI,CAAC,CAAC,CAAC;aAC7B;YACD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,EAAE;gBACtC,mBAAmB,IAAI,CAAC,CAAC,CAAC;aAC7B;YACD,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,0BAA0B,EAAE,GAAG,CAAC,EAAE;gBAC7D,mBAAmB,IAAI,CAAC,CAAC,CAAC;aAC7B;YACD,IAAI,CAAC,KAAK,IAAI,mBAAmB,GAAG,iBAAiB,CAAC;YAEtD,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;YAEzD,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,oBAAoB,CAAC;YACzC,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,OAAO,CAAC;YACzC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,OAAO,CAAC;YACxC,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,OAAO,CAAC;YAC1C,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,OAAO,EAAE;gBAC9C,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;aAChC;YACD,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,OAAO,EAAE;gBAC7C,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;aAC/B;YACD,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,EAAE;gBAC5D,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;aACjC;SACJ;QAED,kBAAkB;QAClB,IAAI,IAAI,CAAC,gBAAgB,KAAK,CAAC,IAAI,IAAI,CAAC,gBAAgB,KAAK,CAAC,EAAE;YAC5D,MAAM,cAAc,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAExG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC1D,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACjD,OAAO,CAAC,oBAAoB,CAAC,cAAc,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACtG,uCAAuC;YACvC,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE;gBACxC,IAAI,CAAC,qBAAqB,CAAC,CAAC,GAAG,CAAC,CAAC;aACpC;YAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACnB,IAAI,IAAI,CAAC,oBAAoB,EAAE;oBAC3B,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACpD,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;oBACtG,IAAI,eAAe,IAAI,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,EAAE;wBAC1E,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;qBACrD;iBACJ;qBAAM;oBACH,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;iBACvD;aACJ;YAED,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,cAAc,CAAC;YAC7C,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,cAAc,CAAC;YAE7C,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,EAAE;gBACxD,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;aAC7B;YACD,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,EAAE;gBACxD,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;aAC7B;SACJ;QAED,SAAS;QACT,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,KAAK,CAAC,YAAY,EAAE,CAAC;IACzB,CAAC;IAES,YAAY;QAClB,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE;YACnE,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,EAAE;gBAC7C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;aACvC;SACJ;aAAM;YACH,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE;gBACjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC;aACnC;SACJ;QAED,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE;YACnE,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE;gBAC9C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;aACvC;SACJ;aAAM;YACH,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE;gBACjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC;aACnC;SACJ;QAED,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE;YACpE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC;SACrC;QACD,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE;YACpE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC;SACrC;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE;YACvE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC;YACpC,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;SACjC;QACD,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE;YACvE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC;YACpC,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;SACjC;IACL,CAAC;IAED;;OAEG;IACI,sBAAsB;QACzB,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAEjF,4DAA4D;QAC5D,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,EAAE;YAC9E,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAC1G;QAED,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;QAE/C,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACnB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,iCAAiC;SAC1D;QAED,QAAQ;QACR,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC;QACjC,IAAI,IAAI,CAAC,kBAAkB,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,CAAC,KAAK,CAAC,EAAE;YACpE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,wEAAwE;SACrG;aAAM;YACH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;SAClJ;QAED,IAAI,IAAI,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,EAAE;YAC/B,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;SACzC;QAED,4EAA4E;QAC5E,MAAM,oBAAoB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QACxF,yFAAyF;QACzF,IAAI,CAAC,KAAK,IAAI,oBAAoB,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;QAEnD,OAAO;QACP,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QAE/D,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,QAAiB;QAChC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;YACjC,OAAO;SACV;QACD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAElC,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAClC,CAAC;IAED;;;;;;;;OAQG;IACI,SAAS,CAAC,MAA8B,EAAE,gBAAgB,GAAG,KAAK,EAAE,iBAAiB,GAAG,KAAK,EAAE,oBAAoB,GAAG,KAAK;;QAC9H,oBAAoB,GAAG,MAAA,IAAI,CAAC,4BAA4B,mCAAI,oBAAoB,CAAC;QAEjF,IAAU,MAAO,CAAC,eAAe,EAAE;YAC/B,IAAI,gBAAgB,EAAE;gBAClB,IAAI,CAAC,qBAAqB,GAAS,MAAO,CAAC,eAAe,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;aAChG;iBAAM;gBACH,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;aACrC;YACc,MAAO,CAAC,kBAAkB,EAAE,CAAC;YAC5C,IAAI,CAAC,WAAW,GAAiB,MAAM,CAAC;YACxC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAEzC,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SACxE;aAAM;YACH,MAAM,SAAS,GAAY,MAAM,CAAC;YAClC,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAChD,IAAI,aAAa,IAAI,CAAC,iBAAiB,IAAI,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;gBACxE,OAAO;aACV;YACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;YACzB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;YAClC,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SAC5D;QAED,IAAI,CAAC,oBAAoB,EAAE;YACvB,IAAI,CAAC,sBAAsB,EAAE,CAAC;SACjC;IACL,CAAC;IAED,gBAAgB;IACT,cAAc;QACjB,UAAU;QACV,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE/B,IAAI,IAAI,KAAK,CAAC,EAAE;YACZ,IAAI,GAAG,MAAM,CAAC;SACjB;QAED,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACnB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,iCAAiC;SAC1D;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACzC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,IAAI,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;QAEjH,gCAAgC;QAChC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,EAAE;YAC9E,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAC1G;QAED,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5D,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,iBAAiB,IAAI,IAAI,CAAC,eAAe,EAAE;YAC3D,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,CAAC;YACzD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACjB,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,cAAc,EAAE,CAAC;aACjD;YACD,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC;YAC9C,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACzE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAChC,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,0BAA0B,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SAChJ;aAAM;YACH,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAE3C,IAAI,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;YACvB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,GAAG,CAAC,EAAE;gBAClC,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;aACpB;YAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;YAEpD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;YAC3D,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;SAC9D;QACD,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC;QAC7B,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAyCD;;;;OAIG;IACI,MAAM,CAAC,MAAuB,EAAE,eAAe,GAAG,KAAK;QAC1D,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC;QAE1C,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACzC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC;QAEtE,IAAI,CAAC,MAAM,GAAG,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC;QAE3C,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG,EAAE,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,CAAC,GAAG,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,eAAe,CAAC,CAAC;IACxG,CAAC;IAED;;;;;OAKG;IACI,OAAO,CAAC,+BAAkG,EAAE,eAAe,GAAG,KAAK;QACtI,IAAI,oBAAoD,CAAC;QACzD,IAAI,QAAgB,CAAC;QAErB,IAAU,+BAAgC,CAAC,GAAG,KAAK,SAAS,EAAE;YAC1D,SAAS;YACT,MAAM,MAAM,GAAmB,+BAA+B,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC;YACzF,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC3C,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC,GAAG,EAAE,oBAAoB,CAAC,GAAG,CAAC,CAAC;SACnF;aAAM;YACH,2BAA2B;YAC3B,MAAM,uBAAuB,GAAQ,+BAA+B,CAAC;YACrE,oBAAoB,GAAG,uBAAuB,CAAC;YAC/C,QAAQ,GAAG,uBAAuB,CAAC,QAAQ,CAAC;SAC/C;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QAEjD,IAAI,CAAC,eAAe,EAAE;YAClB,IAAI,CAAC,IAAI,GAAG,QAAQ,GAAG,CAAC,CAAC;SAC5B;IACL,CAAC;IAED;;;OAGG;IACI,eAAe,CAAC,IAAY,EAAE,WAAmB;QACpD,IAAI,UAAU,GAAW,CAAC,CAAC;QAC3B,QAAQ,IAAI,CAAC,aAAa,EAAE;YACxB,KAAK,MAAM,CAAC,8BAA8B,CAAC;YAC3C,KAAK,MAAM,CAAC,yCAAyC,CAAC;YACtD,KAAK,MAAM,CAAC,+BAA+B,CAAC;YAC5C,KAAK,MAAM,CAAC,gCAAgC,CAAC;YAC7C,KAAK,MAAM,CAAC,WAAW;gBACnB,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,GAAG,CAAC,WAAW,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClF,MAAM;YACV,KAAK,MAAM,CAAC,0CAA0C;gBAClD,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,GAAG,CAAC,WAAW,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClF,MAAM;SACb;QACD,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,GAAG,UAAU,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QACzH,MAAM,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC7B,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC;QAC1B,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;QACxB,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAEhC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACxB,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAClC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACpC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACtC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAEhC,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACI,iBAAiB;QACpB,MAAM,OAAO,GAAoB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACrD,MAAM,QAAQ,GAAoB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAEtD,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAEzC,QAAQ,IAAI,CAAC,aAAa,EAAE;YACxB,KAAK,MAAM,CAAC,8BAA8B,CAAC;YAC3C,KAAK,MAAM,CAAC,yCAAyC,CAAC;YACtD,KAAK,MAAM,CAAC,+BAA+B,CAAC;YAC5C,KAAK,MAAM,CAAC,gCAAgC,CAAC;YAC7C,KAAK,MAAM,CAAC,WAAW;gBACnB,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;gBACnE,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;gBACpE,MAAM;YACV,KAAK,MAAM,CAAC,0CAA0C;gBAClD,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;gBACnE,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;gBACpE,MAAM;SACb;QACD,KAAK,CAAC,iBAAiB,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QACpB,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,iBAAiB,CAAC;IAC7B,CAAC;CACJ;AAvwCG;IADC,SAAS,EAAE;8CACS;AAMrB;IADC,SAAS,EAAE;6CACQ;AAMpB;IADC,SAAS,EAAE;+CACU;AAOtB;IADC,SAAS,EAAE;qEAC2C;AAGvD;IADC,kBAAkB,CAAC,QAAQ,CAAC;gDACF;AAE3B;IADC,wBAAwB,CAAC,YAAY,CAAC;oDACO;AAuF9C;IADC,SAAS,EAAE;4DACmB;AAO/B;IADC,SAAS,EAAE;2DACkB;AAO9B;IADC,SAAS,EAAE;6DACoB;AAOhC;IADC,SAAS,EAAE;wDACoC;AAOhD;IADC,SAAS,EAAE;wDACoC;AAOhD;IADC,SAAS,EAAE;uDACmC;AAO/C;IADC,SAAS,EAAE;uDAC6C;AAOzD;IADC,SAAS,EAAE;yDACqC;AAOjD;IADC,SAAS,EAAE;yDACqC;AAMjD;IADC,SAAS,EAAE;yDACwB;AAMpC;IADC,SAAS,EAAE;yDACwB;AAQpC;IADC,SAAS,EAAE;8DAC8B;AAO1C;IADC,SAAS,EAAE;6DACyC;AAMrD;IADC,kBAAkB,EAAE;4DACgC;AAOrD;IADC,SAAS,EAAE;uDACgB;AAgO5B;IADC,SAAS,EAAE;0DAQX;AAoCD;IADC,SAAS,EAAE;qDACY;AAMxB;IADC,kBAAkB,EAAE;2DACsB;AAO3C;IADC,SAAS,EAAE;wDACkB;AAM9B;IADC,SAAS,EAAE;+DACyB", "sourcesContent": ["import { serialize, serializeAsVector3, serializeAsMeshReference, serializeAsVector2 } from \"../Misc/decorators\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Matrix, Vector3, Vector2 } from \"../Maths/math.vector\";\r\nimport { Node } from \"../node\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport { AutoRotationBehavior } from \"../Behaviors/Cameras/autoRotationBehavior\";\r\nimport { BouncingBehavior } from \"../Behaviors/Cameras/bouncingBehavior\";\r\nimport { FramingBehavior } from \"../Behaviors/Cameras/framingBehavior\";\r\nimport { Camera } from \"./camera\";\r\nimport { TargetCamera } from \"./targetCamera\";\r\nimport type { ArcRotateCameraPointersInput } from \"../Cameras/Inputs/arcRotateCameraPointersInput\";\r\nimport type { ArcRotateCameraKeyboardMoveInput } from \"../Cameras/Inputs/arcRotateCameraKeyboardMoveInput\";\r\nimport type { ArcRotateCameraMouseWheelInput } from \"../Cameras/Inputs/arcRotateCameraMouseWheelInput\";\r\nimport { ArcRotateCameraInputsManager } from \"../Cameras/arcRotateCameraInputsManager\";\r\nimport { Epsilon } from \"../Maths/math.constants\";\r\nimport { Tools } from \"../Misc/tools\";\r\n\r\ndeclare type Collider = import(\"../Collisions/collider\").Collider;\r\n\r\nNode.AddNodeConstructor(\"ArcRotateCamera\", (name, scene) => {\r\n    return () => new ArcRotateCamera(name, 0, 0, 1.0, Vector3.Zero(), scene);\r\n});\r\n\r\n/**\r\n * This represents an orbital type of camera.\r\n *\r\n * This camera always points towards a given target position and can be rotated around that target with the target as the centre of rotation. It can be controlled with cursors and mouse, or with touch events.\r\n * Think of this camera as one orbiting its target position, or more imaginatively as a spy satellite orbiting the earth. Its position relative to the target (earth) can be set by three parameters, alpha (radians) the longitudinal rotation, beta (radians) the latitudinal rotation and radius the distance from the target position.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#arc-rotate-camera\r\n */\r\nexport class ArcRotateCamera extends TargetCamera {\r\n    /**\r\n     * Defines the rotation angle of the camera along the longitudinal axis.\r\n     */\r\n    @serialize()\r\n    public alpha: number;\r\n\r\n    /**\r\n     * Defines the rotation angle of the camera along the latitudinal axis.\r\n     */\r\n    @serialize()\r\n    public beta: number;\r\n\r\n    /**\r\n     * Defines the radius of the camera from it s target point.\r\n     */\r\n    @serialize()\r\n    public radius: number;\r\n\r\n    /**\r\n     * Defines an override value to use as the parameter to setTarget.\r\n     * This allows the parameter to be specified when animating the target (e.g. using FramingBehavior).\r\n     */\r\n    @serialize()\r\n    public overrideCloneAlphaBetaRadius: Nullable<boolean>;\r\n\r\n    @serializeAsVector3(\"target\")\r\n    protected _target: Vector3;\r\n    @serializeAsMeshReference(\"targetHost\")\r\n    protected _targetHost: Nullable<AbstractMesh>;\r\n\r\n    /**\r\n     * Defines the target point of the camera.\r\n     * The camera looks towards it from the radius distance.\r\n     */\r\n    public get target(): Vector3 {\r\n        return this._target;\r\n    }\r\n    public set target(value: Vector3) {\r\n        this.setTarget(value);\r\n    }\r\n\r\n    /**\r\n     * Defines the target mesh of the camera.\r\n     * The camera looks towards it from the radius distance.\r\n     * Please note that setting a target host will disable panning.\r\n     */\r\n    public get targetHost(): Nullable<AbstractMesh> {\r\n        return this._targetHost;\r\n    }\r\n    public set targetHost(value: Nullable<AbstractMesh>) {\r\n        if (value) {\r\n            this.setTarget(value);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Return the current target position of the camera. This value is expressed in local space.\r\n     * @returns the target position\r\n     */\r\n    public getTarget(): Vector3 {\r\n        return this.target;\r\n    }\r\n\r\n    /**\r\n     * Define the current local position of the camera in the scene\r\n     */\r\n    public get position(): Vector3 {\r\n        return this._position;\r\n    }\r\n\r\n    public set position(newPosition: Vector3) {\r\n        this.setPosition(newPosition);\r\n    }\r\n\r\n    protected _upToYMatrix: Matrix;\r\n    protected _yToUpMatrix: Matrix;\r\n\r\n    /**\r\n     * The vector the camera should consider as up. (default is Vector3(0, 1, 0) as returned by Vector3.Up())\r\n     * Setting this will copy the given vector to the camera's upVector, and set rotation matrices to and from Y up.\r\n     * DO NOT set the up vector using copyFrom or copyFromFloats, as this bypasses setting the above matrices.\r\n     */\r\n    set upVector(vec: Vector3) {\r\n        if (!this._upToYMatrix) {\r\n            this._yToUpMatrix = new Matrix();\r\n            this._upToYMatrix = new Matrix();\r\n\r\n            this._upVector = Vector3.Zero();\r\n        }\r\n\r\n        vec.normalize();\r\n        this._upVector.copyFrom(vec);\r\n        this.setMatUp();\r\n    }\r\n\r\n    get upVector() {\r\n        return this._upVector;\r\n    }\r\n\r\n    /**\r\n     * Sets the Y-up to camera up-vector rotation matrix, and the up-vector to Y-up rotation matrix.\r\n     */\r\n    public setMatUp() {\r\n        // from y-up to custom-up (used in _getViewMatrix)\r\n        Matrix.RotationAlignToRef(Vector3.UpReadOnly, this._upVector, this._yToUpMatrix);\r\n\r\n        // from custom-up to y-up (used in rebuildAnglesAndRadius)\r\n        Matrix.RotationAlignToRef(this._upVector, Vector3.UpReadOnly, this._upToYMatrix);\r\n    }\r\n\r\n    /**\r\n     * Current inertia value on the longitudinal axis.\r\n     * The bigger this number the longer it will take for the camera to stop.\r\n     */\r\n    @serialize()\r\n    public inertialAlphaOffset = 0;\r\n\r\n    /**\r\n     * Current inertia value on the latitudinal axis.\r\n     * The bigger this number the longer it will take for the camera to stop.\r\n     */\r\n    @serialize()\r\n    public inertialBetaOffset = 0;\r\n\r\n    /**\r\n     * Current inertia value on the radius axis.\r\n     * The bigger this number the longer it will take for the camera to stop.\r\n     */\r\n    @serialize()\r\n    public inertialRadiusOffset = 0;\r\n\r\n    /**\r\n     * Minimum allowed angle on the longitudinal axis.\r\n     * This can help limiting how the Camera is able to move in the scene.\r\n     */\r\n    @serialize()\r\n    public lowerAlphaLimit: Nullable<number> = null;\r\n\r\n    /**\r\n     * Maximum allowed angle on the longitudinal axis.\r\n     * This can help limiting how the Camera is able to move in the scene.\r\n     */\r\n    @serialize()\r\n    public upperAlphaLimit: Nullable<number> = null;\r\n\r\n    /**\r\n     * Minimum allowed angle on the latitudinal axis.\r\n     * This can help limiting how the Camera is able to move in the scene.\r\n     */\r\n    @serialize()\r\n    public lowerBetaLimit: Nullable<number> = 0.01;\r\n\r\n    /**\r\n     * Maximum allowed angle on the latitudinal axis.\r\n     * This can help limiting how the Camera is able to move in the scene.\r\n     */\r\n    @serialize()\r\n    public upperBetaLimit: Nullable<number> = Math.PI - 0.01;\r\n\r\n    /**\r\n     * Minimum allowed distance of the camera to the target (The camera can not get closer).\r\n     * This can help limiting how the Camera is able to move in the scene.\r\n     */\r\n    @serialize()\r\n    public lowerRadiusLimit: Nullable<number> = null;\r\n\r\n    /**\r\n     * Maximum allowed distance of the camera to the target (The camera can not get further).\r\n     * This can help limiting how the Camera is able to move in the scene.\r\n     */\r\n    @serialize()\r\n    public upperRadiusLimit: Nullable<number> = null;\r\n\r\n    /**\r\n     * Defines the current inertia value used during panning of the camera along the X axis.\r\n     */\r\n    @serialize()\r\n    public inertialPanningX: number = 0;\r\n\r\n    /**\r\n     * Defines the current inertia value used during panning of the camera along the Y axis.\r\n     */\r\n    @serialize()\r\n    public inertialPanningY: number = 0;\r\n\r\n    /**\r\n     * Defines the distance used to consider the camera in pan mode vs pinch/zoom.\r\n     * Basically if your fingers moves away from more than this distance you will be considered\r\n     * in pinch mode.\r\n     */\r\n    @serialize()\r\n    public pinchToPanMaxDistance: number = 20;\r\n\r\n    /**\r\n     * Defines the maximum distance the camera can pan.\r\n     * This could help keeping the camera always in your scene.\r\n     */\r\n    @serialize()\r\n    public panningDistanceLimit: Nullable<number> = null;\r\n\r\n    /**\r\n     * Defines the target of the camera before panning.\r\n     */\r\n    @serializeAsVector3()\r\n    public panningOriginTarget: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * Defines the value of the inertia used during panning.\r\n     * 0 would mean stop inertia and one would mean no deceleration at all.\r\n     */\r\n    @serialize()\r\n    public panningInertia = 0.9;\r\n\r\n    //-- begin properties for backward compatibility for inputs\r\n\r\n    /**\r\n     * Gets or Set the pointer angular sensibility  along the X axis or how fast is the camera rotating.\r\n     */\r\n    public get angularSensibilityX(): number {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            return pointers.angularSensibilityX;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    public set angularSensibilityX(value: number) {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            pointers.angularSensibilityX = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the pointer angular sensibility along the Y axis or how fast is the camera rotating.\r\n     */\r\n    public get angularSensibilityY(): number {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            return pointers.angularSensibilityY;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    public set angularSensibilityY(value: number) {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            pointers.angularSensibilityY = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the pointer pinch precision or how fast is the camera zooming.\r\n     */\r\n    public get pinchPrecision(): number {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            return pointers.pinchPrecision;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    public set pinchPrecision(value: number) {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            pointers.pinchPrecision = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the pointer pinch delta percentage or how fast is the camera zooming.\r\n     * It will be used instead of pinchDeltaPrecision if different from 0.\r\n     * It defines the percentage of current camera.radius to use as delta when pinch zoom is used.\r\n     */\r\n    public get pinchDeltaPercentage(): number {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            return pointers.pinchDeltaPercentage;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    public set pinchDeltaPercentage(value: number) {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            pointers.pinchDeltaPercentage = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the pointer use natural pinch zoom to override the pinch precision\r\n     * and pinch delta percentage.\r\n     * When useNaturalPinchZoom is true, multi touch zoom will zoom in such\r\n     * that any object in the plane at the camera's target point will scale\r\n     * perfectly with finger motion.\r\n     */\r\n    public get useNaturalPinchZoom(): boolean {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            return pointers.useNaturalPinchZoom;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public set useNaturalPinchZoom(value: boolean) {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            pointers.useNaturalPinchZoom = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the pointer panning sensibility or how fast is the camera moving.\r\n     */\r\n    public get panningSensibility(): number {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            return pointers.panningSensibility;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    public set panningSensibility(value: number) {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            pointers.panningSensibility = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control beta angle in a positive direction.\r\n     */\r\n    public get keysUp(): number[] {\r\n        const keyboard = <ArcRotateCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysUp;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    public set keysUp(value: number[]) {\r\n        const keyboard = <ArcRotateCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysUp = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control beta angle in a negative direction.\r\n     */\r\n    public get keysDown(): number[] {\r\n        const keyboard = <ArcRotateCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysDown;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    public set keysDown(value: number[]) {\r\n        const keyboard = <ArcRotateCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysDown = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control alpha angle in a negative direction.\r\n     */\r\n    public get keysLeft(): number[] {\r\n        const keyboard = <ArcRotateCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysLeft;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    public set keysLeft(value: number[]) {\r\n        const keyboard = <ArcRotateCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysLeft = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control alpha angle in a positive direction.\r\n     */\r\n    public get keysRight(): number[] {\r\n        const keyboard = <ArcRotateCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysRight;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    public set keysRight(value: number[]) {\r\n        const keyboard = <ArcRotateCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysRight = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the mouse wheel precision or how fast is the camera zooming.\r\n     */\r\n    public get wheelPrecision(): number {\r\n        const mousewheel = <ArcRotateCameraMouseWheelInput>this.inputs.attached[\"mousewheel\"];\r\n        if (mousewheel) {\r\n            return mousewheel.wheelPrecision;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    public set wheelPrecision(value: number) {\r\n        const mousewheel = <ArcRotateCameraMouseWheelInput>this.inputs.attached[\"mousewheel\"];\r\n        if (mousewheel) {\r\n            mousewheel.wheelPrecision = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the boolean value that controls whether or not the mouse wheel\r\n     * zooms to the location of the mouse pointer or not.  The default is false.\r\n     */\r\n    @serialize()\r\n    public get zoomToMouseLocation(): boolean {\r\n        const mousewheel = <ArcRotateCameraMouseWheelInput>this.inputs.attached[\"mousewheel\"];\r\n        if (mousewheel) {\r\n            return mousewheel.zoomToMouseLocation;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public set zoomToMouseLocation(value: boolean) {\r\n        const mousewheel = <ArcRotateCameraMouseWheelInput>this.inputs.attached[\"mousewheel\"];\r\n        if (mousewheel) {\r\n            mousewheel.zoomToMouseLocation = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the mouse wheel delta percentage or how fast is the camera zooming.\r\n     * It will be used instead of pinchDeltaPrecision if different from 0.\r\n     * It defines the percentage of current camera.radius to use as delta when pinch zoom is used.\r\n     */\r\n    public get wheelDeltaPercentage(): number {\r\n        const mousewheel = <ArcRotateCameraMouseWheelInput>this.inputs.attached[\"mousewheel\"];\r\n        if (mousewheel) {\r\n            return mousewheel.wheelDeltaPercentage;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    public set wheelDeltaPercentage(value: number) {\r\n        const mousewheel = <ArcRotateCameraMouseWheelInput>this.inputs.attached[\"mousewheel\"];\r\n        if (mousewheel) {\r\n            mousewheel.wheelDeltaPercentage = value;\r\n        }\r\n    }\r\n\r\n    //-- end properties for backward compatibility for inputs\r\n\r\n    /**\r\n     * Defines how much the radius should be scaled while zooming on a particular mesh (through the zoomOn function)\r\n     */\r\n    @serialize()\r\n    public zoomOnFactor = 1;\r\n\r\n    /**\r\n     * Defines a screen offset for the camera position.\r\n     */\r\n    @serializeAsVector2()\r\n    public targetScreenOffset = Vector2.Zero();\r\n\r\n    /**\r\n     * Allows the camera to be completely reversed.\r\n     * If false the camera can not arrive upside down.\r\n     */\r\n    @serialize()\r\n    public allowUpsideDown = true;\r\n\r\n    /**\r\n     * Define if double tap/click is used to restore the previously saved state of the camera.\r\n     */\r\n    @serialize()\r\n    public useInputToRestoreState = true;\r\n\r\n    /** @internal */\r\n    public _viewMatrix = new Matrix();\r\n    /** @internal */\r\n    public _useCtrlForPanning: boolean;\r\n    /** @internal */\r\n    public _panningMouseButton: number;\r\n\r\n    /**\r\n     * Defines the input associated to the camera.\r\n     */\r\n    public inputs: ArcRotateCameraInputsManager;\r\n\r\n    /** @internal */\r\n    public _reset: () => void;\r\n\r\n    /**\r\n     * Defines the allowed panning axis.\r\n     */\r\n    public panningAxis: Vector3 = new Vector3(1, 1, 0);\r\n    protected _transformedDirection: Vector3 = new Vector3();\r\n\r\n    /**\r\n     * Defines if camera will eliminate transform on y axis.\r\n     */\r\n    public mapPanning: boolean = false;\r\n\r\n    // Behaviors\r\n    private _bouncingBehavior: Nullable<BouncingBehavior>;\r\n\r\n    /**\r\n     * Gets the bouncing behavior of the camera if it has been enabled.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors/cameraBehaviors#bouncing-behavior\r\n     */\r\n    public get bouncingBehavior(): Nullable<BouncingBehavior> {\r\n        return this._bouncingBehavior;\r\n    }\r\n\r\n    /**\r\n     * Defines if the bouncing behavior of the camera is enabled on the camera.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors/cameraBehaviors#bouncing-behavior\r\n     */\r\n    public get useBouncingBehavior(): boolean {\r\n        return this._bouncingBehavior != null;\r\n    }\r\n\r\n    public set useBouncingBehavior(value: boolean) {\r\n        if (value === this.useBouncingBehavior) {\r\n            return;\r\n        }\r\n\r\n        if (value) {\r\n            this._bouncingBehavior = new BouncingBehavior();\r\n            this.addBehavior(this._bouncingBehavior);\r\n        } else if (this._bouncingBehavior) {\r\n            this.removeBehavior(this._bouncingBehavior);\r\n            this._bouncingBehavior = null;\r\n        }\r\n    }\r\n\r\n    private _framingBehavior: Nullable<FramingBehavior>;\r\n\r\n    /**\r\n     * Gets the framing behavior of the camera if it has been enabled.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors/cameraBehaviors#framing-behavior\r\n     */\r\n    public get framingBehavior(): Nullable<FramingBehavior> {\r\n        return this._framingBehavior;\r\n    }\r\n\r\n    /**\r\n     * Defines if the framing behavior of the camera is enabled on the camera.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors/cameraBehaviors#framing-behavior\r\n     */\r\n    public get useFramingBehavior(): boolean {\r\n        return this._framingBehavior != null;\r\n    }\r\n\r\n    public set useFramingBehavior(value: boolean) {\r\n        if (value === this.useFramingBehavior) {\r\n            return;\r\n        }\r\n\r\n        if (value) {\r\n            this._framingBehavior = new FramingBehavior();\r\n            this.addBehavior(this._framingBehavior);\r\n        } else if (this._framingBehavior) {\r\n            this.removeBehavior(this._framingBehavior);\r\n            this._framingBehavior = null;\r\n        }\r\n    }\r\n\r\n    private _autoRotationBehavior: Nullable<AutoRotationBehavior>;\r\n\r\n    /**\r\n     * Gets the auto rotation behavior of the camera if it has been enabled.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors/cameraBehaviors#autorotation-behavior\r\n     */\r\n    public get autoRotationBehavior(): Nullable<AutoRotationBehavior> {\r\n        return this._autoRotationBehavior;\r\n    }\r\n\r\n    /**\r\n     * Defines if the auto rotation behavior of the camera is enabled on the camera.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors/cameraBehaviors#autorotation-behavior\r\n     */\r\n    public get useAutoRotationBehavior(): boolean {\r\n        return this._autoRotationBehavior != null;\r\n    }\r\n\r\n    public set useAutoRotationBehavior(value: boolean) {\r\n        if (value === this.useAutoRotationBehavior) {\r\n            return;\r\n        }\r\n\r\n        if (value) {\r\n            this._autoRotationBehavior = new AutoRotationBehavior();\r\n            this.addBehavior(this._autoRotationBehavior);\r\n        } else if (this._autoRotationBehavior) {\r\n            this.removeBehavior(this._autoRotationBehavior);\r\n            this._autoRotationBehavior = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Observable triggered when the mesh target has been changed on the camera.\r\n     */\r\n    public onMeshTargetChangedObservable = new Observable<Nullable<AbstractMesh>>();\r\n\r\n    /**\r\n     * Event raised when the camera is colliding with a mesh.\r\n     */\r\n    public onCollide: (collidedMesh: AbstractMesh) => void;\r\n\r\n    /**\r\n     * Defines whether the camera should check collision with the objects oh the scene.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_collisions#how-can-i-do-this-\r\n     */\r\n    public checkCollisions = false;\r\n\r\n    /**\r\n     * Defines the collision radius of the camera.\r\n     * This simulates a sphere around the camera.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_collisions#arcrotatecamera\r\n     */\r\n    public collisionRadius = new Vector3(0.5, 0.5, 0.5);\r\n\r\n    protected _collider: Collider;\r\n    protected _previousPosition = Vector3.Zero();\r\n    protected _collisionVelocity = Vector3.Zero();\r\n    protected _newPosition = Vector3.Zero();\r\n    protected _previousAlpha: number;\r\n    protected _previousBeta: number;\r\n    protected _previousRadius: number;\r\n    //due to async collision inspection\r\n    protected _collisionTriggered: boolean;\r\n\r\n    protected _targetBoundingCenter: Nullable<Vector3>;\r\n\r\n    private _computationVector: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * Instantiates a new ArcRotateCamera in a given scene\r\n     * @param name Defines the name of the camera\r\n     * @param alpha Defines the camera rotation along the longitudinal axis\r\n     * @param beta Defines the camera rotation along the latitudinal axis\r\n     * @param radius Defines the camera distance from its target\r\n     * @param target Defines the camera target\r\n     * @param scene Defines the scene the camera belongs to\r\n     * @param setActiveOnSceneIfNoneActive Defines whether the camera should be marked as active if not other active cameras have been defined\r\n     */\r\n    constructor(name: string, alpha: number, beta: number, radius: number, target: Vector3, scene?: Scene, setActiveOnSceneIfNoneActive = true) {\r\n        super(name, Vector3.Zero(), scene, setActiveOnSceneIfNoneActive);\r\n\r\n        this._target = Vector3.Zero();\r\n        if (target) {\r\n            this.setTarget(target);\r\n        }\r\n\r\n        this.alpha = alpha;\r\n        this.beta = beta;\r\n        this.radius = radius;\r\n\r\n        this.getViewMatrix();\r\n        this.inputs = new ArcRotateCameraInputsManager(this);\r\n        this.inputs.addKeyboard().addMouseWheel().addPointers();\r\n    }\r\n\r\n    // Cache\r\n    /** @internal */\r\n    public _initCache(): void {\r\n        super._initCache();\r\n        this._cache._target = new Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n        this._cache.alpha = undefined;\r\n        this._cache.beta = undefined;\r\n        this._cache.radius = undefined;\r\n        this._cache.targetScreenOffset = Vector2.Zero();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _updateCache(ignoreParentClass?: boolean): void {\r\n        if (!ignoreParentClass) {\r\n            super._updateCache();\r\n        }\r\n\r\n        this._cache._target.copyFrom(this._getTargetPosition());\r\n        this._cache.alpha = this.alpha;\r\n        this._cache.beta = this.beta;\r\n        this._cache.radius = this.radius;\r\n        this._cache.targetScreenOffset.copyFrom(this.targetScreenOffset);\r\n    }\r\n\r\n    protected _getTargetPosition(): Vector3 {\r\n        if (this._targetHost && this._targetHost.getAbsolutePosition) {\r\n            const pos: Vector3 = this._targetHost.getAbsolutePosition();\r\n            if (this._targetBoundingCenter) {\r\n                pos.addToRef(this._targetBoundingCenter, this._target);\r\n            } else {\r\n                this._target.copyFrom(pos);\r\n            }\r\n        }\r\n\r\n        const lockedTargetPosition = this._getLockedTargetPosition();\r\n\r\n        if (lockedTargetPosition) {\r\n            return lockedTargetPosition;\r\n        }\r\n\r\n        return this._target;\r\n    }\r\n\r\n    private _storedAlpha: number;\r\n    private _storedBeta: number;\r\n    private _storedRadius: number;\r\n    private _storedTarget: Vector3;\r\n    private _storedTargetScreenOffset: Vector2;\r\n\r\n    /**\r\n     * Stores the current state of the camera (alpha, beta, radius and target)\r\n     * @returns the camera itself\r\n     */\r\n    public storeState(): Camera {\r\n        this._storedAlpha = this.alpha;\r\n        this._storedBeta = this.beta;\r\n        this._storedRadius = this.radius;\r\n        this._storedTarget = this._getTargetPosition().clone();\r\n        this._storedTargetScreenOffset = this.targetScreenOffset.clone();\r\n\r\n        return super.storeState();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Restored camera state. You must call storeState() first\r\n     */\r\n    public _restoreStateValues(): boolean {\r\n        if (!super._restoreStateValues()) {\r\n            return false;\r\n        }\r\n\r\n        this.setTarget(this._storedTarget.clone());\r\n        this.alpha = this._storedAlpha;\r\n        this.beta = this._storedBeta;\r\n        this.radius = this._storedRadius;\r\n        this.targetScreenOffset = this._storedTargetScreenOffset.clone();\r\n\r\n        this.inertialAlphaOffset = 0;\r\n        this.inertialBetaOffset = 0;\r\n        this.inertialRadiusOffset = 0;\r\n        this.inertialPanningX = 0;\r\n        this.inertialPanningY = 0;\r\n\r\n        return true;\r\n    }\r\n\r\n    // Synchronized\r\n    /** @internal */\r\n    public _isSynchronizedViewMatrix(): boolean {\r\n        if (!super._isSynchronizedViewMatrix()) {\r\n            return false;\r\n        }\r\n\r\n        return (\r\n            this._cache._target.equals(this._getTargetPosition()) &&\r\n            this._cache.alpha === this.alpha &&\r\n            this._cache.beta === this.beta &&\r\n            this._cache.radius === this.radius &&\r\n            this._cache.targetScreenOffset.equals(this.targetScreenOffset)\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public attachControl(noPreventDefault?: boolean): void;\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * @param ignored defines an ignored parameter kept for backward compatibility.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public attachControl(ignored: any, noPreventDefault?: boolean): void;\r\n    /**\r\n     * Attached controls to the current camera.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     * @param useCtrlForPanning  Defines whether ctrl is used for panning within the controls\r\n     */\r\n    public attachControl(noPreventDefault: boolean, useCtrlForPanning: boolean): void;\r\n    /**\r\n     * Attached controls to the current camera.\r\n     * @param ignored defines an ignored parameter kept for backward compatibility.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     * @param useCtrlForPanning  Defines whether ctrl is used for panning within the controls\r\n     */\r\n    public attachControl(ignored: any, noPreventDefault: boolean, useCtrlForPanning: boolean): void;\r\n    /**\r\n     * Attached controls to the current camera.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     * @param useCtrlForPanning  Defines whether ctrl is used for panning within the controls\r\n     * @param panningMouseButton Defines whether panning is allowed through mouse click button\r\n     */\r\n    public attachControl(noPreventDefault: boolean, useCtrlForPanning: boolean, panningMouseButton: number): void;\r\n    /**\r\n     * Attached controls to the current camera.\r\n     * @param ignored defines an ignored parameter kept for backward compatibility.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     * @param useCtrlForPanning  Defines whether ctrl is used for panning within the controls\r\n     * @param panningMouseButton Defines whether panning is allowed through mouse click button\r\n     */\r\n    public attachControl(ignored: any, noPreventDefault?: boolean, useCtrlForPanning: boolean | number = true, panningMouseButton: number = 2): void {\r\n        // eslint-disable-next-line prefer-rest-params\r\n        const args = arguments;\r\n\r\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(args);\r\n        this._useCtrlForPanning = useCtrlForPanning as boolean;\r\n        this._panningMouseButton = panningMouseButton;\r\n        // backwards compatibility\r\n        if (typeof args[0] === \"boolean\") {\r\n            if (args.length > 1) {\r\n                this._useCtrlForPanning = args[1];\r\n            }\r\n            if (args.length > 2) {\r\n                this._panningMouseButton = args[2];\r\n            }\r\n        }\r\n\r\n        this.inputs.attachElement(noPreventDefault);\r\n\r\n        this._reset = () => {\r\n            this.inertialAlphaOffset = 0;\r\n            this.inertialBetaOffset = 0;\r\n            this.inertialRadiusOffset = 0;\r\n            this.inertialPanningX = 0;\r\n            this.inertialPanningY = 0;\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    public detachControl(): void {\r\n        this.inputs.detachElement();\r\n\r\n        if (this._reset) {\r\n            this._reset();\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _checkInputs(): void {\r\n        //if (async) collision inspection was triggered, don't update the camera's position - until the collision callback was called.\r\n        if (this._collisionTriggered) {\r\n            return;\r\n        }\r\n\r\n        this.inputs.checkInputs();\r\n        // Inertia\r\n        if (this.inertialAlphaOffset !== 0 || this.inertialBetaOffset !== 0 || this.inertialRadiusOffset !== 0) {\r\n            const directionModifier = this.invertRotation ? -1 : 1;\r\n            let inertialAlphaOffset = this.inertialAlphaOffset;\r\n            if (this.beta <= 0) {\r\n                inertialAlphaOffset *= -1;\r\n            }\r\n            if (this.getScene().useRightHandedSystem) {\r\n                inertialAlphaOffset *= -1;\r\n            }\r\n            if (this.parent && this.parent._getWorldMatrixDeterminant() < 0) {\r\n                inertialAlphaOffset *= -1;\r\n            }\r\n            this.alpha += inertialAlphaOffset * directionModifier;\r\n\r\n            this.beta += this.inertialBetaOffset * directionModifier;\r\n\r\n            this.radius -= this.inertialRadiusOffset;\r\n            this.inertialAlphaOffset *= this.inertia;\r\n            this.inertialBetaOffset *= this.inertia;\r\n            this.inertialRadiusOffset *= this.inertia;\r\n            if (Math.abs(this.inertialAlphaOffset) < Epsilon) {\r\n                this.inertialAlphaOffset = 0;\r\n            }\r\n            if (Math.abs(this.inertialBetaOffset) < Epsilon) {\r\n                this.inertialBetaOffset = 0;\r\n            }\r\n            if (Math.abs(this.inertialRadiusOffset) < this.speed * Epsilon) {\r\n                this.inertialRadiusOffset = 0;\r\n            }\r\n        }\r\n\r\n        // Panning inertia\r\n        if (this.inertialPanningX !== 0 || this.inertialPanningY !== 0) {\r\n            const localDirection = new Vector3(this.inertialPanningX, this.inertialPanningY, this.inertialPanningY);\r\n\r\n            this._viewMatrix.invertToRef(this._cameraTransformMatrix);\r\n            localDirection.multiplyInPlace(this.panningAxis);\r\n            Vector3.TransformNormalToRef(localDirection, this._cameraTransformMatrix, this._transformedDirection);\r\n            // Eliminate y if mapPanning is enabled\r\n            if (this.mapPanning || !this.panningAxis.y) {\r\n                this._transformedDirection.y = 0;\r\n            }\r\n\r\n            if (!this._targetHost) {\r\n                if (this.panningDistanceLimit) {\r\n                    this._transformedDirection.addInPlace(this._target);\r\n                    const distanceSquared = Vector3.DistanceSquared(this._transformedDirection, this.panningOriginTarget);\r\n                    if (distanceSquared <= this.panningDistanceLimit * this.panningDistanceLimit) {\r\n                        this._target.copyFrom(this._transformedDirection);\r\n                    }\r\n                } else {\r\n                    this._target.addInPlace(this._transformedDirection);\r\n                }\r\n            }\r\n\r\n            this.inertialPanningX *= this.panningInertia;\r\n            this.inertialPanningY *= this.panningInertia;\r\n\r\n            if (Math.abs(this.inertialPanningX) < this.speed * Epsilon) {\r\n                this.inertialPanningX = 0;\r\n            }\r\n            if (Math.abs(this.inertialPanningY) < this.speed * Epsilon) {\r\n                this.inertialPanningY = 0;\r\n            }\r\n        }\r\n\r\n        // Limits\r\n        this._checkLimits();\r\n\r\n        super._checkInputs();\r\n    }\r\n\r\n    protected _checkLimits() {\r\n        if (this.lowerBetaLimit === null || this.lowerBetaLimit === undefined) {\r\n            if (this.allowUpsideDown && this.beta > Math.PI) {\r\n                this.beta = this.beta - 2 * Math.PI;\r\n            }\r\n        } else {\r\n            if (this.beta < this.lowerBetaLimit) {\r\n                this.beta = this.lowerBetaLimit;\r\n            }\r\n        }\r\n\r\n        if (this.upperBetaLimit === null || this.upperBetaLimit === undefined) {\r\n            if (this.allowUpsideDown && this.beta < -Math.PI) {\r\n                this.beta = this.beta + 2 * Math.PI;\r\n            }\r\n        } else {\r\n            if (this.beta > this.upperBetaLimit) {\r\n                this.beta = this.upperBetaLimit;\r\n            }\r\n        }\r\n\r\n        if (this.lowerAlphaLimit !== null && this.alpha < this.lowerAlphaLimit) {\r\n            this.alpha = this.lowerAlphaLimit;\r\n        }\r\n        if (this.upperAlphaLimit !== null && this.alpha > this.upperAlphaLimit) {\r\n            this.alpha = this.upperAlphaLimit;\r\n        }\r\n\r\n        if (this.lowerRadiusLimit !== null && this.radius < this.lowerRadiusLimit) {\r\n            this.radius = this.lowerRadiusLimit;\r\n            this.inertialRadiusOffset = 0;\r\n        }\r\n        if (this.upperRadiusLimit !== null && this.radius > this.upperRadiusLimit) {\r\n            this.radius = this.upperRadiusLimit;\r\n            this.inertialRadiusOffset = 0;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Rebuilds angles (alpha, beta) and radius from the give position and target\r\n     */\r\n    public rebuildAnglesAndRadius(): void {\r\n        this._position.subtractToRef(this._getTargetPosition(), this._computationVector);\r\n\r\n        // need to rotate to Y up equivalent if up vector not Axis.Y\r\n        if (this._upVector.x !== 0 || this._upVector.y !== 1.0 || this._upVector.z !== 0) {\r\n            Vector3.TransformCoordinatesToRef(this._computationVector, this._upToYMatrix, this._computationVector);\r\n        }\r\n\r\n        this.radius = this._computationVector.length();\r\n\r\n        if (this.radius === 0) {\r\n            this.radius = 0.0001; // Just to avoid division by zero\r\n        }\r\n\r\n        // Alpha\r\n        const previousAlpha = this.alpha;\r\n        if (this._computationVector.x === 0 && this._computationVector.z === 0) {\r\n            this.alpha = Math.PI / 2; // avoid division by zero when looking along up axis, and set to acos(0)\r\n        } else {\r\n            this.alpha = Math.acos(this._computationVector.x / Math.sqrt(Math.pow(this._computationVector.x, 2) + Math.pow(this._computationVector.z, 2)));\r\n        }\r\n\r\n        if (this._computationVector.z < 0) {\r\n            this.alpha = 2 * Math.PI - this.alpha;\r\n        }\r\n\r\n        // Calculate the number of revolutions between the new and old alpha values.\r\n        const alphaCorrectionTurns = Math.round((previousAlpha - this.alpha) / (2.0 * Math.PI));\r\n        // Adjust alpha so that its numerical representation is the closest one to the old value.\r\n        this.alpha += alphaCorrectionTurns * 2.0 * Math.PI;\r\n\r\n        // Beta\r\n        this.beta = Math.acos(this._computationVector.y / this.radius);\r\n\r\n        this._checkLimits();\r\n    }\r\n\r\n    /**\r\n     * Use a position to define the current camera related information like alpha, beta and radius\r\n     * @param position Defines the position to set the camera at\r\n     */\r\n    public setPosition(position: Vector3): void {\r\n        if (this._position.equals(position)) {\r\n            return;\r\n        }\r\n        this._position.copyFrom(position);\r\n\r\n        this.rebuildAnglesAndRadius();\r\n    }\r\n\r\n    /**\r\n     * Defines the target the camera should look at.\r\n     * This will automatically adapt alpha beta and radius to fit within the new target.\r\n     * Please note that setting a target as a mesh will disable panning.\r\n     * @param target Defines the new target as a Vector or a mesh\r\n     * @param toBoundingCenter In case of a mesh target, defines whether to target the mesh position or its bounding information center\r\n     * @param allowSamePosition If false, prevents reapplying the new computed position if it is identical to the current one (optim)\r\n     * @param cloneAlphaBetaRadius If true, replicate the current setup (alpha, beta, radius) on the new target\r\n     */\r\n    public setTarget(target: AbstractMesh | Vector3, toBoundingCenter = false, allowSamePosition = false, cloneAlphaBetaRadius = false): void {\r\n        cloneAlphaBetaRadius = this.overrideCloneAlphaBetaRadius ?? cloneAlphaBetaRadius;\r\n\r\n        if ((<any>target).getBoundingInfo) {\r\n            if (toBoundingCenter) {\r\n                this._targetBoundingCenter = (<any>target).getBoundingInfo().boundingBox.centerWorld.clone();\r\n            } else {\r\n                this._targetBoundingCenter = null;\r\n            }\r\n            (<AbstractMesh>target).computeWorldMatrix();\r\n            this._targetHost = <AbstractMesh>target;\r\n            this._target = this._getTargetPosition();\r\n\r\n            this.onMeshTargetChangedObservable.notifyObservers(this._targetHost);\r\n        } else {\r\n            const newTarget = <Vector3>target;\r\n            const currentTarget = this._getTargetPosition();\r\n            if (currentTarget && !allowSamePosition && currentTarget.equals(newTarget)) {\r\n                return;\r\n            }\r\n            this._targetHost = null;\r\n            this._target = newTarget;\r\n            this._targetBoundingCenter = null;\r\n            this.onMeshTargetChangedObservable.notifyObservers(null);\r\n        }\r\n\r\n        if (!cloneAlphaBetaRadius) {\r\n            this.rebuildAnglesAndRadius();\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _getViewMatrix(): Matrix {\r\n        // Compute\r\n        const cosa = Math.cos(this.alpha);\r\n        const sina = Math.sin(this.alpha);\r\n        const cosb = Math.cos(this.beta);\r\n        let sinb = Math.sin(this.beta);\r\n\r\n        if (sinb === 0) {\r\n            sinb = 0.0001;\r\n        }\r\n\r\n        if (this.radius === 0) {\r\n            this.radius = 0.0001; // Just to avoid division by zero\r\n        }\r\n\r\n        const target = this._getTargetPosition();\r\n        this._computationVector.copyFromFloats(this.radius * cosa * sinb, this.radius * cosb, this.radius * sina * sinb);\r\n\r\n        // Rotate according to up vector\r\n        if (this._upVector.x !== 0 || this._upVector.y !== 1.0 || this._upVector.z !== 0) {\r\n            Vector3.TransformCoordinatesToRef(this._computationVector, this._yToUpMatrix, this._computationVector);\r\n        }\r\n\r\n        target.addToRef(this._computationVector, this._newPosition);\r\n        if (this.getScene().collisionsEnabled && this.checkCollisions) {\r\n            const coordinator = this.getScene().collisionCoordinator;\r\n            if (!this._collider) {\r\n                this._collider = coordinator.createCollider();\r\n            }\r\n            this._collider._radius = this.collisionRadius;\r\n            this._newPosition.subtractToRef(this._position, this._collisionVelocity);\r\n            this._collisionTriggered = true;\r\n            coordinator.getNewPosition(this._position, this._collisionVelocity, this._collider, 3, null, this._onCollisionPositionChange, this.uniqueId);\r\n        } else {\r\n            this._position.copyFrom(this._newPosition);\r\n\r\n            let up = this.upVector;\r\n            if (this.allowUpsideDown && sinb < 0) {\r\n                up = up.negate();\r\n            }\r\n\r\n            this._computeViewMatrix(this._position, target, up);\r\n\r\n            this._viewMatrix.addAtIndex(12, this.targetScreenOffset.x);\r\n            this._viewMatrix.addAtIndex(13, this.targetScreenOffset.y);\r\n        }\r\n        this._currentTarget = target;\r\n        return this._viewMatrix;\r\n    }\r\n\r\n    protected _onCollisionPositionChange = (collisionId: number, newPosition: Vector3, collidedMesh: Nullable<AbstractMesh> = null) => {\r\n        if (!collidedMesh) {\r\n            this._previousPosition.copyFrom(this._position);\r\n        } else {\r\n            this.setPosition(newPosition);\r\n\r\n            if (this.onCollide) {\r\n                this.onCollide(collidedMesh);\r\n            }\r\n        }\r\n\r\n        // Recompute because of constraints\r\n        const cosa = Math.cos(this.alpha);\r\n        const sina = Math.sin(this.alpha);\r\n        const cosb = Math.cos(this.beta);\r\n        let sinb = Math.sin(this.beta);\r\n\r\n        if (sinb === 0) {\r\n            sinb = 0.0001;\r\n        }\r\n\r\n        const target = this._getTargetPosition();\r\n        this._computationVector.copyFromFloats(this.radius * cosa * sinb, this.radius * cosb, this.radius * sina * sinb);\r\n        target.addToRef(this._computationVector, this._newPosition);\r\n        this._position.copyFrom(this._newPosition);\r\n\r\n        let up = this.upVector;\r\n        if (this.allowUpsideDown && this.beta < 0) {\r\n            up = up.clone();\r\n            up = up.negate();\r\n        }\r\n\r\n        this._computeViewMatrix(this._position, target, up);\r\n        this._viewMatrix.addAtIndex(12, this.targetScreenOffset.x);\r\n        this._viewMatrix.addAtIndex(13, this.targetScreenOffset.y);\r\n\r\n        this._collisionTriggered = false;\r\n    };\r\n\r\n    /**\r\n     * Zooms on a mesh to be at the min distance where we could see it fully in the current viewport.\r\n     * @param meshes Defines the mesh to zoom on\r\n     * @param doNotUpdateMaxZ Defines whether or not maxZ should be updated whilst zooming on the mesh (this can happen if the mesh is big and the maxradius pretty small for instance)\r\n     */\r\n    public zoomOn(meshes?: AbstractMesh[], doNotUpdateMaxZ = false): void {\r\n        meshes = meshes || this.getScene().meshes;\r\n\r\n        const minMaxVector = Mesh.MinMax(meshes);\r\n        const distance = Vector3.Distance(minMaxVector.min, minMaxVector.max);\r\n\r\n        this.radius = distance * this.zoomOnFactor;\r\n\r\n        this.focusOn({ min: minMaxVector.min, max: minMaxVector.max, distance: distance }, doNotUpdateMaxZ);\r\n    }\r\n\r\n    /**\r\n     * Focus on a mesh or a bounding box. This adapts the target and maxRadius if necessary but does not update the current radius.\r\n     * The target will be changed but the radius\r\n     * @param meshesOrMinMaxVectorAndDistance Defines the mesh or bounding info to focus on\r\n     * @param doNotUpdateMaxZ Defines whether or not maxZ should be updated whilst zooming on the mesh (this can happen if the mesh is big and the maxradius pretty small for instance)\r\n     */\r\n    public focusOn(meshesOrMinMaxVectorAndDistance: AbstractMesh[] | { min: Vector3; max: Vector3; distance: number }, doNotUpdateMaxZ = false): void {\r\n        let meshesOrMinMaxVector: { min: Vector3; max: Vector3 };\r\n        let distance: number;\r\n\r\n        if ((<any>meshesOrMinMaxVectorAndDistance).min === undefined) {\r\n            // meshes\r\n            const meshes = <AbstractMesh[]>meshesOrMinMaxVectorAndDistance || this.getScene().meshes;\r\n            meshesOrMinMaxVector = Mesh.MinMax(meshes);\r\n            distance = Vector3.Distance(meshesOrMinMaxVector.min, meshesOrMinMaxVector.max);\r\n        } else {\r\n            //minMaxVector and distance\r\n            const minMaxVectorAndDistance = <any>meshesOrMinMaxVectorAndDistance;\r\n            meshesOrMinMaxVector = minMaxVectorAndDistance;\r\n            distance = minMaxVectorAndDistance.distance;\r\n        }\r\n\r\n        this._target = Mesh.Center(meshesOrMinMaxVector);\r\n\r\n        if (!doNotUpdateMaxZ) {\r\n            this.maxZ = distance * 2;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @override\r\n     * Override Camera.createRigCamera\r\n     */\r\n    public createRigCamera(name: string, cameraIndex: number): Camera {\r\n        let alphaShift: number = 0;\r\n        switch (this.cameraRigMode) {\r\n            case Camera.RIG_MODE_STEREOSCOPIC_ANAGLYPH:\r\n            case Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_PARALLEL:\r\n            case Camera.RIG_MODE_STEREOSCOPIC_OVERUNDER:\r\n            case Camera.RIG_MODE_STEREOSCOPIC_INTERLACED:\r\n            case Camera.RIG_MODE_VR:\r\n                alphaShift = this._cameraRigParams.stereoHalfAngle * (cameraIndex === 0 ? 1 : -1);\r\n                break;\r\n            case Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_CROSSEYED:\r\n                alphaShift = this._cameraRigParams.stereoHalfAngle * (cameraIndex === 0 ? -1 : 1);\r\n                break;\r\n        }\r\n        const rigCam = new ArcRotateCamera(name, this.alpha + alphaShift, this.beta, this.radius, this._target, this.getScene());\r\n        rigCam._cameraRigParams = {};\r\n        rigCam.isRigCamera = true;\r\n        rigCam.rigParent = this;\r\n        rigCam.upVector = this.upVector;\r\n\r\n        rigCam.mode = this.mode;\r\n        rigCam.orthoLeft = this.orthoLeft;\r\n        rigCam.orthoRight = this.orthoRight;\r\n        rigCam.orthoBottom = this.orthoBottom;\r\n        rigCam.orthoTop = this.orthoTop;\r\n\r\n        return rigCam;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * @override\r\n     * Override Camera._updateRigCameras\r\n     */\r\n    public _updateRigCameras() {\r\n        const camLeft = <ArcRotateCamera>this._rigCameras[0];\r\n        const camRight = <ArcRotateCamera>this._rigCameras[1];\r\n\r\n        camLeft.beta = camRight.beta = this.beta;\r\n\r\n        switch (this.cameraRigMode) {\r\n            case Camera.RIG_MODE_STEREOSCOPIC_ANAGLYPH:\r\n            case Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_PARALLEL:\r\n            case Camera.RIG_MODE_STEREOSCOPIC_OVERUNDER:\r\n            case Camera.RIG_MODE_STEREOSCOPIC_INTERLACED:\r\n            case Camera.RIG_MODE_VR:\r\n                camLeft.alpha = this.alpha - this._cameraRigParams.stereoHalfAngle;\r\n                camRight.alpha = this.alpha + this._cameraRigParams.stereoHalfAngle;\r\n                break;\r\n            case Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_CROSSEYED:\r\n                camLeft.alpha = this.alpha + this._cameraRigParams.stereoHalfAngle;\r\n                camRight.alpha = this.alpha - this._cameraRigParams.stereoHalfAngle;\r\n                break;\r\n        }\r\n        super._updateRigCameras();\r\n    }\r\n\r\n    /**\r\n     * Destroy the camera and release the current resources hold by it.\r\n     */\r\n    public dispose(): void {\r\n        this.inputs.clear();\r\n        super.dispose();\r\n    }\r\n\r\n    /**\r\n     * Gets the current object class name.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"ArcRotateCamera\";\r\n    }\r\n}\r\n"]}