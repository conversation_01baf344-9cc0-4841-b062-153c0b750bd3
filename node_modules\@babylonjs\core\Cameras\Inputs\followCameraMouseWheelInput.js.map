{"version": 3, "file": "followCameraMouseWheelInput.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Cameras/Inputs/followCameraMouseWheelInput.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAIlD,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AAErE,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAE/D,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEzC;;;GAGG;AACH,MAAM,OAAO,2BAA2B;IAAxC;QAMI;;WAEG;QAEI,sBAAiB,GAAY,IAAI,CAAC;QAEzC;;WAEG;QAEI,sBAAiB,GAAY,KAAK,CAAC;QAE1C;;WAEG;QAEI,wBAAmB,GAAY,KAAK,CAAC;QAE5C;;;WAGG;QAEI,mBAAc,GAAG,GAAG,CAAC;QAE5B;;;WAGG;QAEI,yBAAoB,GAAG,CAAC,CAAC;IA2FpC,CAAC;IAtFG;;;OAGG;IACI,aAAa,CAAC,gBAA0B;QAC3C,gBAAgB,GAAG,KAAK,CAAC,gCAAgC,CAAC,SAAS,CAAC,CAAC;QACrE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE;YAChB,sDAAsD;YACtD,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,YAAY,EAAE;gBAC3C,OAAO;aACV;YACD,MAAM,KAAK,GAAgB,CAAC,CAAC,KAAK,CAAC;YACnC,IAAI,KAAK,GAAG,CAAC,CAAC;YAEd,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;YAC3D,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC3B,OAAO,CAAC,MAAM,CACQ,IAAI,CAAC,iBAAkB,GAAqB,IAAI,CAAC,iBAAkB,GAAqB,IAAI,CAAC,mBAAoB,IAAI,CAAC,EACxI,oDAAoD;oBAChD,qBAAqB;oBACrB,qBAAqB;oBACrB,qBAAqB;oBACrB,IAAI,CAAC,iBAAiB;oBACtB,6BAA6B;oBAC7B,IAAI,CAAC,iBAAiB;oBACtB,+BAA+B;oBAC/B,IAAI,CAAC,mBAAmB,CAC/B,CAAC;gBAEF,IAAI,IAAI,CAAC,iBAAiB,EAAE;oBACxB,KAAK,GAAG,UAAU,GAAG,IAAI,GAAG,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;iBAC9E;qBAAM,IAAI,IAAI,CAAC,iBAAiB,EAAE;oBAC/B,KAAK,GAAG,UAAU,GAAG,IAAI,GAAG,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;iBACpF;qBAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE;oBACjC,KAAK,GAAG,UAAU,GAAG,IAAI,GAAG,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;iBACtF;aACJ;iBAAM;gBACH,KAAK,GAAG,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC;aAC5C;YAED,IAAI,KAAK,EAAE;gBACP,IAAI,IAAI,CAAC,iBAAiB,EAAE;oBACxB,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,KAAK,CAAC;iBAC/B;qBAAM,IAAI,IAAI,CAAC,iBAAiB,EAAE;oBAC/B,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,KAAK,CAAC;iBACrC;qBAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE;oBACjC,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,KAAK,CAAC;iBACvC;aACJ;YAED,IAAI,KAAK,CAAC,cAAc,EAAE;gBACtB,IAAI,CAAC,gBAAgB,EAAE;oBACnB,KAAK,CAAC,cAAc,EAAE,CAAC;iBAC1B;aACJ;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,yBAAyB,CAAC,IAAI,CAAC,MAAM,EAAE,iBAAiB,CAAC,YAAY,CAAC,CAAC;IACjI,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,4BAA4B,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAClF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;SACtB;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,gCAAgC,CAAC;IAC5C,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,YAAY,CAAC;IACxB,CAAC;CACJ;AArHG;IADC,SAAS,EAAE;sEAC6B;AAMzC;IADC,SAAS,EAAE;sEAC8B;AAM1C;IADC,SAAS,EAAE;wEACgC;AAO5C;IADC,SAAS,EAAE;mEACgB;AAO5B;IADC,SAAS,EAAE;yEACoB;AA6F9B,gBAAiB,CAAC,6BAA6B,CAAC,GAAG,2BAA2B,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { serialize } from \"../../Misc/decorators\";\r\nimport type { EventState, Observer } from \"../../Misc/observable\";\r\nimport type { FollowCamera } from \"../../Cameras/followCamera\";\r\nimport type { ICameraInput } from \"../../Cameras/cameraInputsManager\";\r\nimport { CameraInputTypes } from \"../../Cameras/cameraInputsManager\";\r\nimport type { PointerInfo } from \"../../Events/pointerEvents\";\r\nimport { PointerEventTypes } from \"../../Events/pointerEvents\";\r\nimport type { IWheelEvent } from \"../../Events/deviceInputEvents\";\r\nimport { Tools } from \"../../Misc/tools\";\r\n\r\n/**\r\n * Manage the mouse wheel inputs to control a follow camera.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n */\r\nexport class FollowCameraMouseWheelInput implements ICameraInput<FollowCamera> {\r\n    /**\r\n     * Defines the camera the input is attached to.\r\n     */\r\n    public camera: FollowCamera;\r\n\r\n    /**\r\n     * <PERSON>ue wheel controls zoom. (Mouse wheel modifies camera.radius value.)\r\n     */\r\n    @serialize()\r\n    public axisControlRadius: boolean = true;\r\n\r\n    /**\r\n     * Moue wheel controls height. (Mouse wheel modifies camera.heightOffset value.)\r\n     */\r\n    @serialize()\r\n    public axisControlHeight: boolean = false;\r\n\r\n    /**\r\n     * Moue wheel controls angle. (Mouse wheel modifies camera.rotationOffset value.)\r\n     */\r\n    @serialize()\r\n    public axisControlRotation: boolean = false;\r\n\r\n    /**\r\n     * Gets or Set the mouse wheel precision or how fast is the camera moves in\r\n     * relation to mouseWheel events.\r\n     */\r\n    @serialize()\r\n    public wheelPrecision = 3.0;\r\n\r\n    /**\r\n     * wheelDeltaPercentage will be used instead of wheelPrecision if different from 0.\r\n     * It defines the percentage of current camera.radius to use as delta when wheel is used.\r\n     */\r\n    @serialize()\r\n    public wheelDeltaPercentage = 0;\r\n\r\n    private _wheel: Nullable<(p: PointerInfo, s: EventState) => void>;\r\n    private _observer: Nullable<Observer<PointerInfo>>;\r\n\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public attachControl(noPreventDefault?: boolean): void {\r\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(arguments);\r\n        this._wheel = (p) => {\r\n            // sanity check - this should be a PointerWheel event.\r\n            if (p.type !== PointerEventTypes.POINTERWHEEL) {\r\n                return;\r\n            }\r\n            const event = <IWheelEvent>p.event;\r\n            let delta = 0;\r\n\r\n            const wheelDelta = Math.max(-1, Math.min(1, event.deltaY));\r\n            if (this.wheelDeltaPercentage) {\r\n                console.assert(\r\n                    <number>(<unknown>this.axisControlRadius) + <number>(<unknown>this.axisControlHeight) + <number>(<unknown>this.axisControlRotation) <= 1,\r\n                    \"wheelDeltaPercentage only usable when mouse wheel \" +\r\n                        \"controls ONE axis. \" +\r\n                        \"Currently enabled: \" +\r\n                        \"axisControlRadius: \" +\r\n                        this.axisControlRadius +\r\n                        \", axisControlHeightOffset: \" +\r\n                        this.axisControlHeight +\r\n                        \", axisControlRotationOffset: \" +\r\n                        this.axisControlRotation\r\n                );\r\n\r\n                if (this.axisControlRadius) {\r\n                    delta = wheelDelta * 0.01 * this.wheelDeltaPercentage * this.camera.radius;\r\n                } else if (this.axisControlHeight) {\r\n                    delta = wheelDelta * 0.01 * this.wheelDeltaPercentage * this.camera.heightOffset;\r\n                } else if (this.axisControlRotation) {\r\n                    delta = wheelDelta * 0.01 * this.wheelDeltaPercentage * this.camera.rotationOffset;\r\n                }\r\n            } else {\r\n                delta = wheelDelta * this.wheelPrecision;\r\n            }\r\n\r\n            if (delta) {\r\n                if (this.axisControlRadius) {\r\n                    this.camera.radius += delta;\r\n                } else if (this.axisControlHeight) {\r\n                    this.camera.heightOffset -= delta;\r\n                } else if (this.axisControlRotation) {\r\n                    this.camera.rotationOffset -= delta;\r\n                }\r\n            }\r\n\r\n            if (event.preventDefault) {\r\n                if (!noPreventDefault) {\r\n                    event.preventDefault();\r\n                }\r\n            }\r\n        };\r\n\r\n        this._observer = this.camera.getScene()._inputManager._addCameraPointerObserver(this._wheel, PointerEventTypes.POINTERWHEEL);\r\n    }\r\n\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    public detachControl(): void {\r\n        if (this._observer) {\r\n            this.camera.getScene()._inputManager._removeCameraPointerObserver(this._observer);\r\n            this._observer = null;\r\n            this._wheel = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of the current input.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"ArcRotateCameraMouseWheelInput\";\r\n    }\r\n\r\n    /**\r\n     * Get the friendly name associated with the input class.\r\n     * @returns the input friendly name\r\n     */\r\n    public getSimpleName(): string {\r\n        return \"mousewheel\";\r\n    }\r\n}\r\n\r\n(<any>CameraInputTypes)[\"FollowCameraMouseWheelInput\"] = FollowCameraMouseWheelInput;\r\n"]}