{"version": 3, "file": "arcRotateCameraKeyboardMoveInput.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Cameras/Inputs/arcRotateCameraKeyboardMoveInput.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAKlD,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AAGrE,OAAO,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AACjE,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEzC;;;GAGG;AACH,MAAM,OAAO,gCAAgC;IAA7C;QAMI;;WAEG;QAEI,WAAM,GAAG,CAAC,EAAE,CAAC,CAAC;QAErB;;WAEG;QAEI,aAAQ,GAAG,CAAC,EAAE,CAAC,CAAC;QAEvB;;WAEG;QAEI,aAAQ,GAAG,CAAC,EAAE,CAAC,CAAC;QAEvB;;WAEG;QAEI,cAAS,GAAG,CAAC,EAAE,CAAC,CAAC;QAExB;;;WAGG;QAEI,cAAS,GAAG,CAAC,GAAG,CAAC,CAAC;QAEzB;;;WAGG;QAEI,uBAAkB,GAAW,IAAI,CAAC;QAEzC;;;WAGG;QAEI,uBAAkB,GAAW,IAAI,CAAC;QAEzC;;;WAGG;QAEI,iBAAY,GAAY,IAAI,CAAC;QAEpC;;WAEG;QAEI,iBAAY,GAAG,IAAI,CAAC;QAEnB,UAAK,GAAG,IAAI,KAAK,EAAU,CAAC;IA+JxC,CAAC;IAvJG;;;OAGG;IACI,aAAa,CAAC,gBAA0B;QAC3C,uCAAuC;QACvC,8CAA8C;QAC9C,gBAAgB,GAAG,KAAK,CAAC,gCAAgC,CAAC,SAAS,CAAC,CAAC;QAErE,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,OAAO;SACV;QAED,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,GAAG,CAAC,GAAG,EAAE;YACtE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACrE,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;YACvB,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;gBACd,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB,CAAC,OAAO,EAAE;oBAC1C,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC;oBAChC,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC;oBAE9B,IACI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBACvC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBACzC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBACzC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC1C,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAC5C;wBACE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;wBAE9C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;4BACd,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;yBAChC;wBAED,IAAI,GAAG,CAAC,cAAc,EAAE;4BACpB,IAAI,CAAC,gBAAgB,EAAE;gCACnB,GAAG,CAAC,cAAc,EAAE,CAAC;6BACxB;yBACJ;qBACJ;iBACJ;qBAAM;oBACH,IACI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBACvC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBACzC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBACzC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC1C,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAC5C;wBACE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;wBAE9C,IAAI,KAAK,IAAI,CAAC,EAAE;4BACZ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;yBAC/B;wBAED,IAAI,GAAG,CAAC,cAAc,EAAE;4BACpB,IAAI,CAAC,gBAAgB,EAAE;gCACnB,GAAG,CAAC,cAAc,EAAE,CAAC;6BACxB;yBACJ;qBACJ;iBACJ;aACJ;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;aACrE;YACD,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;aAC1E;YACD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAChC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;SACrC;QAED,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAE3B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBACpD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;oBACvC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE;wBACrD,MAAM,CAAC,gBAAgB,IAAI,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;qBAC1D;yBAAM;wBACH,MAAM,CAAC,mBAAmB,IAAI,IAAI,CAAC,YAAY,CAAC;qBACnD;iBACJ;qBAAM,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC5C,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE;wBACrD,MAAM,CAAC,gBAAgB,IAAI,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;qBAC1D;yBAAM,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,YAAY,EAAE;wBAC9C,MAAM,CAAC,oBAAoB,IAAI,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;qBAC9D;yBAAM;wBACH,MAAM,CAAC,kBAAkB,IAAI,IAAI,CAAC,YAAY,CAAC;qBAClD;iBACJ;qBAAM,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC/C,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE;wBACrD,MAAM,CAAC,gBAAgB,IAAI,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;qBAC1D;yBAAM;wBACH,MAAM,CAAC,mBAAmB,IAAI,IAAI,CAAC,YAAY,CAAC;qBACnD;iBACJ;qBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC9C,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE;wBACrD,MAAM,CAAC,gBAAgB,IAAI,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;qBAC1D;yBAAM,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,YAAY,EAAE;wBAC9C,MAAM,CAAC,oBAAoB,IAAI,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;qBAC9D;yBAAM;wBACH,MAAM,CAAC,kBAAkB,IAAI,IAAI,CAAC,YAAY,CAAC;qBAClD;iBACJ;qBAAM,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC/C,IAAI,MAAM,CAAC,sBAAsB,EAAE;wBAC/B,MAAM,CAAC,YAAY,EAAE,CAAC;qBACzB;iBACJ;aACJ;SACJ;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,kCAAkC,CAAC;IAC9C,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,UAAU,CAAC;IACtB,CAAC;CACJ;AArNG;IADC,SAAS,EAAE;gEACS;AAMrB;IADC,SAAS,EAAE;kEACW;AAMvB;IADC,SAAS,EAAE;kEACW;AAMvB;IADC,SAAS,EAAE;mEACY;AAOxB;IADC,SAAS,EAAE;mEACa;AAOzB;IADC,SAAS,EAAE;4EAC6B;AAOzC;IADC,SAAS,EAAE;4EAC6B;AAOzC;IADC,SAAS,EAAE;sEACwB;AAMpC;IADC,SAAS,EAAE;sEACe;AAmKzB,gBAAiB,CAAC,kCAAkC,CAAC,GAAG,gCAAgC,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { serialize } from \"../../Misc/decorators\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { ArcRotateCamera } from \"../../Cameras/arcRotateCamera\";\r\nimport type { ICameraInput } from \"../../Cameras/cameraInputsManager\";\r\nimport { CameraInputTypes } from \"../../Cameras/cameraInputsManager\";\r\nimport type { Engine } from \"../../Engines/engine\";\r\nimport type { KeyboardInfo } from \"../../Events/keyboardEvents\";\r\nimport { KeyboardEventTypes } from \"../../Events/keyboardEvents\";\r\nimport { Tools } from \"../../Misc/tools\";\r\n\r\n/**\r\n * Manage the keyboard inputs to control the movement of an arc rotate camera.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n */\r\nexport class ArcRotateCameraKeyboardMoveInput implements ICameraInput<ArcRotateCamera> {\r\n    /**\r\n     * Defines the camera the input is attached to.\r\n     */\r\n    public camera: ArcRotateCamera;\r\n\r\n    /**\r\n     * Defines the list of key codes associated with the up action (increase alpha)\r\n     */\r\n    @serialize()\r\n    public keysUp = [38];\r\n\r\n    /**\r\n     * Defines the list of key codes associated with the down action (decrease alpha)\r\n     */\r\n    @serialize()\r\n    public keysDown = [40];\r\n\r\n    /**\r\n     * Defines the list of key codes associated with the left action (increase beta)\r\n     */\r\n    @serialize()\r\n    public keysLeft = [37];\r\n\r\n    /**\r\n     * Defines the list of key codes associated with the right action (decrease beta)\r\n     */\r\n    @serialize()\r\n    public keysRight = [39];\r\n\r\n    /**\r\n     * Defines the list of key codes associated with the reset action.\r\n     * Those keys reset the camera to its last stored state (with the method camera.storeState())\r\n     */\r\n    @serialize()\r\n    public keysReset = [220];\r\n\r\n    /**\r\n     * Defines the panning sensibility of the inputs.\r\n     * (How fast is the camera panning)\r\n     */\r\n    @serialize()\r\n    public panningSensibility: number = 50.0;\r\n\r\n    /**\r\n     * Defines the zooming sensibility of the inputs.\r\n     * (How fast is the camera zooming)\r\n     */\r\n    @serialize()\r\n    public zoomingSensibility: number = 25.0;\r\n\r\n    /**\r\n     * Defines whether maintaining the alt key down switch the movement mode from\r\n     * orientation to zoom.\r\n     */\r\n    @serialize()\r\n    public useAltToZoom: boolean = true;\r\n\r\n    /**\r\n     * Rotation speed of the camera\r\n     */\r\n    @serialize()\r\n    public angularSpeed = 0.01;\r\n\r\n    private _keys = new Array<number>();\r\n    private _ctrlPressed: boolean;\r\n    private _altPressed: boolean;\r\n    private _onCanvasBlurObserver: Nullable<Observer<Engine>>;\r\n    private _onKeyboardObserver: Nullable<Observer<KeyboardInfo>>;\r\n    private _engine: Engine;\r\n    private _scene: Scene;\r\n\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public attachControl(noPreventDefault?: boolean): void {\r\n        // was there a second variable defined?\r\n        // eslint-disable-next-line prefer-rest-params\r\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(arguments);\r\n\r\n        if (this._onCanvasBlurObserver) {\r\n            return;\r\n        }\r\n\r\n        this._scene = this.camera.getScene();\r\n        this._engine = this._scene.getEngine();\r\n\r\n        this._onCanvasBlurObserver = this._engine.onCanvasBlurObservable.add(() => {\r\n            this._keys.length = 0;\r\n        });\r\n\r\n        this._onKeyboardObserver = this._scene.onKeyboardObservable.add((info) => {\r\n            const evt = info.event;\r\n            if (!evt.metaKey) {\r\n                if (info.type === KeyboardEventTypes.KEYDOWN) {\r\n                    this._ctrlPressed = evt.ctrlKey;\r\n                    this._altPressed = evt.altKey;\r\n\r\n                    if (\r\n                        this.keysUp.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysDown.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysLeft.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysRight.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysReset.indexOf(evt.keyCode) !== -1\r\n                    ) {\r\n                        const index = this._keys.indexOf(evt.keyCode);\r\n\r\n                        if (index === -1) {\r\n                            this._keys.push(evt.keyCode);\r\n                        }\r\n\r\n                        if (evt.preventDefault) {\r\n                            if (!noPreventDefault) {\r\n                                evt.preventDefault();\r\n                            }\r\n                        }\r\n                    }\r\n                } else {\r\n                    if (\r\n                        this.keysUp.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysDown.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysLeft.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysRight.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysReset.indexOf(evt.keyCode) !== -1\r\n                    ) {\r\n                        const index = this._keys.indexOf(evt.keyCode);\r\n\r\n                        if (index >= 0) {\r\n                            this._keys.splice(index, 1);\r\n                        }\r\n\r\n                        if (evt.preventDefault) {\r\n                            if (!noPreventDefault) {\r\n                                evt.preventDefault();\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    public detachControl(): void {\r\n        if (this._scene) {\r\n            if (this._onKeyboardObserver) {\r\n                this._scene.onKeyboardObservable.remove(this._onKeyboardObserver);\r\n            }\r\n            if (this._onCanvasBlurObserver) {\r\n                this._engine.onCanvasBlurObservable.remove(this._onCanvasBlurObserver);\r\n            }\r\n            this._onKeyboardObserver = null;\r\n            this._onCanvasBlurObserver = null;\r\n        }\r\n\r\n        this._keys.length = 0;\r\n    }\r\n\r\n    /**\r\n     * Update the current camera state depending on the inputs that have been used this frame.\r\n     * This is a dynamically created lambda to avoid the performance penalty of looping for inputs in the render loop.\r\n     */\r\n    public checkInputs(): void {\r\n        if (this._onKeyboardObserver) {\r\n            const camera = this.camera;\r\n\r\n            for (let index = 0; index < this._keys.length; index++) {\r\n                const keyCode = this._keys[index];\r\n                if (this.keysLeft.indexOf(keyCode) !== -1) {\r\n                    if (this._ctrlPressed && this.camera._useCtrlForPanning) {\r\n                        camera.inertialPanningX -= 1 / this.panningSensibility;\r\n                    } else {\r\n                        camera.inertialAlphaOffset -= this.angularSpeed;\r\n                    }\r\n                } else if (this.keysUp.indexOf(keyCode) !== -1) {\r\n                    if (this._ctrlPressed && this.camera._useCtrlForPanning) {\r\n                        camera.inertialPanningY += 1 / this.panningSensibility;\r\n                    } else if (this._altPressed && this.useAltToZoom) {\r\n                        camera.inertialRadiusOffset += 1 / this.zoomingSensibility;\r\n                    } else {\r\n                        camera.inertialBetaOffset -= this.angularSpeed;\r\n                    }\r\n                } else if (this.keysRight.indexOf(keyCode) !== -1) {\r\n                    if (this._ctrlPressed && this.camera._useCtrlForPanning) {\r\n                        camera.inertialPanningX += 1 / this.panningSensibility;\r\n                    } else {\r\n                        camera.inertialAlphaOffset += this.angularSpeed;\r\n                    }\r\n                } else if (this.keysDown.indexOf(keyCode) !== -1) {\r\n                    if (this._ctrlPressed && this.camera._useCtrlForPanning) {\r\n                        camera.inertialPanningY -= 1 / this.panningSensibility;\r\n                    } else if (this._altPressed && this.useAltToZoom) {\r\n                        camera.inertialRadiusOffset -= 1 / this.zoomingSensibility;\r\n                    } else {\r\n                        camera.inertialBetaOffset += this.angularSpeed;\r\n                    }\r\n                } else if (this.keysReset.indexOf(keyCode) !== -1) {\r\n                    if (camera.useInputToRestoreState) {\r\n                        camera.restoreState();\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of the current input.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"ArcRotateCameraKeyboardMoveInput\";\r\n    }\r\n\r\n    /**\r\n     * Get the friendly name associated with the input class.\r\n     * @returns the input friendly name\r\n     */\r\n    public getSimpleName(): string {\r\n        return \"keyboard\";\r\n    }\r\n}\r\n\r\n(<any>CameraInputTypes)[\"ArcRotateCameraKeyboardMoveInput\"] = ArcRotateCameraKeyboardMoveInput;\r\n"]}