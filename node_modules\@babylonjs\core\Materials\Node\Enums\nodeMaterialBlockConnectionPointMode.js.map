{"version": 3, "file": "nodeMaterialBlockConnectionPointMode.js", "sourceRoot": "", "sources": ["../../../../../../lts/core/generated/Materials/Node/Enums/nodeMaterialBlockConnectionPointMode.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,CAAN,IAAY,oCASX;AATD,WAAY,oCAAoC;IAC5C,0BAA0B;IAC1B,qGAAO,CAAA;IACP,gCAAgC;IAChC,yGAAS,CAAA;IACT,6DAA6D;IAC7D,qGAAO,CAAA;IACP,wBAAwB;IACxB,yGAAS,CAAA;AACb,CAAC,EATW,oCAAoC,KAApC,oCAAoC,QAS/C", "sourcesContent": ["/**\r\n * Enum defining the mode of a NodeMaterialBlockConnectionPoint\r\n */\r\nexport enum NodeMaterialBlockConnectionPointMode {\r\n    /** Value is an uniform */\r\n    Uniform,\r\n    /** Value is a mesh attribute */\r\n    Attribute,\r\n    /** Value is a varying between vertex and fragment shaders */\r\n    Varying,\r\n    /** Mode is undefined */\r\n    Undefined,\r\n}\r\n"]}