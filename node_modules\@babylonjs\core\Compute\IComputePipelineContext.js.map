{"version": 3, "file": "IComputePipelineContext.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Compute/IComputePipelineContext.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\r\n * Class used to store and describe the pipeline context associated with a compute effect\r\n */\r\nexport interface IComputePipelineContext {\r\n    /**\r\n     * Gets a boolean indicating that this pipeline context is supporting asynchronous creating\r\n     */\r\n    isAsync: boolean;\r\n    /**\r\n     * Gets a boolean indicating that the context is ready to be used (like shader / pipeline are compiled and ready for instance)\r\n     */\r\n    isReady: boolean;\r\n\r\n    /** @internal */\r\n    _name?: string;\r\n\r\n    /** @internal */\r\n    _getComputeShaderCode(): string | null;\r\n\r\n    /** Releases the resources associated with the pipeline. */\r\n    dispose(): void;\r\n}\r\n"]}