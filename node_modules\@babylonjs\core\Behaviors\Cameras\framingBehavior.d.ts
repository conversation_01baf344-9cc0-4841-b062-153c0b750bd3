import type { Behavior } from "../../Behaviors/behavior";
import type { ArcRotateCamera } from "../../Cameras/arcRotateCamera";
import { ExponentialEase } from "../../Animations/easing";
import { Observable } from "../../Misc/observable";
import type { Nullable } from "../../types";
import type { AbstractMesh } from "../../Meshes/abstractMesh";
import { Vector3 } from "../../Maths/math.vector";
/**
 * The framing behavior (FramingBehavior) is designed to automatically position an ArcRotateCamera when its target is set to a mesh. It is also useful if you want to prevent the camera to go under a virtual horizontal plane.
 * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors/cameraBehaviors#framing-behavior
 */
export declare class FramingBehavior implements Behavior<ArcRotateCamera> {
    /**
     * Gets the name of the behavior.
     */
    get name(): string;
    /**
     * An event triggered when the animation to zoom on target mesh has ended
     */
    onTargetFramingAnimationEndObservable: Observable<void>;
    private _mode;
    private _radiusScale;
    private _positionScale;
    private _defaultElevation;
    private _elevationReturnTime;
    private _elevationReturnWaitTime;
    private _zoomStopsAnimation;
    private _framingTime;
    /**
     * The easing function used by animations
     */
    static EasingFunction: ExponentialEase;
    /**
     * The easing mode used by animations
     */
    static EasingMode: number;
    /**
     * Sets the current mode used by the behavior
     */
    set mode(mode: number);
    /**
     * Gets current mode used by the behavior.
     */
    get mode(): number;
    /**
     * Sets the scale applied to the radius (1 by default)
     */
    set radiusScale(radius: number);
    /**
     * Gets the scale applied to the radius
     */
    get radiusScale(): number;
    /**
     * Sets the scale to apply on Y axis to position camera focus. 0.5 by default which means the center of the bounding box.
     */
    set positionScale(scale: number);
    /**
     * Gets the scale to apply on Y axis to position camera focus. 0.5 by default which means the center of the bounding box.
     */
    get positionScale(): number;
    /**
     * Sets the angle above/below the horizontal plane to return to when the return to default elevation idle
     * behaviour is triggered, in radians.
     */
    set defaultElevation(elevation: number);
    /**
     * Gets the angle above/below the horizontal plane to return to when the return to default elevation idle
     * behaviour is triggered, in radians.
     */
    get defaultElevation(): number;
    /**
     * Sets the time (in milliseconds) taken to return to the default beta position.
     * Negative value indicates camera should not return to default.
     */
    set elevationReturnTime(speed: number);
    /**
     * Gets the time (in milliseconds) taken to return to the default beta position.
     * Negative value indicates camera should not return to default.
     */
    get elevationReturnTime(): number;
    /**
     * Sets the delay (in milliseconds) taken before the camera returns to the default beta position.
     */
    set elevationReturnWaitTime(time: number);
    /**
     * Gets the delay (in milliseconds) taken before the camera returns to the default beta position.
     */
    get elevationReturnWaitTime(): number;
    /**
     * Sets the flag that indicates if user zooming should stop animation.
     */
    set zoomStopsAnimation(flag: boolean);
    /**
     * Gets the flag that indicates if user zooming should stop animation.
     */
    get zoomStopsAnimation(): boolean;
    /**
     * Sets the transition time when framing the mesh, in milliseconds
     */
    set framingTime(time: number);
    /**
     * Gets the transition time when framing the mesh, in milliseconds
     */
    get framingTime(): number;
    /**
     * Define if the behavior should automatically change the configured
     * camera limits and sensibilities.
     */
    autoCorrectCameraLimitsAndSensibility: boolean;
    private _onPrePointerObservableObserver;
    private _onAfterCheckInputsObserver;
    private _onMeshTargetChangedObserver;
    private _attachedCamera;
    private _isPointerDown;
    private _lastInteractionTime;
    /**
     * Initializes the behavior.
     */
    init(): void;
    /**
     * Attaches the behavior to its arc rotate camera.
     * @param camera Defines the camera to attach the behavior to
     */
    attach(camera: ArcRotateCamera): void;
    /**
     * Detaches the behavior from its current arc rotate camera.
     */
    detach(): void;
    private _animatables;
    private _betaIsAnimating;
    private _betaTransition;
    private _radiusTransition;
    private _vectorTransition;
    /**
     * Targets the given mesh and updates zoom level accordingly.
     * @param mesh  The mesh to target.
     * @param focusOnOriginXZ Determines if the camera should focus on 0 in the X and Z axis instead of the mesh
     * @param onAnimationEnd Callback triggered at the end of the framing animation
     */
    zoomOnMesh(mesh: AbstractMesh, focusOnOriginXZ?: boolean, onAnimationEnd?: Nullable<() => void>): void;
    /**
     * Targets the given mesh with its children and updates zoom level accordingly.
     * @param mesh  The mesh to target.
     * @param focusOnOriginXZ Determines if the camera should focus on 0 in the X and Z axis instead of the mesh
     * @param onAnimationEnd Callback triggered at the end of the framing animation
     */
    zoomOnMeshHierarchy(mesh: AbstractMesh, focusOnOriginXZ?: boolean, onAnimationEnd?: Nullable<() => void>): void;
    /**
     * Targets the given meshes with their children and updates zoom level accordingly.
     * @param meshes  The mesh to target.
     * @param focusOnOriginXZ Determines if the camera should focus on 0 in the X and Z axis instead of the mesh
     * @param onAnimationEnd Callback triggered at the end of the framing animation
     */
    zoomOnMeshesHierarchy(meshes: AbstractMesh[], focusOnOriginXZ?: boolean, onAnimationEnd?: Nullable<() => void>): void;
    /**
     * Targets the bounding box info defined by its extends and updates zoom level accordingly.
     * @param minimumWorld Determines the smaller position of the bounding box extend
     * @param maximumWorld Determines the bigger position of the bounding box extend
     * @param focusOnOriginXZ Determines if the camera should focus on 0 in the X and Z axis instead of the mesh
     * @param onAnimationEnd Callback triggered at the end of the framing animation
     */
    zoomOnBoundingInfo(minimumWorld: Vector3, maximumWorld: Vector3, focusOnOriginXZ?: boolean, onAnimationEnd?: Nullable<() => void>): void;
    /**
     * Calculates the lowest radius for the camera based on the bounding box of the mesh.
     * @param minimumWorld
     * @param maximumWorld
     * @returns The minimum distance from the primary mesh's center point at which the camera must be kept in order
     *		 to fully enclose the mesh in the viewing frustum.
     */
    protected _calculateLowerRadiusFromModelBoundingSphere(minimumWorld: Vector3, maximumWorld: Vector3): number;
    /**
     * Keeps the camera above the ground plane. If the user pulls the camera below the ground plane, the camera
     * is automatically returned to its default position (expected to be above ground plane).
     */
    private _maintainCameraAboveGround;
    /**
     * Returns the frustum slope based on the canvas ratio and camera FOV
     * @returns The frustum slope represented as a Vector2 with X and Y slopes
     */
    private _getFrustumSlope;
    /**
     * Removes all animation locks. Allows new animations to be added to any of the arcCamera properties.
     */
    private _clearAnimationLocks;
    /**
     *  Applies any current user interaction to the camera. Takes into account maximum alpha rotation.
     */
    private _applyUserInteraction;
    /**
     * Stops and removes all animations that have been applied to the camera
     */
    stopAllAnimations(): void;
    /**
     * Gets a value indicating if the user is moving the camera
     */
    get isUserIsMoving(): boolean;
    /**
     * The camera can move all the way towards the mesh.
     */
    static IgnoreBoundsSizeMode: number;
    /**
     * The camera is not allowed to zoom closer to the mesh than the point at which the adjusted bounding sphere touches the frustum sides
     */
    static FitFrustumSidesMode: number;
}
