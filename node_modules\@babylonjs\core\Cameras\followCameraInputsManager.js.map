{"version": 3, "file": "followCameraInputsManager.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Cameras/followCameraInputsManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAE5D,OAAO,EAAE,6BAA6B,EAAE,MAAM,wCAAwC,CAAC;AACvF,OAAO,EAAE,2BAA2B,EAAE,MAAM,sCAAsC,CAAC;AACnF,OAAO,EAAE,yBAAyB,EAAE,MAAM,oCAAoC,CAAC;AAE/E;;;;GAIG;AACH,MAAM,OAAO,yBAA0B,SAAQ,mBAAiC;IAC5E;;;OAGG;IACH,YAAY,MAAoB;QAC5B,KAAK,CAAC,MAAM,CAAC,CAAC;IAClB,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,IAAI,CAAC,GAAG,CAAC,IAAI,6BAA6B,EAAE,CAAC,CAAC;QAC9C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,IAAI,CAAC,GAAG,CAAC,IAAI,2BAA2B,EAAE,CAAC,CAAC;QAC5C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,IAAI,CAAC,GAAG,CAAC,IAAI,yBAAyB,EAAE,CAAC,CAAC;QAC1C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,sBAAsB;QACzB,OAAO,CAAC,IAAI,CAAC,iEAAiE,CAAC,CAAC;QAChF,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ", "sourcesContent": ["import { CameraInputsManager } from \"./cameraInputsManager\";\r\nimport type { FollowCamera } from \"./followCamera\";\r\nimport { FollowCameraKeyboardMoveInput } from \"./Inputs/followCameraKeyboardMoveInput\";\r\nimport { FollowCameraMouseWheelInput } from \"./Inputs/followCameraMouseWheelInput\";\r\nimport { FollowCameraPointersInput } from \"./Inputs/followCameraPointersInput\";\r\n\r\n/**\r\n * Default Inputs manager for the FollowCamera.\r\n * It groups all the default supported inputs for ease of use.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n */\r\nexport class FollowCameraInputsManager extends CameraInputsManager<FollowCamera> {\r\n    /**\r\n     * Instantiates a new FollowCameraInputsManager.\r\n     * @param camera Defines the camera the inputs belong to\r\n     */\r\n    constructor(camera: FollowCamera) {\r\n        super(camera);\r\n    }\r\n\r\n    /**\r\n     * Add keyboard input support to the input manager.\r\n     * @returns the current input manager\r\n     */\r\n    public addKeyboard(): FollowCameraInputsManager {\r\n        this.add(new FollowCameraKeyboardMoveInput());\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Add mouse wheel input support to the input manager.\r\n     * @returns the current input manager\r\n     */\r\n    public addMouseWheel(): FollowCameraInputsManager {\r\n        this.add(new FollowCameraMouseWheelInput());\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Add pointers input support to the input manager.\r\n     * @returns the current input manager\r\n     */\r\n    public addPointers(): FollowCameraInputsManager {\r\n        this.add(new FollowCameraPointersInput());\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Add orientation input support to the input manager.\r\n     * @returns the current input manager\r\n     */\r\n    public addVRDeviceOrientation(): FollowCameraInputsManager {\r\n        console.warn(\"DeviceOrientation support not yet implemented for FollowCamera.\");\r\n        return this;\r\n    }\r\n}\r\n"]}