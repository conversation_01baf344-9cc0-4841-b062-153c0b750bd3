/* Musicmodules/music.css - Modern Glassmorphism Redesign */

/* ------------------------- */
/* --- 变量和全局样式 --- */
/* ------------------------- */

:root {
    /* 从主样式继承颜色变量 (已移至 body 选择器) */
    
    /* 玻璃效果变量 (深色主题) */
    --glass-bg: rgba(20, 20, 20, 0.6);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: rgba(0, 0, 0, 0.3);
    --hover-bg: rgba(255, 255, 255, 0.08);

    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
}

/* 浅色主题下的变量覆盖 */
body.light-theme {
    --glass-bg: rgba(255, 255, 255, 0.65);
    --glass-border: rgba(0, 0, 0, 0.1);
    --glass-shadow: rgba(0, 0, 0, 0.1);
    --hover-bg: rgba(0, 0, 0, 0.05);
}

body {
    /* 从主样式继承颜色变量 */
    --music-bg: var(--primary-bg);
    --music-text: var(--primary-text);
    --music-text-secondary: var(--secondary-text);
    --music-highlight: var(--highlight-text);
    --music-border: var(--border-color);

    margin: 0;
    font-family: var(--font-family);
    color: var(--music-text);
    background-color: transparent; /* body背景透明，让主程序背景透出 */
    overflow: hidden; /* 防止出现不必要的滚动条 */
}

/* ------------------------- */
/* --- 布局和背景 --- */
/* ------------------------- */

#player-background {
    position: fixed;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background-size: cover;
    background-position: center;
    filter: blur(25px) brightness(0.6);
    transform: scale(1.1);
    transition: background-image 0.8s ease-in-out;
    z-index: -1; /* 置于最底层 */
}

.music-player-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    padding: 20px;
    box-sizing: border-box;
    gap: 20px; /* 控制上下两块玻璃的间距 */
}

/* 核心玻璃面板样式 */
.glass-panel {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 8px 32px 0 var(--glass-shadow);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    transition: background 0.3s ease, border 0.3s ease;
}

/* ------------------------- */
/* --- 播放器控制区 --- */
/* ------------------------- */

.music-player {
    flex-shrink: 0; /* 防止被压缩 */
}

.track-info {
    display: flex;
    align-items: flex-start; /* 改为顶部对齐，以解决按钮垂直位置问题 */
    gap: 20px;
    margin-bottom: 20px;
}

#share-btn {
    margin-left: auto; /* 将按钮推到右侧 */
}

.album-art-wrapper {
    width: 90px;
    height: 90px;
    flex-shrink: 0;
    border-radius: 16px;
    box-shadow: 0 4px 15px var(--glass-shadow);
    transition: transform 0.3s ease;
}
.album-art-wrapper:hover {
    transform: scale(1.05);
}

.album-art {
    width: 100%;
    height: 100%;
    border-radius: 16px;
    background-size: cover;
    background-position: center;
    background-image: url('../assets/musicdark.jpeg'); /* 默认封面 */
    transition: background-image 0.5s ease-in-out;
}
body.light-theme .album-art {
    background-image: url('../assets/musiclight.jpeg');
}

.track-details {
    overflow: hidden; /* 防止文字过长溢出 */
}

.track-title {
    font-size: 1.5em;
    font-weight: 600;
    color: var(--music-text);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.track-artist {
    font-size: 1em;
    color: var(--music-text-secondary);
    font-weight: 400;
    margin-top: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}


/* 进度条 */
.progress-section {
    margin-bottom: 15px;
}

/* 可视化工具样式 */
#visualizer {
    width: 100%;
    height: 80px;
    display: block;
    margin-bottom: 10px;
}

.progress-container {
    width: 100%;
    padding: 5px 0; /* 增加点击区域 */
    cursor: pointer;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background-color: var(--hover-bg);
    border-radius: 3px;
    overflow: hidden;
}

.progress {
    width: 0;
    height: 100%;
    background-color: var(--music-highlight);
    border-radius: 3px;
    transition: width 0.1s linear;
}

.time-display {
    display: flex;
    justify-content: space-between;
    font-size: 0.75em;
    color: var(--music-text-secondary);
    margin-top: 6px;
}

/* 控制按钮 */
.controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.control-btn {
    background: transparent;
    border: none;
    color: var(--music-text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.3s ease, background-color 0.3s ease, transform 0.2s ease;
}

.control-btn:hover {
    color: var(--music-text);
    background-color: var(--hover-bg);
}
.control-btn:active {
    transform: scale(0.9);
}

.control-btn.play-btn {
    background-color: var(--music-highlight);
    color: white;
    width: 56px;
    height: 56px;
    border-radius: 50%;
}
.control-btn.play-btn:hover {
    filter: brightness(1.1);
}
.control-btn.play-btn svg {
    width: 32px;
    height: 32px;
}

.play-icon { display: block; }
.pause-icon { display: none; }
.control-btn.is-playing .play-icon { display: none; }
.control-btn.is-playing .pause-icon { display: block; }

/* 音量控制 */
.volume-control {
    display: flex;
    align-items: center;
    gap: 8px;
}

.volume-icon { display: block; }
.mute-icon { display: none; }
#volume-btn.is-muted .volume-icon { display: none; }
#volume-btn.is-muted .mute-icon { display: block; }

#volume-slider {
    -webkit-appearance: none;
    appearance: none;
    width: 80px;
    height: 5px;
    background: var(--hover-bg);
    border-radius: 3px;
    outline: none;
    transition: opacity 0.2s;
    cursor: pointer;
}

#volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 14px;
    height: 14px;
    background: var(--music-text);
    border-radius: 50%;
    transition: background 0.3s ease;
}
#volume-slider::-webkit-slider-thumb:hover {
    background: var(--music-highlight);
}
#volume-slider::-moz-range-thumb {
    width: 14px;
    height: 14px;
    background: var(--music-text);
    border-radius: 50%;
    transition: background 0.3s ease;
}
#volume-slider::-moz-range-thumb:hover {
    background: var(--music-highlight);
}


/* 播放模式按钮 */
#mode-btn {
    -webkit-mask-size: cover;
    mask-size: cover;
    background-color: var(--music-text-secondary);
    width: 24px;
    height: 24px;
}
#mode-btn:hover {
    background-color: var(--music-text);
}
#mode-btn.active {
    background-color: var(--music-highlight);
}
#mode-btn.repeat {
    -webkit-mask-image: url('../assets/repeat.svg');
    mask-image: url('../assets/repeat.svg');
}
#mode-btn.repeat-one {
    -webkit-mask-image: url('../assets/repeat-one.svg');
    mask-image: url('../assets/repeat-one.svg');
}
#mode-btn.shuffle {
    -webkit-mask-image: url('../assets/shuffle.svg');
    mask-image: url('../assets/shuffle.svg');
}


/* ------------------------- */
/* --- 播放列表 --- */
/* ------------------------- */

.playlist-container {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 防止内容溢出玻璃面板 */
}

.playlist-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-shrink: 0;
}
.playlist-header h3 {
    margin: 0;
    font-size: 1.2em;
    font-weight: 600;
}
.playlist-actions {
    display: flex;
    gap: 10px;
}

#search-input, #add-folder-btn {
    border: none;
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 0.9em;
}

#search-input {
    background-color: var(--hover-bg);
    color: var(--music-text);
    width: 150px;
    transition: background-color 0.3s ease;
}
#search-input:focus {
    outline: none;
    background-color: rgba(255, 255, 255, 0.15);
}
body.light-theme #search-input:focus {
    background-color: rgba(0, 0, 0, 0.1);
}

#add-folder-btn {
    background-color: var(--music-highlight);
    color: white;
    cursor: pointer;
    transition: filter 0.2s ease;
}
#add-folder-btn:hover {
    filter: brightness(1.15);
}

/* --- New Settings Section Styles --- */
.settings-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px 15px 15px; /* Add padding */
    gap: 20px;
    border-bottom: 1px solid var(--glass-border); /* Add a separator */
    margin-bottom: 10px; /* Space before the list */
    flex-wrap: wrap; /* Allow items to wrap */
}

.device-selection {
    flex-grow: 1; /* Allow it to grow */
    min-width: 180px; /* Minimum width before it gets too small */
}

.device-selection, .wasapi-toggle, .upsampling-selection {
   display: flex;
   align-items: center;
   gap: 10px;
   font-size: 0.9em;
   color: var(--music-text-secondary);
}

#device-select, #upsampling-select {
    background-color: var(--hover-bg);
    color: var(--music-text);
    border: 1px solid var(--glass-border);
    border-radius: 6px;
    padding: 5px 8px;
    outline: none;
    width: 100%; /* Make it fill the container */
    box-sizing: border-box; /* Include padding and border in the element's total width and height */
}

#upsampling-select {
    flex-grow: 1;
}
 
 /* --- Toggle Switch Styles --- */
.switch {
 position: relative;
 display: inline-block;
 width: 44px;
 height: 24px;
}

.switch input {
 opacity: 0;
 width: 0;
 height: 0;
}

.slider {
 position: absolute;
 cursor: pointer;
 top: 0;
 left: 0;
 right: 0;
 bottom: 0;
 background-color: var(--hover-bg);
 transition: .4s;
}

.slider:before {
 position: absolute;
 content: "";
 height: 18px;
 width: 18px;
 left: 3px;
 bottom: 3px;
 background-color: white;
 transition: .4s;
}

input:checked + .slider {
 background-color: var(--music-highlight);
}

input:focus + .slider {
 box-shadow: 0 0 1px var(--music-highlight);
}

input:checked + .slider:before {
 transform: translateX(20px);
}

.slider.round {
 border-radius: 24px;
}

.slider.round:before {
  border-radius: 50%;
}

/* --- New EQ Section Styles --- */
.eq-section {
   padding: 15px;
   border-bottom: 1px solid var(--glass-border);
   margin-bottom: 10px;
}

.eq-header {
   display: flex;
   justify-content: space-between;
   align-items: center;
   margin-bottom: 15px;
}

.eq-header h4 {
    margin: 0;
    font-size: 1em;
    font-weight: 500;
    color: var(--music-text-secondary);
}

.eq-controls {
   display: flex;
   align-items: center;
   gap: 15px;
}

.eq-reset-btn {
   background: transparent;
   border: 1px solid var(--glass-border);
   color: var(--music-text-secondary);
   padding: 4px 10px;
   border-radius: 6px;
   cursor: pointer;
   font-size: 0.8em;
   transition: all 0.2s ease;
}

.eq-reset-btn:hover {
   background-color: var(--hover-bg);
   color: var(--music-text);
}

.eq-bands {
   display: flex;
   justify-content: space-around;
   align-items: center;
   gap: 5px; /* Reduce gap for more bands */
   max-height: 0;
   overflow: hidden;
   transition: max-height 0.4s ease-out, padding 0.4s ease-out;
   padding-top: 0;
}

.eq-section.expanded .eq-bands {
   max-height: 200px; /* A height large enough to show the sliders */
   padding-top: 15px; /* Add some space when expanded */
}

.eq-band {
   display: flex;
   flex-direction: column;
   align-items: center;
   gap: 8px;
   /* Add a container for rotation */
   width: 30px;
   height: 120px;
   justify-content: center;
}

.eq-band label {
   font-size: 0.75em;
   color: var(--music-text-secondary);
}

.eq-band input[type="range"] {
   -webkit-appearance: none;
   appearance: none;
   transform: rotate(-90deg);
   width: 100px; /* This is the new height */
   height: 8px; /* This is the new width */
   background: var(--hover-bg);
   border-radius: 4px;
   outline: none;
   transition: opacity 0.2s;
   cursor: pointer;
}

.eq-band input[type="range"]::-webkit-slider-thumb {
   -webkit-appearance: none;
   appearance: none;
   width: 16px;
   height: 16px;
   background: var(--music-text);
   border-radius: 50%;
   transition: background 0.3s ease;
}
.eq-band input[type="range"]::-webkit-slider-thumb:hover {
   background: var(--music-highlight);
}

.eq-band input[type="range"]::-moz-range-thumb {
   width: 16px;
   height: 16px;
   background: var(--music-text);
   border-radius: 50%;
   transition: background 0.3s ease;
}
.eq-band input[type="range"]::-moz-range-thumb:hover {
   background: var(--music-highlight);
}

.playlist {
    list-style: none;
    padding: 0;
    margin: 0;
    overflow-y: auto; /* 列表内容可滚动 */
    flex-grow: 1;
}
/* 自定义滚动条样式 */
.playlist::-webkit-scrollbar {
    width: 6px;
}
.playlist::-webkit-scrollbar-track {
    background: transparent;
}
.playlist::-webkit-scrollbar-thumb {
    background: var(--hover-bg);
    border-radius: 3px;
}
.playlist::-webkit-scrollbar-thumb:hover {
    background: var(--glass-border);
}

.playlist li {
    padding: 12px 15px;
    cursor: pointer;
    border-radius: 8px;
    margin-bottom: 4px; /* 代替边框线 */
    transition: background-color 0.2s ease;
    font-size: 0.95em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.playlist li:hover {
    background-color: var(--hover-bg);
}
.playlist li.active {
    background-color: var(--music-highlight);
    color: white;
    font-weight: 500;
}

/* ------------------------- */
/* --- 加载指示器 --- */
/* ------------------------- */

.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 15px;
    color: var(--music-text);
}

.spinner {
    border: 4px solid var(--hover-bg);
    border-top: 4px solid var(--music-highlight);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.scan-progress-container {
    width: 200px;
    height: 8px;
    background-color: var(--hover-bg);
    border-radius: 4px;
    overflow: hidden;
}

.scan-progress-bar {
    width: 0%;
    height: 100%;
    background-color: var(--music-highlight);
    transition: width 0.1s linear;
}

.scan-progress-label {
    font-size: 0.9em;
    color: var(--music-text-secondary);
}
