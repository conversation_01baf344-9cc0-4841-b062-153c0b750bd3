{"version": 3, "file": "arcRotateCameraPointersInput.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Cameras/Inputs/arcRotateCameraPointersInput.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAElD,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AACrE,OAAO,EAAE,uBAAuB,EAAE,MAAM,8CAA8C,CAAC;AAIvF;;;GAGG;AACH,MAAM,OAAO,4BAA6B,SAAQ,uBAAuB;IAAzE;;QAmBI;;WAEG;QAEI,YAAO,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3B;;;WAGG;QAEI,wBAAmB,GAAG,MAAM,CAAC;QAEpC;;;WAGG;QAEI,wBAAmB,GAAG,MAAM,CAAC;QAEpC;;WAEG;QAEI,mBAAc,GAAG,IAAI,CAAC;QAE7B;;;;;WAKG;QAEI,yBAAoB,GAAG,CAAC,CAAC;QAEhC;;;;;WAKG;QAEI,wBAAmB,GAAY,KAAK,CAAC;QAE5C;;WAEG;QAEI,cAAS,GAAY,IAAI,CAAC;QAEjC;;WAEG;QAEI,uBAAkB,GAAW,MAAM,CAAC;QAE3C;;WAEG;QAEI,sBAAiB,GAAY,IAAI,CAAC;QAEzC;;;WAGG;QAEI,yBAAoB,GAAY,IAAI,CAAC;QAE5C;;WAEG;QACI,iBAAY,GAAG,IAAI,CAAC;QAEnB,gBAAW,GAAY,KAAK,CAAC;QAC7B,4BAAuB,GAAW,CAAC,CAAC;QACpC,gBAAW,GAAY,KAAK,CAAC;IAkJzC,CAAC;IAtOG;;;OAGG;IACI,YAAY;QACf,OAAO,8BAA8B,CAAC;IAC1C,CAAC;IAgFD;;;;OAIG;IACK,yBAAyB,CAAC,6BAAqD,EAAE,qBAA6C;QAClI,IAAI,IAAI,CAAC,kBAAkB,KAAK,CAAC,IAAI,6BAA6B,IAAI,qBAAqB,EAAE;YACzF,MAAM,UAAU,GAAG,qBAAqB,CAAC,CAAC,GAAG,6BAA6B,CAAC,CAAC,CAAC;YAC7E,MAAM,UAAU,GAAG,qBAAqB,CAAC,CAAC,GAAG,6BAA6B,CAAC,CAAC,CAAC;YAC7E,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC;YACtE,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC;SACxE;IACL,CAAC;IAED;;;;OAIG;IACK,iBAAiB,CAAC,4BAAoC,EAAE,oBAA4B;QACxF,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,4BAA4B,CAAC,qBAAqB,CAAC;QACxF,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SAC7G;aAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAClC,IAAI,CAAC,MAAM,CAAC,oBAAoB,IAAI,CAAC,oBAAoB,GAAG,4BAA4B,CAAC,GAAG,KAAK,GAAG,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC;SAC1I;aAAM;YACH,IAAI,CAAC,MAAM,CAAC,oBAAoB;gBAC5B,CAAC,oBAAoB,GAAG,4BAA4B,CAAC;oBACrD,CAAC,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SAC1H;IACL,CAAC;IAED;;;;;OAKG;IACI,OAAO,CAAC,KAA6B,EAAE,OAAe,EAAE,OAAe;QAC1E,IAAI,IAAI,CAAC,kBAAkB,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,EAAE;YAC1G,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC;YACnE,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC;SACrE;aAAM;YACH,IAAI,CAAC,MAAM,CAAC,mBAAmB,IAAI,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC;YACtE,IAAI,CAAC,MAAM,CAAC,kBAAkB,IAAI,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC;SACxE;IACL,CAAC;IAED;;OAEG;IACI,WAAW;QACd,IAAI,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE;YACpC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;SAC9B;IACL,CAAC;IAED;;;;;;;;OAQG;IACI,YAAY,CACf,MAA8B,EAC9B,MAA8B,EAC9B,4BAAoC,EACpC,oBAA4B,EAC5B,6BAAqD,EACrD,qBAA6C;QAE7C,IAAI,4BAA4B,KAAK,CAAC,IAAI,6BAA6B,KAAK,IAAI,EAAE;YAC9E,kDAAkD;YAClD,2CAA2C;YAC3C,oEAAoE;YACpE,OAAO;SACV;QACD,IAAI,oBAAoB,KAAK,CAAC,IAAI,qBAAqB,KAAK,IAAI,EAAE;YAC9D,yDAAyD;YACzD,OAAO;SACV;QAED,oCAAoC;QACpC,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,iBAAiB,CAAC,4BAA4B,EAAE,oBAAoB,CAAC,CAAC;YAC3E,IAAI,CAAC,yBAAyB,CAAC,6BAA6B,EAAE,qBAAqB,CAAC,CAAC;YAErF,kDAAkD;SACrD;aAAM,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,SAAS,EAAE;YACjD,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAE/B,IACI,IAAI,CAAC,WAAW;gBAChB,CAAC,IAAI,CAAC,uBAAuB,GAAG,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,EAChK;gBACE,kEAAkE;gBAClE,IAAI,CAAC,iBAAiB,CAAC,4BAA4B,EAAE,oBAAoB,CAAC,CAAC;gBAE3E,4DAA4D;gBAC5D,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;aAC3B;iBAAM;gBACH,iFAAiF;gBACjF,IAAI,CAAC,yBAAyB,CAAC,6BAA6B,EAAE,qBAAqB,CAAC,CAAC;aACxF;YAED,iCAAiC;SACpC;aAAM,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC/B,IAAI,CAAC,yBAAyB,CAAC,6BAA6B,EAAE,qBAAqB,CAAC,CAAC;YAErF,iCAAiC;SACpC;aAAM,IAAI,IAAI,CAAC,SAAS,EAAE;YACvB,IAAI,CAAC,iBAAiB,CAAC,4BAA4B,EAAE,oBAAoB,CAAC,CAAC;SAC9E;IACL,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,GAAkB;QAClC,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC;IACtE,CAAC;IAED;;;OAGG;IACI,UAAU;QACb,IAAI,CAAC,uBAAuB,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,WAAW;QACd,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,uBAAuB,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC7B,CAAC;;AA1OD;;GAEG;AACW,kDAAqB,GAAW,KAAK,CAAC;AAcpD;IADC,SAAS,EAAE;6DACe;AAO3B;IADC,SAAS,EAAE;yEACwB;AAOpC;IADC,SAAS,EAAE;yEACwB;AAMpC;IADC,SAAS,EAAE;oEACiB;AAS7B;IADC,SAAS,EAAE;0EACoB;AAShC;IADC,SAAS,EAAE;yEACgC;AAM5C;IADC,SAAS,EAAE;+DACqB;AAMjC;IADC,SAAS,EAAE;wEAC+B;AAM3C;IADC,SAAS,EAAE;uEAC6B;AAOzC;IADC,SAAS,EAAE;0EACgC;AA4J1C,gBAAiB,CAAC,8BAA8B,CAAC,GAAG,4BAA4B,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { serialize } from \"../../Misc/decorators\";\r\nimport type { ArcRotateCamera } from \"../../Cameras/arcRotateCamera\";\r\nimport { CameraInputTypes } from \"../../Cameras/cameraInputsManager\";\r\nimport { BaseCameraPointersInput } from \"../../Cameras/Inputs/BaseCameraPointersInput\";\r\nimport type { PointerTouch } from \"../../Events/pointerEvents\";\r\nimport type { IPointerEvent } from \"../../Events/deviceInputEvents\";\r\n\r\n/**\r\n * Manage the pointers inputs to control an arc rotate camera.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n */\r\nexport class ArcRotateCameraPointersInput extends BaseCameraPointersInput {\r\n    /**\r\n     * Defines the camera the input is attached to.\r\n     */\r\n    public camera: ArcRotateCamera;\r\n\r\n    /**\r\n     * The minimum radius used for pinch, to avoid radius lock at 0\r\n     */\r\n    public static MinimumRadiusForPinch: number = 0.001;\r\n\r\n    /**\r\n     * Gets the class name of the current input.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"ArcRotateCameraPointersInput\";\r\n    }\r\n\r\n    /**\r\n     * Defines the buttons associated with the input to handle camera move.\r\n     */\r\n    @serialize()\r\n    public buttons = [0, 1, 2];\r\n\r\n    /**\r\n     * Defines the pointer angular sensibility  along the X axis or how fast is\r\n     * the camera rotating.\r\n     */\r\n    @serialize()\r\n    public angularSensibilityX = 1000.0;\r\n\r\n    /**\r\n     * Defines the pointer angular sensibility along the Y axis or how fast is\r\n     * the camera rotating.\r\n     */\r\n    @serialize()\r\n    public angularSensibilityY = 1000.0;\r\n\r\n    /**\r\n     * Defines the pointer pinch precision or how fast is the camera zooming.\r\n     */\r\n    @serialize()\r\n    public pinchPrecision = 12.0;\r\n\r\n    /**\r\n     * pinchDeltaPercentage will be used instead of pinchPrecision if different\r\n     * from 0.\r\n     * It defines the percentage of current camera.radius to use as delta when\r\n     * pinch zoom is used.\r\n     */\r\n    @serialize()\r\n    public pinchDeltaPercentage = 0;\r\n\r\n    /**\r\n     * When useNaturalPinchZoom is true, multi touch zoom will zoom in such\r\n     * that any object in the plane at the camera's target point will scale\r\n     * perfectly with finger motion.\r\n     * Overrides pinchDeltaPercentage and pinchPrecision.\r\n     */\r\n    @serialize()\r\n    public useNaturalPinchZoom: boolean = false;\r\n\r\n    /**\r\n     * Defines whether zoom (2 fingers pinch) is enabled through multitouch\r\n     */\r\n    @serialize()\r\n    public pinchZoom: boolean = true;\r\n\r\n    /**\r\n     * Defines the pointer panning sensibility or how fast is the camera moving.\r\n     */\r\n    @serialize()\r\n    public panningSensibility: number = 1000.0;\r\n\r\n    /**\r\n     * Defines whether panning (2 fingers swipe) is enabled through multitouch.\r\n     */\r\n    @serialize()\r\n    public multiTouchPanning: boolean = true;\r\n\r\n    /**\r\n     * Defines whether panning is enabled for both pan (2 fingers swipe) and\r\n     * zoom (pinch) through multitouch.\r\n     */\r\n    @serialize()\r\n    public multiTouchPanAndZoom: boolean = true;\r\n\r\n    /**\r\n     * Revers pinch action direction.\r\n     */\r\n    public pinchInwards = true;\r\n\r\n    private _isPanClick: boolean = false;\r\n    private _twoFingerActivityCount: number = 0;\r\n    private _isPinching: boolean = false;\r\n\r\n    /**\r\n     * Move camera from multi touch panning positions.\r\n     * @param previousMultiTouchPanPosition\r\n     * @param multiTouchPanPosition\r\n     */\r\n    private _computeMultiTouchPanning(previousMultiTouchPanPosition: Nullable<PointerTouch>, multiTouchPanPosition: Nullable<PointerTouch>): void {\r\n        if (this.panningSensibility !== 0 && previousMultiTouchPanPosition && multiTouchPanPosition) {\r\n            const moveDeltaX = multiTouchPanPosition.x - previousMultiTouchPanPosition.x;\r\n            const moveDeltaY = multiTouchPanPosition.y - previousMultiTouchPanPosition.y;\r\n            this.camera.inertialPanningX += -moveDeltaX / this.panningSensibility;\r\n            this.camera.inertialPanningY += moveDeltaY / this.panningSensibility;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Move camera from pinch zoom distances.\r\n     * @param previousPinchSquaredDistance\r\n     * @param pinchSquaredDistance\r\n     */\r\n    private _computePinchZoom(previousPinchSquaredDistance: number, pinchSquaredDistance: number): void {\r\n        const radius = this.camera.radius || ArcRotateCameraPointersInput.MinimumRadiusForPinch;\r\n        if (this.useNaturalPinchZoom) {\r\n            this.camera.radius = (radius * Math.sqrt(previousPinchSquaredDistance)) / Math.sqrt(pinchSquaredDistance);\r\n        } else if (this.pinchDeltaPercentage) {\r\n            this.camera.inertialRadiusOffset += (pinchSquaredDistance - previousPinchSquaredDistance) * 0.001 * radius * this.pinchDeltaPercentage;\r\n        } else {\r\n            this.camera.inertialRadiusOffset +=\r\n                (pinchSquaredDistance - previousPinchSquaredDistance) /\r\n                ((this.pinchPrecision * (this.pinchInwards ? 1 : -1) * (this.angularSensibilityX + this.angularSensibilityY)) / 2);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Called on pointer POINTERMOVE event if only a single touch is active.\r\n     * @param point\r\n     * @param offsetX\r\n     * @param offsetY\r\n     */\r\n    public onTouch(point: Nullable<PointerTouch>, offsetX: number, offsetY: number): void {\r\n        if (this.panningSensibility !== 0 && ((this._ctrlKey && this.camera._useCtrlForPanning) || this._isPanClick)) {\r\n            this.camera.inertialPanningX += -offsetX / this.panningSensibility;\r\n            this.camera.inertialPanningY += offsetY / this.panningSensibility;\r\n        } else {\r\n            this.camera.inertialAlphaOffset -= offsetX / this.angularSensibilityX;\r\n            this.camera.inertialBetaOffset -= offsetY / this.angularSensibilityY;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Called on pointer POINTERDOUBLETAP event.\r\n     */\r\n    public onDoubleTap() {\r\n        if (this.camera.useInputToRestoreState) {\r\n            this.camera.restoreState();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Called on pointer POINTERMOVE event if multiple touches are active.\r\n     * @param pointA\r\n     * @param pointB\r\n     * @param previousPinchSquaredDistance\r\n     * @param pinchSquaredDistance\r\n     * @param previousMultiTouchPanPosition\r\n     * @param multiTouchPanPosition\r\n     */\r\n    public onMultiTouch(\r\n        pointA: Nullable<PointerTouch>,\r\n        pointB: Nullable<PointerTouch>,\r\n        previousPinchSquaredDistance: number,\r\n        pinchSquaredDistance: number,\r\n        previousMultiTouchPanPosition: Nullable<PointerTouch>,\r\n        multiTouchPanPosition: Nullable<PointerTouch>\r\n    ): void {\r\n        if (previousPinchSquaredDistance === 0 && previousMultiTouchPanPosition === null) {\r\n            // First time this method is called for new pinch.\r\n            // Next time this is called there will be a\r\n            // previousPinchSquaredDistance and pinchSquaredDistance to compare.\r\n            return;\r\n        }\r\n        if (pinchSquaredDistance === 0 && multiTouchPanPosition === null) {\r\n            // Last time this method is called at the end of a pinch.\r\n            return;\r\n        }\r\n\r\n        // Zoom and panning enabled together\r\n        if (this.multiTouchPanAndZoom) {\r\n            this._computePinchZoom(previousPinchSquaredDistance, pinchSquaredDistance);\r\n            this._computeMultiTouchPanning(previousMultiTouchPanPosition, multiTouchPanPosition);\r\n\r\n            // Zoom and panning enabled but only one at a time\r\n        } else if (this.multiTouchPanning && this.pinchZoom) {\r\n            this._twoFingerActivityCount++;\r\n\r\n            if (\r\n                this._isPinching ||\r\n                (this._twoFingerActivityCount < 20 && Math.abs(Math.sqrt(pinchSquaredDistance) - Math.sqrt(previousPinchSquaredDistance)) > this.camera.pinchToPanMaxDistance)\r\n            ) {\r\n                // Since pinch has not been active long, assume we intend to zoom.\r\n                this._computePinchZoom(previousPinchSquaredDistance, pinchSquaredDistance);\r\n\r\n                // Since we are pinching, remain pinching on next iteration.\r\n                this._isPinching = true;\r\n            } else {\r\n                // Pause between pinch starting and moving implies not a zoom event. Pan instead.\r\n                this._computeMultiTouchPanning(previousMultiTouchPanPosition, multiTouchPanPosition);\r\n            }\r\n\r\n            // Panning enabled, zoom disabled\r\n        } else if (this.multiTouchPanning) {\r\n            this._computeMultiTouchPanning(previousMultiTouchPanPosition, multiTouchPanPosition);\r\n\r\n            // Zoom enabled, panning disabled\r\n        } else if (this.pinchZoom) {\r\n            this._computePinchZoom(previousPinchSquaredDistance, pinchSquaredDistance);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Called each time a new POINTERDOWN event occurs. Ie, for each button\r\n     * press.\r\n     * @param evt\r\n     */\r\n    public onButtonDown(evt: IPointerEvent): void {\r\n        this._isPanClick = evt.button === this.camera._panningMouseButton;\r\n    }\r\n\r\n    /**\r\n     * Called each time a new POINTERUP event occurs. Ie, for each button\r\n     * release.\r\n     */\r\n    public onButtonUp(): void {\r\n        this._twoFingerActivityCount = 0;\r\n        this._isPinching = false;\r\n    }\r\n\r\n    /**\r\n     * Called when window becomes inactive.\r\n     */\r\n    public onLostFocus(): void {\r\n        this._isPanClick = false;\r\n        this._twoFingerActivityCount = 0;\r\n        this._isPinching = false;\r\n    }\r\n}\r\n(<any>CameraInputTypes)[\"ArcRotateCameraPointersInput\"] = ArcRotateCameraPointersInput;\r\n"]}