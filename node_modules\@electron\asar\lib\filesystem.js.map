{"version": 3, "file": "filesystem.js", "sourceRoot": "", "sources": ["../src/filesystem.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uCAAyB;AACzB,2CAA6B;AAC7B,+BAAiC;AACjC,+CAAiC;AAEjC,2CAA8D;AAC9D,8DAA8B;AAG9B,MAAM,UAAU,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAE/B,MAAM,QAAQ,GAAG,IAAA,gBAAS,EAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AAwB5C,MAAa,UAAU;IAMrB,YAAY,GAAW;QACrB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;QAC7C,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB,CAAC;IAED,SAAS;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,aAAa;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED,SAAS,CAAC,MAAuB,EAAE,UAAkB;QACnD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED,uBAAuB,CAAC,CAAS;QAC/B,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;QACvB,MAAM,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC/B,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,IAAI,GAAG,KAAK,GAAG,EAAE,CAAC;gBAChB,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;oBACpB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;wBACrB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;oBACnD,CAAC;oBACD,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACzB,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,+CAA+C,GAAG,CAAC,CAAC,CAAC;gBACvE,CAAC;YACH,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,kBAAkB,CAAC,CAAS;QAC1B,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAC/B,IAAI,CAAC,CAAC,EAAE,CAAC;YACP,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC;QACD,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAA6B,CAAC;QACvF,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED,eAAe,CAAC,CAAS,EAAE,YAAqB;QAC9C,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAA6B,CAAC;QACpE,IAAI,YAAY,EAAE,CAAC;YACjB,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC;QAC/B,CAAC;QACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC/C,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,KAAK,CAAC,UAAU,CACd,CAAS,EACT,YAAqB,EACrB,IAAqB,EACrB,UAEI,EAAE;QAEN,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAA6B,CAAC;QACrF,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAwB,CAAC;QAC/D,IAAI,YAAY,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACrC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YAC3B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,SAAS,GAAG,MAAM,IAAA,4BAAgB,EAAC,CAAC,CAAC,CAAC;YAC3C,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;QAED,IAAI,IAAY,CAAC;QAEjB,MAAM,WAAW,GAAG,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAC9D,IAAI,WAAW,EAAE,CAAC;YAChB,MAAM,MAAM,GAAG,MAAM,oBAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;YACjE,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,GAAG,GAAG,oBAAE,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAC1C,MAAM,UAAU,GAAG,oBAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;YAE1C,MAAM,QAAQ,CAAC,UAAU,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;YAC7C,IAAI,CAAC,WAAW,GAAG;gBACjB,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,MAAM,oBAAE,CAAC,KAAK,CAAC,OAAO,CAAC;aAC9B,CAAC;YACF,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;QACpC,CAAC;aAAM,CAAC;YACN,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QACxB,CAAC;QAED,8DAA8D;QAC9D,IAAI,IAAI,GAAG,UAAU,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;QACrC,IAAI,CAAC,SAAS,GAAG,MAAM,IAAA,4BAAgB,EAAC,CAAC,CAAC,CAAC;QAC3C,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK,EAAE,CAAC;YAC3D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACzB,CAAC;QACD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED,UAAU,CAAC,CAAS,EAAE,YAAqB;QACzC,MAAM,OAAO,GAAG,oBAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACnC,uBAAuB;QACvB,MAAM,UAAU,GAAG,oBAAE,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,oBAAE,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC;QACtF,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,WAAW,IAAI,4BAA4B,CAAC,CAAC;QACnE,CAAC;QACD,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAwB,CAAC;QAC/D,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAA6B,CAAC;QACrF,IAAI,YAAY,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACrC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACvB,CAAC;QACD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,SAAS,CAAC,OAA6B;QACrC,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,MAAM,qBAAqB,GAAG,UAAU,QAAgB,EAAE,QAAyB;YACjF,IAAI,CAAC,CAAC,OAAO,IAAI,QAAQ,CAAC,EAAE,CAAC;gBAC3B,OAAO;YACT,CAAC;YAED,KAAK,MAAM,CAAC,SAAS,EAAE,aAAa,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxE,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;gBAChD,MAAM,SAAS,GACb,UAAU,IAAI,aAAa,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC;gBAC9E,KAAK,CAAC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,SAAS,MAAM,QAAQ,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;gBAChF,qBAAqB,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;YACjD,CAAC;QACH,CAAC,CAAC;QAEF,qBAAqB,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACxC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,CAAC,CAAS,EAAE,cAAuB,IAAI;QAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,MAAM,IAAI,IAAI,IAAI,WAAW,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;QAClD,CAAC;QACD,IAAI,IAAI,EAAE,CAAC;YACT,OAAQ,IAAiC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACxD,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO,CAAC,CAAS,EAAE,cAAuB,IAAI;QAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;QAE1C,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAC1D,CAAC;QAED,oDAAoD;QACpD,IAAI,MAAM,IAAI,IAAI,IAAI,WAAW,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAC9C,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;CACF;AA5LD,gCA4LC"}