import { Observable } from "../Misc/observable";
import type { IDisposable } from "../scene";
import type { IWebXRControllerOptions } from "./webXRInputSource";
import { WebXRInputSource } from "./webXRInputSource";
import type { WebXRSessionManager } from "./webXRSessionManager";
import type { WebXRCamera } from "./webXRCamera";
/**
 * The schema for initialization options of the XR Input class
 */
export interface IWebXRInputOptions {
    /**
     * If set to true no model will be automatically loaded
     */
    doNotLoadControllerMeshes?: boolean;
    /**
     * If set, this profile will be used for all controllers loaded (for example "microsoft-mixed-reality")
     * If not found, the xr input profile data will be used.
     * Profiles are defined here - https://github.com/immersive-web/webxr-input-profiles/
     */
    forceInputProfile?: string;
    /**
     * Do not send a request to the controller repository to load the profile.
     *
     * Instead, use the controllers available in babylon itself.
     */
    disableOnlineControllerRepository?: boolean;
    /**
     * A custom URL for the controllers repository
     */
    customControllersRepositoryURL?: string;
    /**
     * Should the controller model's components not move according to the user input
     */
    disableControllerAnimation?: boolean;
    /**
     * Optional options to pass to the controller. Will be overridden by the Input options where applicable
     */
    controllerOptions?: IWebXRControllerOptions;
}
/**
 * XR input used to track XR inputs such as controllers/rays
 */
export declare class WebXRInput implements IDisposable {
    /**
     * the xr session manager for this session
     */
    xrSessionManager: WebXRSessionManager;
    /**
     * the WebXR camera for this session. Mainly used for teleportation
     */
    xrCamera: WebXRCamera;
    private readonly _options;
    /**
     * XR controllers being tracked
     */
    controllers: Array<WebXRInputSource>;
    private _frameObserver;
    private _sessionEndedObserver;
    private _sessionInitObserver;
    /**
     * Event when a controller has been connected/added
     */
    onControllerAddedObservable: Observable<WebXRInputSource>;
    /**
     * Event when a controller has been removed/disconnected
     */
    onControllerRemovedObservable: Observable<WebXRInputSource>;
    /**
     * Initializes the WebXRInput
     * @param xrSessionManager the xr session manager for this session
     * @param xrCamera the WebXR camera for this session. Mainly used for teleportation
     * @param _options = initialization options for this xr input
     */
    constructor(
    /**
     * the xr session manager for this session
     */
    xrSessionManager: WebXRSessionManager, 
    /**
     * the WebXR camera for this session. Mainly used for teleportation
     */
    xrCamera: WebXRCamera, _options?: IWebXRInputOptions);
    private _onInputSourcesChange;
    private _addAndRemoveControllers;
    /**
     * Disposes of the object
     */
    dispose(): void;
}
