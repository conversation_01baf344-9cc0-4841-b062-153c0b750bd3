{"version": 3, "file": "freeCameraVirtualJoystickInput.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Cameras/Inputs/freeCameraVirtualJoystickInput.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAE3E,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AAErE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAC1D,OAAO,EAAE,uBAAuB,EAAE,MAAM,uCAAuC,CAAC;AAahF;;;GAGG;AACH,uBAAuB,CAAC,SAAS,CAAC,kBAAkB,GAAG;IACnD,IAAI,CAAC,GAAG,CAAC,IAAI,8BAA8B,EAAE,CAAC,CAAC;IAC/C,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,OAAO,8BAA8B;IASvC;;;OAGG;IACI,eAAe;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,gBAAgB;QACnB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAC3B,MAAM,KAAK,GAAG,MAAM,CAAC,wBAAwB,EAAE,GAAG,EAAE,CAAC;YACrD,MAAM,eAAe,GAAG,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC7F,MAAM,cAAc,GAAG,OAAO,CAAC,oBAAoB,CAC/C,IAAI,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,GAAG,KAAK,CAAC,EAC/I,eAAe,CAClB,CAAC;YACF,MAAM,CAAC,eAAe,GAAG,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YACpE,MAAM,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAE5F,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;gBAC7B,IAAI,CAAC,aAAa,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aAClF;YACD,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;gBAC9B,IAAI,CAAC,cAAc,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aACpF;SACJ;IACL,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,IAAI,CAAC,aAAa,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACvD,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAChD,IAAI,CAAC,cAAc,GAAG,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACrD,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,cAAc,CAAC,aAAa,GAAG,IAAI,CAAC;QACzC,IAAI,CAAC,cAAc,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QACjD,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;QACnC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;IACxC,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,gCAAgC,CAAC;IAC5C,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,iBAAiB,CAAC;IAC7B,CAAC;CACJ;AAEK,gBAAiB,CAAC,gCAAgC,CAAC,GAAG,8BAA8B,CAAC", "sourcesContent": ["import { VirtualJoystick, JoystickAxis } from \"../../Misc/virtualJoystick\";\r\nimport type { ICameraInput } from \"../../Cameras/cameraInputsManager\";\r\nimport { CameraInputTypes } from \"../../Cameras/cameraInputsManager\";\r\nimport type { FreeCamera } from \"../../Cameras/freeCamera\";\r\nimport { Matrix, Vector3 } from \"../../Maths/math.vector\";\r\nimport { FreeCameraInputsManager } from \"../../Cameras/freeCameraInputsManager\";\r\n\r\n// Module augmentation to abstract virtual joystick from camera.\r\ndeclare module \"../../Cameras/freeCameraInputsManager\" {\r\n    export interface FreeCameraInputsManager {\r\n        /**\r\n         * Add virtual joystick input support to the input manager.\r\n         * @returns the current input manager\r\n         */\r\n        addVirtualJoystick(): FreeCameraInputsManager;\r\n    }\r\n}\r\n\r\n/**\r\n * Add virtual joystick input support to the input manager.\r\n * @returns the current input manager\r\n */\r\nFreeCameraInputsManager.prototype.addVirtualJoystick = function (): FreeCameraInputsManager {\r\n    this.add(new FreeCameraVirtualJoystickInput());\r\n    return this;\r\n};\r\n\r\n/**\r\n * Manage the Virtual Joystick inputs to control the movement of a free camera.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n */\r\nexport class FreeCameraVirtualJoystickInput implements ICameraInput<FreeCamera> {\r\n    /**\r\n     * Defines the camera the input is attached to.\r\n     */\r\n    public camera: FreeCamera;\r\n\r\n    private _leftjoystick: VirtualJoystick;\r\n    private _rightjoystick: VirtualJoystick;\r\n\r\n    /**\r\n     * Gets the left stick of the virtual joystick.\r\n     * @returns The virtual Joystick\r\n     */\r\n    public getLeftJoystick(): VirtualJoystick {\r\n        return this._leftjoystick;\r\n    }\r\n\r\n    /**\r\n     * Gets the right stick of the virtual joystick.\r\n     * @returns The virtual Joystick\r\n     */\r\n    public getRightJoystick(): VirtualJoystick {\r\n        return this._rightjoystick;\r\n    }\r\n\r\n    /**\r\n     * Update the current camera state depending on the inputs that have been used this frame.\r\n     * This is a dynamically created lambda to avoid the performance penalty of looping for inputs in the render loop.\r\n     */\r\n    public checkInputs() {\r\n        if (this._leftjoystick) {\r\n            const camera = this.camera;\r\n            const speed = camera._computeLocalCameraSpeed() * 50;\r\n            const cameraTransform = Matrix.RotationYawPitchRoll(camera.rotation.y, camera.rotation.x, 0);\r\n            const deltaTransform = Vector3.TransformCoordinates(\r\n                new Vector3(this._leftjoystick.deltaPosition.x * speed, this._leftjoystick.deltaPosition.y * speed, this._leftjoystick.deltaPosition.z * speed),\r\n                cameraTransform\r\n            );\r\n            camera.cameraDirection = camera.cameraDirection.add(deltaTransform);\r\n            camera.cameraRotation = camera.cameraRotation.addVector3(this._rightjoystick.deltaPosition);\r\n\r\n            if (!this._leftjoystick.pressed) {\r\n                this._leftjoystick.deltaPosition = this._leftjoystick.deltaPosition.scale(0.9);\r\n            }\r\n            if (!this._rightjoystick.pressed) {\r\n                this._rightjoystick.deltaPosition = this._rightjoystick.deltaPosition.scale(0.9);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     */\r\n    public attachControl(): void {\r\n        this._leftjoystick = new VirtualJoystick(true);\r\n        this._leftjoystick.setAxisForUpDown(JoystickAxis.Z);\r\n        this._leftjoystick.setAxisForLeftRight(JoystickAxis.X);\r\n        this._leftjoystick.setJoystickSensibility(0.15);\r\n        this._rightjoystick = new VirtualJoystick(false);\r\n        this._rightjoystick.setAxisForUpDown(JoystickAxis.X);\r\n        this._rightjoystick.setAxisForLeftRight(JoystickAxis.Y);\r\n        this._rightjoystick.reverseUpDown = true;\r\n        this._rightjoystick.setJoystickSensibility(0.05);\r\n        this._rightjoystick.setJoystickColor(\"yellow\");\r\n    }\r\n\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    public detachControl(): void {\r\n        this._leftjoystick.releaseCanvas();\r\n        this._rightjoystick.releaseCanvas();\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of the current input.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"FreeCameraVirtualJoystickInput\";\r\n    }\r\n\r\n    /**\r\n     * Get the friendly name associated with the input class.\r\n     * @returns the input friendly name\r\n     */\r\n    public getSimpleName(): string {\r\n        return \"virtualJoystick\";\r\n    }\r\n}\r\n\r\n(<any>CameraInputTypes)[\"FreeCameraVirtualJoystickInput\"] = FreeCameraVirtualJoystickInput;\r\n"]}