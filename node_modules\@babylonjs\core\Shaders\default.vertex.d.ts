import "./ShadersInclude/defaultVertexDeclaration";
import "./ShadersInclude/defaultUboDeclaration";
import "./ShadersInclude/uvAttributeDeclaration";
import "./ShadersInclude/helperFunctions";
import "./ShadersInclude/bonesDeclaration";
import "./ShadersInclude/bakedVertexAnimationDeclaration";
import "./ShadersInclude/instancesDeclaration";
import "./ShadersInclude/prePassVertexDeclaration";
import "./ShadersInclude/mainUVVaryingDeclaration";
import "./ShadersInclude/samplerVertexDeclaration";
import "./ShadersInclude/bumpVertexDeclaration";
import "./ShadersInclude/clipPlaneVertexDeclaration";
import "./ShadersInclude/fogVertexDeclaration";
import "./ShadersInclude/lightVxFragmentDeclaration";
import "./ShadersInclude/lightVxUboDeclaration";
import "./ShadersInclude/morphTargetsVertexGlobalDeclaration";
import "./ShadersInclude/morphTargetsVertexDeclaration";
import "./ShadersInclude/logDepthDeclaration";
import "./ShadersInclude/morphTargetsVertexGlobal";
import "./ShadersInclude/morphTargetsVertex";
import "./ShadersInclude/instancesVertex";
import "./ShadersInclude/bonesVertex";
import "./ShadersInclude/bakedVertexAnimation";
import "./ShadersInclude/prePassVertex";
import "./ShadersInclude/uvVariableDeclaration";
import "./ShadersInclude/samplerVertexImplementation";
import "./ShadersInclude/bumpVertex";
import "./ShadersInclude/clipPlaneVertex";
import "./ShadersInclude/fogVertex";
import "./ShadersInclude/shadowsVertex";
import "./ShadersInclude/vertexColorMixing";
import "./ShadersInclude/pointCloudVertex";
import "./ShadersInclude/logDepthVertex";
/** @internal */
export declare const defaultVertexShader: {
    name: string;
    shader: string;
};
