import { Vector3 } from "../Maths/math.vector";
import type { TransformNode } from "../Meshes/transformNode";
import type { Bone } from "./bone";
import { Space } from "../Maths/math.axis";
/**
 * Class used to make a bone look toward a point in space
 * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/bonesSkeletons#bonelookcontroller
 */
export declare class BoneLookController {
    private static _TmpVecs;
    private static _TmpQuat;
    private static _TmpMats;
    /**
     * The target Vector3 that the bone will look at
     */
    target: Vector3;
    /**
     * The TransformNode that the bone is attached to
     * Name kept as mesh for back compatibility
     */
    mesh: TransformNode;
    /**
     * The bone that will be looking to the target
     */
    bone: Bone;
    /**
     * The up axis of the coordinate system that is used when the bone is rotated
     */
    upAxis: Vector3;
    /**
     * The space that the up axis is in - Space.BONE, Space.LOCAL (default), or Space.WORLD
     */
    upAxisSpace: Space;
    /**
     * Used to make an adjustment to the yaw of the bone
     */
    adjustYaw: number;
    /**
     * Used to make an adjustment to the pitch of the bone
     */
    adjustPitch: number;
    /**
     * Used to make an adjustment to the roll of the bone
     */
    adjustRoll: number;
    /**
     * The amount to slerp (spherical linear interpolation) to the target.  Set this to a value between 0 and 1 (a value of 1 disables slerp)
     */
    slerpAmount: number;
    private _minYaw;
    private _maxYaw;
    private _minPitch;
    private _maxPitch;
    private _minYawSin;
    private _minYawCos;
    private _maxYawSin;
    private _maxYawCos;
    private _midYawConstraint;
    private _minPitchTan;
    private _maxPitchTan;
    private _boneQuat;
    private _slerping;
    private _transformYawPitch;
    private _transformYawPitchInv;
    private _firstFrameSkipped;
    private _yawRange;
    private _fowardAxis;
    /**
     * Gets or sets the minimum yaw angle that the bone can look to
     */
    get minYaw(): number;
    set minYaw(value: number);
    /**
     * Gets or sets the maximum yaw angle that the bone can look to
     */
    get maxYaw(): number;
    set maxYaw(value: number);
    /**
     * Gets or sets the minimum pitch angle that the bone can look to
     */
    get minPitch(): number;
    set minPitch(value: number);
    /**
     * Gets or sets the maximum pitch angle that the bone can look to
     */
    get maxPitch(): number;
    set maxPitch(value: number);
    /**
     * Create a BoneLookController
     * @param mesh the TransformNode that the bone belongs to
     * @param bone the bone that will be looking to the target
     * @param target the target Vector3 to look at
     * @param options optional settings:
     * * maxYaw: the maximum angle the bone will yaw to
     * * minYaw: the minimum angle the bone will yaw to
     * * maxPitch: the maximum angle the bone will pitch to
     * * minPitch: the minimum angle the bone will yaw to
     * * slerpAmount: set the between 0 and 1 to make the bone slerp to the target.
     * * upAxis: the up axis of the coordinate system
     * * upAxisSpace: the space that the up axis is in - Space.BONE, Space.LOCAL (default), or Space.WORLD.
     * * yawAxis: set yawAxis if the bone does not yaw on the y axis
     * * pitchAxis: set pitchAxis if the bone does not pitch on the x axis
     * * adjustYaw: used to make an adjustment to the yaw of the bone
     * * adjustPitch: used to make an adjustment to the pitch of the bone
     * * adjustRoll: used to make an adjustment to the roll of the bone
     * @param options.maxYaw
     * @param options.minYaw
     * @param options.maxPitch
     * @param options.minPitch
     * @param options.slerpAmount
     * @param options.upAxis
     * @param options.upAxisSpace
     * @param options.yawAxis
     * @param options.pitchAxis
     * @param options.adjustYaw
     * @param options.adjustPitch
     * @param options.adjustRoll
     **/
    constructor(mesh: TransformNode, bone: Bone, target: Vector3, options?: {
        maxYaw?: number;
        minYaw?: number;
        maxPitch?: number;
        minPitch?: number;
        slerpAmount?: number;
        upAxis?: Vector3;
        upAxisSpace?: Space;
        yawAxis?: Vector3;
        pitchAxis?: Vector3;
        adjustYaw?: number;
        adjustPitch?: number;
        adjustRoll?: number;
    });
    /**
     * Update the bone to look at the target.  This should be called before the scene is rendered (use scene.registerBeforeRender())
     */
    update(): void;
    private _getAngleDiff;
    private _getAngleBetween;
    private _isAngleBetween;
    private _updateLinkedTransformRotation;
}
