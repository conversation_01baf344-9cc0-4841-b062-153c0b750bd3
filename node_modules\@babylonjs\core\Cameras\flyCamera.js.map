{"version": 3, "file": "flyCamera.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Cameras/flyCamera.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AAInE,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAC;AAGlE,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAItC;;;GAGG;AACH,MAAM,OAAO,SAAU,SAAQ,YAAY;IAwEvC;;;OAGG;IACH,IAAW,kBAAkB;QACzB,MAAM,KAAK,GAAwB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACjE,IAAI,KAAK,EAAE;YACP,OAAO,KAAK,CAAC,kBAAkB,CAAC;SACnC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;OAGG;IACH,IAAW,kBAAkB,CAAC,KAAa;QACvC,MAAM,KAAK,GAAwB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACjE,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC;SACpC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,MAAM,QAAQ,GAA2B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC1E,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,WAAW,CAAC;SAC/B;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;OAEG;IACH,IAAW,WAAW,CAAC,KAAe;QAClC,MAAM,QAAQ,GAA2B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC1E,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,WAAW,GAAG,KAAK,CAAC;SAChC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,MAAM,QAAQ,GAA2B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC1E,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,YAAY,CAAC;SAChC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAW,YAAY,CAAC,KAAe;QACnC,MAAM,QAAQ,GAA2B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC1E,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAC;SACjC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,MAAM,QAAQ,GAA2B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC1E,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,MAAM,CAAC;SAC1B;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;OAEG;IACH,IAAW,MAAM,CAAC,KAAe;QAC7B,MAAM,QAAQ,GAA2B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC1E,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;SAC3B;IACL,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,MAAM,QAAQ,GAA2B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC1E,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,QAAQ,CAAC;SAC5B;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ,CAAC,KAAe;QAC/B,MAAM,QAAQ,GAA2B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC1E,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC;SAC7B;IACL,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,MAAM,QAAQ,GAA2B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC1E,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,QAAQ,CAAC;SAC5B;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ,CAAC,KAAe;QAC/B,MAAM,QAAQ,GAA2B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC1E,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC;SAC7B;IACL,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,MAAM,QAAQ,GAA2B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC1E,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,SAAS,CAAC;SAC7B;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;OAEG;IACH,IAAW,SAAS,CAAC,KAAe;QAChC,MAAM,QAAQ,GAA2B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC1E,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;SAC9B;IACL,CAAC;IAkBD;;;;;;;;OAQG;IACH,YAAY,IAAY,EAAE,QAAiB,EAAE,KAAa,EAAE,4BAA4B,GAAG,IAAI;QAC3F,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,4BAA4B,CAAC,CAAC;QA1P/D;;;;WAIG;QAEI,cAAS,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAExC;;;;WAIG;QAEI,oBAAe,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE9C;;WAEG;QAEI,oBAAe,GAAG,KAAK,CAAC;QAE/B;;WAEG;QAEI,iBAAY,GAAG,KAAK,CAAC;QAE5B;;WAEG;QACI,oBAAe,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAQxC;;WAEG;QACI,eAAU,GAAW,CAAC,CAAC;QAE9B;;WAEG;QACI,gBAAW,GAAW,GAAG,CAAC;QAEjC;;;WAGG;QACI,eAAU,GAAY,KAAK,CAAC;QAEnC;;WAEG;QACI,oBAAe,GAAW,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAE7C;;;WAGG;QACI,yBAAoB,GAAW,CAAC,CAAC;QAsKhC,wBAAmB,GAAG,KAAK,CAAC;QAC5B,iBAAY,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC9B,kBAAa,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC/B,iBAAY,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAgDtC,cAAc;QACN,mBAAc,GAAG,CAAC,CAAC,CAAC;QAmD5B;;WAEG;QACK,+BAA0B,GAAG,CAAC,WAAmB,EAAE,WAAoB,EAAE,eAAuC,IAAI,EAAE,EAAE;YAC5H,MAAM,cAAc,GAAG,CAAC,MAAe,EAAE,EAAE;gBACvC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAEnC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;gBAEvE,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,iBAAiB,EAAE;oBACxD,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;oBAC7C,IAAI,IAAI,CAAC,SAAS,IAAI,YAAY,EAAE;wBAChC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;qBAChC;iBACJ;YACL,CAAC,CAAC;YAEF,cAAc,CAAC,WAAW,CAAC,CAAC;QAChC,CAAC,CAAC;QApGE,IAAI,CAAC,MAAM,GAAG,IAAI,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,QAAQ,EAAE,CAAC;IACzC,CAAC;IAOD;;;;OAIG;IACI,aAAa,CAAC,OAAY,EAAE,gBAA0B;QACzD,8CAA8C;QAC9C,gBAAgB,GAAG,KAAK,CAAC,gCAAgC,CAAC,SAAS,CAAC,CAAC;QACrE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;IAChD,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;QAE5B,IAAI,CAAC,eAAe,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAChD,CAAC;IAKD;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAW,aAAa,CAAC,IAAY;QACjC,IAAI,CAAC,cAAc,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACI,iBAAiB,CAAC,YAAqB;QAC1C,IAAI,cAAuB,CAAC;QAE5B,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,cAAc,GAAG,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;SAC9F;aAAM;YACH,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;SAClC;QAED,cAAc,CAAC,uBAAuB,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAClF,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACnD,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,CAAC;QAEzD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,cAAc,EAAE,CAAC;SACjD;QAED,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC;QACxC,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QAEnD,mDAAmD;QACnD,IAAI,kBAAkB,GAAG,YAAY,CAAC;QAEtC,+DAA+D;QAC/D,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,yFAAyF;YACzF,kBAAkB,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,CAAC;SAClE;QAED,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,EAAE,kBAAkB,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,0BAA0B,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC/I,CAAC;IAsBD,gBAAgB;IACT,YAAY;QACf,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;YACtC,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;SAC/C;QAED,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAE1B,KAAK,CAAC,YAAY,EAAE,CAAC;IACzB,CAAC;IAED,gBAAgB;IACT,oBAAoB;QACvB,OAAO,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC5J,CAAC;IAED,gBAAgB;IACT,eAAe;QAClB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,iBAAiB,EAAE;YAC3D,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAChD;aAAM;YACH,KAAK,CAAC,eAAe,EAAE,CAAC;SAC3B;IACL,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,IAAY;QAC3B,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,eAAe;QAC9C,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,gBAAgB;QAC3C,MAAM,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,sBAAsB;QAE/C,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,sDAAsD;QAE5E,oDAAoD;QACpD,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,MAAM,EAAE;YAC3B,6CAA6C;YAC7C,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC;YAEhC,0BAA0B;YAC1B,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE;gBAC7C,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,CAAC;aAC3B;SACJ;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QACpB,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,WAAW,CAAC;IACvB,CAAC;CACJ;AA5ZG;IADC,kBAAkB,EAAE;4CACmB;AAQxC;IADC,kBAAkB,EAAE;kDACyB;AAM9C;IADC,SAAS,EAAE;kDACmB;AAM/B;IADC,SAAS,EAAE;+CACgB", "sourcesContent": ["import { serialize, serializeAsVector3 } from \"../Misc/decorators\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { Quaternion } from \"../Maths/math.vector\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport { Engine } from \"../Engines/engine\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { TargetCamera } from \"./targetCamera\";\r\nimport { FlyCameraInputsManager } from \"./flyCameraInputsManager\";\r\nimport type { FlyCameraMouseInput } from \"../Cameras/Inputs/flyCameraMouseInput\";\r\nimport type { FlyCameraKeyboardInput } from \"../Cameras/Inputs/flyCameraKeyboardInput\";\r\nimport { Tools } from \"../Misc/tools\";\r\n\r\ndeclare type Collider = import(\"../Collisions/collider\").Collider;\r\n\r\n/**\r\n * This is a flying camera, designed for 3D movement and rotation in all directions,\r\n * such as in a 3D Space Shooter or a Flight Simulator.\r\n */\r\nexport class FlyCamera extends TargetCamera {\r\n    /**\r\n     * Define the collision ellipsoid of the camera.\r\n     * This is helpful for simulating a camera body, like a player's body.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_collisions#arcrotatecamera\r\n     */\r\n    @serializeAsVector3()\r\n    public ellipsoid = new Vector3(1, 1, 1);\r\n\r\n    /**\r\n     * Define an offset for the position of the ellipsoid around the camera.\r\n     * This can be helpful if the camera is attached away from the player's body center,\r\n     * such as at its head.\r\n     */\r\n    @serializeAsVector3()\r\n    public ellipsoidOffset = new Vector3(0, 0, 0);\r\n\r\n    /**\r\n     * Enable or disable collisions of the camera with the rest of the scene objects.\r\n     */\r\n    @serialize()\r\n    public checkCollisions = false;\r\n\r\n    /**\r\n     * Enable or disable gravity on the camera.\r\n     */\r\n    @serialize()\r\n    public applyGravity = false;\r\n\r\n    /**\r\n     * Define the current direction the camera is moving to.\r\n     */\r\n    public cameraDirection = Vector3.Zero();\r\n\r\n    /**\r\n     * Define the current local rotation of the camera as a quaternion to prevent Gimbal lock.\r\n     * This overrides and empties cameraRotation.\r\n     */\r\n    public rotationQuaternion: Quaternion;\r\n\r\n    /**\r\n     * Track Roll to maintain the wanted Rolling when looking around.\r\n     */\r\n    public _trackRoll: number = 0;\r\n\r\n    /**\r\n     * Slowly correct the Roll to its original value after a Pitch+Yaw rotation.\r\n     */\r\n    public rollCorrect: number = 100;\r\n\r\n    /**\r\n     * Mimic a banked turn, Rolling the camera when Yawing.\r\n     * It's recommended to use rollCorrect = 10 for faster banking correction.\r\n     */\r\n    public bankedTurn: boolean = false;\r\n\r\n    /**\r\n     * Limit in radians for how much Roll banking will add. (Default: 90°)\r\n     */\r\n    public bankedTurnLimit: number = Math.PI / 2;\r\n\r\n    /**\r\n     * Value of 0 disables the banked Roll.\r\n     * Value of 1 is equal to the Yaw angle in radians.\r\n     */\r\n    public bankedTurnMultiplier: number = 1;\r\n\r\n    /**\r\n     * The inputs manager loads all the input sources, such as keyboard and mouse.\r\n     */\r\n    public inputs: FlyCameraInputsManager;\r\n\r\n    /**\r\n     * Gets the input sensibility for mouse input.\r\n     * Higher values reduce sensitivity.\r\n     */\r\n    public get angularSensibility(): number {\r\n        const mouse = <FlyCameraMouseInput>this.inputs.attached[\"mouse\"];\r\n        if (mouse) {\r\n            return mouse.angularSensibility;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * Sets the input sensibility for a mouse input.\r\n     * Higher values reduce sensitivity.\r\n     */\r\n    public set angularSensibility(value: number) {\r\n        const mouse = <FlyCameraMouseInput>this.inputs.attached[\"mouse\"];\r\n        if (mouse) {\r\n            mouse.angularSensibility = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the keys for camera movement forward.\r\n     */\r\n    public get keysForward(): number[] {\r\n        const keyboard = <FlyCameraKeyboardInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysForward;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    /**\r\n     * Set the keys for camera movement forward.\r\n     */\r\n    public set keysForward(value: number[]) {\r\n        const keyboard = <FlyCameraKeyboardInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysForward = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the keys for camera movement backward.\r\n     */\r\n    public get keysBackward(): number[] {\r\n        const keyboard = <FlyCameraKeyboardInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysBackward;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    public set keysBackward(value: number[]) {\r\n        const keyboard = <FlyCameraKeyboardInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysBackward = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the keys for camera movement up.\r\n     */\r\n    public get keysUp(): number[] {\r\n        const keyboard = <FlyCameraKeyboardInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysUp;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    /**\r\n     * Set the keys for camera movement up.\r\n     */\r\n    public set keysUp(value: number[]) {\r\n        const keyboard = <FlyCameraKeyboardInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysUp = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the keys for camera movement down.\r\n     */\r\n    public get keysDown(): number[] {\r\n        const keyboard = <FlyCameraKeyboardInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysDown;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    /**\r\n     * Set the keys for camera movement down.\r\n     */\r\n    public set keysDown(value: number[]) {\r\n        const keyboard = <FlyCameraKeyboardInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysDown = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the keys for camera movement left.\r\n     */\r\n    public get keysLeft(): number[] {\r\n        const keyboard = <FlyCameraKeyboardInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysLeft;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    /**\r\n     * Set the keys for camera movement left.\r\n     */\r\n    public set keysLeft(value: number[]) {\r\n        const keyboard = <FlyCameraKeyboardInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysLeft = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set the keys for camera movement right.\r\n     */\r\n    public get keysRight(): number[] {\r\n        const keyboard = <FlyCameraKeyboardInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysRight;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    /**\r\n     * Set the keys for camera movement right.\r\n     */\r\n    public set keysRight(value: number[]) {\r\n        const keyboard = <FlyCameraKeyboardInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysRight = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Event raised when the camera collides with a mesh in the scene.\r\n     */\r\n    public onCollide: (collidedMesh: AbstractMesh) => void;\r\n\r\n    private _collider: Collider;\r\n    private _needMoveForGravity = false;\r\n    private _oldPosition = Vector3.Zero();\r\n    private _diffPosition = Vector3.Zero();\r\n    private _newPosition = Vector3.Zero();\r\n\r\n    /** @internal */\r\n    public _localDirection: Vector3;\r\n    /** @internal */\r\n    public _transformedDirection: Vector3;\r\n\r\n    /**\r\n     * Instantiates a FlyCamera.\r\n     * This is a flying camera, designed for 3D movement and rotation in all directions,\r\n     * such as in a 3D Space Shooter or a Flight Simulator.\r\n     * @param name Define the name of the camera in the scene.\r\n     * @param position Define the starting position of the camera in the scene.\r\n     * @param scene Define the scene the camera belongs to.\r\n     * @param setActiveOnSceneIfNoneActive Defines whether the camera should be marked as active, if no other camera has been defined as active.\r\n     */\r\n    constructor(name: string, position: Vector3, scene?: Scene, setActiveOnSceneIfNoneActive = true) {\r\n        super(name, position, scene, setActiveOnSceneIfNoneActive);\r\n        this.inputs = new FlyCameraInputsManager(this);\r\n        this.inputs.addKeyboard().addMouse();\r\n    }\r\n\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public attachControl(noPreventDefault?: boolean): void;\r\n    /**\r\n     * Attached controls to the current camera.\r\n     * @param ignored defines an ignored parameter kept for backward compatibility.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public attachControl(ignored: any, noPreventDefault?: boolean): void {\r\n        // eslint-disable-next-line prefer-rest-params\r\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(arguments);\r\n        this.inputs.attachElement(noPreventDefault);\r\n    }\r\n\r\n    /**\r\n     * Detach a control from the HTML DOM element.\r\n     * The camera will stop reacting to that input.\r\n     */\r\n    public detachControl(): void {\r\n        this.inputs.detachElement();\r\n\r\n        this.cameraDirection = new Vector3(0, 0, 0);\r\n    }\r\n\r\n    // Collisions.\r\n    private _collisionMask = -1;\r\n\r\n    /**\r\n     * Get the mask that the camera ignores in collision events.\r\n     */\r\n    public get collisionMask(): number {\r\n        return this._collisionMask;\r\n    }\r\n\r\n    /**\r\n     * Set the mask that the camera ignores in collision events.\r\n     */\r\n    public set collisionMask(mask: number) {\r\n        this._collisionMask = !isNaN(mask) ? mask : -1;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _collideWithWorld(displacement: Vector3): void {\r\n        let globalPosition: Vector3;\r\n\r\n        if (this.parent) {\r\n            globalPosition = Vector3.TransformCoordinates(this.position, this.parent.getWorldMatrix());\r\n        } else {\r\n            globalPosition = this.position;\r\n        }\r\n\r\n        globalPosition.subtractFromFloatsToRef(0, this.ellipsoid.y, 0, this._oldPosition);\r\n        this._oldPosition.addInPlace(this.ellipsoidOffset);\r\n        const coordinator = this.getScene().collisionCoordinator;\r\n\r\n        if (!this._collider) {\r\n            this._collider = coordinator.createCollider();\r\n        }\r\n\r\n        this._collider._radius = this.ellipsoid;\r\n        this._collider.collisionMask = this._collisionMask;\r\n\r\n        // No need for clone, as long as gravity is not on.\r\n        let actualDisplacement = displacement;\r\n\r\n        // Add gravity to direction to prevent dual-collision checking.\r\n        if (this.applyGravity) {\r\n            // This prevents mending with cameraDirection, a global variable of the fly camera class.\r\n            actualDisplacement = displacement.add(this.getScene().gravity);\r\n        }\r\n\r\n        coordinator.getNewPosition(this._oldPosition, actualDisplacement, this._collider, 3, null, this._onCollisionPositionChange, this.uniqueId);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    private _onCollisionPositionChange = (collisionId: number, newPosition: Vector3, collidedMesh: Nullable<AbstractMesh> = null) => {\r\n        const updatePosition = (newPos: Vector3) => {\r\n            this._newPosition.copyFrom(newPos);\r\n\r\n            this._newPosition.subtractToRef(this._oldPosition, this._diffPosition);\r\n\r\n            if (this._diffPosition.length() > Engine.CollisionsEpsilon) {\r\n                this.position.addInPlace(this._diffPosition);\r\n                if (this.onCollide && collidedMesh) {\r\n                    this.onCollide(collidedMesh);\r\n                }\r\n            }\r\n        };\r\n\r\n        updatePosition(newPosition);\r\n    };\r\n\r\n    /** @internal */\r\n    public _checkInputs(): void {\r\n        if (!this._localDirection) {\r\n            this._localDirection = Vector3.Zero();\r\n            this._transformedDirection = Vector3.Zero();\r\n        }\r\n\r\n        this.inputs.checkInputs();\r\n\r\n        super._checkInputs();\r\n    }\r\n\r\n    /** @internal */\r\n    public _decideIfNeedsToMove(): boolean {\r\n        return this._needMoveForGravity || Math.abs(this.cameraDirection.x) > 0 || Math.abs(this.cameraDirection.y) > 0 || Math.abs(this.cameraDirection.z) > 0;\r\n    }\r\n\r\n    /** @internal */\r\n    public _updatePosition(): void {\r\n        if (this.checkCollisions && this.getScene().collisionsEnabled) {\r\n            this._collideWithWorld(this.cameraDirection);\r\n        } else {\r\n            super._updatePosition();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Restore the Roll to its target value at the rate specified.\r\n     * @param rate - Higher means slower restoring.\r\n     * @internal\r\n     */\r\n    public restoreRoll(rate: number): void {\r\n        const limit = this._trackRoll; // Target Roll.\r\n        const z = this.rotation.z; // Current Roll.\r\n        const delta = limit - z; // Difference in Roll.\r\n\r\n        const minRad = 0.001; // Tenth of a radian is a barely noticable difference.\r\n\r\n        // If the difference is noticable, restore the Roll.\r\n        if (Math.abs(delta) >= minRad) {\r\n            // Change Z rotation towards the target Roll.\r\n            this.rotation.z += delta / rate;\r\n\r\n            // Match when near enough.\r\n            if (Math.abs(limit - this.rotation.z) <= minRad) {\r\n                this.rotation.z = limit;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Destroy the camera and release the current resources held by it.\r\n     */\r\n    public dispose(): void {\r\n        this.inputs.clear();\r\n        super.dispose();\r\n    }\r\n\r\n    /**\r\n     * Get the current object class name.\r\n     * @returns the class name.\r\n     */\r\n    public getClassName(): string {\r\n        return \"FlyCamera\";\r\n    }\r\n}\r\n"]}