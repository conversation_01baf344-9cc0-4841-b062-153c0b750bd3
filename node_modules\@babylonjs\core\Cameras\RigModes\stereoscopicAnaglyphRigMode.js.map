{"version": 3, "file": "stereoscopicAnaglyphRigMode.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Cameras/RigModes/stereoscopicAnaglyphRigMode.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAe,EAAE,MAAM,qCAAqC,CAAC;AACtE,OAAO,EAAE,mBAAmB,EAAE,MAAM,yCAAyC,CAAC;AAE9E;;GAEG;AACH,MAAM,UAAU,8BAA8B,CAAC,MAAc;IACzD,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,IAAI,GAAG,WAAW,EAAE,GAAG,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IACnH,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,IAAI,mBAAmB,CAAC,MAAM,CAAC,IAAI,GAAG,WAAW,EAAE,GAAG,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;AACxH,CAAC", "sourcesContent": ["import type { Camera } from \"../camera\";\r\nimport { PassPostProcess } from \"../../PostProcesses/passPostProcess\";\r\nimport { AnaglyphPostProcess } from \"../../PostProcesses/anaglyphPostProcess\";\r\n\r\n/**\r\n * @internal\r\n */\r\nexport function setStereoscopicAnaglyphRigMode(camera: Camera) {\r\n    camera._rigCameras[0]._rigPostProcess = new PassPostProcess(camera.name + \"_passthru\", 1.0, camera._rigCameras[0]);\r\n    camera._rigCameras[1]._rigPostProcess = new AnaglyphPostProcess(camera.name + \"_anaglyph\", 1.0, camera._rigCameras);\r\n}\r\n"]}