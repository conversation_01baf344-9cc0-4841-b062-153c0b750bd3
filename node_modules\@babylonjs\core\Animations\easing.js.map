{"version": 3, "file": "easing.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Animations/easing.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AAmBjD;;;GAGG;AACH,MAAM,OAAO,cAAc;IAA3B;QAgBY,gBAAW,GAAG,cAAc,CAAC,iBAAiB,CAAC;IA8C3D,CAAC;IA5CG;;;OAGG;IACI,aAAa,CAAC,UAAkB;QACnC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;IACzB,CAAC;IACD;;;OAGG;IACI,aAAa;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,6DAA6D;IACtD,UAAU,CAAC,QAAgB;QAC9B,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtD,CAAC;IAED;;;;;OAKG;IACI,IAAI,CAAC,QAAgB;QACxB,QAAQ,IAAI,CAAC,WAAW,EAAE;YACtB,KAAK,cAAc,CAAC,iBAAiB;gBACjC,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACrC,KAAK,cAAc,CAAC,kBAAkB;gBAClC,OAAO,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC;SAChD;QAED,IAAI,QAAQ,IAAI,GAAG,EAAE;YACjB,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;SAChE;QAED,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;IAC/C,CAAC;;AA5DD;;GAEG;AACoB,gCAAiB,GAAG,CAAC,CAAC;AAE7C;;GAEG;AACoB,iCAAkB,GAAG,CAAC,CAAC;AAE9C;;GAEG;AACoB,mCAAoB,GAAG,CAAC,CAAC;AAkDpD;;;;GAIG;AACH,MAAM,OAAO,UAAW,SAAQ,cAAc;IAC1C;;OAEG;IACI,UAAU,CAAC,QAAgB;QAC9B,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC9C,OAAO,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC;IACtD,CAAC;CACJ;AAED;;;;GAIG;AACH,MAAM,OAAO,QAAS,SAAQ,cAAc;IACxC;;;;OAIG;IACH;IACI,4CAA4C;IACrC,YAAoB,CAAC;QAE5B,KAAK,EAAE,CAAC;QAFD,cAAS,GAAT,SAAS,CAAY;IAGhC,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,QAAgB;QAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,QAAQ,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,kBAAkB,GAAG,QAAQ,CAAC,CAAC;IAC9F,CAAC;CACJ;AAED;;;;GAIG;AACH,MAAM,OAAO,UAAW,SAAQ,cAAc;IAC1C;;;;;OAKG;IACH;IACI,oCAAoC;IAC7B,UAAkB,CAAC;IAC1B,0CAA0C;IACnC,aAAqB,CAAC;QAE7B,KAAK,EAAE,CAAC;QAJD,YAAO,GAAP,OAAO,CAAY;QAEnB,eAAU,GAAV,UAAU,CAAY;IAGjC,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,QAAgB;QAC9B,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjC,IAAI,UAAU,IAAI,GAAG,EAAE;YACnB,UAAU,GAAG,KAAK,CAAC;SACtB;QACD,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QACrC,MAAM,IAAI,GAAG,GAAG,GAAG,UAAU,CAAC;QAC9B,MAAM,IAAI,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;QAC9C,MAAM,KAAK,GAAG,QAAQ,GAAG,IAAI,CAAC;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACjF,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC/B,MAAM,KAAK,GAAG,IAAI,GAAG,GAAG,CAAC;QACzB,MAAM,IAAI,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QAChE,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QAClE,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC;QAClC,MAAM,IAAI,GAAG,QAAQ,GAAG,IAAI,CAAC;QAC7B,MAAM,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;QACzB,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;IACnG,CAAC;CACJ;AAED;;;;GAIG;AACH,MAAM,OAAO,SAAU,SAAQ,cAAc;IACzC;;OAEG;IACI,UAAU,CAAC,QAAgB;QAC9B,OAAO,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAC1C,CAAC;CACJ;AAED;;;;GAIG;AACH,MAAM,OAAO,WAAY,SAAQ,cAAc;IAC3C;;;;;OAKG;IACH;IACI,wCAAwC;IACjC,eAAuB,CAAC;IAC/B,+CAA+C;IACxC,cAAsB,CAAC;QAE9B,KAAK,EAAE,CAAC;QAJD,iBAAY,GAAZ,YAAY,CAAY;QAExB,gBAAW,GAAX,WAAW,CAAY;IAGlC,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,QAAgB;QAC9B,IAAI,IAAI,CAAC;QACT,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAC9C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAE5C,IAAI,GAAG,IAAI,CAAC,EAAE;YACV,IAAI,GAAG,QAAQ,CAAC;SACnB;aAAM;YACH,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;SACnE;QACD,OAAO,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,kBAAkB,GAAG,IAAI,GAAG,kBAAkB,CAAC,GAAG,QAAQ,CAAC,CAAC;IACxF,CAAC;CACJ;AAED;;;;GAIG;AACH,MAAM,OAAO,eAAgB,SAAQ,cAAc;IAC/C;;;;OAIG;IACH;IACI,2CAA2C;IACpC,WAAmB,CAAC;QAE3B,KAAK,EAAE,CAAC;QAFD,aAAQ,GAAR,QAAQ,CAAY;IAG/B,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,QAAgB;QAC9B,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,EAAE;YACpB,OAAO,QAAQ,CAAC;SACnB;QAED,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC;IACxF,CAAC;CACJ;AAED;;;;GAIG;AACH,MAAM,OAAO,SAAU,SAAQ,cAAc;IACzC;;;;OAIG;IACH;IACI,wCAAwC;IACjC,QAAgB,CAAC;QAExB,KAAK,EAAE,CAAC;QAFD,UAAK,GAAL,KAAK,CAAY;IAG5B,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,QAAgB;QAC9B,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IACjC,CAAC;CACJ;AAED;;;;GAIG;AACH,MAAM,OAAO,aAAc,SAAQ,cAAc;IAC7C;;OAEG;IACI,UAAU,CAAC,QAAgB;QAC9B,OAAO,QAAQ,GAAG,QAAQ,CAAC;IAC/B,CAAC;CACJ;AAED;;;;GAIG;AACH,MAAM,OAAO,WAAY,SAAQ,cAAc;IAC3C;;OAEG;IACI,UAAU,CAAC,QAAgB;QAC9B,OAAO,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACrD,CAAC;CACJ;AAED;;;;GAIG;AACH,MAAM,OAAO,WAAY,SAAQ,cAAc;IAC3C;;OAEG;IACI,UAAU,CAAC,QAAgB;QAC9B,OAAO,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAChE,CAAC;CACJ;AAED;;;;GAIG;AACH,MAAM,OAAO,QAAS,SAAQ,cAAc;IACxC;;OAEG;IACI,UAAU,CAAC,QAAgB;QAC9B,OAAO,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,kBAAkB,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC;IACjE,CAAC;CACJ;AAED;;;;GAIG;AACH,MAAM,OAAO,eAAgB,SAAQ,cAAc;IAC/C;;;;;;;OAOG;IACH;IACI,uEAAuE;IAChE,KAAa,CAAC;IACrB,uEAAuE;IAChE,KAAa,CAAC;IACrB,qEAAqE;IAC9D,KAAa,CAAC;IACrB,qEAAqE;IAC9D,KAAa,CAAC;QAErB,KAAK,EAAE,CAAC;QARD,OAAE,GAAF,EAAE,CAAY;QAEd,OAAE,GAAF,EAAE,CAAY;QAEd,OAAE,GAAF,EAAE,CAAY;QAEd,OAAE,GAAF,EAAE,CAAY;IAGzB,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,QAAgB;QAC9B,OAAO,WAAW,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IACjF,CAAC;CACJ", "sourcesContent": ["import { BezierCurve } from \"../Maths/math.path\";\r\n\r\n/**\r\n * This represents the main contract an easing function should follow.\r\n * Easing functions are used throughout the animation system.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport interface IEasingFunction {\r\n    /**\r\n     * Given an input gradient between 0 and 1, this returns the corresponding value\r\n     * of the easing function.\r\n     * The link below provides some of the most common examples of easing functions.\r\n     * @see https://easings.net/\r\n     * @param gradient Defines the value between 0 and 1 we want the easing value for\r\n     * @returns the corresponding value on the curve defined by the easing function\r\n     */\r\n    ease(gradient: number): number;\r\n}\r\n\r\n/**\r\n * Base class used for every default easing function.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class EasingFunction implements IEasingFunction {\r\n    /**\r\n     * Interpolation follows the mathematical formula associated with the easing function.\r\n     */\r\n    public static readonly EASINGMODE_EASEIN = 0;\r\n\r\n    /**\r\n     * Interpolation follows 100% interpolation minus the output of the formula associated with the easing function.\r\n     */\r\n    public static readonly EASINGMODE_EASEOUT = 1;\r\n\r\n    /**\r\n     * Interpolation uses EaseIn for the first half of the animation and EaseOut for the second half.\r\n     */\r\n    public static readonly EASINGMODE_EASEINOUT = 2;\r\n\r\n    private _easingMode = EasingFunction.EASINGMODE_EASEIN;\r\n\r\n    /**\r\n     * Sets the easing mode of the current function.\r\n     * @param easingMode Defines the willing mode (EASINGMODE_EASEIN, EASINGMODE_EASEOUT or EASINGMODE_EASEINOUT)\r\n     */\r\n    public setEasingMode(easingMode: number) {\r\n        const n = Math.min(Math.max(easingMode, 0), 2);\r\n        this._easingMode = n;\r\n    }\r\n    /**\r\n     * Gets the current easing mode.\r\n     * @returns the easing mode\r\n     */\r\n    public getEasingMode(): number {\r\n        return this._easingMode;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public easeInCore(gradient: number): number {\r\n        throw new Error(\"You must implement this method\");\r\n    }\r\n\r\n    /**\r\n     * Given an input gradient between 0 and 1, this returns the corresponding value\r\n     * of the easing function.\r\n     * @param gradient Defines the value between 0 and 1 we want the easing value for\r\n     * @returns the corresponding value on the curve defined by the easing function\r\n     */\r\n    public ease(gradient: number): number {\r\n        switch (this._easingMode) {\r\n            case EasingFunction.EASINGMODE_EASEIN:\r\n                return this.easeInCore(gradient);\r\n            case EasingFunction.EASINGMODE_EASEOUT:\r\n                return 1 - this.easeInCore(1 - gradient);\r\n        }\r\n\r\n        if (gradient >= 0.5) {\r\n            return (1 - this.easeInCore((1 - gradient) * 2)) * 0.5 + 0.5;\r\n        }\r\n\r\n        return this.easeInCore(gradient * 2) * 0.5;\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with a circle shape (see link below).\r\n * @see https://easings.net/#easeInCirc\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class CircleEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * @internal\r\n     */\r\n    public easeInCore(gradient: number): number {\r\n        gradient = Math.max(0, Math.min(1, gradient));\r\n        return 1.0 - Math.sqrt(1.0 - gradient * gradient);\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with a ease back shape (see link below).\r\n * @see https://easings.net/#easeInBack\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class BackEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * Instantiates a back ease easing\r\n     * @see https://easings.net/#easeInBack\r\n     * @param amplitude Defines the amplitude of the function\r\n     */\r\n    constructor(\r\n        /** Defines the amplitude of the function */\r\n        public amplitude: number = 1\r\n    ) {\r\n        super();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public easeInCore(gradient: number): number {\r\n        const num = Math.max(0, this.amplitude);\r\n        return Math.pow(gradient, 3.0) - gradient * num * Math.sin(3.1415926535897931 * gradient);\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with a bouncing shape (see link below).\r\n * @see https://easings.net/#easeInBounce\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class BounceEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * Instantiates a bounce easing\r\n     * @see https://easings.net/#easeInBounce\r\n     * @param bounces Defines the number of bounces\r\n     * @param bounciness Defines the amplitude of the bounce\r\n     */\r\n    constructor(\r\n        /** Defines the number of bounces */\r\n        public bounces: number = 3,\r\n        /** Defines the amplitude of the bounce */\r\n        public bounciness: number = 2\r\n    ) {\r\n        super();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public easeInCore(gradient: number): number {\r\n        const y = Math.max(0.0, this.bounces);\r\n        let bounciness = this.bounciness;\r\n        if (bounciness <= 1.0) {\r\n            bounciness = 1.001;\r\n        }\r\n        const num9 = Math.pow(bounciness, y);\r\n        const num5 = 1.0 - bounciness;\r\n        const num4 = (1.0 - num9) / num5 + num9 * 0.5;\r\n        const num15 = gradient * num4;\r\n        const num65 = Math.log(-num15 * (1.0 - bounciness) + 1.0) / Math.log(bounciness);\r\n        const num3 = Math.floor(num65);\r\n        const num13 = num3 + 1.0;\r\n        const num8 = (1.0 - Math.pow(bounciness, num3)) / (num5 * num4);\r\n        const num12 = (1.0 - Math.pow(bounciness, num13)) / (num5 * num4);\r\n        const num7 = (num8 + num12) * 0.5;\r\n        const num6 = gradient - num7;\r\n        const num2 = num7 - num8;\r\n        return (-Math.pow(1.0 / bounciness, y - num3) / (num2 * num2)) * (num6 - num2) * (num6 + num2);\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with a power of 3 shape (see link below).\r\n * @see https://easings.net/#easeInCubic\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class CubicEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * @internal\r\n     */\r\n    public easeInCore(gradient: number): number {\r\n        return gradient * gradient * gradient;\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with an elastic shape (see link below).\r\n * @see https://easings.net/#easeInElastic\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class ElasticEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * Instantiates an elastic easing function\r\n     * @see https://easings.net/#easeInElastic\r\n     * @param oscillations Defines the number of oscillations\r\n     * @param springiness Defines the amplitude of the oscillations\r\n     */\r\n    constructor(\r\n        /** Defines the number of oscillations*/\r\n        public oscillations: number = 3,\r\n        /** Defines the amplitude of the oscillations*/\r\n        public springiness: number = 3\r\n    ) {\r\n        super();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public easeInCore(gradient: number): number {\r\n        let num2;\r\n        const num3 = Math.max(0.0, this.oscillations);\r\n        const num = Math.max(0.0, this.springiness);\r\n\r\n        if (num == 0) {\r\n            num2 = gradient;\r\n        } else {\r\n            num2 = (Math.exp(num * gradient) - 1.0) / (Math.exp(num) - 1.0);\r\n        }\r\n        return num2 * Math.sin((6.2831853071795862 * num3 + 1.5707963267948966) * gradient);\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with an exponential shape (see link below).\r\n * @see https://easings.net/#easeInExpo\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class ExponentialEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * Instantiates an exponential easing function\r\n     * @see https://easings.net/#easeInExpo\r\n     * @param exponent Defines the exponent of the function\r\n     */\r\n    constructor(\r\n        /** Defines the exponent of the function */\r\n        public exponent: number = 2\r\n    ) {\r\n        super();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public easeInCore(gradient: number): number {\r\n        if (this.exponent <= 0) {\r\n            return gradient;\r\n        }\r\n\r\n        return (Math.exp(this.exponent * gradient) - 1.0) / (Math.exp(this.exponent) - 1.0);\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with a power shape (see link below).\r\n * @see https://easings.net/#easeInQuad\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class PowerEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * Instantiates an power base easing function\r\n     * @see https://easings.net/#easeInQuad\r\n     * @param power Defines the power of the function\r\n     */\r\n    constructor(\r\n        /** Defines the power of the function */\r\n        public power: number = 2\r\n    ) {\r\n        super();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public easeInCore(gradient: number): number {\r\n        const y = Math.max(0.0, this.power);\r\n        return Math.pow(gradient, y);\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with a power of 2 shape (see link below).\r\n * @see https://easings.net/#easeInQuad\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class QuadraticEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * @internal\r\n     */\r\n    public easeInCore(gradient: number): number {\r\n        return gradient * gradient;\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with a power of 4 shape (see link below).\r\n * @see https://easings.net/#easeInQuart\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class QuarticEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * @internal\r\n     */\r\n    public easeInCore(gradient: number): number {\r\n        return gradient * gradient * gradient * gradient;\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with a power of 5 shape (see link below).\r\n * @see https://easings.net/#easeInQuint\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class QuinticEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * @internal\r\n     */\r\n    public easeInCore(gradient: number): number {\r\n        return gradient * gradient * gradient * gradient * gradient;\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with a sin shape (see link below).\r\n * @see https://easings.net/#easeInSine\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class SineEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * @internal\r\n     */\r\n    public easeInCore(gradient: number): number {\r\n        return 1.0 - Math.sin(1.5707963267948966 * (1.0 - gradient));\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with a bezier shape (see link below).\r\n * @see http://cubic-bezier.com/#.17,.67,.83,.67\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class BezierCurveEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * Instantiates a bezier function\r\n     * @see http://cubic-bezier.com/#.17,.67,.83,.67\r\n     * @param x1 Defines the x component of the start tangent in the bezier curve\r\n     * @param y1 Defines the y component of the start tangent in the bezier curve\r\n     * @param x2 Defines the x component of the end tangent in the bezier curve\r\n     * @param y2 Defines the y component of the end tangent in the bezier curve\r\n     */\r\n    constructor(\r\n        /** Defines the x component of the start tangent in the bezier curve */\r\n        public x1: number = 0,\r\n        /** Defines the y component of the start tangent in the bezier curve */\r\n        public y1: number = 0,\r\n        /** Defines the x component of the end tangent in the bezier curve */\r\n        public x2: number = 1,\r\n        /** Defines the y component of the end tangent in the bezier curve */\r\n        public y2: number = 1\r\n    ) {\r\n        super();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public easeInCore(gradient: number): number {\r\n        return BezierCurve.Interpolate(gradient, this.x1, this.y1, this.x2, this.y2);\r\n    }\r\n}\r\n"]}