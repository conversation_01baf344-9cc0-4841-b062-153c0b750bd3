{"version": 3, "file": "freeCameraKeyboardMoveInput.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Cameras/Inputs/freeCameraKeyboardMoveInput.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAIlD,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AAGrE,OAAO,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AAEjE,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAElD,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AACzC;;;GAGG;AACH,MAAM,OAAO,2BAA2B;IAAxC;QAMI;;WAEG;QAEI,WAAM,GAAG,CAAC,EAAE,CAAC,CAAC;QAErB;;WAEG;QAEI,eAAU,GAAG,CAAC,EAAE,CAAC,CAAC;QAEzB;;WAEG;QAEI,aAAQ,GAAG,CAAC,EAAE,CAAC,CAAC;QAEvB;;WAEG;QAEI,iBAAY,GAAG,CAAC,EAAE,CAAC,CAAC;QAE3B;;WAEG;QAEI,aAAQ,GAAG,CAAC,EAAE,CAAC,CAAC;QAEvB;;WAEG;QAEI,cAAS,GAAG,CAAC,EAAE,CAAC,CAAC;QAExB;;WAEG;QAEI,kBAAa,GAAG,GAAG,CAAC;QAE3B;;WAEG;QAEI,mBAAc,GAAa,EAAE,CAAC;QAErC;;WAEG;QAEI,oBAAe,GAAa,EAAE,CAAC;QAEtC;;WAEG;QAEI,iBAAY,GAAa,EAAE,CAAC;QAEnC;;WAEG;QAEI,mBAAc,GAAa,EAAE,CAAC;QAE7B,UAAK,GAAG,IAAI,KAAK,EAAU,CAAC;IA6KxC,CAAC;IAvKG;;;OAGG;IACI,aAAa,CAAC,gBAA0B;QAC3C,8CAA8C;QAC9C,gBAAgB,GAAG,KAAK,CAAC,gCAAgC,CAAC,SAAS,CAAC,CAAC;QACrE,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,OAAO;SACV;QAED,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,GAAG,CAAC,GAAG,EAAE;YACtE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACrE,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;YACvB,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;gBACd,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB,CAAC,OAAO,EAAE;oBAC1C,IACI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBACvC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBACzC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBACzC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC1C,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC3C,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC7C,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC/C,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAChD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC7C,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EACjD;wBACE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;wBAE9C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;4BACd,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;yBAChC;wBACD,IAAI,CAAC,gBAAgB,EAAE;4BACnB,GAAG,CAAC,cAAc,EAAE,CAAC;yBACxB;qBACJ;iBACJ;qBAAM;oBACH,IACI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBACvC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBACzC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBACzC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC1C,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC3C,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC7C,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC/C,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAChD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC7C,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EACjD;wBACE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;wBAE9C,IAAI,KAAK,IAAI,CAAC,EAAE;4BACZ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;yBAC/B;wBACD,IAAI,CAAC,gBAAgB,EAAE;4BACnB,GAAG,CAAC,cAAc,EAAE,CAAC;yBACxB;qBACJ;iBACJ;aACJ;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IACD;;OAEG;IACI,aAAa;QAChB,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;aACrE;YAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;aAC1E;YACD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAChC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;SACrC;QACD,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAC3B,WAAW;YACX,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBACpD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAClC,MAAM,KAAK,GAAG,MAAM,CAAC,wBAAwB,EAAE,CAAC;gBAEhD,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;oBACvC,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;iBACvD;qBAAM,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC5C,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;iBACtD;qBAAM,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC/C,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;iBACtD;qBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC9C,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;iBACvD;qBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;oBAChD,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;iBACtD;qBAAM,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;oBAClD,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;iBACvD;qBAAM,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;oBACpD,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC/C,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;iBACvD;qBAAM,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;oBACrD,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC/C,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;iBACvD;qBAAM,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;oBAClD,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC/C,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;iBACvD;qBAAM,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;oBACpD,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC/C,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;iBACvD;gBAED,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,oBAAoB,EAAE;oBACxC,MAAM,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;iBAClC;gBAED,MAAM,CAAC,aAAa,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;gBAClE,OAAO,CAAC,oBAAoB,CAAC,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC,sBAAsB,EAAE,MAAM,CAAC,qBAAqB,CAAC,CAAC;gBAClH,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;aACnE;SACJ;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,6BAA6B,CAAC;IACzC,CAAC;IAED,gBAAgB;IACT,YAAY;QACf,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,UAAU,CAAC;IACtB,CAAC;IAEO,iBAAiB;QACrB,IAAI,QAAQ,GAAG,CAAC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,GAAG,IAAI,CAAC;QACzE,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,oBAAoB,EAAE;YAC7C,QAAQ,IAAI,CAAC,CAAC,CAAC;SAClB;QACD,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,0BAA0B,EAAE,GAAG,CAAC,EAAE;YAC3E,QAAQ,IAAI,CAAC,CAAC,CAAC;SAClB;QACD,OAAO,QAAQ,CAAC;IACpB,CAAC;CACJ;AA3OG;IADC,SAAS,EAAE;2DACS;AAMrB;IADC,SAAS,EAAE;+DACa;AAMzB;IADC,SAAS,EAAE;6DACW;AAMvB;IADC,SAAS,EAAE;iEACe;AAM3B;IADC,SAAS,EAAE;6DACW;AAMvB;IADC,SAAS,EAAE;8DACY;AAMxB;IADC,SAAS,EAAE;kEACe;AAM3B;IADC,SAAS,EAAE;mEACyB;AAMrC;IADC,SAAS,EAAE;oEAC0B;AAMtC;IADC,SAAS,EAAE;iEACuB;AAMnC;IADC,SAAS,EAAE;mEACyB;AAiLnC,gBAAiB,CAAC,6BAA6B,CAAC,GAAG,2BAA2B,CAAC", "sourcesContent": ["import { serialize } from \"../../Misc/decorators\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { ICameraInput } from \"../../Cameras/cameraInputsManager\";\r\nimport { CameraInputTypes } from \"../../Cameras/cameraInputsManager\";\r\nimport type { FreeCamera } from \"../../Cameras/freeCamera\";\r\nimport type { KeyboardInfo } from \"../../Events/keyboardEvents\";\r\nimport { KeyboardEventTypes } from \"../../Events/keyboardEvents\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport type { Engine } from \"../../Engines/engine\";\r\nimport { Tools } from \"../../Misc/tools\";\r\n/**\r\n * Manage the keyboard inputs to control the movement of a free camera.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n */\r\nexport class FreeCameraKeyboardMoveInput implements ICameraInput<FreeCamera> {\r\n    /**\r\n     * Defines the camera the input is attached to.\r\n     */\r\n    public camera: FreeCamera;\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control the forward move of the camera.\r\n     */\r\n    @serialize()\r\n    public keysUp = [38];\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control the upward move of the camera.\r\n     */\r\n    @serialize()\r\n    public keysUpward = [33];\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control the backward move of the camera.\r\n     */\r\n    @serialize()\r\n    public keysDown = [40];\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control the downward move of the camera.\r\n     */\r\n    @serialize()\r\n    public keysDownward = [34];\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control the left strafe move of the camera.\r\n     */\r\n    @serialize()\r\n    public keysLeft = [37];\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control the right strafe move of the camera.\r\n     */\r\n    @serialize()\r\n    public keysRight = [39];\r\n\r\n    /**\r\n     * Defines the pointer angular sensibility  along the X and Y axis or how fast is the camera rotating.\r\n     */\r\n    @serialize()\r\n    public rotationSpeed = 0.5;\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control the left rotation move of the camera.\r\n     */\r\n    @serialize()\r\n    public keysRotateLeft: number[] = [];\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control the right rotation move of the camera.\r\n     */\r\n    @serialize()\r\n    public keysRotateRight: number[] = [];\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control the up rotation move of the camera.\r\n     */\r\n    @serialize()\r\n    public keysRotateUp: number[] = [];\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control the down rotation move of the camera.\r\n     */\r\n    @serialize()\r\n    public keysRotateDown: number[] = [];\r\n\r\n    private _keys = new Array<number>();\r\n    private _onCanvasBlurObserver: Nullable<Observer<Engine>>;\r\n    private _onKeyboardObserver: Nullable<Observer<KeyboardInfo>>;\r\n    private _engine: Engine;\r\n    private _scene: Scene;\r\n\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public attachControl(noPreventDefault?: boolean): void {\r\n        // eslint-disable-next-line prefer-rest-params\r\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(arguments);\r\n        if (this._onCanvasBlurObserver) {\r\n            return;\r\n        }\r\n\r\n        this._scene = this.camera.getScene();\r\n        this._engine = this._scene.getEngine();\r\n\r\n        this._onCanvasBlurObserver = this._engine.onCanvasBlurObservable.add(() => {\r\n            this._keys.length = 0;\r\n        });\r\n\r\n        this._onKeyboardObserver = this._scene.onKeyboardObservable.add((info) => {\r\n            const evt = info.event;\r\n            if (!evt.metaKey) {\r\n                if (info.type === KeyboardEventTypes.KEYDOWN) {\r\n                    if (\r\n                        this.keysUp.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysDown.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysLeft.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysRight.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysUpward.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysDownward.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysRotateLeft.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysRotateRight.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysRotateUp.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysRotateDown.indexOf(evt.keyCode) !== -1\r\n                    ) {\r\n                        const index = this._keys.indexOf(evt.keyCode);\r\n\r\n                        if (index === -1) {\r\n                            this._keys.push(evt.keyCode);\r\n                        }\r\n                        if (!noPreventDefault) {\r\n                            evt.preventDefault();\r\n                        }\r\n                    }\r\n                } else {\r\n                    if (\r\n                        this.keysUp.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysDown.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysLeft.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysRight.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysUpward.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysDownward.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysRotateLeft.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysRotateRight.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysRotateUp.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysRotateDown.indexOf(evt.keyCode) !== -1\r\n                    ) {\r\n                        const index = this._keys.indexOf(evt.keyCode);\r\n\r\n                        if (index >= 0) {\r\n                            this._keys.splice(index, 1);\r\n                        }\r\n                        if (!noPreventDefault) {\r\n                            evt.preventDefault();\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        });\r\n    }\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    public detachControl(): void {\r\n        if (this._scene) {\r\n            if (this._onKeyboardObserver) {\r\n                this._scene.onKeyboardObservable.remove(this._onKeyboardObserver);\r\n            }\r\n\r\n            if (this._onCanvasBlurObserver) {\r\n                this._engine.onCanvasBlurObservable.remove(this._onCanvasBlurObserver);\r\n            }\r\n            this._onKeyboardObserver = null;\r\n            this._onCanvasBlurObserver = null;\r\n        }\r\n        this._keys.length = 0;\r\n    }\r\n\r\n    /**\r\n     * Update the current camera state depending on the inputs that have been used this frame.\r\n     * This is a dynamically created lambda to avoid the performance penalty of looping for inputs in the render loop.\r\n     */\r\n    public checkInputs(): void {\r\n        if (this._onKeyboardObserver) {\r\n            const camera = this.camera;\r\n            // Keyboard\r\n            for (let index = 0; index < this._keys.length; index++) {\r\n                const keyCode = this._keys[index];\r\n                const speed = camera._computeLocalCameraSpeed();\r\n\r\n                if (this.keysLeft.indexOf(keyCode) !== -1) {\r\n                    camera._localDirection.copyFromFloats(-speed, 0, 0);\r\n                } else if (this.keysUp.indexOf(keyCode) !== -1) {\r\n                    camera._localDirection.copyFromFloats(0, 0, speed);\r\n                } else if (this.keysRight.indexOf(keyCode) !== -1) {\r\n                    camera._localDirection.copyFromFloats(speed, 0, 0);\r\n                } else if (this.keysDown.indexOf(keyCode) !== -1) {\r\n                    camera._localDirection.copyFromFloats(0, 0, -speed);\r\n                } else if (this.keysUpward.indexOf(keyCode) !== -1) {\r\n                    camera._localDirection.copyFromFloats(0, speed, 0);\r\n                } else if (this.keysDownward.indexOf(keyCode) !== -1) {\r\n                    camera._localDirection.copyFromFloats(0, -speed, 0);\r\n                } else if (this.keysRotateLeft.indexOf(keyCode) !== -1) {\r\n                    camera._localDirection.copyFromFloats(0, 0, 0);\r\n                    camera.cameraRotation.y -= this._getLocalRotation();\r\n                } else if (this.keysRotateRight.indexOf(keyCode) !== -1) {\r\n                    camera._localDirection.copyFromFloats(0, 0, 0);\r\n                    camera.cameraRotation.y += this._getLocalRotation();\r\n                } else if (this.keysRotateUp.indexOf(keyCode) !== -1) {\r\n                    camera._localDirection.copyFromFloats(0, 0, 0);\r\n                    camera.cameraRotation.x -= this._getLocalRotation();\r\n                } else if (this.keysRotateDown.indexOf(keyCode) !== -1) {\r\n                    camera._localDirection.copyFromFloats(0, 0, 0);\r\n                    camera.cameraRotation.x += this._getLocalRotation();\r\n                }\r\n\r\n                if (camera.getScene().useRightHandedSystem) {\r\n                    camera._localDirection.z *= -1;\r\n                }\r\n\r\n                camera.getViewMatrix().invertToRef(camera._cameraTransformMatrix);\r\n                Vector3.TransformNormalToRef(camera._localDirection, camera._cameraTransformMatrix, camera._transformedDirection);\r\n                camera.cameraDirection.addInPlace(camera._transformedDirection);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of the current input.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"FreeCameraKeyboardMoveInput\";\r\n    }\r\n\r\n    /** @internal */\r\n    public _onLostFocus(): void {\r\n        this._keys.length = 0;\r\n    }\r\n\r\n    /**\r\n     * Get the friendly name associated with the input class.\r\n     * @returns the input friendly name\r\n     */\r\n    public getSimpleName(): string {\r\n        return \"keyboard\";\r\n    }\r\n\r\n    private _getLocalRotation(): number {\r\n        let rotation = (this.rotationSpeed * this._engine.getDeltaTime()) / 1000;\r\n        if (this.camera.getScene().useRightHandedSystem) {\r\n            rotation *= -1;\r\n        }\r\n        if (this.camera.parent && this.camera.parent._getWorldMatrixDeterminant() < 0) {\r\n            rotation *= -1;\r\n        }\r\n        return rotation;\r\n    }\r\n}\r\n\r\n(<any>CameraInputTypes)[\"FreeCameraKeyboardMoveInput\"] = FreeCameraKeyboardMoveInput;\r\n"]}