import type { IMinimalMotionControllerObject, MotionControllerComponentType } from "./webXRAbstractMotionController";
import { Observable } from "../../Misc/observable";
import type { IDisposable } from "../../scene";
/**
 * X-Y values for axes in WebXR
 */
export interface IWebXRMotionControllerAxesValue {
    /**
     * The value of the x axis
     */
    x: number;
    /**
     * The value of the y-axis
     */
    y: number;
}
/**
 * changed / previous values for the values of this component
 */
export interface IWebXRMotionControllerComponentChangesValues<T> {
    /**
     * current (this frame) value
     */
    current: T;
    /**
     * previous (last change) value
     */
    previous: T;
}
/**
 * Represents changes in the component between current frame and last values recorded
 */
export interface IWebXRMotionControllerComponentChanges {
    /**
     * will be populated with previous and current values if axes changed
     */
    axes?: IWebXRMotionControllerComponentChangesValues<IWebXRMotionControllerAxesValue>;
    /**
     * will be populated with previous and current values if pressed changed
     */
    pressed?: IWebXRMotionControllerComponentChangesValues<boolean>;
    /**
     * will be populated with previous and current values if touched changed
     */
    touched?: IWebXRMotionControllerComponentChangesValues<boolean>;
    /**
     * will be populated with previous and current values if value changed
     */
    value?: IWebXRMotionControllerComponentChangesValues<number>;
}
/**
 * This class represents a single component (for example button or thumbstick) of a motion controller
 */
export declare class WebXRControllerComponent implements IDisposable {
    /**
     * the id of this component
     */
    id: string;
    /**
     * the type of the component
     */
    type: MotionControllerComponentType;
    private _buttonIndex;
    private _axesIndices;
    private _axes;
    private _changes;
    private _currentValue;
    private _hasChanges;
    private _pressed;
    private _touched;
    /**
     * button component type
     */
    static BUTTON_TYPE: MotionControllerComponentType;
    /**
     * squeeze component type
     */
    static SQUEEZE_TYPE: MotionControllerComponentType;
    /**
     * Thumbstick component type
     */
    static THUMBSTICK_TYPE: MotionControllerComponentType;
    /**
     * Touchpad component type
     */
    static TOUCHPAD_TYPE: MotionControllerComponentType;
    /**
     * trigger component type
     */
    static TRIGGER_TYPE: MotionControllerComponentType;
    /**
     * If axes are available for this component (like a touchpad or thumbstick) the observers will be notified when
     * the axes data changes
     */
    onAxisValueChangedObservable: Observable<{
        x: number;
        y: number;
    }>;
    /**
     * Observers registered here will be triggered when the state of a button changes
     * State change is either pressed / touched / value
     */
    onButtonStateChangedObservable: Observable<WebXRControllerComponent>;
    /**
     * Creates a new component for a motion controller.
     * It is created by the motion controller itself
     *
     * @param id the id of this component
     * @param type the type of the component
     * @param _buttonIndex index in the buttons array of the gamepad
     * @param _axesIndices indices of the values in the axes array of the gamepad
     */
    constructor(
    /**
     * the id of this component
     */
    id: string, 
    /**
     * the type of the component
     */
    type: MotionControllerComponentType, _buttonIndex?: number, _axesIndices?: number[]);
    /**
     * The current axes data. If this component has no axes it will still return an object { x: 0, y: 0 }
     */
    get axes(): IWebXRMotionControllerAxesValue;
    /**
     * Get the changes. Elements will be populated only if they changed with their previous and current value
     */
    get changes(): IWebXRMotionControllerComponentChanges;
    /**
     * Return whether or not the component changed the last frame
     */
    get hasChanges(): boolean;
    /**
     * is the button currently pressed
     */
    get pressed(): boolean;
    /**
     * is the button currently touched
     */
    get touched(): boolean;
    /**
     * Get the current value of this component
     */
    get value(): number;
    /**
     * Dispose this component
     */
    dispose(): void;
    /**
     * Are there axes correlating to this component
     * @returns true is axes data is available
     */
    isAxes(): boolean;
    /**
     * Is this component a button (hence - pressable)
     * @returns true if can be pressed
     */
    isButton(): boolean;
    /**
     * update this component using the gamepad object it is in. Called on every frame
     * @param nativeController the native gamepad controller object
     */
    update(nativeController: IMinimalMotionControllerObject): void;
}
