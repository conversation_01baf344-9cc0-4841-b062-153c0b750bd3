{"version": 3, "file": "dice-box.es.js", "sources": ["../src/components/world/canvas.js", "../src/helpers/index.js", "../src/WorldFacade.js"], "sourcesContent": ["import './canvas.css'\n\nfunction createCanvas(options) {\n  const { selector, id } = options\n\n  let container = document.body\n  let canvas = document.createElement('canvas')\n  canvas.id = id\n  canvas.classList.add('dice-box-canvas')\n\n  if(selector) {\n    if (typeof selector !== 'string') {\n      throw(new Error(\"You must provide a DOM selector as the first argument in order to render the Dice Box\"))\n    }\n\n    container = document.querySelector(selector)\n\n    if(!container?.nodeName){\n      throw(new Error(`DiceBox target DOM node: '${selector}' not found or not available yet. Try invoking inside a DOMContentLoaded event`))\n    }\n  }\n\n  container.appendChild(canvas)\n\n  return canvas\n}\n\nexport { createCanvas }", "export function lerp(a, b, alpha) {\n  return a * (1 - alpha) + b * alpha;\n}\n\n/**\n * Create UUIDs \n * @return {string} Unique UUID\n */\nexport const createUUID = () => {\n  return ([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g, c => {\n    const crypto = window.crypto || window.msCrypto\n    //eslint-disable-next-line\n    return (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)\n  })\n}\n\nexport const recursiveSearch = (obj, searchKey, results = []) => {\n\tconst r = results;\n\tObject.keys(obj).forEach(key => {\n\t\tconst value = obj[key];\n\t\t// if(key === searchKey && typeof value !== 'object'){\n\t\tif(key === searchKey){\n\t\t\tr.push(value);\n\t\t} else if(value && typeof value === 'object'){\n\t\t\trecursiveSearch(value, searchKey, r);\n\t\t}\n\t});\n\treturn r;\n};\n\n/**\n * Debounce functions for better performance\n * (c) 2021 <PERSON>, MIT License, https://gomakethings.com\n * @param  {Function} fn The function to debounce\n */\nexport const debounce = (fn) => {\n\n\t// Setup a timer\n\tlet timeout;\n\n\t// Return a function to run debounced\n\treturn function () {\n\n\t\t// Setup the arguments\n\t\tlet context = this;\n\t\tlet args = arguments;\n\n\t\t// If there's a timer, cancel it\n\t\tif (timeout) {\n\t\t\twindow.cancelAnimationFrame(timeout);\n\t\t}\n\n\t\t// Setup the new requestAnimationFrame()\n\t\ttimeout = window.requestAnimationFrame(function () {\n\t\t\tfn.apply(context, args);\n\t\t});\n\t};\n}\n\n/**\n * Function Queue - ensures async function calls are triggered in the order they are queued\n * By David Adler (david_adler) @ https://stackoverflow.com/questions/53540348/js-async-await-tasks-queue\n * @param  {object} opts Option to dedupe concurrent executions\n * @return {object} returns object with \"push\" function, \"queue\" array, and \"flush\" function\n */\nexport const createAsyncQueue = (opts = { dedupe: false }) => {\n  const { dedupe } = opts\n  let queue = []\n  let running\n  const push = task => {\n    if (dedupe) queue = []\n    queue.push(task)\n    if (!running) running = start()\n    return running.finally(() => {\n      running = undefined\n    })\n  }\n  const start = async () => {\n    const res = []\n    while (queue.length) {\n      const item = queue.shift()\n      res.push(await item())\n    }\n    return res\n  }\n  return { push, queue, flush: () => running || Promise.resolve([]) }\n}\n\n// deep copy objects and break references to original object\n// Note: does not work with the 'scene' object or objects with circular references\nexport const deepCopy = obj => JSON.parse(JSON.stringify(obj))\n\n// Sleeper function to delay execution for testing\nexport const sleeper = (ms) => {\n  return new Promise(resolve => setTimeout(() => resolve(), ms));\n}\n\nexport class Random {\n  /**\n   * Generate a random number between 0 (inclusive) and 1 (exclusive).\n   * A drop in replacement for Math.random()\n   * @return {number}\n   */\n  static value() {\n    const crypto = window.crypto || window.msCrypto;\n    const buffer = new Uint32Array(1);\n    const int = crypto.getRandomValues(buffer)[0];\n\n    return int / 2**32\n  }\n  /**\n   * Generate a very good random number between min (inclusive) and max (exclusive) by using crypto.getRandomValues() twice.\n   * @param  {number} min\n   * @param  {number} max\n   * @return {number}\n   */\n  static range(min, max) {\n    // return Math.floor(this.value() * (max - min) + min); // plain random\n    return (Math.floor(Math.pow(10,14)*this.value()*this.value())%(max-min+1))+min // super random!\n  }\n}\n\n// https://www.30secondsofcode.org/c/js-colors/p/1\nexport const hexToRGB = hex => {\n  let alpha = false,\n    h = hex.slice(hex.startsWith('#') ? 1 : 0);\n  if (h.length === 3) h = [...h].map(x => x + x).join('');\n  else if (h.length === 8) alpha = true;\n  h = parseInt(h, 16);\n  let val = {\n    r: h >>> 16,\n    g: (h & 0x00ff00) >>> 8,\n    b: (h & 0x0000ff)\n  }\n  if(alpha) {\n    val.r = h >>> 24\n    val.g = (h & 0x00ff0000) >>> 16\n    val.b = (h & 0x0000ff00) >>> 8\n    val.a = (h & 0x000000ff)\n  }\n  return val\n\n};\n\nexport const RGBToHSB = (r, g, b) => {\n  r /= 255;\n  g /= 255;\n  b /= 255;\n  const v = Math.max(r, g, b),\n    n = v - Math.min(r, g, b);\n  const h =\n    n === 0 ? 0 : n && v === r ? (g - b) / n : v === g ? 2 + (b - r) / n : 4 + (r - g) / n;\n  return [60 * (h < 0 ? h + 6 : h), v && (n / v) * 100, v * 100];\n};\n\nexport const hexToHSB = (hex) => {\n  const rgb = hexToRGB(hex)\n  return RGBToHSB(rgb.r,rgb.g,rgb.b)\n}\n\nexport function webgl_support () {\n  try {\n    const canvas = document.createElement('canvas'); \n    return !!window.WebGLRenderingContext && (canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));\n  } catch(e) {\n    return false;\n  }\n}", "import { createCanvas } from './components/world/canvas'\nimport physicsWorker from './components/physics.worker.js?worker&inline'\nimport { debounce, createAsyncQueue, Random, hexToRGB, webgl_support } from './helpers'\n\nconst defaultOptions = {\n\tid: `dice-canvas-${Date.now()}`, // set the canvas id\n\tcontainer: null,\n  enableShadows: true, // do dice cast shadows onto DiceBox mesh?\n\tshadowTransparency: .8,\n\tlightIntensity: 1,\n  delay: 10, // delay between dice being generated - 0 causes stuttering and physics popping\n\tscale: 5, // scale the dice\n\ttheme: 'default', // can be a hex color or a pre-defined theme such as 'purpleRock'\n\tpreloadThemes: [],\n\texternalThemes: {}, // point to CDN paths\n\tthemeColor: '#2e8555', // used for color values or named theme variants - not fully implemented yet // green: #2e8555 // yellow: #feea03\n\toffscreen: true, // use offscreen canvas browser feature for performance improvements - will fallback to false based on feature detection\n\tassetPath: '/assets/dice-box/', // path to 'ammo', 'themes' folders and web workers\n\t// origin: location.origin,\n\torigin: typeof window !== \"undefined\" ? window.location.origin : \"\",\n\tsuspendSimulation: false\n}\n\nclass WorldFacade {\n\trollCollectionData = {}\n\trollGroupData = {}\n\trollDiceData = {}\n\tthemeData = []\n\tthemesLoadedData = {}\n\t#collectionIndex = 0\n\t#groupIndex = 0\n\t#rollIndex = 0\n\t#idIndex = 0\n\t#DiceWorld = {}\n\t#diceWorldPromise\n\t#diceWorldResolve\n\t#DicePhysics\n\t#dicePhysicsPromise\n\t#dicePhysicsResolve\n\t#webgl_support = true\n\tnoop = () => {}\n\n  constructor(options = {}){\n\t\t// allow for depricated config with a warning\n\t\tif (arguments.length === 2 && typeof(arguments[0] === 'string') && typeof(arguments[1] === 'object')) {\n\t\t\tconsole.warn(`You are using the old API. Dicebox constructor accepts a config object as it's only argument. Please read the v1.1.0 docs at https://fantasticdice.games/docs/usage/config`)\n\t\t\toptions = arguments[1]\n\t\t\toptions.container = arguments[0]\n\t\t}\n\n\t\tif(typeof options !== 'object') {\n\t\t\tthrow new Error('Config options should be an object. Config reference: https://fantasticdice.games/docs/usage/config#configuration-options')\n\t\t}\n\t\t// pull out callback functions from options\n\t\tconst { onBeforeRoll, onDieComplete, onRollComplete, onRemoveComplete, onThemeConfigLoaded, onThemeLoaded, ...boxOptions } = options\n\n\t\t// extend defaults with options\n\t\tthis.config = {...defaultOptions, ...boxOptions}\n\n\t\t// assign callback functions\n\t\tthis.onBeforeRoll = options.onBeforeRoll || this.noop\n\t\tthis.onDieComplete = options.onDieComplete || this.noop\n\t\tthis.onRollComplete = options.onRollComplete || this.noop\n\t\tthis.onRemoveComplete = options.onRemoveComplete || this.noop\n\t\tthis.onThemeLoaded = options.onThemeLoaded || this.noop\n\t\tthis.onThemeConfigLoaded = options.onThemeConfigLoaded || this.noop\n\n\t\t// is webGL supported?\n\t\tif(webgl_support()){\n\t\t\t// if a canvas selector is provided then that will be used for the dicebox, otherwise a canvas will be created using the config.id\n\t\t\tthis.canvas = createCanvas({\n\t\t\t\tselector: this.config.container,\n\t\t\t\tid: this.config.id\n\t\t\t})\n\t\t\tthis.isVisible = true\n\t\t} else {\n\t\t\tthis.#webgl_support = false\n\t\t}\n\n\t\t// create a queue to prevent theme being loaded multiple times\n\t\tthis.loadThemeQueue = createAsyncQueue()\n  }\n\n\t// Load the BabylonJS World\n\tasync #loadWorld(){\n\n\t\t// set up a promise to be fulfilled when a message comes back from DiceWorld indicating init is complete\n\t\tthis.#diceWorldPromise = new Promise((resolve, reject) => {\n\t\t\tthis.#diceWorldResolve = resolve\n\t\t})\n\n\t\t// resolve the promise one onInitComplete callback is triggered\n\t\tconst onInitComplete = () => {\n\t\t\tthis.#diceWorldResolve()\n\t\t}\n\n    if(!this.#webgl_support){\n      console.warn('This browser does not support WebGL which is required for 3D rendering. Falling back to random number generator')\n      const WorldNone = await import('./components/world.none').then(module => module.default)\n      this.#DiceWorld = new WorldNone({\n\t\t\t\tcanvas: this.canvas,\n\t\t\t\toptions: this.config,\n\t\t\t\tonInitComplete\n\t\t\t})\n    }\n\t\telse if (\"OffscreenCanvas\" in window && \"transferControlToOffscreen\" in this.canvas && this.config.offscreen) {\n\t\t\t// Ok to use offscreen canvas - transfer control offscreen\n\t\t\tconst WorldOffscreen = await import('./components/world.offscreen').then(module => module.default)\n\t\t\t// WorldOffscreen is just a container class that passes all method calls to the Offscreen Canvas worker\n\t\t\tthis.#DiceWorld = new WorldOffscreen({\n\t\t\t\tcanvas: this.canvas,\n\t\t\t\toptions: this.config,\n\t\t\t\tonInitComplete\n\t\t\t})\n\t\t} else {\n\t\t\tif(this.config.offscreen){\n\t\t\t\tconsole.warn(\"This browser does not support OffscreenCanvas. Using standard canvas fallback.\")\n\t\t\t\tthis.config.offscreen = false\n\t\t\t}\n\t\t\t// code splitting out WorldOnscreen. It's essentially the same as offscreenCanvas.worker.js but communicates with the main thread differently\n\t\t\tconst WorldOnscreen = await import('./components/world.onscreen').then(module => module.default)\n\t\t\tthis.#DiceWorld = new WorldOnscreen({\n\t\t\t\tcanvas: this.canvas,\n\t\t\t\toptions: this.config,\n\t\t\t\tonInitComplete\n\t\t\t})\n\t\t}\n\t}\n\n\t// Load the AmmoJS physics world\n\t#loadPhysics(){\n\t\t// initialize physics world in which AmmoJS runs\n\t\tthis.#DicePhysics = new physicsWorker()\n\t\t// set up a promise to be fulfilled when a message comes back from physics.worker indicating init is complete\n\t\tthis.#dicePhysicsPromise = new Promise((resolve, reject) => {\n\t\t\tthis.#dicePhysicsResolve = resolve\n\t\t})\n\t\tthis.#DicePhysics.onmessage = (e) => {\n\t\t\tswitch( e.data.action ) {\n\t\t\t\tcase \"init-complete\":\n\t\t\t\t\tthis.#dicePhysicsResolve() // fulfill promise so other things can run\n\t\t\t}\n    }\n\t\t// initialize the AmmoJS physics worker\n\t\tthis.#DicePhysics.postMessage({\n\t\t\taction: \"init\",\n\t\t\twidth: this.canvas.clientWidth,\n\t\t\theight: this.canvas.clientHeight,\n\t\t\toptions: this.config\n\t\t})\n\t}\n\n\t#connectWorld(){\n\t\tconst channel = new MessageChannel()\n\t\t\n\t\t// create message channel for the visual world and the physics world to communicate through\n\t\tthis.#DiceWorld.connect(channel.port1)\n\n\t\t// create message channel for this WorldFacade class to communicate with physics world\n\t\tthis.#DicePhysics.postMessage({\n\t\t\taction: \"connect\"\n\t\t},[ channel.port2 ])\n\t}\n\n\tresizeWorld(){\n\t\t// send resize events to workers - debounced for performance\n\t\tconst resizeWorkers = () => {\n\t\t\tthis.#DiceWorld.resize({width: this.canvas.clientWidth, height: this.canvas.clientHeight})\n\t\t\tif(this.#DicePhysics){\n\t\t\t\tthis.#DicePhysics.postMessage({action: \"resize\", width: this.canvas.clientWidth, height: this.canvas.clientHeight});\n\t\t\t}\n\t\t}\n\t\tconst debounceResize = debounce(resizeWorkers)\n\t\twindow.addEventListener(\"resize\", debounceResize)\n\t}\n\n  async init() {\n\t\t// trigger physics first so it can load in parallel with world\n    if(this.#webgl_support){\n      this.#loadPhysics()\n    } else {\n      this.#dicePhysicsPromise = Promise.resolve()\n    }\n\t\tawait this.#loadWorld()\n\t\tthis.resizeWorld()\n\n\t\t// now that DiceWorld is ready we can attach our callbacks\n\t\tthis.#DiceWorld.onRollResult = (result) => {\n\t\t\tconst die = this.rollDiceData[result.rollId]\n\t\t\tconst group = this.rollGroupData[die.groupId]\n\t\t\tconst collection = this.rollCollectionData[die.collectionId]\n\n\t\t\t// map die results back to our rollData\n\t\t\t// since all rolls are references to this.rollDiceDate the values will be added to those objects\n\t\t\tgroup.rolls[die.rollId].value = result.value\n\n\t\t\t// increment the completed roll count for this group\n\t\t\tcollection.completedRolls++\n\t\t\t// if all rolls are completed then resolve the collection promise - returning dice that were in this collection\n\t\t\tif(collection.completedRolls == collection.rolls.length) {\n\t\t\t\t// pull out roll.collectionId and roll.id? They're meant to be internal values\n\t\t\t\tcollection.resolve(Object.values(collection.rolls).map(({collectionId, id, meshName, ...rest}) => rest))\n\t\t\t}\n\n\t\t\t// trigger callback passing individual die result\n\t\t\tconst {collectionId, id, ...returnDie} = die\n\t\t\tthis.onDieComplete(returnDie)\n\t\t}\n\t\tthis.#DiceWorld.onRollComplete = () => {\n\t\t\t// trigger callback passing the roll results\n\t\t\tthis.onRollComplete(this.getRollResults())\n\t\t}\n\n\t\tthis.#DiceWorld.onDieRemoved = (rollId) => {\n\t\t\t// get die information from cache\n\t\t\tlet die = this.rollDiceData[rollId]\n\t\t\tconst collection = this.rollCollectionData[die.removeCollectionId]\n\t\t\tcollection.completedRolls++\n\n\t\t\t// remove this die from cache\n\t\t\tdelete this.rollDiceData[die.rollId]\n\n\t\t\t// remove this die from it's group rolls\n\t\t\tconst group = this.rollGroupData[die.groupId]\n\t\t\tdelete group.rolls[die.rollId]\n\n\t\t\t// parse the group value now that the die has been removed from data\n\t\t\tconst groupData = this.#parseGroup(die.groupId)\n\t\t\t// update the value and quantity values\n\t\t\tgroup.value = groupData.value\n\t\t\tgroup.qty = groupData.rollsArray.length\n\n\t\t\t// if all rolls are completed then resolve the collection promise - returning dice that were removed\n\t\t\tif(collection.completedRolls == collection.rolls.length) {\n\t\t\t\tcollection.resolve(Object.values(collection.rolls).map(({id, ...rest}) => rest))\n\t\t\t}\n\t\t\tconst {collectionId, id, removeCollectionId, meshName, ...returnDie} = die\n\t\t\tthis.onRemoveComplete(returnDie)\n\t\t}\n\n    // wait for both DiceWorld and DicePhysics to initialize\n\t\tawait Promise.all([this.#diceWorldPromise, this.#dicePhysicsPromise])\n    \n    if(this.#DicePhysics){\n      // set up message channels between Dice World and Dice Physics\n      this.#connectWorld()\n    }\n\n\t\t// queue load of the theme defined in the config\n\t\tawait this.loadThemeQueue.push(() => this.loadTheme(this.config.theme))\n\t\t\n\t\t// queue load of other defined themes\n\n\t\tthis.config.preloadThemes.forEach(async function(theme) {\n\t\t\tawait this.loadThemeQueue.push(() => this.loadTheme(theme))\n\t\t}.bind(this))\n\n\n\t\t//TODO: this should probably return a promise\n\t\t// make this method chainable\n\t\treturn this\n  }\n\n\t// fetch the theme config and return a themeData object\n\tasync getThemeConfig(theme){\n\t\tlet basePath = `${this.config.origin}${this.config.assetPath}themes/${theme}`\n\n\t\tif(this.config.externalThemes[theme]){\n\t\t\tbasePath = this.config.externalThemes[theme]\n\t\t}\n\n\n\t\t// fetch the theme.config file\n\t\tlet themeData = await fetch(`${basePath}/theme.config.json`).then(resp => {\n\t\t\tif(resp.ok) {\n\t\t\t\tconst contentType = resp.headers.get(\"content-type\")\n\t\t\t\tif (contentType && contentType.indexOf(\"application/json\") !== -1) {\n\t\t\t\t\treturn resp.json()\n\t\t\t\t} \n\t\t\t\telse if (resp.type && resp.type === 'basic') {\n\t\t\t\t\treturn resp.json()\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\t// return resp\n\t\t\t\t\tthrow new Error(`Incorrect contentType: ${contentType}. Expected \"application/json\" or \"basic\"`)\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tthrow new Error(`Unable to fetch config file for theme: '${theme}'. Request rejected with status ${resp.status}: ${resp.statusText}`)\n\t\t\t}\n\t\t}).catch(error => console.error(error))\n\n\t\tif(!themeData){\n\t\t\tthrow new Error(\"No theme config data to work with.\")\n\t\t}\n\n\t\tlet meshName = 'default'\n\t\tlet meshFilePath = `${this.config.origin}${this.config.assetPath}themes/default/default.json`\n\n\t\tif(themeData.hasOwnProperty('meshFile')){\n\t\t\tmeshName = themeData.meshFile.replace(/(.*)\\..{2,4}$/,'$1')\n\t\t\tmeshFilePath = `${basePath}/${themeData.meshFile}`\n\t\t}\n\n\t\t// if diceAvailable is not specified then assume the default set of seven\n\t\tif(!themeData.hasOwnProperty('diceAvailable')){\n\t\t\tthrow new Error('A theme must indicate which dice are available by defining \"diceAvailable\".')\n\t\t}\n\n\t\t// extend themes\n\t\t// if a theme extends another theme then the diceAvailable will be added to the diceExtended value of the extended theme\n\t\t// the extended them can then roll those dice as if it were part of it's own theme\n\t\t// example: diceOfRolling-fate extends diceOfRolling. You can now roll 3dfate with the theme: 'diceOfRolling'\n\n\t\tif(themeData.hasOwnProperty(\"extends\")){\n\t\t\tconst target = await this.loadTheme(themeData.extends).catch(error => console.error(error))\n\n\t\t\t// can not extend a theme that extends another theme\n\t\t\tif(target.hasOwnProperty(\"extends\")){\n\t\t\t\tthrow new Error('Cannot extend a theme that extends another theme.')\n\t\t\t}\n\n\t\t\t// add target diceAvailable into theme.diceExtended\n\t\t\tconst newDice = {}\n\t\t\tthemeData.diceAvailable.forEach(die => {\n\t\t\t\tnewDice[die] = themeData.systemName\n\t\t\t})\n\t\t\ttarget.diceExtended = {...target.diceExtended, ...newDice}\n\t\t\t// set the theme to the parent theme\n\t\t\tthis.config.theme = themeData.extends\n\t\t}\n\n\n\t\tObject.assign(themeData,\n\t\t\t{\n\t\t\t\tbasePath,\n\t\t\t\tmeshFilePath,\n\t\t\t\tmeshName,\n\t\t\t\ttheme,\n\t\t\t}\n\t\t)\n\n\t\t// this.onThemeConfigLoaded(themeData)\n\n\t\treturn themeData\n\t}\n\n\tasync loadTheme(theme){\n\t\t// check the cache\n\t\tif(this.themesLoadedData[theme]) {\n\t\t\t// short circuit if theme has been previously loaded\n\t\t\t// console.log(`${theme} has already been loaded. Returning cache`)\n\t\t\treturn this.themesLoadedData[theme]\n\t\t}\n\t\t// console.log(`${theme} is loading ...`)\n\n\t\t// fetch and save the themeData for later\n\t\tconst themeConfig = this.themesLoadedData[theme] = await this.getThemeConfig(theme).catch(error => console.error(error))\n\t\tthis.onThemeConfigLoaded(themeConfig)\n\n\t\tif(!themeConfig) return\n\n\t\t// pass config onto DiceWorld to load - the theme loader needs 'scene' from DiceWorld\n\t\tawait this.#DiceWorld.loadTheme(themeConfig).catch(error => console.error(error))\n\n\t\tthis.onThemeLoaded(themeConfig)\n\n\t\treturn themeConfig\n\t}\n\n\t// TODO: use getter and setter\n\t// change config options\n\tasync updateConfig(options) {\n\t\tconst newConfig = {...this.config,...options}\n\t\t// console.log('newConfig', newConfig)\n\t\t// const config = await this.loadThemeQueue.push(() => this.loadTheme(newConfig.theme))\n\t\t// const themeData = config.at(-1) //get the last entry returned from the queue\n\n\t\tthis.config = newConfig\n\n\t\tif(newConfig.theme){\n\t\t\tconst config = await this.loadThemeQueue.push(() => this.loadTheme(newConfig.theme))\n\t\t\tconst themeData = config.at(-1) //get the last entry returned from the queue\n\t\t\tif(themeData.hasOwnProperty(\"extends\")){\n\t\t\t\t// set the theme to the parent theme\n\t\t\t\tthis.config.theme = themeData.extends\n\t\t\t}\n\t\t}\n\n\t\t// pass updates to DiceWorld\n\t\tthis.#DiceWorld.updateConfig(newConfig)\n\n\t\tif(this.#DicePhysics){\n\t\t// pass updates to PhysicsWorld\n\t\t\tthis.#DicePhysics.postMessage({\n\t\t\t\taction: 'updateConfig',\n\t\t\t\toptions: newConfig\n\t\t\t})\n\t\t}\n\n\t\t// make this method chainable\n\t\treturn this\n\t}\n\n\tclear() {\n\t\t// reset indexes\n\t\tthis.#collectionIndex = 0\n\t\tthis.#groupIndex = 0\n\t\tthis.#rollIndex = 0\n\t\tthis.#idIndex = 0\n\t\t// reset internal data objects\n\t\tthis.rollCollectionData = {}\n\t\tthis.rollGroupData = {}\n\t\tthis.rollDiceData = {}\n\t\t// clear all rendered die bodies\n\t\tthis.#DiceWorld.clear()\n\t\tif(this.#DicePhysics){\n\t\t\t// clear all physics die bodies\n\t\t\tthis.#DicePhysics.postMessage({action: \"clearDice\"})\n\t\t}\n\n\t\t// make this method chainable\n\t\treturn this\n  }\n\n\thide(className) {\n\t\tif(className){\n\t\t\tthis.canvas.dataset.hideClass = className\n\t\t\tthis.canvas.classList.add(className)\n\t\t} else {\n\t\t\tthis.canvas.style.display = 'none'\n\t\t}\n\n\t\tthis.isVisible = false;\n\n\t\t// make this method chainable\n\t\treturn this\n\t}\n\n\tshow() {\n\t\tconst hideClass = this.canvas.dataset?.hideClass\n\t\tif(hideClass){\n\t\t\tdelete this.canvas.dataset.hideClass\n\t\t\tthis.canvas.classList.remove(hideClass)\n\t\t} else {\n\t\t\tthis.canvas.style.display = 'block'\n\t\t}\n\t\tthis.isVisible = true;\n\t\tthis.resizeWorld();\n\n\t\t// make this method chainable\n\t\treturn this\n\t}\n\n\t// TODO: pass data with roll - such as roll name. Passed back at the end in the results\n\troll(notation, {theme = this.config.theme, themeColor = this.config.themeColor, newStartPoint = true} = {}) {\n\t\t// note: to add to a roll on screen use .add method\n\t\t// reset the offscreen worker and physics worker with each new roll\n\t\tthis.clear()\n\t\tconst collectionId = this.#collectionIndex++\n\t\t\n\t\tthis.rollCollectionData[collectionId] = new Collection({\n\t\t\tid: collectionId,\n\t\t\tnotation,\n\t\t\ttheme,\n\t\t\tthemeColor,\n\t\t\tnewStartPoint\n\t\t})\n\n\t\tconst parsedNotation = this.createNotationArray(notation, this.themesLoadedData[theme].diceAvailable)\n\t\tthis.#makeRoll(parsedNotation, collectionId)\n\n\t\t// returns a Promise that is resolved in onRollComplete\n\t\treturn this.rollCollectionData[collectionId].promise\n\t}\n\n  add(notation, {theme = this.config.theme, themeColor = this.config.themeColor, newStartPoint = true} = {}) {\n\n\t\tconst collectionId = this.#collectionIndex++\n\n\t\tthis.rollCollectionData[collectionId] = new Collection({\n\t\t\tid: collectionId,\n\t\t\tnotation,\n\t\t\ttheme,\n\t\t\tthemeColor,\n\t\t\tnewStartPoint\n\t\t})\n\t\t\n\t\tconst parsedNotation = this.createNotationArray(notation, this.themesLoadedData[theme].diceAvailable)\n\t\tthis.#makeRoll(parsedNotation, collectionId)\n\n\t\t// returns a Promise that is resolved in onRollComplete\n\t\treturn this.rollCollectionData[collectionId].promise\n  }\n\n\treroll(notation, {remove = false, hide = false, newStartPoint = true} = {}) {\n\t\t// TODO: add hide if you want to keep the die result for an external parser\n\n\t\t// ensure notation is an array\n\t\tconst rollArray = Array.isArray(notation) ? notation : [notation]\n\n\t\t// destructure out 'sides', 'theme', 'groupId', 'rollId' - basically just getting rid of value - could do ({value, ...rest}) => rest\n\t\tconst cleanNotation = rollArray.map(({value, ...rest}) => rest)\n\n\t\tif(remove === true){\n\t\t\tthis.remove(cleanNotation, {hide})\n\t\t}\n\n\t\t// .add will return a promise that will then be returned here\n\t\treturn this.add(cleanNotation, {newStartPoint})\n\t}\n\n\tremove(notation, {hide = false} = {}) {\n\t\t// ensure notation is an array\n\t\tconst rollArray = Array.isArray(notation) ? notation : [notation]\n\n\t\tconst collectionId = this.#collectionIndex++\n\n\t\tthis.rollCollectionData[collectionId] = new Collection({\n\t\t\tid: collectionId,\n\t\t\tnotation,\n\t\t\trolls: rollArray,\n\t\t})\n\n\t\t// loop through each die to be removed\n\t\trollArray.map(die => {\n\t\t\t// add the collectionId to the die so it can be looked up in the callback\n\t\t\tthis.rollDiceData[die.rollId].removeCollectionId = collectionId\n\t\t\t// assign the id for this die from our cache - required for removal\n\t\t\t// die.id = this.rollDiceData[die.rollId].id - note: can appear in async roll result data if attached to die object\n\t\t\tlet id = this.rollDiceData[die.rollId].id\n\t\t\t// remove the die from the render - don't like having to pass two ids. rollId is passed over just so it can be passed back for callback\n\t\t\tthis.#DiceWorld.remove({id,rollId: die.rollId})\n\t\t\tif(this.#DicePhysics){\n\t\t\t\t// remove the die from the physics bodies\n\t\t\t\tthis.#DicePhysics.postMessage({action: \"removeDie\", id })\n\t\t\t}\n\t\t})\n\n\t\treturn this.rollCollectionData[collectionId].promise\n\t}\n\n\t// used by both .add and .roll - .roll clears the box and .add does not\n\tasync #makeRoll(parsedNotation, collectionId){\n\n\t\tthis.onBeforeRoll(parsedNotation)\n\n\t\tconst collection = this.rollCollectionData[collectionId]\n\t\tlet newStartPoint = collection.newStartPoint\n\n\t\t// loop through the number of dice in the group and roll each one\n\t\tparsedNotation.forEach(async notation => {\n\t\t\tif(!notation.sides) {\n\t\t\t\tthrow new Error(\"Improper dice notation or unable to parse notation\")\n\t\t\t}\n\t\t\tlet theme = notation.theme || collection.theme || this.config.theme\n\t\t\tconst themeColor = notation.themeColor || collection.themeColor || this.config.themeColor\n\t\t\tconst rolls = {}\n\t\t\tconst hasGroupId = notation.groupId !== undefined\n\t\t\tlet index\n\t\t\t\n\t\t\t// load the theme, will be short circuited if previously loaded\n\t\t\tconst loadTheme = () => this.loadTheme(theme)\n\t\t\tawait this.loadThemeQueue.push(loadTheme)\n\n\t\t\t// const {meshName, diceAvailable, diceInherited = {}, material: { type: materialType }} = this.themesLoadedData[theme]\n\t\t\tlet meshName = this.themesLoadedData[theme].meshName\n\t\t\tlet diceAvailable = this.themesLoadedData[theme]?.diceAvailable\n\t\t\tlet diceExtended = this.themesLoadedData[theme].diceExtended || {}\n\t\t\tlet materialType = this.themesLoadedData[theme]?.material?.type\n\n\t\t\tconst diceExtra = Object.keys(diceExtended)\n\n\t\t\tif(diceExtra && diceExtra.includes(notation.sides)){\n\t\t\t\ttheme = diceExtended[notation.sides]\n\t\t\t\tconst loadExtendedTheme = () => this.loadTheme(theme)\n\t\t\t\tthis.loadThemeQueue.push(loadExtendedTheme)\n\t\t\t\tmeshName = this.themesLoadedData[theme].meshName\n\t\t\t\tdiceAvailable = this.themesLoadedData[theme]?.diceAvailable\n\t\t\t\tmaterialType = this.themesLoadedData[theme]?.material?.type\n\t\t\t}\n\n\t\t\tlet colorSuffix = '', color\n\n\t\t\tif(materialType === \"color\") {\n\t\t\t\tcolor = hexToRGB(themeColor)\n\t\t\t\t// dat.gui uses HSB(a.k.a HSV) brightness greater than .5 and saturation less than .5\n\t\t\t\tcolorSuffix = ((color.r*0.299 + color.g*0.587 + color.b*0.114) > 175) ? '_dark' : '_light'\n\t\t\t}\n\n\t\t\t// TODO: should I validate that added dice are only joining groups of the same \"sides\" value - e.g.: d6's can only be added to groups when sides: 6? Probably.\n\t\t\tfor (var i = 0, len = notation.qty; i < len; i++) {\n\t\t\t\t// id's start at zero and zero can be falsy, so we check for undefined\n\t\t\t\tlet rollId = notation.rollId !== undefined ? notation.rollId : this.#rollIndex++\n\t\t\t\tlet id = notation.id !== undefined ? notation.id : this.#idIndex++\n\t\t\t\tindex = hasGroupId ? notation.groupId : this.#groupIndex\n\n        // when a roll object is passed in, notation.sides could be an integer - convert the die type to include the \"d\" prefix so it will match the model names\n\t\t\t\tconst dieType = Number.isInteger(notation.sides) ? `d${notation.sides}` : notation.sides\n\n        // when a roll string is passed in, notation.sides will be a string - convert it to an integer so it will match the object data type of a notation object\n        if(/^d[1-9]{1}[0-9]{0,1}0?$/.test(notation.sides)){\n          notation.sides =  parseInt(notation.sides.replace('d', ''))\n        }\n\n\t\t\t\tconst roll = {\n\t\t\t\t\tsides: notation.sides,\n\t\t\t\t\tdata: notation.data,\n\t\t\t\t\tdieType,\n\t\t\t\t\tgroupId: index,\n\t\t\t\t\tcollectionId: collection.id,\n\t\t\t\t\trollId,\n\t\t\t\t\tid,\n\t\t\t\t\ttheme,\n\t\t\t\t\tthemeColor,\n\t\t\t\t\tmeshName\n\t\t\t\t}\n\n\t\t\t\trolls[rollId] = roll\n\t\t\t\tthis.rollDiceData[rollId] = roll\n\t\t\t\tcollection.rolls.push(this.rollDiceData[rollId])\n\n\t\t\t\t// TODO: eliminate the 'd' for more flexible naming such as 'fate' - ensure numbers are strings\n\t\t\t\tif (roll.sides === 'fate' && (!diceAvailable.includes(dieType) && !diceExtra.includes(dieType)) || roll.sides === 'fate' && !this.#webgl_support){\n\t\t\t\t\tconsole.warn(`fate die unavailable in '${theme}' theme. Using fallback.`)\n\t\t\t\t\tconst min = -1\n\t\t\t\t\tconst max = 1\n\t\t\t\t\troll.value = Random.range(min,max)\n\t\t\t\t\tthis.#DiceWorld.addNonDie(roll)\n\t\t\t\t} else if(this.config.suspendSimulation || (!diceAvailable.includes(dieType) && !diceExtra.includes(dieType)) || !this.#webgl_support){\n\t\t\t\t\t// check if the requested roll is available in the current theme, if not then use crypto fallback\n\t\t\t\t\tconst warning = \n\t\t\t\t\t!this.#webgl_support \n\t\t\t\t\t\t? `This browser does not support webGL. Using random number fallback.` \n\t\t\t\t\t\t: this.config.suspendSimulation \n\t\t\t\t\t\t\t? \"3D simulation suspended. Using fallback.\" \n\t\t\t\t\t\t\t: `${roll.sides} die unavailable in '${theme}' theme. Using fallback.`\n\t\t\t\t\tconsole.warn(warning)\n\t\t\t\t\tconst max = Number.isInteger(roll.sides) ? roll.sides : parseInt(roll.sides.replace(/\\D/g,''))\n\t\t\t\t\troll.value = Random.range(1, max)\n\t\t\t\t\tthis.#DiceWorld.addNonDie(roll)\n\t\t\t\t} else {\n\t\t\t\t\tlet extendedTheme\n\t\t\t\t\tif(diceExtra.includes(dieType)) {\n\t\t\t\t\t\tconst extendedThemeName = diceExtended[dieType]\n\t\t\t\t\t\textendedTheme = this.themesLoadedData[extendedThemeName]\n\t\t\t\t\t}\n\t\t\t\t\tthis.#DiceWorld.add({\n\t\t\t\t\t\t...roll,\n\t\t\t\t\t\tnewStartPoint,\n\t\t\t\t\t\ttheme: extendedTheme?.systemName || theme,\n\t\t\t\t\t\tmeshName: extendedTheme?.meshName || meshName,\n\t\t\t\t\t\tcolorSuffix\n\t\t\t\t\t})\n\t\t\t\t}\n\n\t\t\t\t// turn flag off\n\t\t\t\tnewStartPoint = false\n\t\t\t}\n\n\t\t\tif(hasGroupId) {\n\t\t\t\tObject.assign(this.rollGroupData[index].rolls, rolls)\n\t\t\t} else {\n\t\t\t\t// save this roll group for later\n\t\t\t\tnotation.rolls = rolls\n\t\t\t\tnotation.id = index\n\t\t\t\tthis.rollGroupData[index] = notation\n\t\t\t\t++this.#groupIndex\n\t\t\t}\n\t\t})\n\t}\n\n\t// accepts simple notations eg: 4d6\n\t// accepts array of notations eg: ['4d6','2d10']\n\t// accepts object {sides:int, qty:int}\n\t// accepts array of objects eg: [{sides:int, qty:int, mods:[]}]\n\tcreateNotationArray(input, diceAvailable){\n\t\tconst notation = Array.isArray( input ) ? input : [ input ]\n\t\tlet parsedNotation = []\n\n\n\t\tconst verifyObject = ( object ) => {\n\t\t\tif(!object.hasOwnProperty('qty')) {\n\t\t\t\tobject.qty = 1\n\t\t\t}\n\t\t\tif ( object.hasOwnProperty('sides') ) {\n\t\t\t\tif(object.sides === '100'){\n\t\t\t\t\tobject.sides = 100\n\t\t\t\t\tobject.data = 'single'\n\t\t\t\t}\n\t\t\t\treturn true\n\t\t\t} else {\n\t\t\t\tconst err = \"Roll notation is missing sides\"\n\t\t\t\tthrow new Error(err);\n\t\t\t}\n\t\t}\n\n\t\tconst incrementId = (key) => {\n\t\t\tkey = key.toString()\n\t\t\tlet splitKey = key.split(\".\")\n\t\t\tif(splitKey[1]){\n\t\t\t\tsplitKey[1] = parseInt(splitKey[1]) + 1\n\t\t\t} else {\n\t\t\t\tsplitKey[1] = 1\n\t\t\t}\n\t\t\treturn splitKey[0] + \".\" + splitKey[1]\n\t\t}\n\n\t\t// verify that the rollId is unique. If not then increment it by .1\n\t\t// rollIds become keys in the rollDiceData object, so they must be unique or they will overwrite another entry\n\t\tconst verifyRollId = ( object ) => {\n\t\t\tif(object.hasOwnProperty('rollId')){\n\t\t\t\tif(this.rollDiceData.hasOwnProperty(object.rollId)){\n\t\t\t\t\tobject.rollId = incrementId(object.rollId)\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(!object.hasOwnProperty('modifier')){\n\t\t\t\tobject.modifier = 0\n\t\t\t}\n\t\t}\n\n\t\t// notation is an array of strings or objects\n\t\tnotation.forEach(roll => {\n\t\t\t// console.log('roll', roll)\n\t\t\t// if notation is an array of strings\n\t\t\tif ( typeof roll === 'string' ) {\n\t\t\t\tparsedNotation.push( this.parse( roll, diceAvailable ) )\n\t\t\t} else if ( typeof notation === 'object' ) {\n\t\t\t\tverifyRollId( roll )\n\t\t\t\tverifyObject( roll )  && parsedNotation.push( roll )\n\t\t\t}\n\t\t})\n\n\t\treturn parsedNotation\n\t}\n\n  // parse text die notation such as 2d10+3 => {number:2, type:6, modifier:3}\n  // taken from https://github.com/ChapelR/dice-notation\n  parse(notation, diceAvailable) {\n    const diceNotation = /(\\d+)([dD]{1}\\d+)(.*)$/i\n\t\tconst percentNotation = /(\\d+)[dD](00|%)(.*)$/i\n\t\tconst fudgeNotation = /(\\d+)[dD](f+[ate]*)(.*)$/i\n\t\t// const customNotation = /(\\d+)[dD](.*)([+-])/i\n\t\tconst customNotation = /(\\d+)[dD]([\\d\\w]+)([+-]{0,1}\\d+)?/i\n    const modifier = /([+-])(\\d+)/\n    const cleanNotation = notation.trim().replace(/\\s+/g, '')\n    const validNumber = (n, err) => {\n      n = Number(n)\n      if (Number.isNaN(n) || !Number.isInteger(n) || n < 1) {\n        throw new Error(err);\n      }\n      return n\n    }\n\n\t\t// match percentNotation before diceNotation\n    const roll = cleanNotation.match(percentNotation) || cleanNotation.match(diceNotation) || cleanNotation.match(fudgeNotation) || cleanNotation.match(customNotation);\n\n\t\tlet mod = 0;\n    const msg = 'Invalid notation: ' + notation + '';\n\n    if (!roll || !roll.length || roll.length < 3) {\n      throw new Error(msg);\n    }\n    if (roll[3] && modifier.test(roll[3])) {\n      const modParts = roll[3].match(modifier);\n      let basicMod = validNumber(modParts[2], msg);\n      if (modParts[1].trim() === '-') {\n        basicMod *= -1;\n      }\n      mod = basicMod;\n    }\n\n\t\tconst returnObj = {\n\t\t\tqty : validNumber(roll[1], msg),\n      modifier: mod,\n\t\t}\n\n\t\tif(cleanNotation.match(percentNotation)){\n\t\t\treturnObj.sides = 'd100'\n\t\t\treturnObj.data = 'single'\n\t\t} else if(cleanNotation.match(fudgeNotation)){\n\t\t\treturnObj.sides = 'fate' // force lowercase\n\t\t} else if(diceAvailable.includes(cleanNotation.match(customNotation)[2])){\n\t\t\treturnObj.sides = roll[2] // dice type instead of number\n\t\t} else {\n\t\t\treturnObj.sides = roll[2];\n\t\t}\n\n    return returnObj\n  }\n\n\t#parseGroup(groupId) {\n\t\t// console.log('groupId', groupId)\n\t\tconst rollGroup = this.rollGroupData[groupId]\n\t\t// turn object into an array\n\t\tconst rollsArray = Object.values(rollGroup.rolls).map(({collectionId, id, meshName, ...rest}) => rest)\n\t\t// add up the values\n\t\t// some dice may still be rolling, should this be a promise?\n\t\t// if dice are still rolling in the group then the value is undefined - hence the isNaN check\n\t\tlet value = rollsArray.reduce((val,roll) => {\n\t\t\tconst rollVal = isNaN(roll.value) ? 0 : roll.value\n\t\t\treturn val + rollVal\n\t\t},0)\n\t\t// add the modifier\n\t\tvalue += rollGroup.modifier ? rollGroup.modifier : 0\n\t\t// return the value and the rollsArray\n\t\treturn {value, rollsArray}\n\t}\n\n\tgetRollResults(){\n\t\t// loop through each roll group\n\t\treturn Object.entries(this.rollGroupData).map(([key,val]) => {\n\t\t\t// parse the group data to get the value and the rolls as an array\n\t\t\tconst groupData = this.#parseGroup(key)\n\t\t\t// set the value for this roll group in this.rollGroupData\n\t\t\tval.value = groupData.value\n\t\t\t// set the qty equal to the number of rolls - this can be changed by rerolls and removals\n\t\t\tval.qty = groupData.rollsArray.length\n\t\t\t// copy the group that will be put into the return object\n\t\t\tconst groupCopy = {...val}\n\t\t\t// replace the rolls object with a rolls array\n\t\t\tgroupCopy.rolls = groupData.rollsArray\n\t\t\t// return the groupCopy - note: we never return this.rollGroupData\n\t\t\treturn groupCopy\n\t\t})\n\t}\n}\n\nclass Collection{\n\tconstructor(options){\n\t\tObject.assign(this,options)\n\t\tthis.rolls = options.rolls || []\n\t\tthis.completedRolls = 0\n\t\tconst that = this\n\t\tthis.promise = new Promise((resolve,reject) => {\n\t\t\tthat.resolve = resolve\n\t\t\tthat.reject = reject\n\t\t})\n\t}\n}\n\nexport default WorldFacade"], "names": ["createCanvas", "options", "selector", "id", "container", "canvas", "debounce", "fn", "timeout", "context", "args", "createAsyncQueue", "opts", "dedupe", "queue", "running", "push", "task", "start", "res", "item", "deepCopy", "obj", "Random", "crypto", "buffer", "min", "max", "hexToRGB", "hex", "alpha", "h", "x", "val", "webgl_support", "defaultOptions", "WorldFacade", "__privateAdd", "_loadWorld", "_loadPhysics", "_connectWorld", "_makeRoll", "_parseGroup", "__publicField", "_collectionIndex", "_groupIndex", "_rollIndex", "_idIndex", "_DiceWorld", "_diceWorldPromise", "_diceWorldResolve", "_DicePhysics", "_dicePhysicsPromise", "_dicePhysicsResolve", "_webgl_support", "onBeforeRoll", "onDieComplete", "onRollComplete", "onRemoveComplete", "onThemeConfigLoaded", "onThemeLoaded", "boxOptions", "__privateSet", "debounceResize", "__privateGet", "__privateMethod", "loadPhysics_fn", "loadWorld_fn", "result", "die", "group", "collection", "collectionId", "meshName", "rest", "returnDie", "rollId", "groupData", "parseGroup_fn", "removeCollectionId", "connectWorld_fn", "theme", "basePath", "themeData", "resp", "contentType", "error", "meshFilePath", "target", "newDice", "themeConfig", "newConfig", "className", "hideClass", "_a", "notation", "themeColor", "newStartPoint", "__privateWrapper", "Collection", "parsedNotation", "makeRoll_fn", "remove", "hide", "cleanNotation", "value", "rollArray", "input", "diceAvailable", "verifyObject", "object", "err", "incrementId", "key", "splitKey", "verifyRollId", "roll", "diceNotation", "percentNotation", "fudgeNotation", "customNotation", "modifier", "validNumber", "n", "mod", "msg", "modParts", "basicMod", "returnObj", "groupCopy", "resolve", "reject", "onInitComplete", "WorldOffscreen", "module", "WorldOnscreen", "WorldNone", "physicsWorker", "e", "channel", "rolls", "hasGroupId", "index", "loadTheme", "diceExtended", "materialType", "_c", "_b", "diceExtra", "loadExtendedTheme", "_d", "_f", "_e", "colorSuffix", "color", "i", "len", "dieType", "warning", "extendedTheme", "extendedThemeName", "groupId", "rollGroup", "rollsArray", "rollVal", "that"], "mappings": ";;;;;;;;;;;;;;;;;;;AAEA,SAASA,GAAaC,GAAS;AAC7B,QAAM,EAAE,UAAAC,GAAU,IAAAC,EAAE,IAAKF;AAEzB,MAAIG,IAAY,SAAS,MACrBC,IAAS,SAAS,cAAc,QAAQ;AAI5C,MAHAA,EAAO,KAAKF,GACZE,EAAO,UAAU,IAAI,iBAAiB,GAEnCH,GAAU;AACX,QAAI,OAAOA,KAAa;AACtB,YAAM,IAAI,MAAM,uFAAuF;AAKzG,QAFAE,IAAY,SAAS,cAAcF,CAAQ,GAExC,EAACE,KAAA,QAAAA,EAAW;AACb,YAAM,IAAI,MAAM,6BAA6BF,CAAQ,gFAAgF;AAAA,EAExI;AAED,SAAAE,EAAU,YAAYC,CAAM,GAErBA;AACT;;;;;;;;;;;;;;ACUO,MAAMC,KAAW,CAACC,MAAO;AAG/B,MAAIC;AAGJ,SAAO,WAAY;AAGlB,QAAIC,IAAU,MACVC,IAAO;AAGX,IAAIF,KACH,OAAO,qBAAqBA,CAAO,GAIpCA,IAAU,OAAO,sBAAsB,WAAY;AAClD,MAAAD,EAAG,MAAME,GAASC,CAAI;AAAA,IACzB,CAAG;AAAA,EACH;AACA,GAQaC,KAAmB,CAACC,IAAO,EAAE,QAAQ,GAAK,MAAO;AAC5D,QAAM,EAAE,QAAAC,EAAM,IAAKD;AACnB,MAAIE,IAAQ,CAAE,GACVC;AACJ,QAAMC,IAAO,CAAAC,OACPJ,MAAQC,IAAQ,CAAE,IACtBA,EAAM,KAAKG,CAAI,GACVF,MAASA,IAAUG,EAAO,IACxBH,EAAQ,QAAQ,MAAM;AAC3B,IAAAA,IAAU;AAAA,EAChB,CAAK,IAEGG,IAAQ,YAAY;AACxB,UAAMC,IAAM,CAAE;AACd,WAAOL,EAAM,UAAQ;AACnB,YAAMM,IAAON,EAAM,MAAO;AAC1B,MAAAK,EAAI,KAAK,MAAMC,GAAM;AAAA,IACtB;AACD,WAAOD;AAAA,EACR;AACD,SAAO,EAAE,MAAAH,GAAM,OAAAF,GAAO,OAAO,MAAMC,KAAW,QAAQ,QAAQ,CAAA,CAAE,EAAG;AACrE,GAIaM,KAAW,CAAAC,MAAO,KAAK,MAAM,KAAK,UAAUA,CAAG,CAAC;AAOtD,MAAMC,GAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlB,OAAO,QAAQ;AACb,UAAMC,IAAS,OAAO,UAAU,OAAO,UACjCC,IAAS,IAAI,YAAY,CAAC;AAGhC,WAFYD,EAAO,gBAAgBC,CAAM,EAAE,CAAC,IAE/B,KAAG;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,MAAMC,GAAKC,GAAK;AAErB,WAAQ,KAAK,MAAM,KAAK,IAAI,IAAG,EAAE,IAAE,KAAK,MAAO,IAAC,KAAK,MAAO,CAAA,KAAGA,IAAID,IAAI,KAAIA;AAAA,EAC5E;AACH;AAGO,MAAME,KAAW,CAAAC,MAAO;AAC7B,MAAIC,IAAQ,IACVC,IAAIF,EAAI,MAAMA,EAAI,WAAW,GAAG,IAAI,IAAI,CAAC;AAC3C,EAAIE,EAAE,WAAW,IAAGA,IAAI,CAAC,GAAGA,CAAC,EAAE,IAAI,CAAAC,MAAKA,IAAIA,CAAC,EAAE,KAAK,EAAE,IAC7CD,EAAE,WAAW,MAAGD,IAAQ,KACjCC,IAAI,SAASA,GAAG,EAAE;AAClB,MAAIE,IAAM;AAAA,IACR,GAAGF,MAAM;AAAA,IACT,IAAIA,IAAI,WAAc;AAAA,IACtB,GAAIA,IAAI;AAAA,EACT;AACD,SAAGD,MACDG,EAAI,IAAIF,MAAM,IACdE,EAAI,KAAKF,IAAI,cAAgB,IAC7BE,EAAI,KAAKF,IAAI,WAAgB,GAC7BE,EAAI,IAAKF,IAAI,MAERE;AAET;AAkBO,SAASC,KAAiB;AAC/B,MAAI;AACF,UAAM7B,IAAS,SAAS,cAAc,QAAQ;AAC9C,WAAO,CAAC,CAAC,OAAO,0BAA0BA,EAAO,WAAW,OAAO,KAAKA,EAAO,WAAW,oBAAoB;AAAA,EAC/G,QAAU;AACT,WAAO;AAAA,EACR;AACH;ACnKA,MAAM8B,KAAiB;AAAA,EACtB,IAAI,eAAe,KAAK,IAAK,CAAA;AAAA;AAAA,EAC7B,WAAW;AAAA,EACV,eAAe;AAAA;AAAA,EAChB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EACf,OAAO;AAAA;AAAA,EACR,OAAO;AAAA;AAAA,EACP,OAAO;AAAA;AAAA,EACP,eAAe,CAAE;AAAA,EACjB,gBAAgB,CAAE;AAAA;AAAA,EAClB,YAAY;AAAA;AAAA,EACZ,WAAW;AAAA;AAAA,EACX,WAAW;AAAA;AAAA;AAAA,EAEX,QAAQ,OAAO,SAAW,MAAc,OAAO,SAAS,SAAS;AAAA,EACjE,mBAAmB;AACpB;;AAEA,MAAMC,GAAY;AAAA,EAmBhB,YAAYnC,IAAU,IAAG;AA0C1B;AAAA,IAAAoC,EAAA,MAAMC;AA8CN;AAAA,IAAAD,EAAA,MAAAE;AAsBA,IAAAF,EAAA,MAAAG;AAsYA;AAAA,IAAAH,EAAA,MAAMI;AAwPN,IAAAJ,EAAA,MAAAK;AA9vBA,IAAAC,EAAA,4BAAqB,CAAE;AACvB,IAAAA,EAAA,uBAAgB,CAAE;AAClB,IAAAA,EAAA,sBAAe,CAAE;AACjB,IAAAA,EAAA,mBAAY,CAAE;AACd,IAAAA,EAAA,0BAAmB,CAAE;AACrB,IAAAN,EAAA,MAAAO,GAAmB;AACnB,IAAAP,EAAA,MAAAQ,GAAc;AACd,IAAAR,EAAA,MAAAS,GAAa;AACb,IAAAT,EAAA,MAAAU,GAAW;AACX,IAAAV,EAAA,MAAAW,GAAa,CAAE;AACf,IAAAX,EAAA,MAAAY,GAAA;AACA,IAAAZ,EAAA,MAAAa,GAAA;AACA,IAAAb,EAAA,MAAAc,GAAA;AACA,IAAAd,EAAA,MAAAe,GAAA;AACA,IAAAf,EAAA,MAAAgB,GAAA;AACA,IAAAhB,EAAA,MAAAiB,GAAiB;AACjB,IAAAX,EAAA,cAAO,MAAM;AAAA,IAAE;AAUd,QANI,UAAU,WAAW,KAAK,QAAO,UAAU,CAAC,MAAM,aAAa,QAAO,UAAU,CAAC,MAAM,cAC1F,QAAQ,KAAK,4KAA4K,GACzL1C,IAAU,UAAU,CAAC,GACrBA,EAAQ,YAAY,UAAU,CAAC,IAG7B,OAAOA,KAAY;AACrB,YAAM,IAAI,MAAM,2HAA2H;AAG5I,UAAM,EAAE,cAAAsD,GAAc,eAAAC,GAAe,gBAAAC,GAAgB,kBAAAC,GAAkB,qBAAAC,GAAqB,eAAAC,GAAe,GAAGC,EAAU,IAAK5D;AAG7H,SAAK,SAAS,EAAC,GAAGkC,IAAgB,GAAG0B,EAAU,GAG/C,KAAK,eAAe5D,EAAQ,gBAAgB,KAAK,MACjD,KAAK,gBAAgBA,EAAQ,iBAAiB,KAAK,MACnD,KAAK,iBAAiBA,EAAQ,kBAAkB,KAAK,MACrD,KAAK,mBAAmBA,EAAQ,oBAAoB,KAAK,MACzD,KAAK,gBAAgBA,EAAQ,iBAAiB,KAAK,MACnD,KAAK,sBAAsBA,EAAQ,uBAAuB,KAAK,MAG5DiC,GAAa,KAEf,KAAK,SAASlC,GAAa;AAAA,MAC1B,UAAU,KAAK,OAAO;AAAA,MACtB,IAAI,KAAK,OAAO;AAAA,IACpB,CAAI,GACD,KAAK,YAAY,MAEjB8D,EAAA,MAAKR,GAAiB,KAIvB,KAAK,iBAAiB3C,GAAkB;AAAA,EACvC;AAAA,EAmFF,cAAa;AAQZ,UAAMoD,IAAiBzD,GAND,MAAM;AAC3B,MAAA0D,EAAA,MAAKhB,GAAW,OAAO,EAAC,OAAO,KAAK,OAAO,aAAa,QAAQ,KAAK,OAAO,aAAY,CAAC,GACtFgB,EAAA,MAAKb,MACPa,EAAA,MAAKb,GAAa,YAAY,EAAC,QAAQ,UAAU,OAAO,KAAK,OAAO,aAAa,QAAQ,KAAK,OAAO,aAAY,CAAC;AAAA,IAEnH,CAC4C;AAC7C,WAAO,iBAAiB,UAAUY,CAAc;AAAA,EAChD;AAAA,EAEA,MAAM,OAAO;AAEX,WAAGC,EAAA,MAAKV,KACNW,EAAA,MAAK1B,GAAA2B,IAAL,aAEAJ,EAAA,MAAKV,GAAsB,QAAQ,QAAS,IAEhD,MAAMa,EAAA,MAAK3B,GAAA6B,IAAL,YACN,KAAK,YAAa,GAGlBH,EAAA,MAAKhB,GAAW,eAAe,CAACoB,MAAW;AAC1C,YAAMC,IAAM,KAAK,aAAaD,EAAO,MAAM,GACrCE,IAAQ,KAAK,cAAcD,EAAI,OAAO,GACtCE,IAAa,KAAK,mBAAmBF,EAAI,YAAY;AAI3D,MAAAC,EAAM,MAAMD,EAAI,MAAM,EAAE,QAAQD,EAAO,OAGvCG,EAAW,kBAERA,EAAW,kBAAkBA,EAAW,MAAM,UAEhDA,EAAW,QAAQ,OAAO,OAAOA,EAAW,KAAK,EAAE,IAAI,CAAC,EAAC,cAAAC,GAAc,IAAArE,GAAI,UAAAsE,GAAU,GAAGC,EAAI,MAAMA,CAAI,CAAC;AAIxG,YAAM,EAAC,cAAAF,GAAc,IAAArE,GAAI,GAAGwE,EAAS,IAAIN;AACzC,WAAK,cAAcM,CAAS;AAAA,IAC5B,GACDX,EAAA,MAAKhB,GAAW,iBAAiB,MAAM;AAEtC,WAAK,eAAe,KAAK,gBAAgB;AAAA,IACzC,GAEDgB,EAAA,MAAKhB,GAAW,eAAe,CAAC4B,MAAW;AAE1C,UAAIP,IAAM,KAAK,aAAaO,CAAM;AAClC,YAAML,IAAa,KAAK,mBAAmBF,EAAI,kBAAkB;AACjE,MAAAE,EAAW,kBAGX,OAAO,KAAK,aAAaF,EAAI,MAAM;AAGnC,YAAMC,IAAQ,KAAK,cAAcD,EAAI,OAAO;AAC5C,aAAOC,EAAM,MAAMD,EAAI,MAAM;AAG7B,YAAMQ,IAAYZ,EAAA,MAAKvB,GAAAoC,GAAL,WAAiBT,EAAI;AAEvC,MAAAC,EAAM,QAAQO,EAAU,OACxBP,EAAM,MAAMO,EAAU,WAAW,QAG9BN,EAAW,kBAAkBA,EAAW,MAAM,UAChDA,EAAW,QAAQ,OAAO,OAAOA,EAAW,KAAK,EAAE,IAAI,CAAC,EAAC,IAAApE,GAAI,GAAGuE,EAAI,MAAMA,CAAI,CAAC;AAEhF,YAAM,EAAC,cAAAF,GAAc,IAAArE,GAAI,oBAAA4E,GAAoB,UAAAN,GAAU,GAAGE,EAAS,IAAIN;AACvE,WAAK,iBAAiBM,CAAS;AAAA,IAC/B,GAGD,MAAM,QAAQ,IAAI,CAACX,EAAA,MAAKf,IAAmBe,EAAA,MAAKZ,EAAmB,CAAC,GAE/DY,EAAA,MAAKb,MAENc,EAAA,MAAKzB,GAAAwC,IAAL,YAIJ,MAAM,KAAK,eAAe,KAAK,MAAM,KAAK,UAAU,KAAK,OAAO,KAAK,CAAC,GAItE,KAAK,OAAO,cAAc,SAAQ,eAAeC,GAAO;AACvD,YAAM,KAAK,eAAe,KAAK,MAAM,KAAK,UAAUA,CAAK,CAAC;AAAA,IAC7D,GAAI,KAAK,IAAI,CAAC,GAKL;AAAA,EACN;AAAA;AAAA,EAGF,MAAM,eAAeA,GAAM;AAC1B,QAAIC,IAAW,GAAG,KAAK,OAAO,MAAM,GAAG,KAAK,OAAO,SAAS,UAAUD,CAAK;AAE3E,IAAG,KAAK,OAAO,eAAeA,CAAK,MAClCC,IAAW,KAAK,OAAO,eAAeD,CAAK;AAK5C,QAAIE,IAAY,MAAM,MAAM,GAAGD,CAAQ,oBAAoB,EAAE,KAAK,CAAAE,MAAQ;AACzE,UAAGA,EAAK,IAAI;AACX,cAAMC,IAAcD,EAAK,QAAQ,IAAI,cAAc;AACnD,YAAIC,KAAeA,EAAY,QAAQ,kBAAkB,MAAM;AAC9D,iBAAOD,EAAK,KAAM;AAEd,YAAIA,EAAK,QAAQA,EAAK,SAAS;AACnC,iBAAOA,EAAK,KAAM;AAIlB,cAAM,IAAI,MAAM,0BAA0BC,CAAW,0CAA0C;AAAA,MAEpG;AACI,cAAM,IAAI,MAAM,2CAA2CJ,CAAK,mCAAmCG,EAAK,MAAM,KAAKA,EAAK,UAAU,EAAE;AAAA,IAExI,CAAG,EAAE,MAAM,CAAAE,MAAS,QAAQ,MAAMA,CAAK,CAAC;AAEtC,QAAG,CAACH;AACH,YAAM,IAAI,MAAM,oCAAoC;AAGrD,QAAIV,IAAW,WACXc,IAAe,GAAG,KAAK,OAAO,MAAM,GAAG,KAAK,OAAO,SAAS;AAQhE,QANGJ,EAAU,eAAe,UAAU,MACrCV,IAAWU,EAAU,SAAS,QAAQ,iBAAgB,IAAI,GAC1DI,IAAe,GAAGL,CAAQ,IAAIC,EAAU,QAAQ,KAI9C,CAACA,EAAU,eAAe,eAAe;AAC3C,YAAM,IAAI,MAAM,6EAA6E;AAQ9F,QAAGA,EAAU,eAAe,SAAS,GAAE;AACtC,YAAMK,IAAS,MAAM,KAAK,UAAUL,EAAU,OAAO,EAAE,MAAM,CAAAG,MAAS,QAAQ,MAAMA,CAAK,CAAC;AAG1F,UAAGE,EAAO,eAAe,SAAS;AACjC,cAAM,IAAI,MAAM,mDAAmD;AAIpE,YAAMC,IAAU,CAAE;AAClB,MAAAN,EAAU,cAAc,QAAQ,CAAAd,MAAO;AACtC,QAAAoB,EAAQpB,CAAG,IAAIc,EAAU;AAAA,MAC7B,CAAI,GACDK,EAAO,eAAe,EAAC,GAAGA,EAAO,cAAc,GAAGC,EAAO,GAEzD,KAAK,OAAO,QAAQN,EAAU;AAAA,IAC9B;AAGD,kBAAO;AAAA,MAAOA;AAAA,MACb;AAAA,QACC,UAAAD;AAAA,QACA,cAAAK;AAAA,QACA,UAAAd;AAAA,QACA,OAAAQ;AAAA,MACA;AAAA,IACD,GAIME;AAAA,EACP;AAAA,EAED,MAAM,UAAUF,GAAM;AAErB,QAAG,KAAK,iBAAiBA,CAAK;AAG7B,aAAO,KAAK,iBAAiBA,CAAK;AAKnC,UAAMS,IAAc,KAAK,iBAAiBT,CAAK,IAAI,MAAM,KAAK,eAAeA,CAAK,EAAE,MAAM,CAAAK,MAAS,QAAQ,MAAMA,CAAK,CAAC;AAGvH,QAFA,KAAK,oBAAoBI,CAAW,GAEjC,EAACA;AAGJ,mBAAM1B,EAAA,MAAKhB,GAAW,UAAU0C,CAAW,EAAE,MAAM,CAAAJ,MAAS,QAAQ,MAAMA,CAAK,CAAC,GAEhF,KAAK,cAAcI,CAAW,GAEvBA;AAAA,EACP;AAAA;AAAA;AAAA,EAID,MAAM,aAAazF,GAAS;AAC3B,UAAM0F,IAAY,EAAC,GAAG,KAAK,QAAO,GAAG1F,EAAO;AAO5C,QAFA,KAAK,SAAS0F,GAEXA,EAAU,OAAM;AAElB,YAAMR,KADS,MAAM,KAAK,eAAe,KAAK,MAAM,KAAK,UAAUQ,EAAU,KAAK,CAAC,GAC1D,GAAG,EAAE;AAC9B,MAAGR,EAAU,eAAe,SAAS,MAEpC,KAAK,OAAO,QAAQA,EAAU;AAAA,IAE/B;AAGD,WAAAnB,EAAA,MAAKhB,GAAW,aAAa2C,CAAS,GAEnC3B,EAAA,MAAKb,MAEPa,EAAA,MAAKb,GAAa,YAAY;AAAA,MAC7B,QAAQ;AAAA,MACR,SAASwC;AAAA,IACb,CAAI,GAIK;AAAA,EACP;AAAA,EAED,QAAQ;AAEP,WAAA7B,EAAA,MAAKlB,GAAmB,IACxBkB,EAAA,MAAKjB,GAAc,IACnBiB,EAAA,MAAKhB,GAAa,IAClBgB,EAAA,MAAKf,GAAW,IAEhB,KAAK,qBAAqB,CAAE,GAC5B,KAAK,gBAAgB,CAAE,GACvB,KAAK,eAAe,CAAE,GAEtBiB,EAAA,MAAKhB,GAAW,MAAO,GACpBgB,EAAA,MAAKb,MAEPa,EAAA,MAAKb,GAAa,YAAY,EAAC,QAAQ,YAAW,CAAC,GAI7C;AAAA,EACN;AAAA,EAEF,KAAKyC,GAAW;AACf,WAAGA,KACF,KAAK,OAAO,QAAQ,YAAYA,GAChC,KAAK,OAAO,UAAU,IAAIA,CAAS,KAEnC,KAAK,OAAO,MAAM,UAAU,QAG7B,KAAK,YAAY,IAGV;AAAA,EACP;AAAA,EAED,OAAO;;AACN,UAAMC,KAAYC,IAAA,KAAK,OAAO,YAAZ,gBAAAA,EAAqB;AACvC,WAAGD,KACF,OAAO,KAAK,OAAO,QAAQ,WAC3B,KAAK,OAAO,UAAU,OAAOA,CAAS,KAEtC,KAAK,OAAO,MAAM,UAAU,SAE7B,KAAK,YAAY,IACjB,KAAK,YAAW,GAGT;AAAA,EACP;AAAA;AAAA,EAGD,KAAKE,GAAU,EAAC,OAAAd,IAAQ,KAAK,OAAO,OAAO,YAAAe,IAAa,KAAK,OAAO,YAAY,eAAAC,IAAgB,GAAI,IAAI,CAAA,GAAI;AAG3G,SAAK,MAAO;AACZ,UAAMzB,IAAe0B,EAAA,MAAKtD,GAAL;AAErB,SAAK,mBAAmB4B,CAAY,IAAI,IAAI2B,EAAW;AAAA,MACtD,IAAI3B;AAAA,MACJ,UAAAuB;AAAA,MACA,OAAAd;AAAA,MACA,YAAAe;AAAA,MACA,eAAAC;AAAA,IACH,CAAG;AAED,UAAMG,IAAiB,KAAK,oBAAoBL,GAAU,KAAK,iBAAiBd,CAAK,EAAE,aAAa;AACpG,WAAAhB,EAAA,MAAKxB,GAAA4D,GAAL,WAAeD,GAAgB5B,IAGxB,KAAK,mBAAmBA,CAAY,EAAE;AAAA,EAC7C;AAAA,EAEA,IAAIuB,GAAU,EAAC,OAAAd,IAAQ,KAAK,OAAO,OAAO,YAAAe,IAAa,KAAK,OAAO,YAAY,eAAAC,IAAgB,GAAI,IAAI,CAAA,GAAI;AAE3G,UAAMzB,IAAe0B,EAAA,MAAKtD,GAAL;AAErB,SAAK,mBAAmB4B,CAAY,IAAI,IAAI2B,EAAW;AAAA,MACtD,IAAI3B;AAAA,MACJ,UAAAuB;AAAA,MACA,OAAAd;AAAA,MACA,YAAAe;AAAA,MACA,eAAAC;AAAA,IACH,CAAG;AAED,UAAMG,IAAiB,KAAK,oBAAoBL,GAAU,KAAK,iBAAiBd,CAAK,EAAE,aAAa;AACpG,WAAAhB,EAAA,MAAKxB,GAAA4D,GAAL,WAAeD,GAAgB5B,IAGxB,KAAK,mBAAmBA,CAAY,EAAE;AAAA,EAC5C;AAAA,EAEF,OAAOuB,GAAU,EAAC,QAAAO,IAAS,IAAO,MAAAC,IAAO,IAAO,eAAAN,IAAgB,GAAI,IAAI,IAAI;AAO3E,UAAMO,KAHY,MAAM,QAAQT,CAAQ,IAAIA,IAAW,CAACA,CAAQ,GAGhC,IAAI,CAAC,EAAC,OAAAU,GAAO,GAAG/B,EAAI,MAAMA,CAAI;AAE9D,WAAG4B,MAAW,MACb,KAAK,OAAOE,GAAe,EAAC,MAAAD,EAAI,CAAC,GAI3B,KAAK,IAAIC,GAAe,EAAC,eAAAP,EAAa,CAAC;AAAA,EAC9C;AAAA,EAED,OAAOF,GAAU,EAAC,MAAAQ,IAAO,GAAK,IAAI,CAAA,GAAI;AAErC,UAAMG,IAAY,MAAM,QAAQX,CAAQ,IAAIA,IAAW,CAACA,CAAQ,GAE1DvB,IAAe0B,EAAA,MAAKtD,GAAL;AAErB,gBAAK,mBAAmB4B,CAAY,IAAI,IAAI2B,EAAW;AAAA,MACtD,IAAI3B;AAAA,MACJ,UAAAuB;AAAA,MACA,OAAOW;AAAA,IACV,CAAG,GAGDA,EAAU,IAAI,CAAArC,MAAO;AAEpB,WAAK,aAAaA,EAAI,MAAM,EAAE,qBAAqBG;AAGnD,UAAIrE,IAAK,KAAK,aAAakE,EAAI,MAAM,EAAE;AAEvC,MAAAL,EAAA,MAAKhB,GAAW,OAAO,EAAC,IAAA7C,GAAG,QAAQkE,EAAI,OAAM,CAAC,GAC3CL,EAAA,MAAKb,MAEPa,EAAA,MAAKb,GAAa,YAAY,EAAC,QAAQ,aAAa,IAAAhD,GAAI;AAAA,IAE5D,CAAG,GAEM,KAAK,mBAAmBqE,CAAY,EAAE;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA,EAwID,oBAAoBmC,GAAOC,GAAc;AACxC,UAAMb,IAAW,MAAM,QAASY,CAAK,IAAKA,IAAQ,CAAEA,CAAO;AAC3D,QAAIP,IAAiB,CAAE;AAGvB,UAAMS,IAAe,CAAEC,MAAY;AAIlC,UAHIA,EAAO,eAAe,KAAK,MAC9BA,EAAO,MAAM,IAETA,EAAO,eAAe,OAAO;AACjC,eAAGA,EAAO,UAAU,UACnBA,EAAO,QAAQ,KACfA,EAAO,OAAO,WAER;AACD;AACN,cAAMC,IAAM;AACZ,cAAM,IAAI,MAAMA,CAAG;AAAA,MACnB;AAAA,IACD,GAEKC,IAAc,CAACC,MAAQ;AAC5B,MAAAA,IAAMA,EAAI,SAAU;AACpB,UAAIC,IAAWD,EAAI,MAAM,GAAG;AAC5B,aAAGC,EAAS,CAAC,IACZA,EAAS,CAAC,IAAI,SAASA,EAAS,CAAC,CAAC,IAAI,IAEtCA,EAAS,CAAC,IAAI,GAERA,EAAS,CAAC,IAAI,MAAMA,EAAS,CAAC;AAAA,IACrC,GAIKC,IAAe,CAAEL,MAAY;AAClC,MAAGA,EAAO,eAAe,QAAQ,KAC7B,KAAK,aAAa,eAAeA,EAAO,MAAM,MAChDA,EAAO,SAASE,EAAYF,EAAO,MAAM,IAGvCA,EAAO,eAAe,UAAU,MACnCA,EAAO,WAAW;AAAA,IAEnB;AAGD,WAAAf,EAAS,QAAQ,CAAAqB,MAAQ;AAGxB,MAAK,OAAOA,KAAS,WACpBhB,EAAe,KAAM,KAAK,MAAOgB,GAAMR,CAAa,CAAI,IAC7C,OAAOb,KAAa,aAC/BoB,EAAcC,CAAM,GACpBP,EAAcO,CAAM,KAAKhB,EAAe,KAAMgB,CAAM;AAAA,IAExD,CAAG,GAEMhB;AAAA,EACP;AAAA;AAAA;AAAA,EAIA,MAAML,GAAUa,GAAe;AAC7B,UAAMS,IAAe,2BACjBC,IAAkB,yBAClBC,IAAgB,6BAEhBC,IAAiB,sCACfC,IAAW,eACXjB,IAAgBT,EAAS,KAAM,EAAC,QAAQ,QAAQ,EAAE,GAClD2B,IAAc,CAACC,GAAGZ,MAAQ;AAE9B,UADAY,IAAI,OAAOA,CAAC,GACR,OAAO,MAAMA,CAAC,KAAK,CAAC,OAAO,UAAUA,CAAC,KAAKA,IAAI;AACjD,cAAM,IAAI,MAAMZ,CAAG;AAErB,aAAOY;AAAA,IACR,GAGKP,IAAOZ,EAAc,MAAMc,CAAe,KAAKd,EAAc,MAAMa,CAAY,KAAKb,EAAc,MAAMe,CAAa,KAAKf,EAAc,MAAMgB,CAAc;AAEpK,QAAII,IAAM;AACR,UAAMC,IAAM,uBAAuB9B;AAEnC,QAAI,CAACqB,KAAQ,CAACA,EAAK,UAAUA,EAAK,SAAS;AACzC,YAAM,IAAI,MAAMS,CAAG;AAErB,QAAIT,EAAK,CAAC,KAAKK,EAAS,KAAKL,EAAK,CAAC,CAAC,GAAG;AACrC,YAAMU,IAAWV,EAAK,CAAC,EAAE,MAAMK,CAAQ;AACvC,UAAIM,IAAWL,EAAYI,EAAS,CAAC,GAAGD,CAAG;AAC3C,MAAIC,EAAS,CAAC,EAAE,KAAI,MAAO,QACzBC,KAAY,KAEdH,IAAMG;AAAA,IACP;AAEH,UAAMC,IAAY;AAAA,MACjB,KAAMN,EAAYN,EAAK,CAAC,GAAGS,CAAG;AAAA,MAC3B,UAAUD;AAAA,IACb;AAED,WAAGpB,EAAc,MAAMc,CAAe,KACrCU,EAAU,QAAQ,QAClBA,EAAU,OAAO,YACRxB,EAAc,MAAMe,CAAa,IAC1CS,EAAU,QAAQ,UACTpB,EAAc,SAASJ,EAAc,MAAMgB,CAAc,EAAE,CAAC,CAAC,GACtEQ,EAAU,QAAQZ,EAAK,CAAC,IAKhBY;AAAA,EACR;AAAA,EAoBF,iBAAgB;AAEf,WAAO,OAAO,QAAQ,KAAK,aAAa,EAAE,IAAI,CAAC,CAACf,GAAIhF,CAAG,MAAM;AAE5D,YAAM4C,IAAYZ,EAAA,MAAKvB,GAAAoC,GAAL,WAAiBmC;AAEnC,MAAAhF,EAAI,QAAQ4C,EAAU,OAEtB5C,EAAI,MAAM4C,EAAU,WAAW;AAE/B,YAAMoD,IAAY,EAAC,GAAGhG,EAAG;AAEzB,aAAAgG,EAAU,QAAQpD,EAAU,YAErBoD;AAAA,IACV,CAAG;AAAA,EACD;AACF;AA5xBCrF,IAAA,eACAC,IAAA,eACAC,IAAA,eACAC,IAAA,eACAC,IAAA,eACAC,IAAA,eACAC,IAAA,eACAC,IAAA,eACAC,IAAA,eACAC,IAAA,eACAC,IAAA,eA6CMhB,IAAA,eAAA6B,KAAU,iBAAE;AAGjB,EAAAL,EAAA,MAAKb,GAAoB,IAAI,QAAQ,CAACiF,GAASC,MAAW;AACzD,IAAArE,EAAA,MAAKZ,GAAoBgF;AAAA,EAC5B,CAAG;AAGD,QAAME,IAAiB,MAAM;AAC5B,IAAApE,EAAA,MAAKd,GAAL;AAAA,EACA;AAEC,MAAIc,EAAA,MAAKV;AASN,QAAI,qBAAqB,UAAU,gCAAgC,KAAK,UAAU,KAAK,OAAO,WAAW;AAE7G,YAAM+E,IAAiB,MAAM,OAAO,sBAA8B,EAAE,KAAK,CAAAC,MAAUA,EAAO,OAAO;AAEjG,MAAAxE,EAAA,MAAKd,GAAa,IAAIqF,EAAe;AAAA,QACpC,QAAQ,KAAK;AAAA,QACb,SAAS,KAAK;AAAA,QACd,gBAAAD;AAAA,MACJ,CAAI;AAAA,IACJ,OAAS;AACN,MAAG,KAAK,OAAO,cACd,QAAQ,KAAK,gFAAgF,GAC7F,KAAK,OAAO,YAAY;AAGzB,YAAMG,IAAgB,MAAM,OAAO,qBAA6B,EAAE,KAAK,CAAAD,MAAUA,EAAO,OAAO;AAC/F,MAAAxE,EAAA,MAAKd,GAAa,IAAIuF,EAAc;AAAA,QACnC,QAAQ,KAAK;AAAA,QACb,SAAS,KAAK;AAAA,QACd,gBAAAH;AAAA,MACJ,CAAI;AAAA,IACD;AAAA,OA9ByB;AACtB,YAAQ,KAAK,iHAAiH;AAC9H,UAAMI,IAAY,MAAM,OAAO,iBAAyB,EAAE,KAAK,CAAAF,MAAUA,EAAO,OAAO;AACvF,IAAAxE,EAAA,MAAKd,GAAa,IAAIwF,EAAU;AAAA,MAClC,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK;AAAA,MACd,gBAAAJ;AAAA,IACJ,CAAI;AAAA,EACC;AAuBH,GAGD7F,IAAA,eAAA2B,KAAY,WAAE;AAEb,EAAAJ,EAAA,MAAKX,GAAe,IAAIsF,GAAe,IAEvC3E,EAAA,MAAKV,GAAsB,IAAI,QAAQ,CAAC8E,GAASC,MAAW;AAC3D,IAAArE,EAAA,MAAKT,GAAsB6E;AAAA,EAC9B,CAAG,IACDlE,EAAA,MAAKb,GAAa,YAAY,CAACuF,MAAM;AACpC,YAAQA,EAAE,KAAK,QAAM;AAAA,MACpB,KAAK;AACJ,QAAA1E,EAAA,MAAKX,GAAL;AAAA,IACD;AAAA,EACC,GAEHW,EAAA,MAAKb,GAAa,YAAY;AAAA,IAC7B,QAAQ;AAAA,IACR,OAAO,KAAK,OAAO;AAAA,IACnB,QAAQ,KAAK,OAAO;AAAA,IACpB,SAAS,KAAK;AAAA,EACjB,CAAG;AACD,GAEDX,IAAA,eAAAwC,KAAa,WAAE;AACd,QAAM2D,IAAU,IAAI,eAAgB;AAGpC,EAAA3E,EAAA,MAAKhB,GAAW,QAAQ2F,EAAQ,KAAK,GAGrC3E,EAAA,MAAKb,GAAa,YAAY;AAAA,IAC7B,QAAQ;AAAA,EACX,GAAI,CAAEwF,EAAQ,MAAO;AACnB,GA4XKlG,IAAA,eAAA4D,IAAS,eAACD,GAAgB5B,GAAa;AAE5C,OAAK,aAAa4B,CAAc;AAEhC,QAAM7B,IAAa,KAAK,mBAAmBC,CAAY;AACvD,MAAIyB,IAAgB1B,EAAW;AAG/B,EAAA6B,EAAe,QAAQ,OAAML,MAAY;;AACxC,QAAG,CAACA,EAAS;AACZ,YAAM,IAAI,MAAM,oDAAoD;AAErE,QAAId,IAAQc,EAAS,SAASxB,EAAW,SAAS,KAAK,OAAO;AAC9D,UAAMyB,IAAaD,EAAS,cAAcxB,EAAW,cAAc,KAAK,OAAO,YACzEqE,IAAQ,CAAE,GACVC,IAAa9C,EAAS,YAAY;AACxC,QAAI+C;AAGJ,UAAMC,IAAY,MAAM,KAAK,UAAU9D,CAAK;AAC5C,UAAM,KAAK,eAAe,KAAK8D,CAAS;AAGxC,QAAItE,IAAW,KAAK,iBAAiBQ,CAAK,EAAE,UACxC2B,KAAgBd,IAAA,KAAK,iBAAiBb,CAAK,MAA3B,gBAAAa,EAA8B,eAC9CkD,IAAe,KAAK,iBAAiB/D,CAAK,EAAE,gBAAgB,CAAE,GAC9DgE,KAAeC,KAAAC,IAAA,KAAK,iBAAiBlE,CAAK,MAA3B,gBAAAkE,EAA8B,aAA9B,gBAAAD,EAAwC;AAE3D,UAAME,IAAY,OAAO,KAAKJ,CAAY;AAE1C,QAAGI,KAAaA,EAAU,SAASrD,EAAS,KAAK,GAAE;AAClD,MAAAd,IAAQ+D,EAAajD,EAAS,KAAK;AACnC,YAAMsD,IAAoB,MAAM,KAAK,UAAUpE,CAAK;AACpD,WAAK,eAAe,KAAKoE,CAAiB,GAC1C5E,IAAW,KAAK,iBAAiBQ,CAAK,EAAE,UACxC2B,KAAgB0C,KAAA,KAAK,iBAAiBrE,CAAK,MAA3B,gBAAAqE,GAA8B,eAC9CL,KAAeM,MAAAC,KAAA,KAAK,iBAAiBvE,CAAK,MAA3B,gBAAAuE,GAA8B,aAA9B,gBAAAD,GAAwC;AAAA,IACvD;AAED,QAAIE,IAAc,IAAIC;AAEtB,IAAGT,MAAiB,YACnBS,IAAQ9H,GAASoE,CAAU,GAE3ByD,IAAgBC,EAAM,IAAE,QAAQA,EAAM,IAAE,QAAQA,EAAM,IAAE,QAAS,MAAO,UAAU;AAInF,aAASC,IAAI,GAAGC,KAAM7D,EAAS,KAAK4D,IAAIC,IAAKD,KAAK;AAEjD,UAAI/E,IAASmB,EAAS,WAAW,SAAYA,EAAS,SAASG,EAAA,MAAKpD,GAAL,KAC3D3C,KAAK4F,EAAS,OAAO,SAAYA,EAAS,KAAKG,EAAA,MAAKnD,GAAL;AACnD,MAAA+F,IAAQD,IAAa9C,EAAS,UAAU/B,EAAA,MAAKnB;AAG7C,YAAMgH,IAAU,OAAO,UAAU9D,EAAS,KAAK,IAAI,IAAIA,EAAS,KAAK,KAAKA,EAAS;AAG/E,MAAG,0BAA0B,KAAKA,EAAS,KAAK,MAC9CA,EAAS,QAAS,SAASA,EAAS,MAAM,QAAQ,KAAK,EAAE,CAAC;AAGhE,YAAMqB,IAAO;AAAA,QACZ,OAAOrB,EAAS;AAAA,QAChB,MAAMA,EAAS;AAAA,QACf,SAAA8D;AAAA,QACA,SAASf;AAAA,QACT,cAAcvE,EAAW;AAAA,QACzB,QAAAK;AAAA,QACA,IAAAzE;AAAA,QACA,OAAA8E;AAAA,QACA,YAAAe;AAAA,QACA,UAAAvB;AAAA,MACA;AAOD,UALAmE,EAAMhE,CAAM,IAAIwC,GAChB,KAAK,aAAaxC,CAAM,IAAIwC,GAC5B7C,EAAW,MAAM,KAAK,KAAK,aAAaK,CAAM,CAAC,GAG3CwC,EAAK,UAAU,UAAW,CAACR,EAAc,SAASiD,CAAO,KAAK,CAACT,EAAU,SAASS,CAAO,KAAMzC,EAAK,UAAU,UAAU,CAACpD,EAAA,MAAKV,IAAe;AAChJ,gBAAQ,KAAK,4BAA4B2B,CAAK,0BAA0B;AACxE,cAAMvD,IAAM,IACNC,IAAM;AACZ,QAAAyF,EAAK,QAAQ7F,GAAO,MAAMG,GAAIC,CAAG,GACjCqC,EAAA,MAAKhB,GAAW,UAAUoE,CAAI;AAAA,MACnC,WAAc,KAAK,OAAO,qBAAsB,CAACR,EAAc,SAASiD,CAAO,KAAK,CAACT,EAAU,SAASS,CAAO,KAAM,CAAC7F,EAAA,MAAKV,IAAe;AAErI,cAAMwG,IACL9F,EAAA,MAAKV,KAEH,KAAK,OAAO,oBACX,6CACA,GAAG8D,EAAK,KAAK,wBAAwBnC,CAAK,6BAH3C;AAIH,gBAAQ,KAAK6E,CAAO;AACpB,cAAMnI,IAAM,OAAO,UAAUyF,EAAK,KAAK,IAAIA,EAAK,QAAQ,SAASA,EAAK,MAAM,QAAQ,OAAM,EAAE,CAAC;AAC7F,QAAAA,EAAK,QAAQ7F,GAAO,MAAM,GAAGI,CAAG,GAChCqC,EAAA,MAAKhB,GAAW,UAAUoE,CAAI;AAAA,MACnC,OAAW;AACN,YAAI2C;AACJ,YAAGX,EAAU,SAASS,CAAO,GAAG;AAC/B,gBAAMG,IAAoBhB,EAAaa,CAAO;AAC9C,UAAAE,IAAgB,KAAK,iBAAiBC,CAAiB;AAAA,QACvD;AACD,QAAAhG,EAAA,MAAKhB,GAAW,IAAI;AAAA,UACnB,GAAGoE;AAAA,UACH,eAAAnB;AAAA,UACA,QAAO8D,KAAA,gBAAAA,EAAe,eAAc9E;AAAA,UACpC,WAAU8E,KAAA,gBAAAA,EAAe,aAAYtF;AAAA,UACrC,aAAAgF;AAAA,QACN,CAAM;AAAA,MACD;AAGD,MAAAxD,IAAgB;AAAA,IAChB;AAED,IAAG4C,IACF,OAAO,OAAO,KAAK,cAAcC,CAAK,EAAE,OAAOF,CAAK,KAGpD7C,EAAS,QAAQ6C,GACjB7C,EAAS,KAAK+C,GACd,KAAK,cAAcA,CAAK,IAAI/C,GACrB,EAALG,EAAA,MAAKrD,GAAL;AAAA,EAEN,CAAG;AACD,GAyHDH,IAAA,eAAAoC,IAAW,SAACmF,GAAS;AAEpB,QAAMC,IAAY,KAAK,cAAcD,CAAO,GAEtCE,IAAa,OAAO,OAAOD,EAAU,KAAK,EAAE,IAAI,CAAC,EAAC,cAAA1F,GAAc,IAAArE,GAAI,UAAAsE,GAAU,GAAGC,EAAI,MAAMA,CAAI;AAIrG,MAAI+B,IAAQ0D,EAAW,OAAO,CAAClI,GAAImF,MAAS;AAC3C,UAAMgD,IAAU,MAAMhD,EAAK,KAAK,IAAI,IAAIA,EAAK;AAC7C,WAAOnF,IAAMmI;AAAA,EACb,GAAC,CAAC;AAEH,SAAA3D,KAASyD,EAAU,WAAWA,EAAU,WAAW,GAE5C,EAAC,OAAAzD,GAAO,YAAA0D,EAAU;AACzB;AAqBF,MAAMhE,EAAU;AAAA,EACf,YAAYlG,GAAQ;AACnB,WAAO,OAAO,MAAKA,CAAO,GAC1B,KAAK,QAAQA,EAAQ,SAAS,CAAE,GAChC,KAAK,iBAAiB;AACtB,UAAMoK,IAAO;AACb,SAAK,UAAU,IAAI,QAAQ,CAACnC,GAAQC,MAAW;AAC9C,MAAAkC,EAAK,UAAUnC,GACfmC,EAAK,SAASlC;AAAA,IACjB,CAAG;AAAA,EACD;AACF;"}