{"version": 3, "file": "boneLookController.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Bones/boneLookController.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAGnE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAEjD;;;GAGG;AACH,MAAM,OAAO,kBAAkB;IAuE3B;;OAEG;IACH,IAAI,MAAM;QACN,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAI,MAAM,CAAC,KAAa;QACpB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;YACtB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;YAC7F,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;SAChD;IACL,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACN,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAI,MAAM,CAAC,KAAa;QACpB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;YACtB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;YAC7F,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;SAChD;IACL,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAI,QAAQ,CAAC,KAAa;QACtB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAI,QAAQ,CAAC,KAAa;QACtB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QA8BI;IACJ,YACI,IAAmB,EACnB,IAAU,EACV,MAAe,EACf,OAaC;QA5JL;;WAEG;QACI,WAAM,GAAY,OAAO,CAAC,EAAE,EAAE,CAAC;QAEtC;;WAEG;QACI,gBAAW,GAAU,KAAK,CAAC,KAAK,CAAC;QAExC;;WAEG;QACI,cAAS,GAAG,CAAC,CAAC;QAErB;;WAEG;QACI,gBAAW,GAAG,CAAC,CAAC;QAEvB;;WAEG;QACI,eAAU,GAAG,CAAC,CAAC;QAEtB;;WAEG;QACI,gBAAW,GAAG,CAAC,CAAC;QAcf,cAAS,GAAe,UAAU,CAAC,QAAQ,EAAE,CAAC;QAC9C,cAAS,GAAG,KAAK,CAAC;QAGlB,uBAAkB,GAAG,KAAK,CAAC;QAE3B,gBAAW,GAAY,OAAO,CAAC,OAAO,EAAE,CAAC;QA8G7C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,IAAI,OAAO,EAAE;YACT,IAAI,OAAO,CAAC,SAAS,EAAE;gBACnB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;aACtC;YAED,IAAI,OAAO,CAAC,WAAW,EAAE;gBACrB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;aAC1C;YAED,IAAI,OAAO,CAAC,UAAU,EAAE;gBACpB,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;aACxC;YAED,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE;gBACxB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;aAChC;iBAAM;gBACH,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC;aACzB;YAED,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE;gBACxB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;aAChC;iBAAM;gBACH,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;aAC1B;YAED,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE;gBAC1B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;aACpC;iBAAM;gBACH,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC;aAC3B;YAED,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE;gBAC1B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;aACpC;iBAAM;gBACH,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;aAC5B;YAED,IAAI,OAAO,CAAC,WAAW,IAAI,IAAI,EAAE;gBAC7B,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;aAC1C;YAED,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE;gBACxB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;aAChC;YAED,IAAI,OAAO,CAAC,WAAW,IAAI,IAAI,EAAE;gBAC7B,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;aAC1C;YAED,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,IAAI,OAAO,CAAC,SAAS,IAAI,IAAI,EAAE;gBACtD,IAAI,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC;gBACxB,IAAI,YAAY,GAAG,IAAI,CAAC,CAAC,CAAC;gBAE1B,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,EAAE;oBACzB,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;oBACrC,UAAU,CAAC,SAAS,EAAE,CAAC;iBAC1B;gBAED,IAAI,OAAO,CAAC,SAAS,IAAI,IAAI,EAAE;oBAC3B,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;oBACzC,YAAY,CAAC,SAAS,EAAE,CAAC;iBAC5B;gBAED,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;gBAE5D,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;gBAC5C,MAAM,CAAC,gBAAgB,CAAC,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBAExF,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;gBAC7D,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;aACpC;SACJ;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,IAAI,EAAE;YACrD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC;SAClC;IACL,CAAC;IAED;;OAEG;IACI,MAAM;QACT,kFAAkF;QAClF,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAClD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,OAAO;SACV;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,OAAO,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAElD,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,MAAM,QAAQ,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAChD,MAAM,QAAQ,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAEhD,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAEpC,MAAM,MAAM,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC9C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE7B,IAAI,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,IAAI,IAAI,UAAU,EAAE;YAC9C,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,OAAO,CAAC,yBAAyB,CAAC,MAAM,EAAE,IAAI,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;aACjF;YACD,UAAU,CAAC,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;SAC3D;aAAM,IAAI,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,KAAK,EAAE;YACxC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACvC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE;gBACnE,MAAM,CAAC,SAAS,EAAE,CAAC;aACtB;SACJ;QAED,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAI,UAAU,GAAG,KAAK,CAAC;QAEvB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;YACrD,QAAQ,GAAG,IAAI,CAAC;SACnB;QACD,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;YACzD,UAAU,GAAG,IAAI,CAAC;SACrB;QAED,IAAI,QAAQ,IAAI,UAAU,EAAE;YACxB,MAAM,QAAQ,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAChD,MAAM,WAAW,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAEnD,IAAI,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,IAAI,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,UAAU,EAAE;gBAC/D,UAAU,CAAC,sBAAsB,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;aACvE;iBAAM,IAAI,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,KAAK,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBACxE,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;aAC5C;iBAAM;gBACH,IAAI,WAAW,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACjD,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAEvC,IAAI,IAAI,CAAC,kBAAkB,EAAE;oBACzB,OAAO,CAAC,yBAAyB,CAAC,WAAW,EAAE,IAAI,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC;iBAC3F;gBAED,IAAI,UAAU,EAAE;oBACZ,UAAU,CAAC,iBAAiB,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;iBACrE;qBAAM;oBACH,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;iBACpD;gBAED,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;gBACrD,SAAS,CAAC,SAAS,EAAE,CAAC;gBACtB,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;gBAE/C,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;aACrE;YAED,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YAElC,IAAI,KAAK,GAAqB,IAAI,CAAC;YAEnC,IAAI,UAAU,EAAE;gBACZ,MAAM,WAAW,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACnD,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;gBAC3C,OAAO,CAAC,yBAAyB,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;gBAEzE,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;gBACjF,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBAC/C,IAAI,QAAQ,GAAG,KAAK,CAAC;gBAErB,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE;oBACxB,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;oBAC1C,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;iBAC7B;qBAAM,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE;oBAC/B,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;oBAC1C,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;iBAC7B;gBAED,IAAI,KAAK,IAAI,QAAQ,EAAE;oBACnB,OAAO,CAAC,yBAAyB,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;oBACtE,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;oBAChC,MAAM,GAAG,WAAW,CAAC;iBACxB;aACJ;YAED,IAAI,QAAQ,EAAE;gBACV,MAAM,WAAW,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACnD,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;gBAC3C,OAAO,CAAC,yBAAyB,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;gBAEzE,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;gBACrD,IAAI,MAAM,GAAG,GAAG,CAAC;gBAEjB,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE;oBAC1C,IAAI,KAAK,IAAI,IAAI,EAAE;wBACf,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;qBACpF;oBAED,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE;wBAC1B,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,EAAE;4BACjE,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;4BACxC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;4BACxC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;yBACzB;6BAAM,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE;4BACxE,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;4BACxC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;4BACxC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;yBACzB;qBACJ;yBAAM;wBACH,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE;4BACpB,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;4BACxC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;4BACxC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;yBACzB;6BAAM,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE;4BAC3B,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;4BACxC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;4BACxC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;yBACzB;qBACJ;iBACJ;gBAED,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE;oBAC5C,sDAAsD;oBACtD,MAAM,OAAO,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;oBAC/C,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBACzB,IAAI,IAAI,CAAC,kBAAkB,EAAE;wBACzB,OAAO,CAAC,yBAAyB,CAAC,OAAO,EAAE,IAAI,CAAC,qBAAqB,EAAE,OAAO,CAAC,CAAC;qBACnF;oBAED,MAAM,UAAU,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;oBAClD,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;oBAC5C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,aAAa,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;oBACjE,OAAO,CAAC,yBAAyB,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;oBAChE,OAAO,CAAC,yBAAyB,CAAC,OAAO,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;oBAEjE,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;oBACjD,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;oBACtD,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;oBAE5E,IAAI,SAAS,GAAG,YAAY,EAAE;wBAC1B,IAAI,KAAK,IAAI,IAAI,EAAE;4BACf,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;yBACpF;wBAED,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;wBAC/D,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;wBAE/D,IAAI,SAAS,GAAG,SAAS,EAAE;4BACvB,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;4BAClC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;4BACzC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;yBAC5C;6BAAM;4BACH,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;4BAClC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;4BACzC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;yBAC5C;qBACJ;iBACJ;gBAED,IAAI,GAAG,IAAI,MAAM,EAAE;oBACf,OAAO,CAAC,yBAAyB,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;oBACtE,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;oBAChC,MAAM,GAAG,WAAW,CAAC;iBACxB;aACJ;SACJ;QAED,MAAM,KAAK,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,KAAK,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,KAAK,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,OAAO,GAAG,kBAAkB,CAAC,QAAQ,CAAC;QAE5C,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACrC,KAAK,CAAC,SAAS,EAAE,CAAC;QAClB,OAAO,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACzC,KAAK,CAAC,SAAS,EAAE,CAAC;QAClB,OAAO,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACxC,KAAK,CAAC,SAAS,EAAE,CAAC;QAClB,MAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QAEvD,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE;YACjD,OAAO;SACV;QAED,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE;YACjD,OAAO;SACV;QAED,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE;YACjD,OAAO;SACV;QAED,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,EAAE;YACvD,MAAM,CAAC,yBAAyB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC9F,QAAQ,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;SAC9C;QAED,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;YACtB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACjB,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;aAChF;YACD,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;aAC7D;YACD,UAAU,CAAC,uBAAuB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACtD,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAEjF,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACxE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;SACzB;aAAM;YACH,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;aAC7D;YACD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9D,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;SAC1B;QAED,IAAI,CAAC,8BAA8B,EAAE,CAAC;IAC1C,CAAC;IAEO,aAAa,CAAC,IAAY,EAAE,IAAY;QAC5C,IAAI,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC;QAC1B,OAAO,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAEvB,IAAI,OAAO,GAAG,IAAI,CAAC,EAAE,EAAE;YACnB,OAAO,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;SAC1B;aAAM,IAAI,OAAO,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE;YAC3B,OAAO,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;SAC1B;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,gBAAgB,CAAC,IAAY,EAAE,IAAY;QAC/C,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QACpB,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAE5C,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QACpB,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAE5C,IAAI,EAAE,GAAG,CAAC,CAAC;QAEX,IAAI,IAAI,GAAG,IAAI,EAAE;YACb,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;SACpB;aAAM;YACH,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;SACpB;QAED,IAAI,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE;YACd,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;SACzB;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAEO,eAAe,CAAC,GAAW,EAAE,IAAY,EAAE,IAAY;QAC3D,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QACnB,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;QACxC,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QACpB,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5C,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QACpB,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAE5C,IAAI,IAAI,GAAG,IAAI,EAAE;YACb,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,EAAE;gBAC1B,OAAO,IAAI,CAAC;aACf;SACJ;aAAM;YACH,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,EAAE;gBAC1B,OAAO,IAAI,CAAC;aACf;SACJ;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,8BAA8B;QAClC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,EAAE;gBAC/C,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;aACnE;YACD,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;SACpG;IACL,CAAC;;AAjjBc,2BAAQ,GAAc,UAAU,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;AAC9D,2BAAQ,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;AACjC,2BAAQ,GAAa,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { ArrayTools } from \"../Misc/arrayTools\";\r\nimport { Vector3, Quaternion, Matrix } from \"../Maths/math.vector\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\nimport type { Bone } from \"./bone\";\r\nimport { Space, Axis } from \"../Maths/math.axis\";\r\n\r\n/**\r\n * Class used to make a bone look toward a point in space\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/bonesSkeletons#bonelookcontroller\r\n */\r\nexport class BoneLookController {\r\n    private static _TmpVecs: Vector3[] = ArrayTools.BuildArray(10, Vector3.Zero);\r\n    private static _TmpQuat = Quaternion.Identity();\r\n    private static _TmpMats: Matrix[] = ArrayTools.BuildArray(5, Matrix.Identity);\r\n\r\n    /**\r\n     * The target Vector3 that the bone will look at\r\n     */\r\n    public target: Vector3;\r\n\r\n    /**\r\n     * The TransformNode that the bone is attached to\r\n     * Name kept as mesh for back compatibility\r\n     */\r\n    public mesh: TransformNode;\r\n\r\n    /**\r\n     * The bone that will be looking to the target\r\n     */\r\n    public bone: Bone;\r\n\r\n    /**\r\n     * The up axis of the coordinate system that is used when the bone is rotated\r\n     */\r\n    public upAxis: Vector3 = Vector3.Up();\r\n\r\n    /**\r\n     * The space that the up axis is in - Space.BONE, Space.LOCAL (default), or Space.WORLD\r\n     */\r\n    public upAxisSpace: Space = Space.LOCAL;\r\n\r\n    /**\r\n     * Used to make an adjustment to the yaw of the bone\r\n     */\r\n    public adjustYaw = 0;\r\n\r\n    /**\r\n     * Used to make an adjustment to the pitch of the bone\r\n     */\r\n    public adjustPitch = 0;\r\n\r\n    /**\r\n     * Used to make an adjustment to the roll of the bone\r\n     */\r\n    public adjustRoll = 0;\r\n\r\n    /**\r\n     * The amount to slerp (spherical linear interpolation) to the target.  Set this to a value between 0 and 1 (a value of 1 disables slerp)\r\n     */\r\n    public slerpAmount = 1;\r\n\r\n    private _minYaw: number;\r\n    private _maxYaw: number;\r\n    private _minPitch: number;\r\n    private _maxPitch: number;\r\n    private _minYawSin: number;\r\n    private _minYawCos: number;\r\n    private _maxYawSin: number;\r\n    private _maxYawCos: number;\r\n    private _midYawConstraint: number;\r\n    private _minPitchTan: number;\r\n    private _maxPitchTan: number;\r\n\r\n    private _boneQuat: Quaternion = Quaternion.Identity();\r\n    private _slerping = false;\r\n    private _transformYawPitch: Matrix;\r\n    private _transformYawPitchInv: Matrix;\r\n    private _firstFrameSkipped = false;\r\n    private _yawRange: number;\r\n    private _fowardAxis: Vector3 = Vector3.Forward();\r\n\r\n    /**\r\n     * Gets or sets the minimum yaw angle that the bone can look to\r\n     */\r\n    get minYaw(): number {\r\n        return this._minYaw;\r\n    }\r\n\r\n    set minYaw(value: number) {\r\n        this._minYaw = value;\r\n        this._minYawSin = Math.sin(value);\r\n        this._minYawCos = Math.cos(value);\r\n        if (this._maxYaw != null) {\r\n            this._midYawConstraint = this._getAngleDiff(this._minYaw, this._maxYaw) * 0.5 + this._minYaw;\r\n            this._yawRange = this._maxYaw - this._minYaw;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the maximum yaw angle that the bone can look to\r\n     */\r\n    get maxYaw(): number {\r\n        return this._maxYaw;\r\n    }\r\n\r\n    set maxYaw(value: number) {\r\n        this._maxYaw = value;\r\n        this._maxYawSin = Math.sin(value);\r\n        this._maxYawCos = Math.cos(value);\r\n        if (this._minYaw != null) {\r\n            this._midYawConstraint = this._getAngleDiff(this._minYaw, this._maxYaw) * 0.5 + this._minYaw;\r\n            this._yawRange = this._maxYaw - this._minYaw;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the minimum pitch angle that the bone can look to\r\n     */\r\n    get minPitch(): number {\r\n        return this._minPitch;\r\n    }\r\n\r\n    set minPitch(value: number) {\r\n        this._minPitch = value;\r\n        this._minPitchTan = Math.tan(value);\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the maximum pitch angle that the bone can look to\r\n     */\r\n    get maxPitch(): number {\r\n        return this._maxPitch;\r\n    }\r\n\r\n    set maxPitch(value: number) {\r\n        this._maxPitch = value;\r\n        this._maxPitchTan = Math.tan(value);\r\n    }\r\n\r\n    /**\r\n     * Create a BoneLookController\r\n     * @param mesh the TransformNode that the bone belongs to\r\n     * @param bone the bone that will be looking to the target\r\n     * @param target the target Vector3 to look at\r\n     * @param options optional settings:\r\n     * * maxYaw: the maximum angle the bone will yaw to\r\n     * * minYaw: the minimum angle the bone will yaw to\r\n     * * maxPitch: the maximum angle the bone will pitch to\r\n     * * minPitch: the minimum angle the bone will yaw to\r\n     * * slerpAmount: set the between 0 and 1 to make the bone slerp to the target.\r\n     * * upAxis: the up axis of the coordinate system\r\n     * * upAxisSpace: the space that the up axis is in - Space.BONE, Space.LOCAL (default), or Space.WORLD.\r\n     * * yawAxis: set yawAxis if the bone does not yaw on the y axis\r\n     * * pitchAxis: set pitchAxis if the bone does not pitch on the x axis\r\n     * * adjustYaw: used to make an adjustment to the yaw of the bone\r\n     * * adjustPitch: used to make an adjustment to the pitch of the bone\r\n     * * adjustRoll: used to make an adjustment to the roll of the bone\r\n     * @param options.maxYaw\r\n     * @param options.minYaw\r\n     * @param options.maxPitch\r\n     * @param options.minPitch\r\n     * @param options.slerpAmount\r\n     * @param options.upAxis\r\n     * @param options.upAxisSpace\r\n     * @param options.yawAxis\r\n     * @param options.pitchAxis\r\n     * @param options.adjustYaw\r\n     * @param options.adjustPitch\r\n     * @param options.adjustRoll\r\n     **/\r\n    constructor(\r\n        mesh: TransformNode,\r\n        bone: Bone,\r\n        target: Vector3,\r\n        options?: {\r\n            maxYaw?: number;\r\n            minYaw?: number;\r\n            maxPitch?: number;\r\n            minPitch?: number;\r\n            slerpAmount?: number;\r\n            upAxis?: Vector3;\r\n            upAxisSpace?: Space;\r\n            yawAxis?: Vector3;\r\n            pitchAxis?: Vector3;\r\n            adjustYaw?: number;\r\n            adjustPitch?: number;\r\n            adjustRoll?: number;\r\n        }\r\n    ) {\r\n        this.mesh = mesh;\r\n        this.bone = bone;\r\n        this.target = target;\r\n\r\n        if (options) {\r\n            if (options.adjustYaw) {\r\n                this.adjustYaw = options.adjustYaw;\r\n            }\r\n\r\n            if (options.adjustPitch) {\r\n                this.adjustPitch = options.adjustPitch;\r\n            }\r\n\r\n            if (options.adjustRoll) {\r\n                this.adjustRoll = options.adjustRoll;\r\n            }\r\n\r\n            if (options.maxYaw != null) {\r\n                this.maxYaw = options.maxYaw;\r\n            } else {\r\n                this.maxYaw = Math.PI;\r\n            }\r\n\r\n            if (options.minYaw != null) {\r\n                this.minYaw = options.minYaw;\r\n            } else {\r\n                this.minYaw = -Math.PI;\r\n            }\r\n\r\n            if (options.maxPitch != null) {\r\n                this.maxPitch = options.maxPitch;\r\n            } else {\r\n                this.maxPitch = Math.PI;\r\n            }\r\n\r\n            if (options.minPitch != null) {\r\n                this.minPitch = options.minPitch;\r\n            } else {\r\n                this.minPitch = -Math.PI;\r\n            }\r\n\r\n            if (options.slerpAmount != null) {\r\n                this.slerpAmount = options.slerpAmount;\r\n            }\r\n\r\n            if (options.upAxis != null) {\r\n                this.upAxis = options.upAxis;\r\n            }\r\n\r\n            if (options.upAxisSpace != null) {\r\n                this.upAxisSpace = options.upAxisSpace;\r\n            }\r\n\r\n            if (options.yawAxis != null || options.pitchAxis != null) {\r\n                let newYawAxis = Axis.Y;\r\n                let newPitchAxis = Axis.X;\r\n\r\n                if (options.yawAxis != null) {\r\n                    newYawAxis = options.yawAxis.clone();\r\n                    newYawAxis.normalize();\r\n                }\r\n\r\n                if (options.pitchAxis != null) {\r\n                    newPitchAxis = options.pitchAxis.clone();\r\n                    newPitchAxis.normalize();\r\n                }\r\n\r\n                const newRollAxis = Vector3.Cross(newPitchAxis, newYawAxis);\r\n\r\n                this._transformYawPitch = Matrix.Identity();\r\n                Matrix.FromXYZAxesToRef(newPitchAxis, newYawAxis, newRollAxis, this._transformYawPitch);\r\n\r\n                this._transformYawPitchInv = this._transformYawPitch.clone();\r\n                this._transformYawPitch.invert();\r\n            }\r\n        }\r\n\r\n        if (!bone.getParent() && this.upAxisSpace == Space.BONE) {\r\n            this.upAxisSpace = Space.LOCAL;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Update the bone to look at the target.  This should be called before the scene is rendered (use scene.registerBeforeRender())\r\n     */\r\n    public update(): void {\r\n        //skip the first frame when slerping so that the TransformNode rotation is correct\r\n        if (this.slerpAmount < 1 && !this._firstFrameSkipped) {\r\n            this._firstFrameSkipped = true;\r\n            return;\r\n        }\r\n\r\n        const bone = this.bone;\r\n        const bonePos = BoneLookController._TmpVecs[0];\r\n        bone.getAbsolutePositionToRef(this.mesh, bonePos);\r\n\r\n        let target = this.target;\r\n        const _tmpMat1 = BoneLookController._TmpMats[0];\r\n        const _tmpMat2 = BoneLookController._TmpMats[1];\r\n\r\n        const mesh = this.mesh;\r\n        const parentBone = bone.getParent();\r\n\r\n        const upAxis = BoneLookController._TmpVecs[1];\r\n        upAxis.copyFrom(this.upAxis);\r\n\r\n        if (this.upAxisSpace == Space.BONE && parentBone) {\r\n            if (this._transformYawPitch) {\r\n                Vector3.TransformCoordinatesToRef(upAxis, this._transformYawPitchInv, upAxis);\r\n            }\r\n            parentBone.getDirectionToRef(upAxis, this.mesh, upAxis);\r\n        } else if (this.upAxisSpace == Space.LOCAL) {\r\n            mesh.getDirectionToRef(upAxis, upAxis);\r\n            if (mesh.scaling.x != 1 || mesh.scaling.y != 1 || mesh.scaling.z != 1) {\r\n                upAxis.normalize();\r\n            }\r\n        }\r\n\r\n        let checkYaw = false;\r\n        let checkPitch = false;\r\n\r\n        if (this._maxYaw != Math.PI || this._minYaw != -Math.PI) {\r\n            checkYaw = true;\r\n        }\r\n        if (this._maxPitch != Math.PI || this._minPitch != -Math.PI) {\r\n            checkPitch = true;\r\n        }\r\n\r\n        if (checkYaw || checkPitch) {\r\n            const spaceMat = BoneLookController._TmpMats[2];\r\n            const spaceMatInv = BoneLookController._TmpMats[3];\r\n\r\n            if (this.upAxisSpace == Space.BONE && upAxis.y == 1 && parentBone) {\r\n                parentBone.getRotationMatrixToRef(Space.WORLD, this.mesh, spaceMat);\r\n            } else if (this.upAxisSpace == Space.LOCAL && upAxis.y == 1 && !parentBone) {\r\n                spaceMat.copyFrom(mesh.getWorldMatrix());\r\n            } else {\r\n                let forwardAxis = BoneLookController._TmpVecs[2];\r\n                forwardAxis.copyFrom(this._fowardAxis);\r\n\r\n                if (this._transformYawPitch) {\r\n                    Vector3.TransformCoordinatesToRef(forwardAxis, this._transformYawPitchInv, forwardAxis);\r\n                }\r\n\r\n                if (parentBone) {\r\n                    parentBone.getDirectionToRef(forwardAxis, this.mesh, forwardAxis);\r\n                } else {\r\n                    mesh.getDirectionToRef(forwardAxis, forwardAxis);\r\n                }\r\n\r\n                const rightAxis = Vector3.Cross(upAxis, forwardAxis);\r\n                rightAxis.normalize();\r\n                forwardAxis = Vector3.Cross(rightAxis, upAxis);\r\n\r\n                Matrix.FromXYZAxesToRef(rightAxis, upAxis, forwardAxis, spaceMat);\r\n            }\r\n\r\n            spaceMat.invertToRef(spaceMatInv);\r\n\r\n            let xzlen: Nullable<number> = null;\r\n\r\n            if (checkPitch) {\r\n                const localTarget = BoneLookController._TmpVecs[3];\r\n                target.subtractToRef(bonePos, localTarget);\r\n                Vector3.TransformCoordinatesToRef(localTarget, spaceMatInv, localTarget);\r\n\r\n                xzlen = Math.sqrt(localTarget.x * localTarget.x + localTarget.z * localTarget.z);\r\n                const pitch = Math.atan2(localTarget.y, xzlen);\r\n                let newPitch = pitch;\r\n\r\n                if (pitch > this._maxPitch) {\r\n                    localTarget.y = this._maxPitchTan * xzlen;\r\n                    newPitch = this._maxPitch;\r\n                } else if (pitch < this._minPitch) {\r\n                    localTarget.y = this._minPitchTan * xzlen;\r\n                    newPitch = this._minPitch;\r\n                }\r\n\r\n                if (pitch != newPitch) {\r\n                    Vector3.TransformCoordinatesToRef(localTarget, spaceMat, localTarget);\r\n                    localTarget.addInPlace(bonePos);\r\n                    target = localTarget;\r\n                }\r\n            }\r\n\r\n            if (checkYaw) {\r\n                const localTarget = BoneLookController._TmpVecs[4];\r\n                target.subtractToRef(bonePos, localTarget);\r\n                Vector3.TransformCoordinatesToRef(localTarget, spaceMatInv, localTarget);\r\n\r\n                const yaw = Math.atan2(localTarget.x, localTarget.z);\r\n                let newYaw = yaw;\r\n\r\n                if (yaw > this._maxYaw || yaw < this._minYaw) {\r\n                    if (xzlen == null) {\r\n                        xzlen = Math.sqrt(localTarget.x * localTarget.x + localTarget.z * localTarget.z);\r\n                    }\r\n\r\n                    if (this._yawRange > Math.PI) {\r\n                        if (this._isAngleBetween(yaw, this._maxYaw, this._midYawConstraint)) {\r\n                            localTarget.z = this._maxYawCos * xzlen;\r\n                            localTarget.x = this._maxYawSin * xzlen;\r\n                            newYaw = this._maxYaw;\r\n                        } else if (this._isAngleBetween(yaw, this._midYawConstraint, this._minYaw)) {\r\n                            localTarget.z = this._minYawCos * xzlen;\r\n                            localTarget.x = this._minYawSin * xzlen;\r\n                            newYaw = this._minYaw;\r\n                        }\r\n                    } else {\r\n                        if (yaw > this._maxYaw) {\r\n                            localTarget.z = this._maxYawCos * xzlen;\r\n                            localTarget.x = this._maxYawSin * xzlen;\r\n                            newYaw = this._maxYaw;\r\n                        } else if (yaw < this._minYaw) {\r\n                            localTarget.z = this._minYawCos * xzlen;\r\n                            localTarget.x = this._minYawSin * xzlen;\r\n                            newYaw = this._minYaw;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                if (this._slerping && this._yawRange > Math.PI) {\r\n                    //are we going to be crossing into the min/max region?\r\n                    const boneFwd = BoneLookController._TmpVecs[8];\r\n                    boneFwd.copyFrom(Axis.Z);\r\n                    if (this._transformYawPitch) {\r\n                        Vector3.TransformCoordinatesToRef(boneFwd, this._transformYawPitchInv, boneFwd);\r\n                    }\r\n\r\n                    const boneRotMat = BoneLookController._TmpMats[4];\r\n                    this._boneQuat.toRotationMatrix(boneRotMat);\r\n                    this.mesh.getWorldMatrix().multiplyToRef(boneRotMat, boneRotMat);\r\n                    Vector3.TransformCoordinatesToRef(boneFwd, boneRotMat, boneFwd);\r\n                    Vector3.TransformCoordinatesToRef(boneFwd, spaceMatInv, boneFwd);\r\n\r\n                    const boneYaw = Math.atan2(boneFwd.x, boneFwd.z);\r\n                    const angBtwTar = this._getAngleBetween(boneYaw, yaw);\r\n                    const angBtwMidYaw = this._getAngleBetween(boneYaw, this._midYawConstraint);\r\n\r\n                    if (angBtwTar > angBtwMidYaw) {\r\n                        if (xzlen == null) {\r\n                            xzlen = Math.sqrt(localTarget.x * localTarget.x + localTarget.z * localTarget.z);\r\n                        }\r\n\r\n                        const angBtwMax = this._getAngleBetween(boneYaw, this._maxYaw);\r\n                        const angBtwMin = this._getAngleBetween(boneYaw, this._minYaw);\r\n\r\n                        if (angBtwMin < angBtwMax) {\r\n                            newYaw = boneYaw + Math.PI * 0.75;\r\n                            localTarget.z = Math.cos(newYaw) * xzlen;\r\n                            localTarget.x = Math.sin(newYaw) * xzlen;\r\n                        } else {\r\n                            newYaw = boneYaw - Math.PI * 0.75;\r\n                            localTarget.z = Math.cos(newYaw) * xzlen;\r\n                            localTarget.x = Math.sin(newYaw) * xzlen;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                if (yaw != newYaw) {\r\n                    Vector3.TransformCoordinatesToRef(localTarget, spaceMat, localTarget);\r\n                    localTarget.addInPlace(bonePos);\r\n                    target = localTarget;\r\n                }\r\n            }\r\n        }\r\n\r\n        const zaxis = BoneLookController._TmpVecs[5];\r\n        const xaxis = BoneLookController._TmpVecs[6];\r\n        const yaxis = BoneLookController._TmpVecs[7];\r\n        const tmpQuat = BoneLookController._TmpQuat;\r\n\r\n        target.subtractToRef(bonePos, zaxis);\r\n        zaxis.normalize();\r\n        Vector3.CrossToRef(upAxis, zaxis, xaxis);\r\n        xaxis.normalize();\r\n        Vector3.CrossToRef(zaxis, xaxis, yaxis);\r\n        yaxis.normalize();\r\n        Matrix.FromXYZAxesToRef(xaxis, yaxis, zaxis, _tmpMat1);\r\n\r\n        if (xaxis.x === 0 && xaxis.y === 0 && xaxis.z === 0) {\r\n            return;\r\n        }\r\n\r\n        if (yaxis.x === 0 && yaxis.y === 0 && yaxis.z === 0) {\r\n            return;\r\n        }\r\n\r\n        if (zaxis.x === 0 && zaxis.y === 0 && zaxis.z === 0) {\r\n            return;\r\n        }\r\n\r\n        if (this.adjustYaw || this.adjustPitch || this.adjustRoll) {\r\n            Matrix.RotationYawPitchRollToRef(this.adjustYaw, this.adjustPitch, this.adjustRoll, _tmpMat2);\r\n            _tmpMat2.multiplyToRef(_tmpMat1, _tmpMat1);\r\n        }\r\n\r\n        if (this.slerpAmount < 1) {\r\n            if (!this._slerping) {\r\n                this.bone.getRotationQuaternionToRef(Space.WORLD, this.mesh, this._boneQuat);\r\n            }\r\n            if (this._transformYawPitch) {\r\n                this._transformYawPitch.multiplyToRef(_tmpMat1, _tmpMat1);\r\n            }\r\n            Quaternion.FromRotationMatrixToRef(_tmpMat1, tmpQuat);\r\n            Quaternion.SlerpToRef(this._boneQuat, tmpQuat, this.slerpAmount, this._boneQuat);\r\n\r\n            this.bone.setRotationQuaternion(this._boneQuat, Space.WORLD, this.mesh);\r\n            this._slerping = true;\r\n        } else {\r\n            if (this._transformYawPitch) {\r\n                this._transformYawPitch.multiplyToRef(_tmpMat1, _tmpMat1);\r\n            }\r\n            this.bone.setRotationMatrix(_tmpMat1, Space.WORLD, this.mesh);\r\n            this._slerping = false;\r\n        }\r\n\r\n        this._updateLinkedTransformRotation();\r\n    }\r\n\r\n    private _getAngleDiff(ang1: number, ang2: number): number {\r\n        let angDiff = ang2 - ang1;\r\n        angDiff %= Math.PI * 2;\r\n\r\n        if (angDiff > Math.PI) {\r\n            angDiff -= Math.PI * 2;\r\n        } else if (angDiff < -Math.PI) {\r\n            angDiff += Math.PI * 2;\r\n        }\r\n\r\n        return angDiff;\r\n    }\r\n\r\n    private _getAngleBetween(ang1: number, ang2: number): number {\r\n        ang1 %= 2 * Math.PI;\r\n        ang1 = ang1 < 0 ? ang1 + 2 * Math.PI : ang1;\r\n\r\n        ang2 %= 2 * Math.PI;\r\n        ang2 = ang2 < 0 ? ang2 + 2 * Math.PI : ang2;\r\n\r\n        let ab = 0;\r\n\r\n        if (ang1 < ang2) {\r\n            ab = ang2 - ang1;\r\n        } else {\r\n            ab = ang1 - ang2;\r\n        }\r\n\r\n        if (ab > Math.PI) {\r\n            ab = Math.PI * 2 - ab;\r\n        }\r\n\r\n        return ab;\r\n    }\r\n\r\n    private _isAngleBetween(ang: number, ang1: number, ang2: number): boolean {\r\n        ang %= 2 * Math.PI;\r\n        ang = ang < 0 ? ang + 2 * Math.PI : ang;\r\n        ang1 %= 2 * Math.PI;\r\n        ang1 = ang1 < 0 ? ang1 + 2 * Math.PI : ang1;\r\n        ang2 %= 2 * Math.PI;\r\n        ang2 = ang2 < 0 ? ang2 + 2 * Math.PI : ang2;\r\n\r\n        if (ang1 < ang2) {\r\n            if (ang > ang1 && ang < ang2) {\r\n                return true;\r\n            }\r\n        } else {\r\n            if (ang > ang2 && ang < ang1) {\r\n                return true;\r\n            }\r\n        }\r\n        return false;\r\n    }\r\n\r\n    private _updateLinkedTransformRotation(): void {\r\n        const bone = this.bone;\r\n        if (bone._linkedTransformNode) {\r\n            if (!bone._linkedTransformNode.rotationQuaternion) {\r\n                bone._linkedTransformNode.rotationQuaternion = new Quaternion();\r\n            }\r\n            bone.getRotationQuaternionToRef(Space.LOCAL, null, bone._linkedTransformNode.rotationQuaternion);\r\n        }\r\n    }\r\n}\r\n"]}