{"version": 3, "file": "arcRotateCameraGamepadInput.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Cameras/Inputs/arcRotateCameraGamepadInput.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAIlD,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AACrE,OAAO,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAC;AACjD;;;GAGG;AACH,MAAM,OAAO,2BAA2B;IAAxC;QAWI;;;WAGG;QAEI,+BAA0B,GAAG,EAAE,CAAC;QAEvC;;;WAGG;QAEI,2BAAsB,GAAG,EAAE,CAAC;QAE3B,gBAAW,GAAG,GAAG,CAAC;IAkG9B,CAAC;IAhGG;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,WAAW,KAAK,GAAG,CAAC;IACpC,CAAC;IAED,IAAW,WAAW,CAAC,KAAc;QACjC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IAC1C,CAAC;IAKD;;OAEG;IACI,aAAa;QAChB,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,cAAc,CAAC;QACtD,IAAI,CAAC,2BAA2B,GAAG,OAAO,CAAC,4BAA4B,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YACpF,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,YAAY,EAAE;gBACvC,4BAA4B;gBAC5B,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,EAAE;oBAChD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;iBAC1B;aACJ;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,8BAA8B,GAAG,OAAO,CAAC,+BAA+B,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YAC1F,IAAI,IAAI,CAAC,OAAO,KAAK,OAAO,EAAE;gBAC1B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;aACvB;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,cAAc,CAAC,4BAA4B,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAC5G,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,cAAc,CAAC,+BAA+B,CAAC,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAClH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;YAEzC,IAAI,QAAQ,EAAE;gBACV,IAAI,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE;oBACjB,MAAM,YAAY,GAAG,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC;oBAClE,IAAI,YAAY,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,KAAK,EAAE;wBACrD,MAAM,CAAC,mBAAmB,IAAI,YAAY,CAAC;qBAC9C;iBACJ;gBAED,IAAI,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE;oBACjB,MAAM,YAAY,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;oBACvF,IAAI,YAAY,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,KAAK,EAAE;wBACrD,MAAM,CAAC,kBAAkB,IAAI,YAAY,CAAC;qBAC7C;iBACJ;aACJ;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;YACxC,IAAI,QAAQ,IAAI,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE;gBAC7B,MAAM,YAAY,GAAG,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC;gBAC9D,IAAI,YAAY,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,KAAK,EAAE;oBACrD,IAAI,CAAC,MAAM,CAAC,oBAAoB,IAAI,YAAY,CAAC;iBACpD;aACJ;SACJ;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,6BAA6B,CAAC;IACzC,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,SAAS,CAAC;IACrB,CAAC;CACJ;AA3GG;IADC,SAAS,EAAE;+EAC2B;AAOvC;IADC,SAAS,EAAE;2EACuB;AAsGjC,gBAAiB,CAAC,6BAA6B,CAAC,GAAG,2BAA2B,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { serialize } from \"../../Misc/decorators\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { ArcRotateCamera } from \"../../Cameras/arcRotateCamera\";\r\nimport type { ICameraInput } from \"../../Cameras/cameraInputsManager\";\r\nimport { CameraInputTypes } from \"../../Cameras/cameraInputsManager\";\r\nimport { Gamepad } from \"../../Gamepads/gamepad\";\r\n/**\r\n * Manage the gamepad inputs to control an arc rotate camera.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n */\r\nexport class ArcRotateCameraGamepadInput implements ICameraInput<ArcRotateCamera> {\r\n    /**\r\n     * Defines the camera the input is attached to.\r\n     */\r\n    public camera: ArcRotateCamera;\r\n\r\n    /**\r\n     * Defines the gamepad the input is gathering event from.\r\n     */\r\n    public gamepad: Nullable<Gamepad>;\r\n\r\n    /**\r\n     * Defines the gamepad rotation sensibility.\r\n     * This is the threshold from when rotation starts to be accounted for to prevent jittering.\r\n     */\r\n    @serialize()\r\n    public gamepadRotationSensibility = 80;\r\n\r\n    /**\r\n     * Defines the gamepad move sensibility.\r\n     * This is the threshold from when moving starts to be accounted for for to prevent jittering.\r\n     */\r\n    @serialize()\r\n    public gamepadMoveSensibility = 40;\r\n\r\n    private _yAxisScale = 1.0;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that Yaxis (for right stick) should be inverted\r\n     */\r\n    public get invertYAxis() {\r\n        return this._yAxisScale !== 1.0;\r\n    }\r\n\r\n    public set invertYAxis(value: boolean) {\r\n        this._yAxisScale = value ? -1.0 : 1.0;\r\n    }\r\n\r\n    private _onGamepadConnectedObserver: Nullable<Observer<Gamepad>>;\r\n    private _onGamepadDisconnectedObserver: Nullable<Observer<Gamepad>>;\r\n\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     */\r\n    public attachControl(): void {\r\n        const manager = this.camera.getScene().gamepadManager;\r\n        this._onGamepadConnectedObserver = manager.onGamepadConnectedObservable.add((gamepad) => {\r\n            if (gamepad.type !== Gamepad.POSE_ENABLED) {\r\n                // prioritize XBOX gamepads.\r\n                if (!this.gamepad || gamepad.type === Gamepad.XBOX) {\r\n                    this.gamepad = gamepad;\r\n                }\r\n            }\r\n        });\r\n\r\n        this._onGamepadDisconnectedObserver = manager.onGamepadDisconnectedObservable.add((gamepad) => {\r\n            if (this.gamepad === gamepad) {\r\n                this.gamepad = null;\r\n            }\r\n        });\r\n\r\n        this.gamepad = manager.getGamepadByType(Gamepad.XBOX);\r\n    }\r\n\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    public detachControl(): void {\r\n        this.camera.getScene().gamepadManager.onGamepadConnectedObservable.remove(this._onGamepadConnectedObserver);\r\n        this.camera.getScene().gamepadManager.onGamepadDisconnectedObservable.remove(this._onGamepadDisconnectedObserver);\r\n        this.gamepad = null;\r\n    }\r\n\r\n    /**\r\n     * Update the current camera state depending on the inputs that have been used this frame.\r\n     * This is a dynamically created lambda to avoid the performance penalty of looping for inputs in the render loop.\r\n     */\r\n    public checkInputs(): void {\r\n        if (this.gamepad) {\r\n            const camera = this.camera;\r\n            const rsValues = this.gamepad.rightStick;\r\n\r\n            if (rsValues) {\r\n                if (rsValues.x != 0) {\r\n                    const normalizedRX = rsValues.x / this.gamepadRotationSensibility;\r\n                    if (normalizedRX != 0 && Math.abs(normalizedRX) > 0.005) {\r\n                        camera.inertialAlphaOffset += normalizedRX;\r\n                    }\r\n                }\r\n\r\n                if (rsValues.y != 0) {\r\n                    const normalizedRY = (rsValues.y / this.gamepadRotationSensibility) * this._yAxisScale;\r\n                    if (normalizedRY != 0 && Math.abs(normalizedRY) > 0.005) {\r\n                        camera.inertialBetaOffset += normalizedRY;\r\n                    }\r\n                }\r\n            }\r\n\r\n            const lsValues = this.gamepad.leftStick;\r\n            if (lsValues && lsValues.y != 0) {\r\n                const normalizedLY = lsValues.y / this.gamepadMoveSensibility;\r\n                if (normalizedLY != 0 && Math.abs(normalizedLY) > 0.005) {\r\n                    this.camera.inertialRadiusOffset -= normalizedLY;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of the current intput.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"ArcRotateCameraGamepadInput\";\r\n    }\r\n\r\n    /**\r\n     * Get the friendly name associated with the input class.\r\n     * @returns the input friendly name\r\n     */\r\n    public getSimpleName(): string {\r\n        return \"gamepad\";\r\n    }\r\n}\r\n\r\n(<any>CameraInputTypes)[\"ArcRotateCameraGamepadInput\"] = ArcRotateCameraGamepadInput;\r\n"]}