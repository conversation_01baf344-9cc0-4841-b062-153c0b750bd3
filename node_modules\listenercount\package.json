{"name": "listenercount", "author": "jden <<EMAIL>>", "version": "1.0.1", "description": "backwards compatible version of builtin events.listenercount", "keywords": ["eventemitter", "events", "listener", "count", "listenercount", "polyfill", "native", "builtin"], "main": "index.js", "scripts": {"test": "standard && mochi"}, "repository": "**************:jden/node-listenercount.git", "license": "ISC", "readmeFilename": "README.md", "devDependencies": {"mochi": "0.3.0", "standard": "^4.0.1"}}