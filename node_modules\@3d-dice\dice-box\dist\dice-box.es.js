var ul = Object.defineProperty;
var Yl = (c, l, b) => l in c ? ul(c, l, { enumerable: !0, configurable: !0, writable: !0, value: b }) : c[l] = b;
var F = (c, l, b) => (Yl(c, typeof l != "symbol" ? l + "" : l, b), b), g = (c, l, b) => {
  if (!l.has(c))
    throw TypeError("Cannot " + b);
};
var W = (c, l, b) => (g(c, l, "read from private field"), b ? b.call(c) : l.get(c)), Y = (c, l, b) => {
  if (l.has(c))
    throw TypeError("Cannot add the same private member more than once");
  l instanceof WeakSet ? l.add(c) : l.set(c, b);
}, i = (c, l, b, d) => (g(c, l, "write to private field"), d ? d.call(c, b) : l.set(c, b), b);
var o = (c, l, b, d) => ({
  set _(X) {
    i(c, l, X, b);
  },
  get _() {
    return W(c, l, d);
  }
}), v = (c, l, b) => (g(c, l, "access private method"), b);
function Jl(c) {
  const { selector: l, id: b } = c;
  let d = document.body, X = document.createElement("canvas");
  if (X.id = b, X.classList.add("dice-box-canvas"), l) {
    if (typeof l != "string")
      throw new Error("You must provide a DOM selector as the first argument in order to render the Dice Box");
    if (d = document.querySelector(l), !(d != null && d.nodeName))
      throw new Error(`DiceBox target DOM node: '${l}' not found or not available yet. Try invoking inside a DOMContentLoaded event`);
  }
  return d.appendChild(X), X;
}
const ml = "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", Xl = typeof window < "u" && window.Blob && new Blob([atob(ml)], { type: "text/javascript;charset=utf-8" });
function il() {
  let c;
  try {
    if (c = Xl && (window.URL || window.webkitURL).createObjectURL(Xl), !c)
      throw "";
    return new Worker(c);
  } catch {
    return new Worker("data:application/javascript;base64," + ml);
  } finally {
    c && (window.URL || window.webkitURL).revokeObjectURL(c);
  }
}
const pl = (c) => {
  let l;
  return function() {
    let b = this, d = arguments;
    l && window.cancelAnimationFrame(l), l = window.requestAnimationFrame(function() {
      c.apply(b, d);
    });
  };
}, hl = (c = { dedupe: !1 }) => {
  const { dedupe: l } = c;
  let b = [], d;
  const X = (m) => (l && (b = []), b.push(m), d || (d = Z()), d.finally(() => {
    d = void 0;
  })), Z = async () => {
    const m = [];
    for (; b.length; ) {
      const V = b.shift();
      m.push(await V());
    }
    return m;
  };
  return { push: X, queue: b, flush: () => d || Promise.resolve([]) };
}, yl = (c) => JSON.parse(JSON.stringify(c));
class Zl {
  /**
   * Generate a random number between 0 (inclusive) and 1 (exclusive).
   * A drop in replacement for Math.random()
   * @return {number}
   */
  static value() {
    const l = window.crypto || window.msCrypto, b = new Uint32Array(1);
    return l.getRandomValues(b)[0] / 2 ** 32;
  }
  /**
   * Generate a very good random number between min (inclusive) and max (exclusive) by using crypto.getRandomValues() twice.
   * @param  {number} min
   * @param  {number} max
   * @return {number}
   */
  static range(l, b) {
    return Math.floor(Math.pow(10, 14) * this.value() * this.value()) % (b - l + 1) + l;
  }
}
const al = (c) => {
  let l = !1, b = c.slice(c.startsWith("#") ? 1 : 0);
  b.length === 3 ? b = [...b].map((X) => X + X).join("") : b.length === 8 && (l = !0), b = parseInt(b, 16);
  let d = {
    r: b >>> 16,
    g: (b & 65280) >>> 8,
    b: b & 255
  };
  return l && (d.r = b >>> 24, d.g = (b & 16711680) >>> 16, d.b = (b & 65280) >>> 8, d.a = b & 255), d;
};
function nl() {
  try {
    const c = document.createElement("canvas");
    return !!window.WebGLRenderingContext && (c.getContext("webgl") || c.getContext("experimental-webgl"));
  } catch {
    return !1;
  }
}
const sl = {
  id: `dice-canvas-${Date.now()}`,
  // set the canvas id
  container: null,
  enableShadows: !0,
  // do dice cast shadows onto DiceBox mesh?
  shadowTransparency: 0.8,
  lightIntensity: 1,
  delay: 10,
  // delay between dice being generated - 0 causes stuttering and physics popping
  scale: 5,
  // scale the dice
  theme: "default",
  // can be a hex color or a pre-defined theme such as 'purpleRock'
  preloadThemes: [],
  externalThemes: {},
  // point to CDN paths
  themeColor: "#2e8555",
  // used for color values or named theme variants - not fully implemented yet // green: #2e8555 // yellow: #feea03
  offscreen: !0,
  // use offscreen canvas browser feature for performance improvements - will fallback to false based on feature detection
  assetPath: "/assets/dice-box/",
  // path to 'ammo', 'themes' folders and web workers
  // origin: location.origin,
  origin: typeof window < "u" ? window.location.origin : "",
  suspendSimulation: !1
};
var f, x, C, B, u, w, U, p, K, k, y, I, cl, E, Gl, T, Wl, Q, D, P, r;
class vl {
  constructor(l = {}) {
    // Load the BabylonJS World
    Y(this, I);
    // Load the AmmoJS physics world
    Y(this, E);
    Y(this, T);
    // used by both .add and .roll - .roll clears the box and .add does not
    Y(this, Q);
    Y(this, P);
    F(this, "rollCollectionData", {});
    F(this, "rollGroupData", {});
    F(this, "rollDiceData", {});
    F(this, "themeData", []);
    F(this, "themesLoadedData", {});
    Y(this, f, 0);
    Y(this, x, 0);
    Y(this, C, 0);
    Y(this, B, 0);
    Y(this, u, {});
    Y(this, w, void 0);
    Y(this, U, void 0);
    Y(this, p, void 0);
    Y(this, K, void 0);
    Y(this, k, void 0);
    Y(this, y, !0);
    F(this, "noop", () => {
    });
    if (arguments.length === 2 && typeof (arguments[0] === "string") && typeof (arguments[1] === "object") && (console.warn("You are using the old API. Dicebox constructor accepts a config object as it's only argument. Please read the v1.1.0 docs at https://fantasticdice.games/docs/usage/config"), l = arguments[1], l.container = arguments[0]), typeof l != "object")
      throw new Error("Config options should be an object. Config reference: https://fantasticdice.games/docs/usage/config#configuration-options");
    const { onBeforeRoll: b, onDieComplete: d, onRollComplete: X, onRemoveComplete: Z, onThemeConfigLoaded: m, onThemeLoaded: V, ...G } = l;
    this.config = { ...sl, ...G }, this.onBeforeRoll = l.onBeforeRoll || this.noop, this.onDieComplete = l.onDieComplete || this.noop, this.onRollComplete = l.onRollComplete || this.noop, this.onRemoveComplete = l.onRemoveComplete || this.noop, this.onThemeLoaded = l.onThemeLoaded || this.noop, this.onThemeConfigLoaded = l.onThemeConfigLoaded || this.noop, nl() ? (this.canvas = Jl({
      selector: this.config.container,
      id: this.config.id
    }), this.isVisible = !0) : i(this, y, !1), this.loadThemeQueue = hl();
  }
  resizeWorld() {
    const b = pl(() => {
      W(this, u).resize({ width: this.canvas.clientWidth, height: this.canvas.clientHeight }), W(this, p) && W(this, p).postMessage({ action: "resize", width: this.canvas.clientWidth, height: this.canvas.clientHeight });
    });
    window.addEventListener("resize", b);
  }
  async init() {
    return W(this, y) ? v(this, E, Gl).call(this) : i(this, K, Promise.resolve()), await v(this, I, cl).call(this), this.resizeWorld(), W(this, u).onRollResult = (l) => {
      const b = this.rollDiceData[l.rollId], d = this.rollGroupData[b.groupId], X = this.rollCollectionData[b.collectionId];
      d.rolls[b.rollId].value = l.value, X.completedRolls++, X.completedRolls == X.rolls.length && X.resolve(Object.values(X.rolls).map(({ collectionId: G, id: J, meshName: R, ...N }) => N));
      const { collectionId: Z, id: m, ...V } = b;
      this.onDieComplete(V);
    }, W(this, u).onRollComplete = () => {
      this.onRollComplete(this.getRollResults());
    }, W(this, u).onDieRemoved = (l) => {
      let b = this.rollDiceData[l];
      const d = this.rollCollectionData[b.removeCollectionId];
      d.completedRolls++, delete this.rollDiceData[b.rollId];
      const X = this.rollGroupData[b.groupId];
      delete X.rolls[b.rollId];
      const Z = v(this, P, r).call(this, b.groupId);
      X.value = Z.value, X.qty = Z.rollsArray.length, d.completedRolls == d.rolls.length && d.resolve(Object.values(d.rolls).map(({ id: N, ...L }) => L));
      const { collectionId: m, id: V, removeCollectionId: G, meshName: J, ...R } = b;
      this.onRemoveComplete(R);
    }, await Promise.all([W(this, w), W(this, K)]), W(this, p) && v(this, T, Wl).call(this), await this.loadThemeQueue.push(() => this.loadTheme(this.config.theme)), this.config.preloadThemes.forEach((async function(l) {
      await this.loadThemeQueue.push(() => this.loadTheme(l));
    }).bind(this)), this;
  }
  // fetch the theme config and return a themeData object
  async getThemeConfig(l) {
    let b = `${this.config.origin}${this.config.assetPath}themes/${l}`;
    this.config.externalThemes[l] && (b = this.config.externalThemes[l]);
    let d = await fetch(`${b}/theme.config.json`).then((m) => {
      if (m.ok) {
        const V = m.headers.get("content-type");
        if (V && V.indexOf("application/json") !== -1)
          return m.json();
        if (m.type && m.type === "basic")
          return m.json();
        throw new Error(`Incorrect contentType: ${V}. Expected "application/json" or "basic"`);
      } else
        throw new Error(`Unable to fetch config file for theme: '${l}'. Request rejected with status ${m.status}: ${m.statusText}`);
    }).catch((m) => console.error(m));
    if (!d)
      throw new Error("No theme config data to work with.");
    let X = "default", Z = `${this.config.origin}${this.config.assetPath}themes/default/default.json`;
    if (d.hasOwnProperty("meshFile") && (X = d.meshFile.replace(/(.*)\..{2,4}$/, "$1"), Z = `${b}/${d.meshFile}`), !d.hasOwnProperty("diceAvailable"))
      throw new Error('A theme must indicate which dice are available by defining "diceAvailable".');
    if (d.hasOwnProperty("extends")) {
      const m = await this.loadTheme(d.extends).catch((G) => console.error(G));
      if (m.hasOwnProperty("extends"))
        throw new Error("Cannot extend a theme that extends another theme.");
      const V = {};
      d.diceAvailable.forEach((G) => {
        V[G] = d.systemName;
      }), m.diceExtended = { ...m.diceExtended, ...V }, this.config.theme = d.extends;
    }
    return Object.assign(
      d,
      {
        basePath: b,
        meshFilePath: Z,
        meshName: X,
        theme: l
      }
    ), d;
  }
  async loadTheme(l) {
    if (this.themesLoadedData[l])
      return this.themesLoadedData[l];
    const b = this.themesLoadedData[l] = await this.getThemeConfig(l).catch((d) => console.error(d));
    if (this.onThemeConfigLoaded(b), !!b)
      return await W(this, u).loadTheme(b).catch((d) => console.error(d)), this.onThemeLoaded(b), b;
  }
  // TODO: use getter and setter
  // change config options
  async updateConfig(l) {
    const b = { ...this.config, ...l };
    if (this.config = b, b.theme) {
      const X = (await this.loadThemeQueue.push(() => this.loadTheme(b.theme))).at(-1);
      X.hasOwnProperty("extends") && (this.config.theme = X.extends);
    }
    return W(this, u).updateConfig(b), W(this, p) && W(this, p).postMessage({
      action: "updateConfig",
      options: b
    }), this;
  }
  clear() {
    return i(this, f, 0), i(this, x, 0), i(this, C, 0), i(this, B, 0), this.rollCollectionData = {}, this.rollGroupData = {}, this.rollDiceData = {}, W(this, u).clear(), W(this, p) && W(this, p).postMessage({ action: "clearDice" }), this;
  }
  hide(l) {
    return l ? (this.canvas.dataset.hideClass = l, this.canvas.classList.add(l)) : this.canvas.style.display = "none", this.isVisible = !1, this;
  }
  show() {
    var b;
    const l = (b = this.canvas.dataset) == null ? void 0 : b.hideClass;
    return l ? (delete this.canvas.dataset.hideClass, this.canvas.classList.remove(l)) : this.canvas.style.display = "block", this.isVisible = !0, this.resizeWorld(), this;
  }
  // TODO: pass data with roll - such as roll name. Passed back at the end in the results
  roll(l, { theme: b = this.config.theme, themeColor: d = this.config.themeColor, newStartPoint: X = !0 } = {}) {
    this.clear();
    const Z = o(this, f)._++;
    this.rollCollectionData[Z] = new M({
      id: Z,
      notation: l,
      theme: b,
      themeColor: d,
      newStartPoint: X
    });
    const m = this.createNotationArray(l, this.themesLoadedData[b].diceAvailable);
    return v(this, Q, D).call(this, m, Z), this.rollCollectionData[Z].promise;
  }
  add(l, { theme: b = this.config.theme, themeColor: d = this.config.themeColor, newStartPoint: X = !0 } = {}) {
    const Z = o(this, f)._++;
    this.rollCollectionData[Z] = new M({
      id: Z,
      notation: l,
      theme: b,
      themeColor: d,
      newStartPoint: X
    });
    const m = this.createNotationArray(l, this.themesLoadedData[b].diceAvailable);
    return v(this, Q, D).call(this, m, Z), this.rollCollectionData[Z].promise;
  }
  reroll(l, { remove: b = !1, hide: d = !1, newStartPoint: X = !0 } = {}) {
    const m = (Array.isArray(l) ? l : [l]).map(({ value: V, ...G }) => G);
    return b === !0 && this.remove(m, { hide: d }), this.add(m, { newStartPoint: X });
  }
  remove(l, { hide: b = !1 } = {}) {
    const d = Array.isArray(l) ? l : [l], X = o(this, f)._++;
    return this.rollCollectionData[X] = new M({
      id: X,
      notation: l,
      rolls: d
    }), d.map((Z) => {
      this.rollDiceData[Z.rollId].removeCollectionId = X;
      let m = this.rollDiceData[Z.rollId].id;
      W(this, u).remove({ id: m, rollId: Z.rollId }), W(this, p) && W(this, p).postMessage({ action: "removeDie", id: m });
    }), this.rollCollectionData[X].promise;
  }
  // accepts simple notations eg: 4d6
  // accepts array of notations eg: ['4d6','2d10']
  // accepts object {sides:int, qty:int}
  // accepts array of objects eg: [{sides:int, qty:int, mods:[]}]
  createNotationArray(l, b) {
    const d = Array.isArray(l) ? l : [l];
    let X = [];
    const Z = (G) => {
      if (G.hasOwnProperty("qty") || (G.qty = 1), G.hasOwnProperty("sides"))
        return G.sides === "100" && (G.sides = 100, G.data = "single"), !0;
      {
        const J = "Roll notation is missing sides";
        throw new Error(J);
      }
    }, m = (G) => {
      G = G.toString();
      let J = G.split(".");
      return J[1] ? J[1] = parseInt(J[1]) + 1 : J[1] = 1, J[0] + "." + J[1];
    }, V = (G) => {
      G.hasOwnProperty("rollId") && this.rollDiceData.hasOwnProperty(G.rollId) && (G.rollId = m(G.rollId)), G.hasOwnProperty("modifier") || (G.modifier = 0);
    };
    return d.forEach((G) => {
      typeof G == "string" ? X.push(this.parse(G, b)) : typeof d == "object" && (V(G), Z(G) && X.push(G));
    }), X;
  }
  // parse text die notation such as 2d10+3 => {number:2, type:6, modifier:3}
  // taken from https://github.com/ChapelR/dice-notation
  parse(l, b) {
    const d = /(\d+)([dD]{1}\d+)(.*)$/i, X = /(\d+)[dD](00|%)(.*)$/i, Z = /(\d+)[dD](f+[ate]*)(.*)$/i, m = /(\d+)[dD]([\d\w]+)([+-]{0,1}\d+)?/i, V = /([+-])(\d+)/, G = l.trim().replace(/\s+/g, ""), J = (h, S) => {
      if (h = Number(h), Number.isNaN(h) || !Number.isInteger(h) || h < 1)
        throw new Error(S);
      return h;
    }, R = G.match(X) || G.match(d) || G.match(Z) || G.match(m);
    let N = 0;
    const L = "Invalid notation: " + l;
    if (!R || !R.length || R.length < 3)
      throw new Error(L);
    if (R[3] && V.test(R[3])) {
      const h = R[3].match(V);
      let S = J(h[2], L);
      h[1].trim() === "-" && (S *= -1), N = S;
    }
    const s = {
      qty: J(R[1], L),
      modifier: N
    };
    return G.match(X) ? (s.sides = "d100", s.data = "single") : G.match(Z) ? s.sides = "fate" : (b.includes(G.match(m)[2]), s.sides = R[2]), s;
  }
  getRollResults() {
    return Object.entries(this.rollGroupData).map(([l, b]) => {
      const d = v(this, P, r).call(this, l);
      b.value = d.value, b.qty = d.rollsArray.length;
      const X = { ...b };
      return X.rolls = d.rollsArray, X;
    });
  }
}
f = new WeakMap(), x = new WeakMap(), C = new WeakMap(), B = new WeakMap(), u = new WeakMap(), w = new WeakMap(), U = new WeakMap(), p = new WeakMap(), K = new WeakMap(), k = new WeakMap(), y = new WeakMap(), I = new WeakSet(), cl = async function() {
  i(this, w, new Promise((b, d) => {
    i(this, U, b);
  }));
  const l = () => {
    W(this, U).call(this);
  };
  if (W(this, y))
    if ("OffscreenCanvas" in window && "transferControlToOffscreen" in this.canvas && this.config.offscreen) {
      const b = await import("./world.offscreen.js").then((d) => d.default);
      i(this, u, new b({
        canvas: this.canvas,
        options: this.config,
        onInitComplete: l
      }));
    } else {
      this.config.offscreen && (console.warn("This browser does not support OffscreenCanvas. Using standard canvas fallback."), this.config.offscreen = !1);
      const b = await import("./world.onscreen.js").then((d) => d.default);
      i(this, u, new b({
        canvas: this.canvas,
        options: this.config,
        onInitComplete: l
      }));
    }
  else {
    console.warn("This browser does not support WebGL which is required for 3D rendering. Falling back to random number generator");
    const b = await import("./world.none.js").then((d) => d.default);
    i(this, u, new b({
      canvas: this.canvas,
      options: this.config,
      onInitComplete: l
    }));
  }
}, E = new WeakSet(), Gl = function() {
  i(this, p, new il()), i(this, K, new Promise((l, b) => {
    i(this, k, l);
  })), W(this, p).onmessage = (l) => {
    switch (l.data.action) {
      case "init-complete":
        W(this, k).call(this);
    }
  }, W(this, p).postMessage({
    action: "init",
    width: this.canvas.clientWidth,
    height: this.canvas.clientHeight,
    options: this.config
  });
}, T = new WeakSet(), Wl = function() {
  const l = new MessageChannel();
  W(this, u).connect(l.port1), W(this, p).postMessage({
    action: "connect"
  }, [l.port2]);
}, Q = new WeakSet(), D = async function(l, b) {
  this.onBeforeRoll(l);
  const d = this.rollCollectionData[b];
  let X = d.newStartPoint;
  l.forEach(async (Z) => {
    var q, $, _, ll, bl, dl;
    if (!Z.sides)
      throw new Error("Improper dice notation or unable to parse notation");
    let m = Z.theme || d.theme || this.config.theme;
    const V = Z.themeColor || d.themeColor || this.config.themeColor, G = {}, J = Z.groupId !== void 0;
    let R;
    const N = () => this.loadTheme(m);
    await this.loadThemeQueue.push(N);
    let L = this.themesLoadedData[m].meshName, s = (q = this.themesLoadedData[m]) == null ? void 0 : q.diceAvailable, h = this.themesLoadedData[m].diceExtended || {}, S = (_ = ($ = this.themesLoadedData[m]) == null ? void 0 : $.material) == null ? void 0 : _.type;
    const t = Object.keys(h);
    if (t && t.includes(Z.sides)) {
      m = h[Z.sides];
      const e = () => this.loadTheme(m);
      this.loadThemeQueue.push(e), L = this.themesLoadedData[m].meshName, s = (ll = this.themesLoadedData[m]) == null ? void 0 : ll.diceAvailable, S = (dl = (bl = this.themesLoadedData[m]) == null ? void 0 : bl.material) == null ? void 0 : dl.type;
    }
    let O = "", j;
    S === "color" && (j = al(V), O = j.r * 0.299 + j.g * 0.587 + j.b * 0.114 > 175 ? "_dark" : "_light");
    for (var A = 0, Vl = Z.qty; A < Vl; A++) {
      let e = Z.rollId !== void 0 ? Z.rollId : o(this, C)._++, Rl = Z.id !== void 0 ? Z.id : o(this, B)._++;
      R = J ? Z.groupId : W(this, x);
      const H = Number.isInteger(Z.sides) ? `d${Z.sides}` : Z.sides;
      /^d[1-9]{1}[0-9]{0,1}0?$/.test(Z.sides) && (Z.sides = parseInt(Z.sides.replace("d", "")));
      const a = {
        sides: Z.sides,
        data: Z.data,
        dieType: H,
        groupId: R,
        collectionId: d.id,
        rollId: e,
        id: Rl,
        theme: m,
        themeColor: V,
        meshName: L
      };
      if (G[e] = a, this.rollDiceData[e] = a, d.rolls.push(this.rollDiceData[e]), a.sides === "fate" && !s.includes(H) && !t.includes(H) || a.sides === "fate" && !W(this, y)) {
        console.warn(`fate die unavailable in '${m}' theme. Using fallback.`);
        const n = -1, z = 1;
        a.value = Zl.range(n, z), W(this, u).addNonDie(a);
      } else if (this.config.suspendSimulation || !s.includes(H) && !t.includes(H) || !W(this, y)) {
        const n = W(this, y) ? this.config.suspendSimulation ? "3D simulation suspended. Using fallback." : `${a.sides} die unavailable in '${m}' theme. Using fallback.` : "This browser does not support webGL. Using random number fallback.";
        console.warn(n);
        const z = Number.isInteger(a.sides) ? a.sides : parseInt(a.sides.replace(/\D/g, ""));
        a.value = Zl.range(1, z), W(this, u).addNonDie(a);
      } else {
        let n;
        if (t.includes(H)) {
          const z = h[H];
          n = this.themesLoadedData[z];
        }
        W(this, u).add({
          ...a,
          newStartPoint: X,
          theme: (n == null ? void 0 : n.systemName) || m,
          meshName: (n == null ? void 0 : n.meshName) || L,
          colorSuffix: O
        });
      }
      X = !1;
    }
    J ? Object.assign(this.rollGroupData[R].rolls, G) : (Z.rolls = G, Z.id = R, this.rollGroupData[R] = Z, ++o(this, x)._);
  });
}, P = new WeakSet(), r = function(l) {
  const b = this.rollGroupData[l], d = Object.values(b.rolls).map(({ collectionId: Z, id: m, meshName: V, ...G }) => G);
  let X = d.reduce((Z, m) => {
    const V = isNaN(m.value) ? 0 : m.value;
    return Z + V;
  }, 0);
  return X += b.modifier ? b.modifier : 0, { value: X, rollsArray: d };
};
class M {
  constructor(l) {
    Object.assign(this, l), this.rolls = l.rolls || [], this.completedRolls = 0;
    const b = this;
    this.promise = new Promise((d, X) => {
      b.resolve = d, b.reject = X;
    });
  }
}
export {
  yl as d,
  vl as default
};
//# sourceMappingURL=dice-box.es.js.map
