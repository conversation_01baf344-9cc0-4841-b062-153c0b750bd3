{"version": 3, "file": "collisionCoordinator.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Collisions/collisionCoordinator.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AACjC,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAkBtC,gBAAgB;AAChB,MAAM,OAAO,2BAA2B;IAAxC;QAGY,oBAAe,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACjC,oBAAe,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAEjC,mBAAc,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;IAgF5C,CAAC;IA9EU,cAAc,CACjB,QAAiB,EACjB,YAAqB,EACrB,QAAkB,EAClB,YAAoB,EACpB,YAA0B,EAC1B,aAA2G,EAC3G,cAAsB;QAEtB,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAC7D,YAAY,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACjE,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC;QAC7B,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QACpB,QAAQ,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;QACjD,QAAQ,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;QACjD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,QAAQ,EAAE,YAAY,EAAE,IAAI,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;QAE9H,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACtD,kBAAkB;QAClB,aAAa,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAC;IAC9E,CAAC;IAEM,cAAc;QACjB,OAAO,IAAI,QAAQ,EAAE,CAAC;IAC1B,CAAC;IAEM,IAAI,CAAC,KAAY;QACpB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IAEO,iBAAiB,CACrB,QAAiB,EACjB,QAAiB,EACjB,QAAkB,EAClB,YAAoB,EACpB,aAAsB,EACtB,eAAuC,IAAI;QAE3C,MAAM,aAAa,GAAG,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAEtD,IAAI,QAAQ,CAAC,MAAM,IAAI,YAAY,EAAE;YACjC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACjC,OAAO;SACV;QAED,4CAA4C;QAC5C,MAAM,aAAa,GAAG,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC;QAEzF,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QAExD,kFAAkF;QAClF,oDAAoD;QACpD,MAAM,MAAM,GAAG,CAAC,YAAY,IAAI,YAAY,CAAC,iBAAiB,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAEtF,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAChD,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;YAC3B,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,KAAK,YAAY,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;gBACpI,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;aAClC;SACJ;QAED,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;YAC1B,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;YAC3C,OAAO;SACV;QAED,IAAI,QAAQ,CAAC,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,CAAC,KAAK,CAAC,EAAE;YAC1D,QAAQ,CAAC,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;SAC7C;QAED,IAAI,QAAQ,CAAC,MAAM,EAAE,IAAI,aAAa,EAAE;YACpC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACjC,OAAO;SACV;QAED,QAAQ,CAAC,MAAM,EAAE,CAAC;QAClB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC;IACpG,CAAC;CACJ;AAED,KAAK,CAAC,2BAA2B,GAAG,GAAG,EAAE;IACrC,OAAO,IAAI,2BAA2B,EAAE,CAAC;AAC7C,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Scene } from \"../scene\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport { Engine } from \"../Engines/engine\";\r\nimport { Collider } from \"./collider\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\n\r\n/** @internal */\r\nexport interface ICollisionCoordinator {\r\n    createCollider(): Collider;\r\n    getNewPosition(\r\n        position: Vector3,\r\n        displacement: Vector3,\r\n        collider: Collider,\r\n        maximumRetry: number,\r\n        excludedMesh: Nullable<AbstractMesh>,\r\n        onNewPosition: (collisionIndex: number, newPosition: Vector3, collidedMesh: Nullable<AbstractMesh>) => void,\r\n        collisionIndex: number\r\n    ): void;\r\n    init(scene: Scene): void;\r\n}\r\n\r\n/** @internal */\r\nexport class DefaultCollisionCoordinator implements ICollisionCoordinator {\r\n    private _scene: Scene;\r\n\r\n    private _scaledPosition = Vector3.Zero();\r\n    private _scaledVelocity = Vector3.Zero();\r\n\r\n    private _finalPosition = Vector3.Zero();\r\n\r\n    public getNewPosition(\r\n        position: Vector3,\r\n        displacement: Vector3,\r\n        collider: Collider,\r\n        maximumRetry: number,\r\n        excludedMesh: AbstractMesh,\r\n        onNewPosition: (collisionIndex: number, newPosition: Vector3, collidedMesh: Nullable<AbstractMesh>) => void,\r\n        collisionIndex: number\r\n    ): void {\r\n        position.divideToRef(collider._radius, this._scaledPosition);\r\n        displacement.divideToRef(collider._radius, this._scaledVelocity);\r\n        collider.collidedMesh = null;\r\n        collider._retry = 0;\r\n        collider._initialVelocity = this._scaledVelocity;\r\n        collider._initialPosition = this._scaledPosition;\r\n        this._collideWithWorld(this._scaledPosition, this._scaledVelocity, collider, maximumRetry, this._finalPosition, excludedMesh);\r\n\r\n        this._finalPosition.multiplyInPlace(collider._radius);\r\n        //run the callback\r\n        onNewPosition(collisionIndex, this._finalPosition, collider.collidedMesh);\r\n    }\r\n\r\n    public createCollider(): Collider {\r\n        return new Collider();\r\n    }\r\n\r\n    public init(scene: Scene): void {\r\n        this._scene = scene;\r\n    }\r\n\r\n    private _collideWithWorld(\r\n        position: Vector3,\r\n        velocity: Vector3,\r\n        collider: Collider,\r\n        maximumRetry: number,\r\n        finalPosition: Vector3,\r\n        excludedMesh: Nullable<AbstractMesh> = null\r\n    ): void {\r\n        const closeDistance = Engine.CollisionsEpsilon * 10.0;\r\n\r\n        if (collider._retry >= maximumRetry) {\r\n            finalPosition.copyFrom(position);\r\n            return;\r\n        }\r\n\r\n        // Check if this is a mesh else camera or -1\r\n        const collisionMask = excludedMesh ? excludedMesh.collisionMask : collider.collisionMask;\r\n\r\n        collider._initialize(position, velocity, closeDistance);\r\n\r\n        // Check if collision detection should happen against specified list of meshes or,\r\n        // if not specified, against all meshes in the scene\r\n        const meshes = (excludedMesh && excludedMesh.surroundingMeshes) || this._scene.meshes;\r\n\r\n        for (let index = 0; index < meshes.length; index++) {\r\n            const mesh = meshes[index];\r\n            if (mesh.isEnabled() && mesh.checkCollisions && mesh.subMeshes && mesh !== excludedMesh && (collisionMask & mesh.collisionGroup) !== 0) {\r\n                mesh._checkCollision(collider);\r\n            }\r\n        }\r\n\r\n        if (!collider.collisionFound) {\r\n            position.addToRef(velocity, finalPosition);\r\n            return;\r\n        }\r\n\r\n        if (velocity.x !== 0 || velocity.y !== 0 || velocity.z !== 0) {\r\n            collider._getResponse(position, velocity);\r\n        }\r\n\r\n        if (velocity.length() <= closeDistance) {\r\n            finalPosition.copyFrom(position);\r\n            return;\r\n        }\r\n\r\n        collider._retry++;\r\n        this._collideWithWorld(position, velocity, collider, maximumRetry, finalPosition, excludedMesh);\r\n    }\r\n}\r\n\r\nScene.CollisionCoordinatorFactory = () => {\r\n    return new DefaultCollisionCoordinator();\r\n};\r\n"]}