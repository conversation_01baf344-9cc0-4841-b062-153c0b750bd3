import { Observable } from "../../Misc/observable";
import type { Nullable } from "../../types";
import type { Camera } from "../../Cameras/camera";
import { DeviceOrientationCamera } from "../../Cameras/deviceOrientationCamera";
import { VRDeviceOrientationFreeCamera } from "../../Cameras/VR/vrDeviceOrientationFreeCamera";
import type { WebVROptions } from "../../Cameras/VR/webVRCamera";
import { WebVRFreeCamera } from "../../Cameras/VR/webVRCamera";
import type { Scene } from "../../scene";
import { Vector3 } from "../../Maths/math.vector";
import { Color3 } from "../../Maths/math.color";
import { WebVRController } from "../../Gamepads/Controllers/webVRController";
import type { AbstractMesh } from "../../Meshes/abstractMesh";
import { Mesh } from "../../Meshes/mesh";
import type { PickingInfo } from "../../Collisions/pickingInfo";
import { EasingFunction } from "../../Animations/easing";
import { VRCameraMetrics } from "../../Cameras/VR/vrCameraMetrics";
import "../../Gamepads/gamepadSceneComponent";
import "../../Animations/animatable";
import type { WebXRDefaultExperience } from "../../XR/webXRDefaultExperience";
/**
 * Options to modify the vr teleportation behavior.
 */
export interface VRTeleportationOptions {
    /**
     * The name of the mesh which should be used as the teleportation floor. (default: null)
     */
    floorMeshName?: string;
    /**
     * A list of meshes to be used as the teleportation floor. (default: empty)
     */
    floorMeshes?: Mesh[];
    /**
     * The teleportation mode. (default: TELEPORTATIONMODE_CONSTANTTIME)
     */
    teleportationMode?: number;
    /**
     * The duration of the animation in ms, apply when animationMode is TELEPORTATIONMODE_CONSTANTTIME. (default 122ms)
     */
    teleportationTime?: number;
    /**
     * The speed of the animation in distance/sec, apply when animationMode is TELEPORTATIONMODE_CONSTANTSPEED. (default 20 units / sec)
     */
    teleportationSpeed?: number;
    /**
     * The easing function used in the animation or null for Linear. (default CircleEase)
     */
    easingFunction?: EasingFunction;
}
/**
 * Options to modify the vr experience helper's behavior.
 */
export interface VRExperienceHelperOptions extends WebVROptions {
    /**
     * Create a DeviceOrientationCamera to be used as your out of vr camera. (default: true)
     */
    createDeviceOrientationCamera?: boolean;
    /**
     * Create a VRDeviceOrientationFreeCamera to be used for VR when no external HMD is found. (default: true)
     */
    createFallbackVRDeviceOrientationFreeCamera?: boolean;
    /**
     * Uses the main button on the controller to toggle the laser casted. (default: true)
     */
    laserToggle?: boolean;
    /**
     * A list of meshes to be used as the teleportation floor. If specified, teleportation will be enabled (default: undefined)
     */
    floorMeshes?: Mesh[];
    /**
     * Distortion metrics for the fallback vrDeviceOrientationCamera (default: VRCameraMetrics.Default)
     */
    vrDeviceOrientationCameraMetrics?: VRCameraMetrics;
    /**
     * Defines if WebXR should be used instead of WebVR (if available)
     */
    useXR?: boolean;
}
/**
 * Event containing information after VR has been entered
 */
export declare class OnAfterEnteringVRObservableEvent {
    /**
     * If entering vr was successful
     */
    success: boolean;
}
/**
 * Helps to quickly add VR support to an existing scene.
 * See https://doc.babylonjs.com/features/featuresDeepDive/cameras/webVRHelper
 * @deprecated
 */
export declare class VRExperienceHelper {
    /** Options to modify the vr experience helper's behavior. */
    webVROptions: VRExperienceHelperOptions;
    private _scene;
    private _position;
    private _btnVR;
    private _btnVRDisplayed;
    private _webVRsupported;
    private _webVRready;
    private _webVRrequesting;
    private _webVRpresenting;
    private _hasEnteredVR;
    private _fullscreenVRpresenting;
    private _inputElement;
    private _webVRCamera;
    private _vrDeviceOrientationCamera;
    private _deviceOrientationCamera;
    private _existingCamera;
    private _onKeyDown;
    private _onVrDisplayPresentChangeBind;
    private _onVRDisplayChangedBind;
    private _onVRRequestPresentStart;
    private _onVRRequestPresentComplete;
    /**
     * Gets or sets a boolean indicating that gaze can be enabled even if pointer lock is not engage (useful on iOS where fullscreen mode and pointer lock are not supported)
     */
    enableGazeEvenWhenNoPointerLock: boolean;
    /**
     * Gets or sets a boolean indicating that the VREXperienceHelper will exit VR if double tap is detected
     */
    exitVROnDoubleTap: boolean;
    /**
     * Observable raised right before entering VR.
     */
    onEnteringVRObservable: Observable<VRExperienceHelper>;
    /**
     * Observable raised when entering VR has completed.
     */
    onAfterEnteringVRObservable: Observable<OnAfterEnteringVRObservableEvent>;
    /**
     * Observable raised when exiting VR.
     */
    onExitingVRObservable: Observable<VRExperienceHelper>;
    /**
     * Observable raised when controller mesh is loaded.
     */
    onControllerMeshLoadedObservable: Observable<WebVRController>;
    /** Return this.onEnteringVRObservable
     * Note: This one is for backward compatibility. Please use onEnteringVRObservable directly
     */
    get onEnteringVR(): Observable<VRExperienceHelper>;
    /** Return this.onExitingVRObservable
     * Note: This one is for backward compatibility. Please use onExitingVRObservable directly
     */
    get onExitingVR(): Observable<VRExperienceHelper>;
    /** Return this.onControllerMeshLoadedObservable
     * Note: This one is for backward compatibility. Please use onControllerMeshLoadedObservable directly
     */
    get onControllerMeshLoaded(): Observable<WebVRController>;
    private _rayLength;
    private _useCustomVRButton;
    private _teleportationRequested;
    private _teleportActive;
    private _floorMeshName;
    private _floorMeshesCollection;
    private _teleportationMode;
    private _teleportationTime;
    private _teleportationSpeed;
    private _teleportationEasing;
    private _rotationAllowed;
    private _teleportBackwardsVector;
    private _teleportationTarget;
    private _isDefaultTeleportationTarget;
    private _postProcessMove;
    private _teleportationFillColor;
    private _teleportationBorderColor;
    private _rotationAngle;
    private _haloCenter;
    private _cameraGazer;
    private _padSensibilityUp;
    private _padSensibilityDown;
    private _leftController;
    private _rightController;
    private _gazeColor;
    private _laserColor;
    private _pickedLaserColor;
    private _pickedGazeColor;
    /**
     * Observable raised when a new mesh is selected based on meshSelectionPredicate
     */
    onNewMeshSelected: Observable<AbstractMesh>;
    /**
     * Observable raised when a new mesh is selected based on meshSelectionPredicate.
     * This observable will provide the mesh and the controller used to select the mesh
     */
    onMeshSelectedWithController: Observable<{
        mesh: AbstractMesh;
        controller: WebVRController;
    }>;
    /**
     * Observable raised when a new mesh is picked based on meshSelectionPredicate
     */
    onNewMeshPicked: Observable<PickingInfo>;
    private _circleEase;
    /**
     * Observable raised before camera teleportation
     */
    onBeforeCameraTeleport: Observable<Vector3>;
    /**
     *  Observable raised after camera teleportation
     */
    onAfterCameraTeleport: Observable<Vector3>;
    /**
     * Observable raised when current selected mesh gets unselected
     */
    onSelectedMeshUnselected: Observable<AbstractMesh>;
    private _raySelectionPredicate;
    /**
     * To be optionally changed by user to define custom ray selection
     */
    raySelectionPredicate: (mesh: AbstractMesh) => boolean;
    /**
     * To be optionally changed by user to define custom selection logic (after ray selection)
     */
    meshSelectionPredicate: (mesh: AbstractMesh) => boolean;
    /**
     * Set teleportation enabled. If set to false camera teleportation will be disabled but camera rotation will be kept.
     */
    teleportationEnabled: boolean;
    private _defaultHeight;
    private _teleportationInitialized;
    private _interactionsEnabled;
    private _interactionsRequested;
    private _displayGaze;
    private _displayLaserPointer;
    /**
     * The mesh used to display where the user is going to teleport.
     */
    get teleportationTarget(): Mesh;
    /**
     * Sets the mesh to be used to display where the user is going to teleport.
     */
    set teleportationTarget(value: Mesh);
    /**
     * The mesh used to display where the user is selecting, this mesh will be cloned and set as the gazeTracker for the left and right controller
     * when set bakeCurrentTransformIntoVertices will be called on the mesh.
     * See https://doc.babylonjs.com/features/featuresDeepDive/mesh/transforms/center_origin/bakingTransforms
     */
    get gazeTrackerMesh(): Mesh;
    set gazeTrackerMesh(value: Mesh);
    /**
     * If the gaze trackers scale should be updated to be constant size when pointing at near/far meshes
     */
    updateGazeTrackerScale: boolean;
    /**
     * If the gaze trackers color should be updated when selecting meshes
     */
    updateGazeTrackerColor: boolean;
    /**
     * If the controller laser color should be updated when selecting meshes
     */
    updateControllerLaserColor: boolean;
    /**
     * The gaze tracking mesh corresponding to the left controller
     */
    get leftControllerGazeTrackerMesh(): Nullable<Mesh>;
    /**
     * The gaze tracking mesh corresponding to the right controller
     */
    get rightControllerGazeTrackerMesh(): Nullable<Mesh>;
    /**
     * If the ray of the gaze should be displayed.
     */
    get displayGaze(): boolean;
    /**
     * Sets if the ray of the gaze should be displayed.
     */
    set displayGaze(value: boolean);
    /**
     * If the ray of the LaserPointer should be displayed.
     */
    get displayLaserPointer(): boolean;
    /**
     * Sets if the ray of the LaserPointer should be displayed.
     */
    set displayLaserPointer(value: boolean);
    /**
     * The deviceOrientationCamera used as the camera when not in VR.
     */
    get deviceOrientationCamera(): Nullable<DeviceOrientationCamera>;
    /**
     * Based on the current WebVR support, returns the current VR camera used.
     */
    get currentVRCamera(): Nullable<Camera>;
    /**
     * The webVRCamera which is used when in VR.
     */
    get webVRCamera(): WebVRFreeCamera;
    /**
     * The deviceOrientationCamera that is used as a fallback when vr device is not connected.
     */
    get vrDeviceOrientationCamera(): Nullable<VRDeviceOrientationFreeCamera>;
    /**
     * The html button that is used to trigger entering into VR.
     */
    get vrButton(): Nullable<HTMLButtonElement>;
    private get _teleportationRequestInitiated();
    /**
     * Defines whether or not Pointer lock should be requested when switching to
     * full screen.
     */
    requestPointerLockOnFullScreen: boolean;
    /**
     * If asking to force XR, this will be populated with the default xr experience
     */
    xr: WebXRDefaultExperience;
    /**
     * Was the XR test done already. If this is true AND this.xr exists, xr is initialized.
     * If this is true and no this.xr, xr exists but is not supported, using WebVR.
     */
    xrTestDone: boolean;
    /**
     * Instantiates a VRExperienceHelper.
     * Helps to quickly add VR support to an existing scene.
     * @param scene The scene the VRExperienceHelper belongs to.
     * @param webVROptions Options to modify the vr experience helper's behavior.
     */
    constructor(scene: Scene, 
    /** Options to modify the vr experience helper's behavior. */
    webVROptions?: VRExperienceHelperOptions);
    private _completeVRInit;
    private _onDefaultMeshLoaded;
    private _onResize;
    private _onFullscreenChange;
    /**
     * Gets a value indicating if we are currently in VR mode.
     */
    get isInVRMode(): boolean;
    private _onVrDisplayPresentChange;
    private _onVRDisplayChanged;
    private _moveButtonToBottomRight;
    private _displayVRButton;
    private _updateButtonVisibility;
    private _cachedAngularSensibility;
    /**
     * Attempt to enter VR. If a headset is connected and ready, will request present on that.
     * Otherwise, will use the fullscreen API.
     */
    enterVR(): void;
    /**
     * Attempt to exit VR, or fullscreen.
     */
    exitVR(): void;
    /**
     * The position of the vr experience helper.
     */
    get position(): Vector3;
    /**
     * Sets the position of the vr experience helper.
     */
    set position(value: Vector3);
    /**
     * Enables controllers and user interactions such as selecting and object or clicking on an object.
     */
    enableInteractions(): void;
    private get _noControllerIsActive();
    private _beforeRender;
    private _isTeleportationFloor;
    /**
     * Adds a floor mesh to be used for teleportation.
     * @param floorMesh the mesh to be used for teleportation.
     */
    addFloorMesh(floorMesh: Mesh): void;
    /**
     * Removes a floor mesh from being used for teleportation.
     * @param floorMesh the mesh to be removed.
     */
    removeFloorMesh(floorMesh: Mesh): void;
    /**
     * Enables interactions and teleportation using the VR controllers and gaze.
     * @param vrTeleportationOptions options to modify teleportation behavior.
     */
    enableTeleportation(vrTeleportationOptions?: VRTeleportationOptions): void;
    private _onNewGamepadConnected;
    private _tryEnableInteractionOnController;
    private _onNewGamepadDisconnected;
    private _enableInteractionOnController;
    private _checkTeleportWithRay;
    private _checkRotate;
    private _checkTeleportBackwards;
    private _enableTeleportationOnController;
    private _createTeleportationCircles;
    private _displayTeleportationTarget;
    private _hideTeleportationTarget;
    private _rotateCamera;
    private _moveTeleportationSelectorTo;
    private _workingVector;
    private _workingQuaternion;
    private _workingMatrix;
    /**
     * Time Constant Teleportation Mode
     */
    static readonly TELEPORTATIONMODE_CONSTANTTIME = 0;
    /**
     * Speed Constant Teleportation Mode
     */
    static readonly TELEPORTATIONMODE_CONSTANTSPEED = 1;
    /**
     * Teleports the users feet to the desired location
     * @param location The location where the user's feet should be placed
     */
    teleportCamera(location: Vector3): void;
    private _convertNormalToDirectionOfRay;
    private _castRayAndSelectObject;
    private _notifySelectedMeshUnselected;
    /**
     * Permanently set new colors for the laser pointer
     * @param color the new laser color
     * @param pickedColor the new laser color when picked mesh detected
     */
    setLaserColor(color: Color3, pickedColor?: Color3): void;
    /**
     * Set lighting enabled / disabled on the laser pointer of both controllers
     * @param enabled should the lighting be enabled on the laser pointer
     */
    setLaserLightingState(enabled?: boolean): void;
    /**
     * Permanently set new colors for the gaze pointer
     * @param color the new gaze color
     * @param pickedColor the new gaze color when picked mesh detected
     */
    setGazeColor(color: Color3, pickedColor?: Color3): void;
    /**
     * Sets the color of the laser ray from the vr controllers.
     * @param color new color for the ray.
     */
    changeLaserColor(color: Color3): void;
    /**
     * Sets the color of the ray from the vr headsets gaze.
     * @param color new color for the ray.
     */
    changeGazeColor(color: Color3): void;
    /**
     * Exits VR and disposes of the vr experience helper
     */
    dispose(): void;
    /**
     * Gets the name of the VRExperienceHelper class
     * @returns "VRExperienceHelper"
     */
    getClassName(): string;
}
