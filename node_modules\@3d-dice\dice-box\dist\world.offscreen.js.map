{"version": 3, "file": "world.offscreen.js", "sources": ["../src/components/world.offscreen.js"], "sourcesContent": ["import worldWorker from './offscreenCanvas.worker?worker&inline' // using vite.js worker import - this will be compiled away\n\nclass WorldOffScreen {\n\tinitialized = false\n\toffscreenWorkerInit = false\n\tthemeLoadedInit = false\n\tpendingThemePromises = {}\n\t#offscreenCanvas\n\t#OffscreenWorker\n\t// onInitComplete = () => {} // init callback\n\tonRollResult = () => {} // individual die callback\n\tonRollComplete = () => {} // roll group callback\n\n\tconstructor(options){\n\t\tthis.onInitComplete = options.onInitComplete\n\n\t\t// transfer control offscreen\n\t\tthis.#offscreenCanvas = options.canvas.transferControlToOffscreen()\n\n\t\t// initialize 3D World in which BabylonJS runs\n\t\tthis.#OffscreenWorker = new worldWorker()\n\t\t// need to initialize the web worker and get confirmation that initialization is complete before other scripts can run\n\t\t// set a property on the worker to a promise that is resolve when the proper message is returned from the worker\n\t\tthis.#OffscreenWorker.init = new Promise((resolve, reject) => {\n\t\t\tthis.offscreenWorkerInit = resolve\n\t\t})\n\n\t\tthis.initialized = this.#initScene(options)\n\t}\n\n\t// initialize the babylon scene\n\tasync #initScene(config) {\n\t\t// initialize the offscreen worker\n\t\tthis.#OffscreenWorker.postMessage({\n\t\t\taction: \"init\",\n\t\t\tcanvas: this.#offscreenCanvas,\n\t\t\twidth: config.canvas.clientWidth,\n\t\t\theight: config.canvas.clientHeight,\n\t\t\toptions: config.options,\n\t\t}, [this.#offscreenCanvas])\n\n\t\t// handle messages from offscreen BabylonJS World\n\t\tthis.#OffscreenWorker.onmessage = (e) => {\n\t\t\tswitch( e.data.action ) {\n\t\t\t\tcase \"init-complete\":\n\t\t\t\t\tthis.offscreenWorkerInit() //fulfill promise so other things can run\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"connect-complete\":\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"theme-loaded\":\n\t\t\t\t\tif(e.data.id){\n\t\t\t\t\t\tthis.pendingThemePromises[e.data.id](e.data.id)\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'roll-result':\n\t\t\t\t\tthis.onRollResult(e.data.die)\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'roll-complete':\n\t\t\t\t\tthis.onRollComplete()\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'die-removed':\n\t\t\t\t\tthis.onDieRemoved(e.data.rollId)\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\t// await Promise.all([this.#OffscreenWorker.init])\n\t\tawait this.#OffscreenWorker.init\n\n\t\tthis.onInitComplete(true)\n\n\t\treturn true\n\t}\n\n\tconnect(port){\n\t\t// Setup the connection: Port 1 is for this.#OffscreenWorker\n\t\tthis.#OffscreenWorker.postMessage({\n\t\t\taction : \"connect\",\n\t\t\tport\n\t\t},[ port ])\n\t}\n\n\tupdateConfig(options){\n\t\tthis.#OffscreenWorker.postMessage({action: \"updateConfig\", options});\n\t}\n\n\tresize(options){\n\t\tthis.#OffscreenWorker.postMessage({action: \"resize\", options});\n\t}\n\n\tasync loadTheme(options) {\n\t\t// prevent multiple requests of the same theme\n\t\treturn new Promise((resolve, reject) => {\n\t\t\tif(Object.keys(this.pendingThemePromises).includes(options.theme)) {\n\t\t\t\treturn resolve()\n\t\t\t}\n\n\t\t\tthis.pendingThemePromises[options.theme] = resolve\n\t\t\tthis.#OffscreenWorker.postMessage({action: \"loadTheme\", options})\n\t\t}).catch(error => console.error(error))\n\t}\n\n\tclear(){\n\t\tthis.#OffscreenWorker.postMessage({action: \"clearDice\"})\n\t}\n\n\tadd(options){\n\t\tthis.#OffscreenWorker.postMessage({action: \"addDie\", options})\n\t}\n\t\n\taddNonDie(options){\n\t\tthis.#OffscreenWorker.postMessage({action: \"addNonDie\", options})\n\t}\n\n\tremove(options){\n\t\t// remove the die from the render\n\t\tthis.#OffscreenWorker.postMessage({action: \"removeDie\", options})\n\t}\n}\n\nexport default WorldOffScreen"], "names": ["WorldOffScreen", "options", "__privateAdd", "_initScene", "__publicField", "_offscreenCanvas", "_OffscreenWorker", "__privateSet", "worldWorker", "__privateGet", "resolve", "reject", "__privateMethod", "initScene_fn", "port", "error", "config", "e"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAMA,EAAe;AAAA;AAAA,EAWpB,YAAYC,GAAQ;AAkBpB;AAAA,IAAAC,EAAA,MAAMC;AA5BN,IAAAC,EAAA,qBAAc;AACd,IAAAA,EAAA,6BAAsB;AACtB,IAAAA,EAAA,yBAAkB;AAClB,IAAAA,EAAA,8BAAuB,CAAE;AACzB,IAAAF,EAAA,MAAAG,GAAA;AACA,IAAAH,EAAA,MAAAI,GAAA;AAEA;AAAA,IAAAF,EAAA,sBAAe,MAAM;AAAA,IAAE;AACvB;AAAA,IAAAA,EAAA,wBAAiB,MAAM;AAAA,IAAE;AAGxB,SAAK,iBAAiBH,EAAQ,gBAG9BM,EAAA,MAAKF,GAAmBJ,EAAQ,OAAO,2BAA4B,IAGnEM,EAAA,MAAKD,GAAmB,IAAIE,EAAa,IAGzCC,EAAA,MAAKH,GAAiB,OAAO,IAAI,QAAQ,CAACI,GAASC,MAAW;AAC7D,WAAK,sBAAsBD;AAAA,IAC9B,CAAG,GAED,KAAK,cAAcE,EAAA,MAAKT,GAAAU,GAAL,WAAgBZ;AAAA,EACnC;AAAA,EA6CD,QAAQa,GAAK;AAEZ,IAAAL,EAAA,MAAKH,GAAiB,YAAY;AAAA,MACjC,QAAS;AAAA,MACT,MAAAQ;AAAA,IACH,GAAI,CAAEA,CAAI,CAAE;AAAA,EACV;AAAA,EAED,aAAab,GAAQ;AACpB,IAAAQ,EAAA,MAAKH,GAAiB,YAAY,EAAC,QAAQ,gBAAgB,SAAAL,EAAO,CAAC;AAAA,EACnE;AAAA,EAED,OAAOA,GAAQ;AACd,IAAAQ,EAAA,MAAKH,GAAiB,YAAY,EAAC,QAAQ,UAAU,SAAAL,EAAO,CAAC;AAAA,EAC7D;AAAA,EAED,MAAM,UAAUA,GAAS;AAExB,WAAO,IAAI,QAAQ,CAACS,GAASC,MAAW;AACvC,UAAG,OAAO,KAAK,KAAK,oBAAoB,EAAE,SAASV,EAAQ,KAAK;AAC/D,eAAOS,EAAS;AAGjB,WAAK,qBAAqBT,EAAQ,KAAK,IAAIS,GAC3CD,EAAA,MAAKH,GAAiB,YAAY,EAAC,QAAQ,aAAa,SAAAL,EAAO,CAAC;AAAA,IACnE,CAAG,EAAE,MAAM,CAAAc,MAAS,QAAQ,MAAMA,CAAK,CAAC;AAAA,EACtC;AAAA,EAED,QAAO;AACN,IAAAN,EAAA,MAAKH,GAAiB,YAAY,EAAC,QAAQ,YAAW,CAAC;AAAA,EACvD;AAAA,EAED,IAAIL,GAAQ;AACX,IAAAQ,EAAA,MAAKH,GAAiB,YAAY,EAAC,QAAQ,UAAU,SAAAL,EAAO,CAAC;AAAA,EAC7D;AAAA,EAED,UAAUA,GAAQ;AACjB,IAAAQ,EAAA,MAAKH,GAAiB,YAAY,EAAC,QAAQ,aAAa,SAAAL,EAAO,CAAC;AAAA,EAChE;AAAA,EAED,OAAOA,GAAQ;AAEd,IAAAQ,EAAA,MAAKH,GAAiB,YAAY,EAAC,QAAQ,aAAa,SAAAL,EAAO,CAAC;AAAA,EAChE;AACF;AA9GCI,IAAA,eACAC,IAAA,eAuBMH,IAAA,eAAAU,IAAU,eAACG,GAAQ;AAExB,SAAAP,EAAA,MAAKH,GAAiB,YAAY;AAAA,IACjC,QAAQ;AAAA,IACR,QAAQG,EAAA,MAAKJ;AAAA,IACb,OAAOW,EAAO,OAAO;AAAA,IACrB,QAAQA,EAAO,OAAO;AAAA,IACtB,SAASA,EAAO;AAAA,EACnB,GAAK,CAACP,EAAA,MAAKJ,EAAgB,CAAC,GAG1BI,EAAA,MAAKH,GAAiB,YAAY,CAACW,MAAM;AACxC,YAAQA,EAAE,KAAK,QAAM;AAAA,MACpB,KAAK;AACJ,aAAK,oBAAqB;AAC1B;AAAA,MACD,KAAK;AACJ;AAAA,MACD,KAAK;AACJ,QAAGA,EAAE,KAAK,MACT,KAAK,qBAAqBA,EAAE,KAAK,EAAE,EAAEA,EAAE,KAAK,EAAE;AAE/C;AAAA,MACD,KAAK;AACJ,aAAK,aAAaA,EAAE,KAAK,GAAG;AAC5B;AAAA,MACD,KAAK;AACJ,aAAK,eAAgB;AACrB;AAAA,MACD,KAAK;AACJ,aAAK,aAAaA,EAAE,KAAK,MAAM;AAC/B;AAAA,IACD;AAAA,EACD,GAED,MAAMR,EAAA,MAAKH,GAAiB,MAE5B,KAAK,eAAe,EAAI,GAEjB;AACP;"}