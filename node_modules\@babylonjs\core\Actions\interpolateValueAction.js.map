{"version": 3, "file": "interpolateValueAction.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Actions/interpolateValueAction.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAGlC,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AACnE,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAElD;;;;GAIG;AACH,MAAM,OAAO,sBAAuB,SAAQ,MAAM;IAmC9C;;;;;;;;;;OAUG;IACH,YACI,cAAmB,EACnB,MAAW,EACX,YAAoB,EACpB,KAAU,EACV,WAAmB,IAAI,EACvB,SAAqB,EACrB,mBAA6B,EAC7B,mBAAgC;QAEhC,KAAK,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;QA7CrC;;WAEG;QACI,aAAQ,GAAW,IAAI,CAAC;QAY/B;;WAEG;QACI,kCAA6B,GAAG,IAAI,UAAU,EAA0B,CAAC;QA6B5E,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAC/C,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAC/C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;IAClD,CAAC;IAED,gBAAgB;IACT,QAAQ;QACX,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAC3F,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,OAAO;QACV,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;QAC7C,MAAM,IAAI,GAAG;YACT;gBACI,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC;aAC/C;YACD;gBACI,KAAK,EAAE,GAAG;gBACV,KAAK,EAAE,IAAI,CAAC,KAAK;aACpB;SACJ,CAAC;QAEF,IAAI,QAAgB,CAAC;QAErB,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;YAChC,QAAQ,GAAG,SAAS,CAAC,mBAAmB,CAAC;SAC5C;aAAM,IAAI,IAAI,CAAC,KAAK,YAAY,MAAM,EAAE;YACrC,QAAQ,GAAG,SAAS,CAAC,oBAAoB,CAAC;SAC7C;aAAM,IAAI,IAAI,CAAC,KAAK,YAAY,OAAO,EAAE;YACtC,QAAQ,GAAG,SAAS,CAAC,qBAAqB,CAAC;SAC9C;aAAM,IAAI,IAAI,CAAC,KAAK,YAAY,MAAM,EAAE;YACrC,QAAQ,GAAG,SAAS,CAAC,oBAAoB,CAAC;SAC7C;aAAM,IAAI,IAAI,CAAC,KAAK,YAAY,UAAU,EAAE;YACzC,QAAQ,GAAG,SAAS,CAAC,wBAAwB,CAAC;SACjD;aAAM;YACH,MAAM,CAAC,IAAI,CAAC,4CAA4C,GAAG,OAAO,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;YACpF,OAAO;SACV;QAED,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC,wBAAwB,EAAE,IAAI,CAAC,SAAS,EAAE,GAAG,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,QAAQ,EAAE,SAAS,CAAC,0BAA0B,CAAC,CAAC;QAE1J,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAExB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC9C;QAED,MAAM,OAAO,GAAG,GAAG,EAAE;YACjB,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACzD,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,IAAI,CAAC,mBAAmB,EAAE,CAAC;aAC9B;QACL,CAAC,CAAC;QAEF,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IAC9F,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,MAAW;QACxB,OAAO,KAAK,CAAC,UAAU,CACnB;YACI,IAAI,EAAE,wBAAwB;YAC9B,UAAU,EAAE;gBACR,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC;gBACvC,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE;gBAClD,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBACpE,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAC1E,EAAE,IAAI,EAAE,qBAAqB,EAAE,KAAK,EAAE,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,KAAK,EAAE;aAC5G;SACJ,EACD,MAAM,CACT,CAAC;IACN,CAAC;CACJ;AAED,aAAa,CAAC,gCAAgC,EAAE,sBAAsB,CAAC,CAAC", "sourcesContent": ["import { Action } from \"./action\";\r\nimport type { Condition } from \"./condition\";\r\n\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport { Vector3, Matrix, Quaternion } from \"../Maths/math.vector\";\r\nimport { Animation } from \"../Animations/animation\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\n\r\n/**\r\n * This defines an action responsible to change the value of a property\r\n * by interpolating between its current value and the newly set one once triggered.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions\r\n */\r\nexport class InterpolateValueAction extends Action {\r\n    /**\r\n     * Defines the path of the property where the value should be interpolated\r\n     */\r\n    public propertyPath: string;\r\n\r\n    /**\r\n     * Defines the target value at the end of the interpolation.\r\n     */\r\n    public value: any;\r\n\r\n    /**\r\n     * Defines the time it will take for the property to interpolate to the value.\r\n     */\r\n    public duration: number = 1000;\r\n\r\n    /**\r\n     * Defines if the other scene animations should be stopped when the action has been triggered\r\n     */\r\n    public stopOtherAnimations?: boolean;\r\n\r\n    /**\r\n     * Defines a callback raised once the interpolation animation has been done.\r\n     */\r\n    public onInterpolationDone?: () => void;\r\n\r\n    /**\r\n     * Observable triggered once the interpolation animation has been done.\r\n     */\r\n    public onInterpolationDoneObservable = new Observable<InterpolateValueAction>();\r\n\r\n    private _target: any;\r\n    private _effectiveTarget: any;\r\n    private _property: string;\r\n\r\n    /**\r\n     * Instantiate the action\r\n     * @param triggerOptions defines the trigger options\r\n     * @param target defines the object containing the value to interpolate\r\n     * @param propertyPath defines the path to the property in the target object\r\n     * @param value defines the target value at the end of the interpolation\r\n     * @param duration defines the time it will take for the property to interpolate to the value.\r\n     * @param condition defines the trigger related conditions\r\n     * @param stopOtherAnimations defines if the other scene animations should be stopped when the action has been triggered\r\n     * @param onInterpolationDone defines a callback raised once the interpolation animation has been done\r\n     */\r\n    constructor(\r\n        triggerOptions: any,\r\n        target: any,\r\n        propertyPath: string,\r\n        value: any,\r\n        duration: number = 1000,\r\n        condition?: Condition,\r\n        stopOtherAnimations?: boolean,\r\n        onInterpolationDone?: () => void\r\n    ) {\r\n        super(triggerOptions, condition);\r\n\r\n        this.propertyPath = propertyPath;\r\n        this.value = value;\r\n        this.duration = duration;\r\n        this.stopOtherAnimations = stopOtherAnimations;\r\n        this.onInterpolationDone = onInterpolationDone;\r\n        this._target = this._effectiveTarget = target;\r\n    }\r\n\r\n    /** @internal */\r\n    public _prepare(): void {\r\n        this._effectiveTarget = this._getEffectiveTarget(this._effectiveTarget, this.propertyPath);\r\n        this._property = this._getProperty(this.propertyPath);\r\n    }\r\n\r\n    /**\r\n     * Execute the action starts the value interpolation.\r\n     */\r\n    public execute(): void {\r\n        const scene = this._actionManager.getScene();\r\n        const keys = [\r\n            {\r\n                frame: 0,\r\n                value: this._effectiveTarget[this._property],\r\n            },\r\n            {\r\n                frame: 100,\r\n                value: this.value,\r\n            },\r\n        ];\r\n\r\n        let dataType: number;\r\n\r\n        if (typeof this.value === \"number\") {\r\n            dataType = Animation.ANIMATIONTYPE_FLOAT;\r\n        } else if (this.value instanceof Color3) {\r\n            dataType = Animation.ANIMATIONTYPE_COLOR3;\r\n        } else if (this.value instanceof Vector3) {\r\n            dataType = Animation.ANIMATIONTYPE_VECTOR3;\r\n        } else if (this.value instanceof Matrix) {\r\n            dataType = Animation.ANIMATIONTYPE_MATRIX;\r\n        } else if (this.value instanceof Quaternion) {\r\n            dataType = Animation.ANIMATIONTYPE_QUATERNION;\r\n        } else {\r\n            Logger.Warn(\"InterpolateValueAction: Unsupported type (\" + typeof this.value + \")\");\r\n            return;\r\n        }\r\n\r\n        const animation = new Animation(\"InterpolateValueAction\", this._property, 100 * (1000.0 / this.duration), dataType, Animation.ANIMATIONLOOPMODE_CONSTANT);\r\n\r\n        animation.setKeys(keys);\r\n\r\n        if (this.stopOtherAnimations) {\r\n            scene.stopAnimation(this._effectiveTarget);\r\n        }\r\n\r\n        const wrapper = () => {\r\n            this.onInterpolationDoneObservable.notifyObservers(this);\r\n            if (this.onInterpolationDone) {\r\n                this.onInterpolationDone();\r\n            }\r\n        };\r\n\r\n        scene.beginDirectAnimation(this._effectiveTarget, [animation], 0, 100, false, 1, wrapper);\r\n    }\r\n\r\n    /**\r\n     * Serializes the actions and its related information.\r\n     * @param parent defines the object to serialize in\r\n     * @returns the serialized object\r\n     */\r\n    public serialize(parent: any): any {\r\n        return super._serialize(\r\n            {\r\n                name: \"InterpolateValueAction\",\r\n                properties: [\r\n                    Action._GetTargetProperty(this._target),\r\n                    { name: \"propertyPath\", value: this.propertyPath },\r\n                    { name: \"value\", value: Action._SerializeValueAsString(this.value) },\r\n                    { name: \"duration\", value: Action._SerializeValueAsString(this.duration) },\r\n                    { name: \"stopOtherAnimations\", value: Action._SerializeValueAsString(this.stopOtherAnimations) || false },\r\n                ],\r\n            },\r\n            parent\r\n        );\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.InterpolateValueAction\", InterpolateValueAction);\r\n"]}