"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.waitForLegacyNotarize = exports.startLegacyNotarize = void 0;
const debug_1 = __importDefault(require("debug"));
const d = (0, debug_1.default)('electron-notarize:legacy');
/** @deprecated */
function startLegacyNotarize(opts) {
    return __awaiter(this, void 0, void 0, function* () {
        d('starting notarize process for app:', opts.appPath);
        throw new Error('Cannot start notarization. Legacy notarization (altool) is no longer available');
    });
}
exports.startLegacyNotarize = startLegacyNotarize;
/** @deprecated */
function waitForLegacyNotarize(opts) {
    return __awaiter(this, void 0, void 0, function* () {
        throw new Error('Cannot wait for notarization. Legacy notarization (altool) is no longer available');
    });
}
exports.waitForLegacyNotarize = waitForLegacyNotarize;
//# sourceMappingURL=legacy.js.map