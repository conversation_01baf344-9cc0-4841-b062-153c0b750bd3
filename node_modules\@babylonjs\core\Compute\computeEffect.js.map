{"version": 3, "file": "computeEffect.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Compute/computeEffect.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC/E,OAAO,EAAE,eAAe,EAAE,MAAM,uCAAuC,CAAC;AAExE,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,cAAc,EAAE,MAAM,6BAA6B,CAAC;AA8B7D;;GAEG;AACH,MAAM,OAAO,aAAa;IAgEtB;;;;;;OAMG;IACH,YAAY,QAAa,EAAE,OAAsC,EAAE,MAAc,EAAE,GAAG,GAAG,EAAE;;QAhE3F;;WAEG;QACI,SAAI,GAAQ,IAAI,CAAC;QACxB;;WAEG;QACI,YAAO,GAAW,EAAE,CAAC;QAC5B;;WAEG;QACI,eAAU,GAA8C,IAAI,CAAC;QACpE;;WAEG;QACI,YAAO,GAA8D,IAAI,CAAC;QACjF;;WAEG;QACI,aAAQ,GAAG,CAAC,CAAC;QACpB;;;WAGG;QACI,wBAAmB,GAAG,IAAI,UAAU,EAAiB,CAAC;QAC7D;;WAEG;QACI,sBAAiB,GAAG,IAAI,UAAU,EAAiB,CAAC;QAC3D;;WAEG;QACI,qBAAgB,GAAG,IAAI,UAAU,EAAiB,CAAC;QAE1D;;;WAGG;QACI,wBAAmB,GAAG,KAAK,CAAC;QAG3B,aAAQ,GAAG,KAAK,CAAC;QACjB,sBAAiB,GAAG,EAAE,CAAC;QAC/B,gBAAgB;QACT,SAAI,GAAW,EAAE,CAAC;QACjB,+BAA0B,GAAW,EAAE,CAAC;QAChD,gBAAgB;QACT,qBAAgB,GAAsC,IAAI,CAAC;QAClE,gBAAgB;QACT,uBAAkB,GAAW,EAAE,CAAC;QAC/B,0BAAqB,GAAW,EAAE,CAAC;QAEnC,oBAAe,GAAG,cAAc,CAAC,IAAI,CAAC;QAa1C,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAEhB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC,aAAa,EAAE,CAAC;QAE9C,IAAI,CAAC,OAAO,GAAG,MAAA,OAAO,CAAC,OAAO,mCAAI,EAAE,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACrC,IAAI,CAAC,WAAW,GAAG,MAAA,OAAO,CAAC,UAAU,mCAAI,MAAM,CAAC;QAEhD,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtE,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAChF,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC,uBAAuB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAErF,IAAI,aAAkB,CAAC;QAEvB,MAAM,YAAY,GAAG,mBAAmB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAEnF,IAAI,QAAQ,CAAC,aAAa,EAAE;YACxB,aAAa,GAAG,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC;SACtD;aAAM,IAAI,QAAQ,CAAC,cAAc,EAAE;YAChC,aAAa,GAAG,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAE3F,IAAI,CAAC,aAAa,EAAE;gBAChB,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC;aAC3C;SACJ;aAAM;YACH,aAAa,GAAG,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC;SAChD;QAED,MAAM,gBAAgB,GAAsB;YACxC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;YACjC,eAAe,EAAE,SAAS;YAC1B,UAAU,EAAE,KAAK;YACjB,4BAA4B,EAAE,KAAK;YACnC,SAAS,EAAE,IAAI;YACf,sBAAsB,EAAE,IAAI,CAAC,OAAO,CAAC,sBAAsB;YAC3D,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,oBAAoB,EAAE,IAAI,CAAC,mBAAmB;YAC9C,OAAO,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE;YAChD,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB;YAC7C,iBAAiB,EAAE,IAAI;YACvB,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;YAC7C,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,qBAAqB;SAC5D,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,WAAW,EAAE,EAAE;YAC3D,eAAe,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;YAC7C,eAAe,CAAC,UAAU,CACtB,WAAW,EACX,gBAAgB,EAChB,CAAC,oBAAoB,EAAE,EAAE;gBACrB,IAAI,CAAC,qBAAqB,GAAG,WAAW,CAAC;gBACzC,IAAI,OAAO,CAAC,gBAAgB,EAAE;oBAC1B,oBAAoB,GAAG,OAAO,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,CAAC;iBACzE;gBACD,MAAM,YAAY,GAAG,eAAe,CAAC,QAAQ,CAAC,oBAAoB,EAAE,EAAE,EAAE,gBAAgB,CAAC,CAAC;gBAC1F,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC1D,CAAC,EACD,IAAI,CAAC,OAAO,CACf,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,aAAa,CAAC,oBAA4B,EAAE,QAAa;QAC7D,IAAI,QAAQ,EAAE;YACV,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC;YAEhG,IAAI,CAAC,kBAAkB,GAAG,gCAAgC,GAAG,OAAO,GAAG,IAAI,GAAG,oBAAoB,CAAC;SACtG;aAAM;YACH,IAAI,CAAC,kBAAkB,GAAG,oBAAoB,CAAC;SAClD;QACD,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,IAAI;YACA,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAClC;QAAC,WAAM;YACJ,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAEO,gBAAgB;QACpB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO,IAAI,CAAC;SACf;QACD,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;SACxC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;;OAGG;IACI,mBAAmB;QACtB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,mBAAmB,CAAC,IAAqC;QAC5D,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;YAChB,IAAI,CAAC,IAAI,CAAC,CAAC;YACX,OAAO;SACV;QAED,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YACpC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE;YACzD,UAAU,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAC7B,CAAC,EAAE,EAAE,CAAC,CAAC;SACV;IACL,CAAC;IAEO,aAAa,CAAC,uBAA0D;QAC5E,IAAI;YACA,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE;gBACzB,OAAO;aACV;SACJ;QAAC,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,yBAAyB,CAAC,CAAC,EAAE,uBAAuB,CAAC,CAAC;YAC3D,OAAO;SACV;QAED,UAAU,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC;QAChD,CAAC,EAAE,EAAE,CAAC,CAAC;IACX,CAAC;IAEO,WAAW,CAAC,MAAW,EAAE,GAAW,EAAE,WAAmB,EAAE,QAA6B;QAC5F,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;YACpC,gBAAgB;YAChB,IAAI,MAAM,YAAY,WAAW,EAAE;gBAC/B,MAAM,UAAU,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;gBAC7C,QAAQ,CAAC,UAAU,CAAC,CAAC;gBACrB,OAAO;aACV;SACJ;QAED,kBAAkB;QAClB,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,SAAS,EAAE;YACnC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,OAAO;SACV;QAED,mBAAmB;QACnB,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,SAAS,EAAE;YACnC,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,QAAQ,CAAC,YAAY,CAAC,CAAC;YACvB,OAAO;SACV;QAED,sBAAsB;QACtB,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC,EAAE;YAC5C,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC;YACrD,OAAO;SACV;QAED,IAAI,WAAW,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,WAAW,GAAG,QAAQ,CAAC,EAAE;YACnE,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC;YAC7D,OAAO;SACV;QAED,IAAI,SAAS,CAAC;QAEd,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;YACvE,SAAS,GAAG,MAAM,CAAC;SACtB;aAAM;YACH,SAAS,GAAG,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC;SAC/C;QAED,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,GAAG,GAAG,GAAG,GAAG,CAAC,WAAW,EAAE,GAAG,KAAK,EAAE,QAAQ,CAAC,CAAC;IAClF,CAAC;IAED;;OAEG;IACH,IAAW,iBAAiB;;QACxB,OAAO,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,MAAA,MAAA,IAAI,CAAC,gBAAgB,0CAAE,qBAAqB,EAAE,mCAAI,IAAI,CAAC,kBAAkB,CAAC;IACzJ,CAAC;IAED;;OAEG;IACH,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAE7B,MAAM,uBAAuB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAEtD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEtB,IAAI;YACA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;YAE5B,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,4BAA4B,EAAE,CAAC;YAC9D,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;YAExC,MAAM,CAAC,8BAA8B,CACjC,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,EAC3F,IAAI,CAAC,qBAAqB,EAC1B,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,EAChD,IAAI,CAAC,WAAW,CACnB,CAAC;YAEF,MAAM,CAAC,kCAAkC,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE;gBAClE,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;gBAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,IAAI,CAAC,UAAU,EAAE;oBACjB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;iBACzB;gBACD,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAC/C,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;gBAEjC,IAAI,uBAAuB,EAAE;oBACzB,IAAI,CAAC,SAAS,EAAE,CAAC,6BAA6B,CAAC,uBAAuB,CAAC,CAAC;iBAC3E;YACL,CAAC,CAAC,CAAC;YAEH,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE;gBAC/B,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC;aAC/C;SACJ;QAAC,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,yBAAyB,CAAC,CAAC,EAAE,uBAAuB,CAAC,CAAC;SAC9D;IACL,CAAC;IAEO,0BAA0B,CAAC,IAAsB,EAAE,KAAuB;QAC9E,MAAM,MAAM,GAAG,iCAAiC,CAAC;QAEjD,IAAI,SAAS,GAAG,IAAI,CAAC;QAErB,IAAI,KAAK,IAAI,IAAI,EAAE;YACf,MAAM,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAChC,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;gBACzB,MAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;gBACnC,IAAI,KAAK,CAAC,MAAM,IAAI,UAAU,EAAE;oBAC5B,SAAS,GAAG,mBAAmB,UAAU,sBAAsB,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC;iBAC1F;aACJ;SACJ;QAED,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC7B,CAAC;IAEO,yBAAyB,CAAC,CAAM,EAAE,0BAA6D,IAAI;;QACvG,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,OAAO,CAAC;QAEnC,kCAAkC;QAClC,MAAM,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;QAClD,MAAM,CAAC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5C,IAAI,aAAa,CAAC,+BAA+B,EAAE;YAC/C,IAAI,eAAe,GAAG,IAAI,EACtB,IAAI,GAAG,IAAI,CAAC;YAChB,IAAI,MAAA,IAAI,CAAC,gBAAgB,0CAAE,qBAAqB,EAAE,EAAE;gBAChD,CAAC,IAAI,EAAE,eAAe,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBACjI,IAAI,IAAI,EAAE;oBACN,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;oBAC9B,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;iBACtB;aACJ;YACD,IAAI,eAAe,EAAE;gBACjB,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;aACjC;SACJ;QACD,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACjD,IAAI,uBAAuB,EAAE;YACzB,IAAI,CAAC,gBAAgB,GAAG,uBAAuB,CAAC;YAChD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,IAAI,CAAC,OAAO,EAAE;gBACd,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;aAC9C;YACD,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SAChD;IACL,CAAC;IAED;;QAEI;IACG,OAAO;QACV,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;SACnC;QACD,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,cAAc,CAAC,IAAY,EAAE,aAAqB;QAC5D,WAAW,CAAC,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,eAAe,CAAC,GAAG,aAAa,CAAC;IAC7F,CAAC;;AAzZc,2BAAa,GAAG,CAAC,CAAC;AAEjC;;GAEG;AACW,6CAA+B,GAAG,IAAI,CAAC", "sourcesContent": ["import { Logger } from \"../Misc/logger\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { IComputePipelineContext } from \"./IComputePipelineContext\";\r\nimport { GetDOMTextContent, IsWindowObjectExist } from \"../Misc/domManagement\";\r\nimport { ShaderProcessor } from \"../Engines/Processors/shaderProcessor\";\r\nimport type { ProcessingOptions } from \"../Engines/Processors/shaderProcessingOptions\";\r\nimport { ShaderStore } from \"../Engines/shaderStore\";\r\nimport { ShaderLanguage } from \"../Materials/shaderLanguage\";\r\n\r\ndeclare type Engine = import(\"../Engines/engine\").Engine;\r\n\r\n/**\r\n * Options to be used when creating a compute effect.\r\n */\r\nexport interface IComputeEffectCreationOptions {\r\n    /**\r\n     * Define statements that will be set in the shader.\r\n     */\r\n    defines: any;\r\n    /**\r\n     * The name of the entry point in the shader source (default: \"main\")\r\n     */\r\n    entryPoint?: string;\r\n    /**\r\n     * Callback that will be called when the shader is compiled.\r\n     */\r\n    onCompiled: Nullable<(effect: ComputeEffect) => void>;\r\n    /**\r\n     * Callback that will be called if an error occurs during shader compilation.\r\n     */\r\n    onError: Nullable<(effect: ComputeEffect, errors: string) => void>;\r\n    /**\r\n     * If provided, will be called with the shader code so that this code can be updated before it is compiled by the GPU\r\n     */\r\n    processFinalCode?: Nullable<(code: string) => string>;\r\n}\r\n\r\n/**\r\n * Effect wrapping a compute shader and let execute (dispatch) the shader\r\n */\r\nexport class ComputeEffect {\r\n    private static _UniqueIdSeed = 0;\r\n\r\n    /**\r\n     * Enable logging of the shader code when a compilation error occurs\r\n     */\r\n    public static LogShaderCodeOnCompilationError = true;\r\n    /**\r\n     * Name of the effect.\r\n     */\r\n    public name: any = null;\r\n    /**\r\n     * String container all the define statements that should be set on the shader.\r\n     */\r\n    public defines: string = \"\";\r\n    /**\r\n     * Callback that will be called when the shader is compiled.\r\n     */\r\n    public onCompiled: Nullable<(effect: ComputeEffect) => void> = null;\r\n    /**\r\n     * Callback that will be called if an error occurs during shader compilation.\r\n     */\r\n    public onError: Nullable<(effect: ComputeEffect, errors: string) => void> = null;\r\n    /**\r\n     * Unique ID of the effect.\r\n     */\r\n    public uniqueId = 0;\r\n    /**\r\n     * Observable that will be called when the shader is compiled.\r\n     * It is recommended to use executeWhenCompile() or to make sure that scene.isReady() is called to get this observable raised.\r\n     */\r\n    public onCompileObservable = new Observable<ComputeEffect>();\r\n    /**\r\n     * Observable that will be called if an error occurs during shader compilation.\r\n     */\r\n    public onErrorObservable = new Observable<ComputeEffect>();\r\n    /**\r\n     * Observable that will be called when effect is bound.\r\n     */\r\n    public onBindObservable = new Observable<ComputeEffect>();\r\n\r\n    /**\r\n     * @internal\r\n     * Specifies if the effect was previously ready\r\n     */\r\n    public _wasPreviouslyReady = false;\r\n\r\n    private _engine: Engine;\r\n    private _isReady = false;\r\n    private _compilationError = \"\";\r\n    /** @internal */\r\n    public _key: string = \"\";\r\n    private _computeSourceCodeOverride: string = \"\";\r\n    /** @internal */\r\n    public _pipelineContext: Nullable<IComputePipelineContext> = null;\r\n    /** @internal */\r\n    public _computeSourceCode: string = \"\";\r\n    private _rawComputeSourceCode: string = \"\";\r\n    private _entryPoint: string;\r\n    private _shaderLanguage = ShaderLanguage.WGSL;\r\n    private _shaderStore: { [key: string]: string };\r\n    private _shaderRepository: string;\r\n    private _includeShaderStore: { [key: string]: string };\r\n\r\n    /**\r\n     * Creates a compute effect that can be used to execute a compute shader\r\n     * @param baseName Name of the effect\r\n     * @param options Set of all options to create the effect\r\n     * @param engine The engine the effect is created for\r\n     * @param key Effect Key identifying uniquely compiled shader variants\r\n     */\r\n    constructor(baseName: any, options: IComputeEffectCreationOptions, engine: Engine, key = \"\") {\r\n        this.name = baseName;\r\n        this._key = key;\r\n\r\n        this._engine = engine;\r\n        this.uniqueId = ComputeEffect._UniqueIdSeed++;\r\n\r\n        this.defines = options.defines ?? \"\";\r\n        this.onError = options.onError;\r\n        this.onCompiled = options.onCompiled;\r\n        this._entryPoint = options.entryPoint ?? \"main\";\r\n\r\n        this._shaderStore = ShaderStore.GetShadersStore(this._shaderLanguage);\r\n        this._shaderRepository = ShaderStore.GetShadersRepository(this._shaderLanguage);\r\n        this._includeShaderStore = ShaderStore.GetIncludesShadersStore(this._shaderLanguage);\r\n\r\n        let computeSource: any;\r\n\r\n        const hostDocument = IsWindowObjectExist() ? this._engine.getHostDocument() : null;\r\n\r\n        if (baseName.computeSource) {\r\n            computeSource = \"source:\" + baseName.computeSource;\r\n        } else if (baseName.computeElement) {\r\n            computeSource = hostDocument ? hostDocument.getElementById(baseName.computeElement) : null;\r\n\r\n            if (!computeSource) {\r\n                computeSource = baseName.computeElement;\r\n            }\r\n        } else {\r\n            computeSource = baseName.compute || baseName;\r\n        }\r\n\r\n        const processorOptions: ProcessingOptions = {\r\n            defines: this.defines.split(\"\\n\"),\r\n            indexParameters: undefined,\r\n            isFragment: false,\r\n            shouldUseHighPrecisionShader: false,\r\n            processor: null,\r\n            supportsUniformBuffers: this._engine.supportsUniformBuffers,\r\n            shadersRepository: this._shaderRepository,\r\n            includesShadersStore: this._includeShaderStore,\r\n            version: (this._engine.version * 100).toString(),\r\n            platformName: this._engine.shaderPlatformName,\r\n            processingContext: null,\r\n            isNDCHalfZRange: this._engine.isNDCHalfZRange,\r\n            useReverseDepthBuffer: this._engine.useReverseDepthBuffer,\r\n        };\r\n\r\n        this._loadShader(computeSource, \"Compute\", \"\", (computeCode) => {\r\n            ShaderProcessor.Initialize(processorOptions);\r\n            ShaderProcessor.PreProcess(\r\n                computeCode,\r\n                processorOptions,\r\n                (migratedCommputeCode) => {\r\n                    this._rawComputeSourceCode = computeCode;\r\n                    if (options.processFinalCode) {\r\n                        migratedCommputeCode = options.processFinalCode(migratedCommputeCode);\r\n                    }\r\n                    const finalShaders = ShaderProcessor.Finalize(migratedCommputeCode, \"\", processorOptions);\r\n                    this._useFinalCode(finalShaders.vertexCode, baseName);\r\n                },\r\n                this._engine\r\n            );\r\n        });\r\n    }\r\n\r\n    private _useFinalCode(migratedCommputeCode: string, baseName: any) {\r\n        if (baseName) {\r\n            const compute = baseName.computeElement || baseName.compute || baseName.spectorName || baseName;\r\n\r\n            this._computeSourceCode = \"//#define SHADER_NAME compute:\" + compute + \"\\n\" + migratedCommputeCode;\r\n        } else {\r\n            this._computeSourceCode = migratedCommputeCode;\r\n        }\r\n        this._prepareEffect();\r\n    }\r\n\r\n    /**\r\n     * Unique key for this effect\r\n     */\r\n    public get key(): string {\r\n        return this._key;\r\n    }\r\n\r\n    /**\r\n     * If the effect has been compiled and prepared.\r\n     * @returns if the effect is compiled and prepared.\r\n     */\r\n    public isReady(): boolean {\r\n        try {\r\n            return this._isReadyInternal();\r\n        } catch {\r\n            return false;\r\n        }\r\n    }\r\n\r\n    private _isReadyInternal(): boolean {\r\n        if (this._isReady) {\r\n            return true;\r\n        }\r\n        if (this._pipelineContext) {\r\n            return this._pipelineContext.isReady;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * The engine the effect was initialized with.\r\n     * @returns the engine.\r\n     */\r\n    public getEngine(): Engine {\r\n        return this._engine;\r\n    }\r\n\r\n    /**\r\n     * The pipeline context for this effect\r\n     * @returns the associated pipeline context\r\n     */\r\n    public getPipelineContext(): Nullable<IComputePipelineContext> {\r\n        return this._pipelineContext;\r\n    }\r\n\r\n    /**\r\n     * The error from the last compilation.\r\n     * @returns the error string.\r\n     */\r\n    public getCompilationError(): string {\r\n        return this._compilationError;\r\n    }\r\n\r\n    /**\r\n     * Adds a callback to the onCompiled observable and call the callback immediately if already ready.\r\n     * @param func The callback to be used.\r\n     */\r\n    public executeWhenCompiled(func: (effect: ComputeEffect) => void): void {\r\n        if (this.isReady()) {\r\n            func(this);\r\n            return;\r\n        }\r\n\r\n        this.onCompileObservable.add((effect) => {\r\n            func(effect);\r\n        });\r\n\r\n        if (!this._pipelineContext || this._pipelineContext.isAsync) {\r\n            setTimeout(() => {\r\n                this._checkIsReady(null);\r\n            }, 16);\r\n        }\r\n    }\r\n\r\n    private _checkIsReady(previousPipelineContext: Nullable<IComputePipelineContext>) {\r\n        try {\r\n            if (this._isReadyInternal()) {\r\n                return;\r\n            }\r\n        } catch (e) {\r\n            this._processCompilationErrors(e, previousPipelineContext);\r\n            return;\r\n        }\r\n\r\n        setTimeout(() => {\r\n            this._checkIsReady(previousPipelineContext);\r\n        }, 16);\r\n    }\r\n\r\n    private _loadShader(shader: any, key: string, optionalKey: string, callback: (data: any) => void): void {\r\n        if (typeof HTMLElement !== \"undefined\") {\r\n            // DOM element ?\r\n            if (shader instanceof HTMLElement) {\r\n                const shaderCode = GetDOMTextContent(shader);\r\n                callback(shaderCode);\r\n                return;\r\n            }\r\n        }\r\n\r\n        // Direct source ?\r\n        if (shader.substr(0, 7) === \"source:\") {\r\n            callback(shader.substr(7));\r\n            return;\r\n        }\r\n\r\n        // Base64 encoded ?\r\n        if (shader.substr(0, 7) === \"base64:\") {\r\n            const shaderBinary = window.atob(shader.substr(7));\r\n            callback(shaderBinary);\r\n            return;\r\n        }\r\n\r\n        // Is in local store ?\r\n        if (this._shaderStore[shader + key + \"Shader\"]) {\r\n            callback(this._shaderStore[shader + key + \"Shader\"]);\r\n            return;\r\n        }\r\n\r\n        if (optionalKey && this._shaderStore[shader + optionalKey + \"Shader\"]) {\r\n            callback(this._shaderStore[shader + optionalKey + \"Shader\"]);\r\n            return;\r\n        }\r\n\r\n        let shaderUrl;\r\n\r\n        if (shader[0] === \".\" || shader[0] === \"/\" || shader.indexOf(\"http\") > -1) {\r\n            shaderUrl = shader;\r\n        } else {\r\n            shaderUrl = this._shaderRepository + shader;\r\n        }\r\n\r\n        this._engine._loadFile(shaderUrl + \".\" + key.toLowerCase() + \".fx\", callback);\r\n    }\r\n\r\n    /**\r\n     * Gets the compute shader source code of this effect\r\n     */\r\n    public get computeSourceCode(): string {\r\n        return this._computeSourceCodeOverride ? this._computeSourceCodeOverride : this._pipelineContext?._getComputeShaderCode() ?? this._computeSourceCode;\r\n    }\r\n\r\n    /**\r\n     * Gets the compute shader source code before it has been processed by the preprocessor\r\n     */\r\n    public get rawComputeSourceCode(): string {\r\n        return this._rawComputeSourceCode;\r\n    }\r\n\r\n    /**\r\n     * Prepares the effect\r\n     * @internal\r\n     */\r\n    public _prepareEffect() {\r\n        const defines = this.defines;\r\n\r\n        const previousPipelineContext = this._pipelineContext;\r\n\r\n        this._isReady = false;\r\n\r\n        try {\r\n            const engine = this._engine;\r\n\r\n            this._pipelineContext = engine.createComputePipelineContext();\r\n            this._pipelineContext._name = this._key;\r\n\r\n            engine._prepareComputePipelineContext(\r\n                this._pipelineContext,\r\n                this._computeSourceCodeOverride ? this._computeSourceCodeOverride : this._computeSourceCode,\r\n                this._rawComputeSourceCode,\r\n                this._computeSourceCodeOverride ? null : defines,\r\n                this._entryPoint\r\n            );\r\n\r\n            engine._executeWhenComputeStateIsCompiled(this._pipelineContext, () => {\r\n                this._compilationError = \"\";\r\n                this._isReady = true;\r\n                if (this.onCompiled) {\r\n                    this.onCompiled(this);\r\n                }\r\n                this.onCompileObservable.notifyObservers(this);\r\n                this.onCompileObservable.clear();\r\n\r\n                if (previousPipelineContext) {\r\n                    this.getEngine()._deleteComputePipelineContext(previousPipelineContext);\r\n                }\r\n            });\r\n\r\n            if (this._pipelineContext.isAsync) {\r\n                this._checkIsReady(previousPipelineContext);\r\n            }\r\n        } catch (e) {\r\n            this._processCompilationErrors(e, previousPipelineContext);\r\n        }\r\n    }\r\n\r\n    private _getShaderCodeAndErrorLine(code: Nullable<string>, error: Nullable<string>): [Nullable<string>, Nullable<string>] {\r\n        const regexp = /COMPUTE SHADER ERROR: 0:(\\d+?):/;\r\n\r\n        let errorLine = null;\r\n\r\n        if (error && code) {\r\n            const res = error.match(regexp);\r\n            if (res && res.length === 2) {\r\n                const lineNumber = parseInt(res[1]);\r\n                const lines = code.split(\"\\n\", -1);\r\n                if (lines.length >= lineNumber) {\r\n                    errorLine = `Offending line [${lineNumber}] in compute code: ${lines[lineNumber - 1]}`;\r\n                }\r\n            }\r\n        }\r\n\r\n        return [code, errorLine];\r\n    }\r\n\r\n    private _processCompilationErrors(e: any, previousPipelineContext: Nullable<IComputePipelineContext> = null) {\r\n        this._compilationError = e.message;\r\n\r\n        // Let's go through fallbacks then\r\n        Logger.Error(\"Unable to compile compute effect:\");\r\n        Logger.Error(\"Defines:\\r\\n\" + this.defines);\r\n        if (ComputeEffect.LogShaderCodeOnCompilationError) {\r\n            let lineErrorVertex = null,\r\n                code = null;\r\n            if (this._pipelineContext?._getComputeShaderCode()) {\r\n                [code, lineErrorVertex] = this._getShaderCodeAndErrorLine(this._pipelineContext._getComputeShaderCode(), this._compilationError);\r\n                if (code) {\r\n                    Logger.Error(\"Compute code:\");\r\n                    Logger.Error(code);\r\n                }\r\n            }\r\n            if (lineErrorVertex) {\r\n                Logger.Error(lineErrorVertex);\r\n            }\r\n        }\r\n        Logger.Error(\"Error: \" + this._compilationError);\r\n        if (previousPipelineContext) {\r\n            this._pipelineContext = previousPipelineContext;\r\n            this._isReady = true;\r\n            if (this.onError) {\r\n                this.onError(this, this._compilationError);\r\n            }\r\n            this.onErrorObservable.notifyObservers(this);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Release all associated resources.\r\n     **/\r\n    public dispose() {\r\n        if (this._pipelineContext) {\r\n            this._pipelineContext.dispose();\r\n        }\r\n        this._engine._releaseComputeEffect(this);\r\n    }\r\n\r\n    /**\r\n     * This function will add a new compute shader to the shader store\r\n     * @param name the name of the shader\r\n     * @param computeShader compute shader content\r\n     */\r\n    public static RegisterShader(name: string, computeShader: string) {\r\n        ShaderStore.GetShadersStore(ShaderLanguage.WGSL)[`${name}ComputeShader`] = computeShader;\r\n    }\r\n}\r\n"]}