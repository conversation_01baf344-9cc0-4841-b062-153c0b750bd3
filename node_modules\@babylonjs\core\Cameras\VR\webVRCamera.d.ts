import type { Nullable } from "../../types";
import { Observable } from "../../Misc/observable";
import { FreeCamera } from "../../Cameras/freeCamera";
import type { Scene } from "../../scene";
import { Quaternion, Matrix, Vector3 } from "../../Maths/math.vector";
import type { WebVRController } from "../../Gamepads/Controllers/webVRController";
import { Node } from "../../node";
import type { Ray } from "../../Culling/ray";
import "../../Engines/Extensions/engine.webVR";
/**
 * This is a copy of VRPose. See https://developer.mozilla.org/en-US/docs/Web/API/VRPose
 * IMPORTANT!! The data is right-hand data.
 * @export
 * @interface DevicePose
 */
export interface DevicePose {
    /**
     * The position of the device, values in array are [x,y,z].
     */
    readonly position: Nullable<Float32Array>;
    /**
     * The linearVelocity of the device, values in array are [x,y,z].
     */
    readonly linearVelocity: Nullable<Float32Array>;
    /**
     * The linearAcceleration of the device, values in array are [x,y,z].
     */
    readonly linearAcceleration: Nullable<Float32Array>;
    /**
     * The orientation of the device in a quaternion array, values in array are [x,y,z,w].
     */
    readonly orientation: Nullable<Float32Array>;
    /**
     * The angularVelocity of the device, values in array are [x,y,z].
     */
    readonly angularVelocity: Nullable<Float32Array>;
    /**
     * The angularAcceleration of the device, values in array are [x,y,z].
     */
    readonly angularAcceleration: Nullable<Float32Array>;
}
/**
 * Interface representing a pose controlled object in Babylon.
 * A pose controlled object has both regular pose values as well as pose values
 * from an external device such as a VR head mounted display
 */
export interface PoseControlled {
    /**
     * The position of the object in babylon space.
     */
    position: Vector3;
    /**
     * The rotation quaternion of the object in babylon space.
     */
    rotationQuaternion: Quaternion;
    /**
     * The position of the device in babylon space.
     */
    devicePosition?: Vector3;
    /**
     * The rotation quaternion of the device in babylon space.
     */
    deviceRotationQuaternion: Quaternion;
    /**
     * The raw pose coming from the device.
     */
    rawPose: Nullable<DevicePose>;
    /**
     * The scale of the device to be used when translating from device space to babylon space.
     */
    deviceScaleFactor: number;
    /**
     * Updates the poseControlled values based on the input device pose.
     * @param poseData the pose data to update the object with
     */
    updateFromDevice(poseData: DevicePose): void;
}
/**
 * Set of options to customize the webVRCamera
 */
export interface WebVROptions {
    /**
     * Sets if the webVR camera should be tracked to the vrDevice. (default: true)
     */
    trackPosition?: boolean;
    /**
     * Sets the scale of the vrDevice in babylon space. (default: 1)
     */
    positionScale?: number;
    /**
     * If there are more than one VRDisplays, this will choose the display matching this name. (default: pick first vrDisplay)
     */
    displayName?: string;
    /**
     * Should the native controller meshes be initialized. (default: true)
     */
    controllerMeshes?: boolean;
    /**
     * Creating a default HemiLight only on controllers. (default: true)
     */
    defaultLightingOnControllers?: boolean;
    /**
     * If you don't want to use the default VR button of the helper. (default: false)
     */
    useCustomVRButton?: boolean;
    /**
     * If you'd like to provide your own button to the VRHelper. (default: standard babylon vr button)
     */
    customVRButton?: HTMLButtonElement;
    /**
     * To change the length of the ray for gaze/controllers. Will be scaled by positionScale. (default: 100)
     */
    rayLength?: number;
    /**
     * To change the default offset from the ground to account for user's height in meters. Will be scaled by positionScale. (default: 1.7)
     */
    defaultHeight?: number;
    /**
     * If multiview should be used if available (default: false)
     */
    useMultiview?: boolean;
}
/**
 * This represents a WebVR camera.
 * The WebVR camera is Babylon's simple interface to interaction with Windows Mixed Reality, HTC Vive and Oculus Rift.
 * @deprecated Use WebXR instead - https://doc.babylonjs.com/features/featuresDeepDive/webXR
 * @example https://doc.babylonjs.com/features/featuresDeepDive/cameras/webVRCamera
 */
export declare class WebVRFreeCamera extends FreeCamera implements PoseControlled {
    private _webVROptions;
    /**
     * @internal
     * The vrDisplay tied to the camera. See https://developer.mozilla.org/en-US/docs/Web/API/VRDisplay
     */
    _vrDevice: any;
    /**
     * The rawPose of the vrDevice.
     */
    rawPose: Nullable<DevicePose>;
    private _onVREnabled;
    private _specsVersion;
    private _attached;
    private _frameData;
    protected _descendants: Array<Node>;
    private _deviceRoomPosition;
    /** @internal */
    _deviceRoomRotationQuaternion: Quaternion;
    private _standingMatrix;
    /**
     * Represents device position in babylon space.
     */
    devicePosition: Vector3;
    /**
     * Represents device rotation in babylon space.
     */
    deviceRotationQuaternion: Quaternion;
    /**
     * The scale of the device to be used when translating from device space to babylon space.
     */
    deviceScaleFactor: number;
    private _deviceToWorld;
    private _worldToDevice;
    /**
     * References to the webVR controllers for the vrDevice.
     */
    controllers: Array<WebVRController>;
    /**
     * Emits an event when a controller is attached.
     */
    onControllersAttachedObservable: Observable<WebVRController[]>;
    /**
     * Emits an event when a controller's mesh has been loaded;
     */
    onControllerMeshLoadedObservable: Observable<WebVRController>;
    /**
     * Emits an event when the HMD's pose has been updated.
     */
    onPoseUpdatedFromDeviceObservable: Observable<any>;
    private _poseSet;
    /**
     * If the rig cameras be used as parent instead of this camera.
     */
    rigParenting: boolean;
    private _lightOnControllers;
    private _defaultHeight?;
    /**
     * Instantiates a WebVRFreeCamera.
     * @param name The name of the WebVRFreeCamera
     * @param position The starting anchor position for the camera
     * @param scene The scene the camera belongs to
     * @param _webVROptions a set of customizable options for the webVRCamera
     */
    constructor(name: string, position: Vector3, scene?: Scene, _webVROptions?: WebVROptions);
    protected _setRigMode: any;
    /**
     * Gets the device distance from the ground in meters.
     * @returns the distance in meters from the vrDevice to ground in device space. If standing matrix is not supported for the vrDevice 0 is returned.
     */
    deviceDistanceToRoomGround(): number;
    /**
     * Enables the standing matrix when supported. This can be used to position the user's view the correct height from the ground.
     * @param callback will be called when the standing matrix is set. Callback parameter is if the standing matrix is supported.
     */
    useStandingMatrix(callback?: (bool: boolean) => void): void;
    /**
     * Enables the standing matrix when supported. This can be used to position the user's view the correct height from the ground.
     * @returns A promise with a boolean set to if the standing matrix is supported.
     */
    useStandingMatrixAsync(): Promise<boolean>;
    /**
     * Disposes the camera
     */
    dispose(): void;
    /**
     * Gets a vrController by name.
     * @param name The name of the controller to retrieve
     * @returns the controller matching the name specified or null if not found
     */
    getControllerByName(name: string): Nullable<WebVRController>;
    private _leftController;
    /**
     * The controller corresponding to the users left hand.
     */
    get leftController(): Nullable<WebVRController>;
    private _rightController;
    /**
     * The controller corresponding to the users right hand.
     */
    get rightController(): Nullable<WebVRController>;
    /**
     * Casts a ray forward from the vrCamera's gaze.
     * @param length Length of the ray (default: 100)
     * @returns the ray corresponding to the gaze
     */
    getForwardRay(length?: number): Ray;
    /**
     * @internal
     * Updates the camera based on device's frame data
     */
    _checkInputs(): void;
    /**
     * Updates the poseControlled values based on the input device pose.
     * @param poseData Pose coming from the device
     */
    updateFromDevice(poseData: DevicePose): void;
    private _detachIfAttached;
    /**
     * WebVR's attach control will start broadcasting frames to the device.
     * Note that in certain browsers (chrome for example) this function must be called
     * within a user-interaction callback. Example:
     * <pre> scene.onPointerDown = function() { camera.attachControl(canvas); }</pre>
     *
     * @param noPreventDefault prevent the default html element operation when attaching the vrDevice
     */
    attachControl(noPreventDefault?: boolean): void;
    /**
     * Detach the current controls from the specified dom element.
     */
    detachControl(): void;
    /**
     * @returns the name of this class
     */
    getClassName(): string;
    /**
     * Calls resetPose on the vrDisplay
     * See: https://developer.mozilla.org/en-US/docs/Web/API/VRDisplay/resetPose
     */
    resetToCurrentRotation(): void;
    /**
     * @internal
     * Updates the rig cameras (left and right eye)
     */
    _updateRigCameras(): void;
    private _workingVector;
    private _oneVector;
    private _workingMatrix;
    private _updateCacheCalled;
    private _correctPositionIfNotTrackPosition;
    /**
     * @internal
     * Updates the cached values of the camera
     * @param ignoreParentClass ignores updating the parent class's cache (default: false)
     */
    _updateCache(ignoreParentClass?: boolean): void;
    /**
     * @internal
     * Get current device position in babylon world
     */
    _computeDevicePosition(): void;
    /**
     * Updates the current device position and rotation in the babylon world
     */
    update(): void;
    /**
     * @internal
     * Gets the view matrix of this camera (Always set to identity as left and right eye cameras contain the actual view matrix)
     * @returns an identity matrix
     */
    _getViewMatrix(): Matrix;
    private _tmpMatrix;
    /**
     * This function is called by the two RIG cameras.
     * 'this' is the left or right camera (and NOT (!!!) the WebVRFreeCamera instance)
     * @internal
     */
    _getWebVRViewMatrix(): Matrix;
    /** @internal */
    _getWebVRProjectionMatrix(): Matrix;
    private _onGamepadConnectedObserver;
    private _onGamepadDisconnectedObserver;
    private _updateCacheWhenTrackingDisabledObserver;
    /**
     * Initializes the controllers and their meshes
     */
    initControllers(): void;
}
