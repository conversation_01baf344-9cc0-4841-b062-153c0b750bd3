{"version": 3, "file": "surfaceMagnetismBehavior.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Behaviors/Meshes/surfaceMagnetismBehavior.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAQ1E;;;GAGG;AACH,MAAM,OAAO,wBAAwB;IAArC;QAGY,4BAAuB,GAAY,IAAI,OAAO,EAAE,CAAC;QAEjD,qBAAgB,GAAY,IAAI,OAAO,EAAE,CAAC;QAC1C,uBAAkB,GAAe,IAAI,UAAU,EAAE,CAAC;QAClD,cAAS,GAAW,CAAC,CAAC,CAAC;QAEvB,SAAI,GAAG,KAAK,CAAC;QAErB;;WAEG;QACI,oBAAe,GAAW,IAAI,CAAC;QAStC;;WAEG;QACI,WAAM,GAAmB,EAAE,CAAC;QAOnC;;WAEG;QACI,oBAAe,GAAG,IAAI,CAAC;QAE9B;;;WAGG;QACI,aAAQ,GAAG,GAAG,CAAC;QAEtB;;WAEG;QACI,4BAAuB,GAAG,IAAI,CAAC;QAEtC;;WAEG;QACI,YAAO,GAAG,IAAI,CAAC;QAEtB;;WAEG;QACI,wBAAmB,GAAG,GAAG,CAAC;IA4KrC,CAAC;IArNG;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAOD;;OAEG;IACI,IAAI,KAAU,CAAC;IA4BtB;;;;OAIG;IACI,MAAM,CAAC,MAAY,EAAE,KAAa;QACrC,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAC5B,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;QACzC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE;YACxC,IAAI,CAAC,aAAa,CAAC,kBAAkB,GAAG,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;SACxK;QACD,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC5D,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;QACxE,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAEO,cAAc,CAAC,WAAwB;QAC3C,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO,IAAI,CAAC;SACf;QAED,IAAI,WAAW,IAAI,WAAW,CAAC,GAAG,EAAE;YAChC,MAAM,YAAY,GAAG,WAAW,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACvD,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;YAE5C,IAAI,CAAC,YAAY,IAAI,CAAC,WAAW,EAAE;gBAC/B,OAAO,IAAI,CAAC;aACf;YACD,YAAY,CAAC,SAAS,EAAE,CAAC;YAEzB,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1C,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YACnC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC/C,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;YAEpC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;gBAC3B,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC;gBACnF,OAAO,CAAC,oBAAoB,CAAC,WAAW,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;aAChF;YAED,OAAO;gBACH,QAAQ,EAAE,WAAW;gBACrB,UAAU,EAAE,UAAU,CAAC,oBAAoB,CACvC,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,EAC5C,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,EAC3I,CAAC,CACJ;aACJ,CAAC;SACL;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,iBAAiB;QACpB,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IAClE,CAAC;IAED;;;;;;OAMG;IACI,mBAAmB,CAAC,QAAqB;QAC5C,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;QAClB,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE;YACf,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjE,IAAI,IAAI,CAAC,aAAa,IAAI,UAAU,IAAI,UAAU,CAAC,GAAG,IAAI,UAAU,CAAC,UAAU,EAAE;YAC7E,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC7C,IAAI,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,mBAAmB,EAAE;gBACjG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC9C,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAClD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;aACpB;SACJ;QAED,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAEO,0BAA0B,CAAC,GAAY;QAC3C,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACd,OAAO;SACV;QAED,MAAM,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC5C,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,kBAAmB,CAAC,CAAC;QAC5D,IAAI,CAAC,aAAa,CAAC,kBAAmB,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAClE,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;QACxC,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,2BAA2B,EAAE,CAAC;QACxE,MAAM,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACrC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QACxD,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACzB,MAAM,CAAC,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;QAChC,yFAAyF;QACzF,MAAM,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACtC,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC1D,OAAO,CAAC,yBAAyB,CAAC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;QACzD,IAAI,CAAC,aAAa,CAAC,kBAAmB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAChE,CAAC;IAEO,sBAAsB,CAAC,OAAe;QAC1C,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACnC,OAAO;SACV;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAC5C,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAEnC,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC1C,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,EAAE,WAAW,CAAC,CAAC;QAE7G,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YACzF,IAAI,CAAC,aAAa,CAAC,kBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACzE,OAAO;SACV;QAED,WAAW;QACX,MAAM,oBAAoB,GAAG,IAAI,OAAO,EAAE,CAAC;QAC3C,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,oBAAoB,CAAC,CAAC;QACtH,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;QAE3D,WAAW;QACX,MAAM,eAAe,GAAG,IAAI,UAAU,EAAE,CAAC;QACzC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,kBAAmB,CAAC,CAAC;QACjE,UAAU,CAAC,WAAW,CAAC,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,kBAAmB,CAAC,CAAC;QAEjI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC5C,CAAC;IAEO,eAAe;QACnB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;YACxE,IAAI,IAAI,CAAC,OAAO,IAAI,WAAW,CAAC,IAAI,IAAI,iBAAiB,CAAC,WAAW,IAAI,WAAW,CAAC,QAAQ,EAAE;gBAC3F,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;aAClD;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;YACjE,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACxB,IAAI,CAAC,sBAAsB,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;YACnD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1B,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,kBAAkB;QACtB,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC9D,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAClE,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAChC,CAAC;CACJ", "sourcesContent": ["import type { PickingInfo } from \"../../Collisions/pickingInfo\";\r\nimport type { PointerInfo } from \"../../Events/pointerEvents\";\r\nimport { PointerEventTypes } from \"../../Events/pointerEvents\";\r\nimport { Quaternion, TmpVectors, Vector3 } from \"../../Maths/math.vector\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../../Meshes/mesh\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Behavior } from \"../behavior\";\r\n\r\n/**\r\n * A behavior that allows a transform node to stick to a surface position/orientation\r\n * @since 5.0.0\r\n */\r\nexport class SurfaceMagnetismBehavior implements Behavior<Mesh> {\r\n    private _scene: Scene;\r\n    private _attachedMesh: Nullable<Mesh>;\r\n    private _attachPointLocalOffset: Vector3 = new Vector3();\r\n    private _pointerObserver: Nullable<Observer<PointerInfo>>;\r\n    private _workingPosition: Vector3 = new Vector3();\r\n    private _workingQuaternion: Quaternion = new Quaternion();\r\n    private _lastTick: number = -1;\r\n    private _onBeforeRender: Nullable<Observer<Scene>>;\r\n    private _hit = false;\r\n\r\n    /**\r\n     * Distance offset from the hit point to place the target at, along the hit normal.\r\n     */\r\n    public hitNormalOffset: number = 0.05;\r\n\r\n    /**\r\n     * Name of the behavior\r\n     */\r\n    public get name(): string {\r\n        return \"SurfaceMagnetism\";\r\n    }\r\n\r\n    /**\r\n     * Spatial mapping meshes to collide with\r\n     */\r\n    public meshes: AbstractMesh[] = [];\r\n\r\n    /**\r\n     * Function called when the behavior needs to be initialized (after attaching it to a target)\r\n     */\r\n    public init(): void {}\r\n\r\n    /**\r\n     * Set to false if the node should strictly follow the camera without any interpolation time\r\n     */\r\n    public interpolatePose = true;\r\n\r\n    /**\r\n     * Rate of interpolation of position and rotation of the attached node.\r\n     * Higher values will give a slower interpolation.\r\n     */\r\n    public lerpTime = 250;\r\n\r\n    /**\r\n     * If true, pitch and roll are omitted.\r\n     */\r\n    public keepOrientationVertical = true;\r\n\r\n    /**\r\n     * Is this behavior reacting to pointer events\r\n     */\r\n    public enabled = true;\r\n\r\n    /**\r\n     * Maximum distance for the node to stick to the surface\r\n     */\r\n    public maxStickingDistance = 0.8;\r\n\r\n    /**\r\n     * Attaches the behavior to a transform node\r\n     * @param target defines the target where the behavior is attached to\r\n     * @param scene the scene\r\n     */\r\n    public attach(target: Mesh, scene?: Scene): void {\r\n        this._attachedMesh = target;\r\n        this._scene = scene || target.getScene();\r\n        if (!this._attachedMesh.rotationQuaternion) {\r\n            this._attachedMesh.rotationQuaternion = Quaternion.RotationYawPitchRoll(this._attachedMesh.rotation.y, this._attachedMesh.rotation.x, this._attachedMesh.rotation.z);\r\n        }\r\n        this.updateAttachPoint();\r\n\r\n        this._workingPosition.copyFrom(this._attachedMesh.position);\r\n        this._workingQuaternion.copyFrom(this._attachedMesh.rotationQuaternion);\r\n        this._addObservables();\r\n    }\r\n\r\n    /**\r\n     * Detaches the behavior\r\n     */\r\n    public detach(): void {\r\n        this._attachedMesh = null;\r\n        this._removeObservables();\r\n    }\r\n\r\n    private _getTargetPose(pickingInfo: PickingInfo): Nullable<{ position: Vector3; quaternion: Quaternion }> {\r\n        if (!this._attachedMesh) {\r\n            return null;\r\n        }\r\n\r\n        if (pickingInfo && pickingInfo.hit) {\r\n            const pickedNormal = pickingInfo.getNormal(true, true);\r\n            const pickedPoint = pickingInfo.pickedPoint;\r\n\r\n            if (!pickedNormal || !pickedPoint) {\r\n                return null;\r\n            }\r\n            pickedNormal.normalize();\r\n\r\n            const worldTarget = TmpVectors.Vector3[0];\r\n            worldTarget.copyFrom(pickedNormal);\r\n            worldTarget.scaleInPlace(this.hitNormalOffset);\r\n            worldTarget.addInPlace(pickedPoint);\r\n\r\n            if (this._attachedMesh.parent) {\r\n                TmpVectors.Matrix[0].copyFrom(this._attachedMesh.parent.getWorldMatrix()).invert();\r\n                Vector3.TransformNormalToRef(worldTarget, TmpVectors.Matrix[0], worldTarget);\r\n            }\r\n\r\n            return {\r\n                position: worldTarget,\r\n                quaternion: Quaternion.RotationYawPitchRoll(\r\n                    -Math.atan2(pickedNormal.x, -pickedNormal.z),\r\n                    this.keepOrientationVertical ? 0 : Math.atan2(pickedNormal.y, Math.sqrt(pickedNormal.z * pickedNormal.z + pickedNormal.x * pickedNormal.x)),\r\n                    0\r\n                ),\r\n            };\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Updates the attach point with the current geometry extents of the attached mesh\r\n     */\r\n    public updateAttachPoint() {\r\n        this._getAttachPointOffsetToRef(this._attachPointLocalOffset);\r\n    }\r\n\r\n    /**\r\n     * Finds the intersection point of the given ray onto the meshes and updates the target.\r\n     * Transformation will be interpolated according to `interpolatePose` and `lerpTime` properties.\r\n     * If no mesh of `meshes` are hit, this does nothing.\r\n     * @param pickInfo The input pickingInfo that will be used to intersect the meshes\r\n     * @returns a boolean indicating if we found a hit to stick to\r\n     */\r\n    public findAndUpdateTarget(pickInfo: PickingInfo): boolean {\r\n        this._hit = false;\r\n        if (!pickInfo.ray) {\r\n            return false;\r\n        }\r\n\r\n        const subPicking = pickInfo.ray.intersectsMeshes(this.meshes)[0];\r\n\r\n        if (this._attachedMesh && subPicking && subPicking.hit && subPicking.pickedMesh) {\r\n            const pose = this._getTargetPose(subPicking);\r\n            if (pose && Vector3.Distance(this._attachedMesh.position, pose.position) < this.maxStickingDistance) {\r\n                this._workingPosition.copyFrom(pose.position);\r\n                this._workingQuaternion.copyFrom(pose.quaternion);\r\n                this._hit = true;\r\n            }\r\n        }\r\n\r\n        return this._hit;\r\n    }\r\n\r\n    private _getAttachPointOffsetToRef(ref: Vector3) {\r\n        if (!this._attachedMesh) {\r\n            ref.setAll(0);\r\n            return;\r\n        }\r\n\r\n        const storedQuat = TmpVectors.Quaternion[0];\r\n        storedQuat.copyFrom(this._attachedMesh.rotationQuaternion!);\r\n        this._attachedMesh.rotationQuaternion!.copyFromFloats(0, 0, 0, 1);\r\n        this._attachedMesh.computeWorldMatrix();\r\n        const boundingMinMax = this._attachedMesh.getHierarchyBoundingVectors();\r\n        const center = TmpVectors.Vector3[0];\r\n        boundingMinMax.max.addToRef(boundingMinMax.min, center);\r\n        center.scaleInPlace(0.5);\r\n        center.z = boundingMinMax.max.z;\r\n        // We max the z coordinate because we want the attach point to be on the back of the mesh\r\n        const invWorld = TmpVectors.Matrix[0];\r\n        this._attachedMesh.getWorldMatrix().invertToRef(invWorld);\r\n        Vector3.TransformCoordinatesToRef(center, invWorld, ref);\r\n        this._attachedMesh.rotationQuaternion!.copyFrom(storedQuat);\r\n    }\r\n\r\n    private _updateTransformToGoal(elapsed: number) {\r\n        if (!this._attachedMesh || !this._hit) {\r\n            return;\r\n        }\r\n\r\n        const oldParent = this._attachedMesh.parent;\r\n        this._attachedMesh.setParent(null);\r\n\r\n        const worldOffset = TmpVectors.Vector3[0];\r\n        Vector3.TransformNormalToRef(this._attachPointLocalOffset, this._attachedMesh.getWorldMatrix(), worldOffset);\r\n\r\n        if (!this.interpolatePose) {\r\n            this._attachedMesh.position.copyFrom(this._workingPosition).subtractInPlace(worldOffset);\r\n            this._attachedMesh.rotationQuaternion!.copyFrom(this._workingQuaternion);\r\n            return;\r\n        }\r\n\r\n        // position\r\n        const interpolatedPosition = new Vector3();\r\n        Vector3.SmoothToRef(this._attachedMesh.position, this._workingPosition, elapsed, this.lerpTime, interpolatedPosition);\r\n        this._attachedMesh.position.copyFrom(interpolatedPosition);\r\n\r\n        // rotation\r\n        const currentRotation = new Quaternion();\r\n        currentRotation.copyFrom(this._attachedMesh.rotationQuaternion!);\r\n        Quaternion.SmoothToRef(currentRotation, this._workingQuaternion, elapsed, this.lerpTime, this._attachedMesh.rotationQuaternion!);\r\n\r\n        this._attachedMesh.setParent(oldParent);\r\n    }\r\n\r\n    private _addObservables() {\r\n        this._pointerObserver = this._scene.onPointerObservable.add((pointerInfo) => {\r\n            if (this.enabled && pointerInfo.type == PointerEventTypes.POINTERMOVE && pointerInfo.pickInfo) {\r\n                this.findAndUpdateTarget(pointerInfo.pickInfo);\r\n            }\r\n        });\r\n\r\n        this._lastTick = Date.now();\r\n        this._onBeforeRender = this._scene.onBeforeRenderObservable.add(() => {\r\n            const tick = Date.now();\r\n            this._updateTransformToGoal(tick - this._lastTick);\r\n            this._lastTick = tick;\r\n        });\r\n    }\r\n\r\n    private _removeObservables() {\r\n        this._scene.onPointerObservable.remove(this._pointerObserver);\r\n        this._scene.onBeforeRenderObservable.remove(this._onBeforeRender);\r\n        this._pointerObserver = null;\r\n        this._onBeforeRender = null;\r\n    }\r\n}\r\n"]}