{"version": 3, "file": "condition.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Actions/condition.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAIlD;;GAEG;AACH,MAAM,OAAO,SAAS;IAiBlB;;;OAGG;IACH,YAAY,aAA4B;QACpC,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;IACxC,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,YAAoB;QACpC,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,MAAW,EAAE,YAAoB;QACxD,OAAO,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;IACzE,CAAC;IAED;;;OAGG;IACI,SAAS,KAAS,CAAC;IAE1B;;OAEG;IACO,UAAU,CAAC,mBAAwB;QACzC,OAAO;YACH,IAAI,EAAE,CAAC;YACP,QAAQ,EAAE,EAAE;YACZ,IAAI,EAAE,mBAAmB,CAAC,IAAI;YAC9B,UAAU,EAAE,mBAAmB,CAAC,UAAU;SAC7C,CAAC;IACN,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,cAAe,SAAQ,SAAS;IAMzC;;OAEG;IACI,MAAM,KAAK,OAAO;QACrB,OAAO,cAAc,CAAC,QAAQ,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,WAAW;QACzB,OAAO,cAAc,CAAC,YAAY,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,SAAS;QACvB,OAAO,cAAc,CAAC,UAAU,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,QAAQ;QACtB,OAAO,cAAc,CAAC,SAAS,CAAC;IACpC,CAAC;IAYD;;;;;;;OAOG;IACH,YACI,aAA4B,EAC5B,MAAW;IACX,gFAAgF;IACzE,YAAoB;IAC3B,+FAA+F;IACxF,KAAU;IACjB,+DAA+D;IACxD,WAAmB,cAAc,CAAC,OAAO;QAEhD,KAAK,CAAC,aAAa,CAAC,CAAC;QANd,iBAAY,GAAZ,YAAY,CAAQ;QAEpB,UAAK,GAAL,KAAK,CAAK;QAEV,aAAQ,GAAR,QAAQ,CAAiC;QAIhD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5E,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC1D,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,QAAQ,IAAI,CAAC,QAAQ,EAAE;YACnB,KAAK,cAAc,CAAC,SAAS;gBACzB,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;YAC9D,KAAK,cAAc,CAAC,QAAQ;gBACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;YAC9D,KAAK,cAAc,CAAC,OAAO,CAAC;YAC5B,KAAK,cAAc,CAAC,WAAW,CAAC,CAAC;gBAC7B,IAAI,KAAc,CAAC;gBAEnB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;oBACnB,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;iBACpE;qBAAM;oBACH,KAAK,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;iBAChE;gBACD,OAAO,IAAI,CAAC,QAAQ,KAAK,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;aACpE;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC;YACnB,IAAI,EAAE,gBAAgB;YACtB,UAAU,EAAE;gBACR,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC;gBACvC,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE;gBAClD,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBACpE,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;aAC7E;SACJ,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,eAAe,CAAC,QAAgB;QAC1C,QAAQ,QAAQ,EAAE;YACd,KAAK,cAAc,CAAC,QAAQ;gBACxB,OAAO,SAAS,CAAC;YACrB,KAAK,cAAc,CAAC,YAAY;gBAC5B,OAAO,aAAa,CAAC;YACzB,KAAK,cAAc,CAAC,UAAU;gBAC1B,OAAO,WAAW,CAAC;YACvB,KAAK,cAAc,CAAC,SAAS;gBACzB,OAAO,UAAU,CAAC;YACtB;gBACI,OAAO,EAAE,CAAC;SACjB;IACL,CAAC;;AAhIc,uBAAQ,GAAG,CAAC,CAAC;AACb,2BAAY,GAAG,CAAC,CAAC;AACjB,yBAAU,GAAG,CAAC,CAAC;AACf,wBAAS,GAAG,CAAC,CAAC;AAgIjC;;GAEG;AACH,MAAM,OAAO,kBAAmB,SAAQ,SAAS;IAO7C;;;;OAIG;IACH,YACI,aAA4B;IAC5B,oEAAoE;IAC7D,SAAwB;QAE/B,KAAK,CAAC,aAAa,CAAC,CAAC;QAFd,cAAS,GAAT,SAAS,CAAe;IAGnC,CAAC;IAED;;OAEG;IACI,OAAO;QACV,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;IAC5B,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,cAAe,SAAQ,SAAS;IASzC;;;;;OAKG;IACH,YACI,aAA4B,EAC5B,MAAW;IACX,0CAA0C;IACnC,KAAa;QAEpB,KAAK,CAAC,aAAa,CAAC,CAAC;QAFd,UAAK,GAAL,KAAK,CAAQ;QAIpB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC;IAC7C,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC;YACnB,IAAI,EAAE,gBAAgB;YACtB,UAAU,EAAE,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;SAC9F,CAAC,CAAC;IACP,CAAC;CACJ;AAED,aAAa,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC;AACxD,aAAa,CAAC,4BAA4B,EAAE,kBAAkB,CAAC,CAAC;AAChE,aAAa,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC", "sourcesContent": ["import { Action } from \"./action\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\n\r\ndeclare type ActionManager = import(\"./actionManager\").ActionManager;\r\n\r\n/**\r\n * A Condition applied to an Action\r\n */\r\nexport class Condition {\r\n    /**\r\n     * Internal only - manager for action\r\n     * @internal\r\n     */\r\n    public _actionManager: ActionManager;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _evaluationId: number;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _currentResult: boolean;\r\n\r\n    /**\r\n     * Creates a new Condition\r\n     * @param actionManager the manager of the action the condition is applied to\r\n     */\r\n    constructor(actionManager: ActionManager) {\r\n        this._actionManager = actionManager;\r\n    }\r\n\r\n    /**\r\n     * Check if the current condition is valid\r\n     * @returns a boolean\r\n     */\r\n    public isValid(): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getProperty(propertyPath: string): string {\r\n        return this._actionManager._getProperty(propertyPath);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getEffectiveTarget(target: any, propertyPath: string): any {\r\n        return this._actionManager._getEffectiveTarget(target, propertyPath);\r\n    }\r\n\r\n    /**\r\n     * Serialize placeholder for child classes\r\n     * @returns the serialized object\r\n     */\r\n    public serialize(): any {}\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    protected _serialize(serializedCondition: any): any {\r\n        return {\r\n            type: 2, // Condition\r\n            children: [],\r\n            name: serializedCondition.name,\r\n            properties: serializedCondition.properties,\r\n        };\r\n    }\r\n}\r\n\r\n/**\r\n * Defines specific conditional operators as extensions of Condition\r\n */\r\nexport class ValueCondition extends Condition {\r\n    private static _IsEqual = 0;\r\n    private static _IsDifferent = 1;\r\n    private static _IsGreater = 2;\r\n    private static _IsLesser = 3;\r\n\r\n    /**\r\n     * returns the number for IsEqual\r\n     */\r\n    public static get IsEqual(): number {\r\n        return ValueCondition._IsEqual;\r\n    }\r\n\r\n    /**\r\n     * Returns the number for IsDifferent\r\n     */\r\n    public static get IsDifferent(): number {\r\n        return ValueCondition._IsDifferent;\r\n    }\r\n\r\n    /**\r\n     * Returns the number for IsGreater\r\n     */\r\n    public static get IsGreater(): number {\r\n        return ValueCondition._IsGreater;\r\n    }\r\n\r\n    /**\r\n     * Returns the number for IsLesser\r\n     */\r\n    public static get IsLesser(): number {\r\n        return ValueCondition._IsLesser;\r\n    }\r\n\r\n    /**\r\n     * Internal only The action manager for the condition\r\n     * @internal\r\n     */\r\n    public _actionManager: ActionManager;\r\n\r\n    private _target: any;\r\n    private _effectiveTarget: any;\r\n    private _property: string;\r\n\r\n    /**\r\n     * Creates a new ValueCondition\r\n     * @param actionManager manager for the action the condition applies to\r\n     * @param target for the action\r\n     * @param propertyPath path to specify the property of the target the conditional operator uses\r\n     * @param value the value compared by the conditional operator against the current value of the property\r\n     * @param operator the conditional operator, default ValueCondition.IsEqual\r\n     */\r\n    constructor(\r\n        actionManager: ActionManager,\r\n        target: any,\r\n        /** path to specify the property of the target the conditional operator uses  */\r\n        public propertyPath: string,\r\n        /** the value compared by the conditional operator against the current value of the property */\r\n        public value: any,\r\n        /** the conditional operator, default ValueCondition.IsEqual */\r\n        public operator: number = ValueCondition.IsEqual\r\n    ) {\r\n        super(actionManager);\r\n\r\n        this._target = target;\r\n        this._effectiveTarget = this._getEffectiveTarget(target, this.propertyPath);\r\n        this._property = this._getProperty(this.propertyPath);\r\n    }\r\n\r\n    /**\r\n     * Compares the given value with the property value for the specified conditional operator\r\n     * @returns the result of the comparison\r\n     */\r\n    public isValid(): boolean {\r\n        switch (this.operator) {\r\n            case ValueCondition.IsGreater:\r\n                return this._effectiveTarget[this._property] > this.value;\r\n            case ValueCondition.IsLesser:\r\n                return this._effectiveTarget[this._property] < this.value;\r\n            case ValueCondition.IsEqual:\r\n            case ValueCondition.IsDifferent: {\r\n                let check: boolean;\r\n\r\n                if (this.value.equals) {\r\n                    check = this.value.equals(this._effectiveTarget[this._property]);\r\n                } else {\r\n                    check = this.value === this._effectiveTarget[this._property];\r\n                }\r\n                return this.operator === ValueCondition.IsEqual ? check : !check;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Serialize the ValueCondition into a JSON compatible object\r\n     * @returns serialization object\r\n     */\r\n    public serialize(): any {\r\n        return this._serialize({\r\n            name: \"ValueCondition\",\r\n            properties: [\r\n                Action._GetTargetProperty(this._target),\r\n                { name: \"propertyPath\", value: this.propertyPath },\r\n                { name: \"value\", value: Action._SerializeValueAsString(this.value) },\r\n                { name: \"operator\", value: ValueCondition.GetOperatorName(this.operator) },\r\n            ],\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Gets the name of the conditional operator for the ValueCondition\r\n     * @param operator the conditional operator\r\n     * @returns the name\r\n     */\r\n    public static GetOperatorName(operator: number): string {\r\n        switch (operator) {\r\n            case ValueCondition._IsEqual:\r\n                return \"IsEqual\";\r\n            case ValueCondition._IsDifferent:\r\n                return \"IsDifferent\";\r\n            case ValueCondition._IsGreater:\r\n                return \"IsGreater\";\r\n            case ValueCondition._IsLesser:\r\n                return \"IsLesser\";\r\n            default:\r\n                return \"\";\r\n        }\r\n    }\r\n}\r\n\r\n/**\r\n * Defines a predicate condition as an extension of Condition\r\n */\r\nexport class PredicateCondition extends Condition {\r\n    /**\r\n     * Internal only - manager for action\r\n     * @internal\r\n     */\r\n    public _actionManager: ActionManager;\r\n\r\n    /**\r\n     * Creates a new PredicateCondition\r\n     * @param actionManager manager for the action the condition applies to\r\n     * @param predicate defines the predicate function used to validate the condition\r\n     */\r\n    constructor(\r\n        actionManager: ActionManager,\r\n        /** defines the predicate function used to validate the condition */\r\n        public predicate: () => boolean\r\n    ) {\r\n        super(actionManager);\r\n    }\r\n\r\n    /**\r\n     * @returns the validity of the predicate condition\r\n     */\r\n    public isValid(): boolean {\r\n        return this.predicate();\r\n    }\r\n}\r\n\r\n/**\r\n * Defines a state condition as an extension of Condition\r\n */\r\nexport class StateCondition extends Condition {\r\n    /**\r\n     * Internal only - manager for action\r\n     * @internal\r\n     */\r\n    public _actionManager: ActionManager;\r\n\r\n    private _target: any;\r\n\r\n    /**\r\n     * Creates a new StateCondition\r\n     * @param actionManager manager for the action the condition applies to\r\n     * @param target of the condition\r\n     * @param value to compare with target state\r\n     */\r\n    constructor(\r\n        actionManager: ActionManager,\r\n        target: any,\r\n        /** Value to compare with target state  */\r\n        public value: string\r\n    ) {\r\n        super(actionManager);\r\n\r\n        this._target = target;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the current condition is met\r\n     * @returns the validity of the state\r\n     */\r\n    public isValid(): boolean {\r\n        return this._target.state === this.value;\r\n    }\r\n\r\n    /**\r\n     * Serialize the StateCondition into a JSON compatible object\r\n     * @returns serialization object\r\n     */\r\n    public serialize(): any {\r\n        return this._serialize({\r\n            name: \"StateCondition\",\r\n            properties: [Action._GetTargetProperty(this._target), { name: \"value\", value: this.value }],\r\n        });\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ValueCondition\", ValueCondition);\r\nRegisterClass(\"BABYLON.PredicateCondition\", PredicateCondition);\r\nRegisterClass(\"BABYLON.StateCondition\", StateCondition);\r\n"]}