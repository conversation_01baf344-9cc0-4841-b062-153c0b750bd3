{"version": 3, "file": "nodeMaterialModes.js", "sourceRoot": "", "sources": ["../../../../../../lts/core/generated/Materials/Node/Enums/nodeMaterialModes.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,CAAN,IAAY,iBASX;AATD,WAAY,iBAAiB;IACzB,uBAAuB;IACvB,iEAAY,CAAA;IACZ,uBAAuB;IACvB,uEAAe,CAAA;IACf,0BAA0B;IAC1B,iEAAY,CAAA;IACZ,6BAA6B;IAC7B,mFAAqB,CAAA;AACzB,CAAC,EATW,iBAAiB,KAAjB,iBAAiB,QAS5B", "sourcesContent": ["/**\r\n * Enum used to define the material modes\r\n */\r\nexport enum NodeMaterialModes {\r\n    /** Regular material */\r\n    Material = 0,\r\n    /** For post process */\r\n    PostProcess = 1,\r\n    /** For particle system */\r\n    Particle = 2,\r\n    /** For procedural texture */\r\n    ProceduralTexture = 3,\r\n}\r\n"]}