{"version": 3, "file": "followCameraKeyboardMoveInput.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Cameras/Inputs/followCameraKeyboardMoveInput.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AAErE,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAKlD,OAAO,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AAEjE,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEzC;;;GAGG;AACH,MAAM,OAAO,6BAA6B;IAA1C;QAMI;;WAEG;QAEI,yBAAoB,GAAG,CAAC,EAAE,CAAC,CAAC;QAEnC;;WAEG;QAEI,yBAAoB,GAAG,CAAC,EAAE,CAAC,CAAC;QAEnC;;WAEG;QAEI,gCAA2B,GAAY,KAAK,CAAC;QAEpD;;WAEG;QAEI,iCAA4B,GAAY,KAAK,CAAC;QAErD;;WAEG;QAEI,kCAA6B,GAAY,KAAK,CAAC;QAEtD;;WAEG;QAEI,2BAAsB,GAAG,CAAC,EAAE,CAAC,CAAC;QAErC;;WAEG;QAEI,2BAAsB,GAAG,CAAC,EAAE,CAAC,CAAC;QAErC;;WAEG;QAEI,kCAA6B,GAAY,KAAK,CAAC;QAEtD;;WAEG;QAEI,mCAA8B,GAAY,KAAK,CAAC;QAEvD;;WAEG;QAEI,oCAA+B,GAAY,KAAK,CAAC;QAExD;;WAEG;QAEI,mBAAc,GAAG,CAAC,EAAE,CAAC,CAAC;QAE7B;;WAEG;QAEI,mBAAc,GAAG,CAAC,EAAE,CAAC,CAAC;QAE7B;;WAEG;QAEI,0BAAqB,GAAY,IAAI,CAAC;QAE7C;;WAEG;QAEI,2BAAsB,GAAY,KAAK,CAAC;QAE/C;;WAEG;QAEI,4BAAuB,GAAY,KAAK,CAAC;QAEhD;;WAEG;QAEI,sBAAiB,GAAW,CAAC,CAAC;QAErC;;WAEG;QAEI,wBAAmB,GAAW,CAAC,CAAC;QAEvC;;WAEG;QAEI,sBAAiB,GAAW,CAAC,CAAC;QAE7B,UAAK,GAAG,IAAI,KAAK,EAAU,CAAC;IA4KxC,CAAC;IAnKG;;;OAGG;IACI,aAAa,CAAC,gBAA0B;QAC3C,8CAA8C;QAC9C,gBAAgB,GAAG,KAAK,CAAC,gCAAgC,CAAC,SAAS,CAAC,CAAC;QACrE,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,OAAO;SACV;QAED,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,GAAG,CAAC,GAAG,EAAE;YACtE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACrE,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;YACvB,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;gBACd,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB,CAAC,OAAO,EAAE;oBAC1C,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC;oBAChC,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC;oBAC9B,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC,QAAQ,CAAC;oBAElC,IACI,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBACrD,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBACrD,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBACvD,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBACvD,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC/C,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EACjD;wBACE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;wBAE9C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;4BACd,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;yBAChC;wBAED,IAAI,GAAG,CAAC,cAAc,EAAE;4BACpB,IAAI,CAAC,gBAAgB,EAAE;gCACnB,GAAG,CAAC,cAAc,EAAE,CAAC;6BACxB;yBACJ;qBACJ;iBACJ;qBAAM;oBACH,IACI,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBACrD,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBACrD,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBACvD,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBACvD,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC/C,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EACjD;wBACE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;wBAE9C,IAAI,KAAK,IAAI,CAAC,EAAE;4BACZ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;yBAC/B;wBAED,IAAI,GAAG,CAAC,cAAc,EAAE;4BACpB,IAAI,CAAC,gBAAgB,EAAE;gCACnB,GAAG,CAAC,cAAc,EAAE,CAAC;6BACxB;yBACJ;qBACJ;iBACJ;aACJ;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;aACrE;YACD,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;aAC1E;YACD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAChC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;SACrC;QAED,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC3B,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE;oBACnF,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,iBAAiB,CAAC;iBACtD;qBAAM,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE;oBAC1F,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,iBAAiB,CAAC;iBACtD;qBAAM,IAAI,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,uBAAuB,EAAE,EAAE;oBAC9F,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,mBAAmB,CAAC;oBACvD,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,GAAG,CAAC;iBACrC;qBAAM,IAAI,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,uBAAuB,EAAE,EAAE;oBAC9F,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,mBAAmB,CAAC;oBACvD,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,GAAG,CAAC;iBACrC;qBAAM,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;oBAC9E,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,iBAAiB,CAAC;iBAChD;qBAAM,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;oBAC9E,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,iBAAiB,CAAC;iBAChD;YACL,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,+BAA+B,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;OAGG;IACK,qBAAqB;QACzB,OAAO,CACH,IAAI,CAAC,2BAA2B,KAAK,IAAI,CAAC,WAAW;YACrD,IAAI,CAAC,4BAA4B,KAAK,IAAI,CAAC,YAAY;YACvD,IAAI,CAAC,6BAA6B,KAAK,IAAI,CAAC,aAAa,CAC5D,CAAC;IACN,CAAC;IAED;;;OAGG;IACK,uBAAuB;QAC3B,OAAO,CACH,IAAI,CAAC,6BAA6B,KAAK,IAAI,CAAC,WAAW;YACvD,IAAI,CAAC,8BAA8B,KAAK,IAAI,CAAC,YAAY;YACzD,IAAI,CAAC,+BAA+B,KAAK,IAAI,CAAC,aAAa,CAC9D,CAAC;IACN,CAAC;IAED;;;OAGG;IACK,eAAe;QACnB,OAAO,IAAI,CAAC,qBAAqB,KAAK,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,sBAAsB,KAAK,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,uBAAuB,KAAK,IAAI,CAAC,aAAa,CAAC;IACvK,CAAC;CACJ;AApRG;IADC,SAAS,EAAE;2EACuB;AAMnC;IADC,SAAS,EAAE;2EACuB;AAMnC;IADC,SAAS,EAAE;kFACwC;AAMpD;IADC,SAAS,EAAE;mFACyC;AAMrD;IADC,SAAS,EAAE;oFAC0C;AAMtD;IADC,SAAS,EAAE;6EACyB;AAMrC;IADC,SAAS,EAAE;6EACyB;AAMrC;IADC,SAAS,EAAE;oFAC0C;AAMtD;IADC,SAAS,EAAE;qFAC2C;AAMvD;IADC,SAAS,EAAE;sFAC4C;AAMxD;IADC,SAAS,EAAE;qEACiB;AAM7B;IADC,SAAS,EAAE;qEACiB;AAM7B;IADC,SAAS,EAAE;4EACiC;AAM7C;IADC,SAAS,EAAE;6EACmC;AAM/C;IADC,SAAS,EAAE;8EACoC;AAMhD;IADC,SAAS,EAAE;wEACyB;AAMrC;IADC,SAAS,EAAE;0EAC2B;AAMvC;IADC,SAAS,EAAE;wEACyB;AAgLnC,gBAAiB,CAAC,+BAA+B,CAAC,GAAG,6BAA6B,CAAC", "sourcesContent": ["import type { ICameraInput } from \"../../Cameras/cameraInputsManager\";\r\nimport { CameraInputTypes } from \"../../Cameras/cameraInputsManager\";\r\nimport type { FollowCamera } from \"../../Cameras/followCamera\";\r\nimport { serialize } from \"../../Misc/decorators\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { Engine } from \"../../Engines/engine\";\r\nimport type { KeyboardInfo } from \"../../Events/keyboardEvents\";\r\nimport { KeyboardEventTypes } from \"../../Events/keyboardEvents\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Tools } from \"../../Misc/tools\";\r\n\r\n/**\r\n * Manage the keyboard inputs to control the movement of a follow camera.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n */\r\nexport class FollowCameraKeyboardMoveInput implements ICameraInput<FollowCamera> {\r\n    /**\r\n     * Defines the camera the input is attached to.\r\n     */\r\n    public camera: FollowCamera;\r\n\r\n    /**\r\n     * Defines the list of key codes associated with the up action (increase heightOffset)\r\n     */\r\n    @serialize()\r\n    public keysHeightOffsetIncr = [38];\r\n\r\n    /**\r\n     * Defines the list of key codes associated with the down action (decrease heightOffset)\r\n     */\r\n    @serialize()\r\n    public keysHeightOffsetDecr = [40];\r\n\r\n    /**\r\n     * Defines whether the Alt modifier key is required to move up/down (alter heightOffset)\r\n     */\r\n    @serialize()\r\n    public keysHeightOffsetModifierAlt: boolean = false;\r\n\r\n    /**\r\n     * Defines whether the Ctrl modifier key is required to move up/down (alter heightOffset)\r\n     */\r\n    @serialize()\r\n    public keysHeightOffsetModifierCtrl: boolean = false;\r\n\r\n    /**\r\n     * Defines whether the Shift modifier key is required to move up/down (alter heightOffset)\r\n     */\r\n    @serialize()\r\n    public keysHeightOffsetModifierShift: boolean = false;\r\n\r\n    /**\r\n     * Defines the list of key codes associated with the left action (increase rotationOffset)\r\n     */\r\n    @serialize()\r\n    public keysRotationOffsetIncr = [37];\r\n\r\n    /**\r\n     * Defines the list of key codes associated with the right action (decrease rotationOffset)\r\n     */\r\n    @serialize()\r\n    public keysRotationOffsetDecr = [39];\r\n\r\n    /**\r\n     * Defines whether the Alt modifier key is required to move left/right (alter rotationOffset)\r\n     */\r\n    @serialize()\r\n    public keysRotationOffsetModifierAlt: boolean = false;\r\n\r\n    /**\r\n     * Defines whether the Ctrl modifier key is required to move left/right (alter rotationOffset)\r\n     */\r\n    @serialize()\r\n    public keysRotationOffsetModifierCtrl: boolean = false;\r\n\r\n    /**\r\n     * Defines whether the Shift modifier key is required to move left/right (alter rotationOffset)\r\n     */\r\n    @serialize()\r\n    public keysRotationOffsetModifierShift: boolean = false;\r\n\r\n    /**\r\n     * Defines the list of key codes associated with the zoom-in action (decrease radius)\r\n     */\r\n    @serialize()\r\n    public keysRadiusIncr = [40];\r\n\r\n    /**\r\n     * Defines the list of key codes associated with the zoom-out action (increase radius)\r\n     */\r\n    @serialize()\r\n    public keysRadiusDecr = [38];\r\n\r\n    /**\r\n     * Defines whether the Alt modifier key is required to zoom in/out (alter radius value)\r\n     */\r\n    @serialize()\r\n    public keysRadiusModifierAlt: boolean = true;\r\n\r\n    /**\r\n     * Defines whether the Ctrl modifier key is required to zoom in/out (alter radius value)\r\n     */\r\n    @serialize()\r\n    public keysRadiusModifierCtrl: boolean = false;\r\n\r\n    /**\r\n     * Defines whether the Shift modifier key is required to zoom in/out (alter radius value)\r\n     */\r\n    @serialize()\r\n    public keysRadiusModifierShift: boolean = false;\r\n\r\n    /**\r\n     * Defines the rate of change of heightOffset.\r\n     */\r\n    @serialize()\r\n    public heightSensibility: number = 1;\r\n\r\n    /**\r\n     * Defines the rate of change of rotationOffset.\r\n     */\r\n    @serialize()\r\n    public rotationSensibility: number = 1;\r\n\r\n    /**\r\n     * Defines the rate of change of radius.\r\n     */\r\n    @serialize()\r\n    public radiusSensibility: number = 1;\r\n\r\n    private _keys = new Array<number>();\r\n    private _ctrlPressed: boolean;\r\n    private _altPressed: boolean;\r\n    private _shiftPressed: boolean;\r\n    private _onCanvasBlurObserver: Nullable<Observer<Engine>>;\r\n    private _onKeyboardObserver: Nullable<Observer<KeyboardInfo>>;\r\n    private _engine: Engine;\r\n    private _scene: Scene;\r\n\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public attachControl(noPreventDefault?: boolean): void {\r\n        // eslint-disable-next-line prefer-rest-params\r\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(arguments);\r\n        if (this._onCanvasBlurObserver) {\r\n            return;\r\n        }\r\n\r\n        this._scene = this.camera.getScene();\r\n        this._engine = this._scene.getEngine();\r\n\r\n        this._onCanvasBlurObserver = this._engine.onCanvasBlurObservable.add(() => {\r\n            this._keys.length = 0;\r\n        });\r\n\r\n        this._onKeyboardObserver = this._scene.onKeyboardObservable.add((info) => {\r\n            const evt = info.event;\r\n            if (!evt.metaKey) {\r\n                if (info.type === KeyboardEventTypes.KEYDOWN) {\r\n                    this._ctrlPressed = evt.ctrlKey;\r\n                    this._altPressed = evt.altKey;\r\n                    this._shiftPressed = evt.shiftKey;\r\n\r\n                    if (\r\n                        this.keysHeightOffsetIncr.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysHeightOffsetDecr.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysRotationOffsetIncr.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysRotationOffsetDecr.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysRadiusIncr.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysRadiusDecr.indexOf(evt.keyCode) !== -1\r\n                    ) {\r\n                        const index = this._keys.indexOf(evt.keyCode);\r\n\r\n                        if (index === -1) {\r\n                            this._keys.push(evt.keyCode);\r\n                        }\r\n\r\n                        if (evt.preventDefault) {\r\n                            if (!noPreventDefault) {\r\n                                evt.preventDefault();\r\n                            }\r\n                        }\r\n                    }\r\n                } else {\r\n                    if (\r\n                        this.keysHeightOffsetIncr.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysHeightOffsetDecr.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysRotationOffsetIncr.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysRotationOffsetDecr.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysRadiusIncr.indexOf(evt.keyCode) !== -1 ||\r\n                        this.keysRadiusDecr.indexOf(evt.keyCode) !== -1\r\n                    ) {\r\n                        const index = this._keys.indexOf(evt.keyCode);\r\n\r\n                        if (index >= 0) {\r\n                            this._keys.splice(index, 1);\r\n                        }\r\n\r\n                        if (evt.preventDefault) {\r\n                            if (!noPreventDefault) {\r\n                                evt.preventDefault();\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    public detachControl(): void {\r\n        if (this._scene) {\r\n            if (this._onKeyboardObserver) {\r\n                this._scene.onKeyboardObservable.remove(this._onKeyboardObserver);\r\n            }\r\n            if (this._onCanvasBlurObserver) {\r\n                this._engine.onCanvasBlurObservable.remove(this._onCanvasBlurObserver);\r\n            }\r\n            this._onKeyboardObserver = null;\r\n            this._onCanvasBlurObserver = null;\r\n        }\r\n\r\n        this._keys.length = 0;\r\n    }\r\n\r\n    /**\r\n     * Update the current camera state depending on the inputs that have been used this frame.\r\n     * This is a dynamically created lambda to avoid the performance penalty of looping for inputs in the render loop.\r\n     */\r\n    public checkInputs(): void {\r\n        if (this._onKeyboardObserver) {\r\n            this._keys.forEach((keyCode) => {\r\n                if (this.keysHeightOffsetIncr.indexOf(keyCode) !== -1 && this._modifierHeightOffset()) {\r\n                    this.camera.heightOffset += this.heightSensibility;\r\n                } else if (this.keysHeightOffsetDecr.indexOf(keyCode) !== -1 && this._modifierHeightOffset()) {\r\n                    this.camera.heightOffset -= this.heightSensibility;\r\n                } else if (this.keysRotationOffsetIncr.indexOf(keyCode) !== -1 && this._modifierRotationOffset()) {\r\n                    this.camera.rotationOffset += this.rotationSensibility;\r\n                    this.camera.rotationOffset %= 360;\r\n                } else if (this.keysRotationOffsetDecr.indexOf(keyCode) !== -1 && this._modifierRotationOffset()) {\r\n                    this.camera.rotationOffset -= this.rotationSensibility;\r\n                    this.camera.rotationOffset %= 360;\r\n                } else if (this.keysRadiusIncr.indexOf(keyCode) !== -1 && this._modifierRadius()) {\r\n                    this.camera.radius += this.radiusSensibility;\r\n                } else if (this.keysRadiusDecr.indexOf(keyCode) !== -1 && this._modifierRadius()) {\r\n                    this.camera.radius -= this.radiusSensibility;\r\n                }\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of the current input.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"FollowCameraKeyboardMoveInput\";\r\n    }\r\n\r\n    /**\r\n     * Get the friendly name associated with the input class.\r\n     * @returns the input friendly name\r\n     */\r\n    public getSimpleName(): string {\r\n        return \"keyboard\";\r\n    }\r\n\r\n    /**\r\n     * Check if the pressed modifier keys (Alt/Ctrl/Shift) match those configured to\r\n     * allow modification of the heightOffset value.\r\n     */\r\n    private _modifierHeightOffset(): boolean {\r\n        return (\r\n            this.keysHeightOffsetModifierAlt === this._altPressed &&\r\n            this.keysHeightOffsetModifierCtrl === this._ctrlPressed &&\r\n            this.keysHeightOffsetModifierShift === this._shiftPressed\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Check if the pressed modifier keys (Alt/Ctrl/Shift) match those configured to\r\n     * allow modification of the rotationOffset value.\r\n     */\r\n    private _modifierRotationOffset(): boolean {\r\n        return (\r\n            this.keysRotationOffsetModifierAlt === this._altPressed &&\r\n            this.keysRotationOffsetModifierCtrl === this._ctrlPressed &&\r\n            this.keysRotationOffsetModifierShift === this._shiftPressed\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Check if the pressed modifier keys (Alt/Ctrl/Shift) match those configured to\r\n     * allow modification of the radius value.\r\n     */\r\n    private _modifierRadius(): boolean {\r\n        return this.keysRadiusModifierAlt === this._altPressed && this.keysRadiusModifierCtrl === this._ctrlPressed && this.keysRadiusModifierShift === this._shiftPressed;\r\n    }\r\n}\r\n\r\n(<any>CameraInputTypes)[\"FollowCameraKeyboardMoveInput\"] = FollowCameraKeyboardMoveInput;\r\n"]}