{"version": 3, "file": "followCameraPointersInput.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Cameras/Inputs/followCameraPointersInput.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAElD,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AACrE,OAAO,EAAE,uBAAuB,EAAE,MAAM,8CAA8C,CAAC;AAGvF;;;GAGG;AACH,MAAM,OAAO,yBAA0B,SAAQ,uBAAuB;IAAtE;;QAcI;;;;WAIG;QAEI,wBAAmB,GAAG,CAAC,CAAC;QAE/B;;;;WAIG;QAEI,wBAAmB,GAAG,CAAC,CAAC;QAE/B;;;WAGG;QAEI,mBAAc,GAAG,OAAO,CAAC;QAEhC;;;;;WAKG;QAEI,yBAAoB,GAAG,CAAC,CAAC;QAEhC;;WAEG;QAEI,uBAAkB,GAAY,KAAK,CAAC;QAE3C;;WAEG;QAEI,uBAAkB,GAAY,KAAK,CAAC;QAE3C;;WAEG;QAEI,yBAAoB,GAAY,IAAI,CAAC;QAE5C;;WAEG;QAEI,uBAAkB,GAAY,KAAK,CAAC;QAE3C;;WAEG;QAEI,uBAAkB,GAAY,IAAI,CAAC;QAE1C;;WAEG;QAEI,yBAAoB,GAAY,KAAK,CAAC;QAE7C;;WAEG;QAEI,2BAAsB,GAAY,IAAI,CAAC;QAE9C;;WAEG;QAEI,2BAAsB,GAAY,KAAK,CAAC;QAE/C;;WAEG;QAEI,6BAAwB,GAAY,KAAK,CAAC;QAEjD;;WAEG;QACI,kBAAa,GAAY,IAAI,CAAC;QAsErC,yCAAyC;QACjC,oBAAe,GAAW,CAAC,CAAC;IA2BxC,CAAC;IAnMG;;;OAGG;IACI,YAAY;QACf,OAAO,2BAA2B,CAAC;IACvC,CAAC;IA6FM,OAAO,CAAC,MAA8B,EAAE,OAAe,EAAE,OAAe;QAC3E,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEhB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC;SACpE;aAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAClC,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC;SACpE;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC;SAClE;aAAM,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAChC,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC;SAClE;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC;SAC5D;aAAM,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAChC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC;SAC5D;IACL,CAAC;IAEM,YAAY,CACf,MAA8B,EAC9B,MAA8B,EAC9B,4BAAoC,EACpC,oBAA4B,EAC5B,6BAAqD,EACrD,qBAA6C;QAE7C,IAAI,4BAA4B,KAAK,CAAC,IAAI,6BAA6B,KAAK,IAAI,EAAE;YAC9E,kDAAkD;YAClD,2CAA2C;YAC3C,oEAAoE;YACpE,OAAO;SACV;QACD,IAAI,oBAAoB,KAAK,CAAC,IAAI,qBAAqB,KAAK,IAAI,EAAE;YAC9D,yDAAyD;YACzD,OAAO;SACV;QACD,IAAI,UAAU,GAAG,CAAC,oBAAoB,GAAG,4BAA4B,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAE7J,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,UAAU,IAAI,IAAI,GAAG,IAAI,CAAC,oBAAoB,CAAC;YAC/C,IAAI,IAAI,CAAC,wBAAwB,EAAE;gBAC/B,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;aACzE;YACD,IAAI,IAAI,CAAC,sBAAsB,EAAE;gBAC7B,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;aACrE;YACD,IAAI,IAAI,CAAC,sBAAsB,EAAE;gBAC7B,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;aACzD;SACJ;aAAM;YACH,IAAI,IAAI,CAAC,wBAAwB,EAAE;gBAC/B,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,UAAU,CAAC;aAC5C;YAED,IAAI,IAAI,CAAC,sBAAsB,EAAE;gBAC7B,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,UAAU,CAAC;aAC1C;YAED,IAAI,IAAI,CAAC,sBAAsB,EAAE;gBAC7B,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,UAAU,CAAC;aACpC;SACJ;IACL,CAAC;IAIO,QAAQ;QACZ,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,eAAe,EAAE,GAAG,GAAG,KAAK,CAAC,EAAE;YAC3D,OAAO;SACV;QACD,MAAM,IAAI,GACN,qDAAqD,GAAG,+DAA+D,GAAG,sCAAsC,CAAC;QAErK,OAAO,CAAC,MAAM,CACQ,IAAI,CAAC,oBAAqB,GAAqB,IAAI,CAAC,kBAAmB,GAAqB,IAAI,CAAC,kBAAmB,IAAI,CAAC,EAC3I,IAAI,GAAG,wBAAwB,GAAG,IAAI,CAAC,oBAAoB,GAAG,wBAAwB,GAAG,IAAI,CAAC,kBAAkB,GAAG,wBAAwB,GAAG,IAAI,CAAC,kBAAkB,CACxK,CAAC;QACF,OAAO,CAAC,MAAM,CACQ,IAAI,CAAC,oBAAqB,GAAqB,IAAI,CAAC,kBAAmB,GAAqB,IAAI,CAAC,kBAAmB,IAAI,CAAC,EAC3I,IAAI,GAAG,wBAAwB,GAAG,IAAI,CAAC,oBAAoB,GAAG,wBAAwB,GAAG,IAAI,CAAC,kBAAkB,GAAG,wBAAwB,GAAG,IAAI,CAAC,kBAAkB,CACxK,CAAC;QACF,OAAO,CAAC,MAAM,CACQ,IAAI,CAAC,wBAAyB,GAAqB,IAAI,CAAC,sBAAuB,GAAqB,IAAI,CAAC,sBAAuB,IAAI,CAAC,EACvJ,IAAI;YACA,4BAA4B;YAC5B,IAAI,CAAC,wBAAwB;YAC7B,4BAA4B;YAC5B,IAAI,CAAC,sBAAsB;YAC3B,4BAA4B;YAC5B,IAAI,CAAC,sBAAsB,CAClC,CAAC;IACN,CAAC;CACJ;AArLG;IADC,SAAS,EAAE;sEACmB;AAQ/B;IADC,SAAS,EAAE;sEACmB;AAO/B;IADC,SAAS,EAAE;iEACoB;AAShC;IADC,SAAS,EAAE;uEACoB;AAMhC;IADC,SAAS,EAAE;qEAC+B;AAM3C;IADC,SAAS,EAAE;qEAC+B;AAM3C;IADC,SAAS,EAAE;uEACgC;AAM5C;IADC,SAAS,EAAE;qEAC+B;AAM3C;IADC,SAAS,EAAE;qEAC8B;AAM1C;IADC,SAAS,EAAE;uEACiC;AAM7C;IADC,SAAS,EAAE;yEACkC;AAM9C;IADC,SAAS,EAAE;yEACmC;AAM/C;IADC,SAAS,EAAE;2EACqC;AAwG/C,gBAAiB,CAAC,2BAA2B,CAAC,GAAG,yBAAyB,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { serialize } from \"../../Misc/decorators\";\r\nimport type { FollowCamera } from \"../../Cameras/followCamera\";\r\nimport { CameraInputTypes } from \"../../Cameras/cameraInputsManager\";\r\nimport { BaseCameraPointersInput } from \"../../Cameras/Inputs/BaseCameraPointersInput\";\r\nimport type { PointerTouch } from \"../../Events/pointerEvents\";\r\n\r\n/**\r\n * Manage the pointers inputs to control an follow camera.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n */\r\nexport class FollowCameraPointersInput extends BaseCameraPointersInput {\r\n    /**\r\n     * Defines the camera the input is attached to.\r\n     */\r\n    public camera: FollowCamera;\r\n\r\n    /**\r\n     * Gets the class name of the current input.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"FollowCameraPointersInput\";\r\n    }\r\n\r\n    /**\r\n     * Defines the pointer angular sensibility along the X axis or how fast is\r\n     * the camera rotating.\r\n     * A negative number will reverse the axis direction.\r\n     */\r\n    @serialize()\r\n    public angularSensibilityX = 1;\r\n\r\n    /**\r\n     * Defines the pointer angular sensibility along the Y axis or how fast is\r\n     * the camera rotating.\r\n     * A negative number will reverse the axis direction.\r\n     */\r\n    @serialize()\r\n    public angularSensibilityY = 1;\r\n\r\n    /**\r\n     * Defines the pointer pinch precision or how fast is the camera zooming.\r\n     * A negative number will reverse the axis direction.\r\n     */\r\n    @serialize()\r\n    public pinchPrecision = 10000.0;\r\n\r\n    /**\r\n     * pinchDeltaPercentage will be used instead of pinchPrecision if different\r\n     * from 0.\r\n     * It defines the percentage of current camera.radius to use as delta when\r\n     * pinch zoom is used.\r\n     */\r\n    @serialize()\r\n    public pinchDeltaPercentage = 0;\r\n\r\n    /**\r\n     * Pointer X axis controls zoom. (X axis modifies camera.radius value.)\r\n     */\r\n    @serialize()\r\n    public axisXControlRadius: boolean = false;\r\n\r\n    /**\r\n     * Pointer X axis controls height. (X axis modifies camera.heightOffset value.)\r\n     */\r\n    @serialize()\r\n    public axisXControlHeight: boolean = false;\r\n\r\n    /**\r\n     * Pointer X axis controls angle. (X axis modifies camera.rotationOffset value.)\r\n     */\r\n    @serialize()\r\n    public axisXControlRotation: boolean = true;\r\n\r\n    /**\r\n     * Pointer Y axis controls zoom. (Y axis modifies camera.radius value.)\r\n     */\r\n    @serialize()\r\n    public axisYControlRadius: boolean = false;\r\n\r\n    /**\r\n     * Pointer Y axis controls height. (Y axis modifies camera.heightOffset value.)\r\n     */\r\n    @serialize()\r\n    public axisYControlHeight: boolean = true;\r\n\r\n    /**\r\n     * Pointer Y axis controls angle. (Y axis modifies camera.rotationOffset value.)\r\n     */\r\n    @serialize()\r\n    public axisYControlRotation: boolean = false;\r\n\r\n    /**\r\n     * Pinch controls zoom. (Pinch modifies camera.radius value.)\r\n     */\r\n    @serialize()\r\n    public axisPinchControlRadius: boolean = true;\r\n\r\n    /**\r\n     * Pinch controls height. (Pinch modifies camera.heightOffset value.)\r\n     */\r\n    @serialize()\r\n    public axisPinchControlHeight: boolean = false;\r\n\r\n    /**\r\n     * Pinch controls angle. (Pinch modifies camera.rotationOffset value.)\r\n     */\r\n    @serialize()\r\n    public axisPinchControlRotation: boolean = false;\r\n\r\n    /**\r\n     * Log error messages if basic misconfiguration has occurred.\r\n     */\r\n    public warningEnable: boolean = true;\r\n\r\n    public onTouch(pointA: Nullable<PointerTouch>, offsetX: number, offsetY: number): void {\r\n        this._warning();\r\n\r\n        if (this.axisXControlRotation) {\r\n            this.camera.rotationOffset += offsetX / this.angularSensibilityX;\r\n        } else if (this.axisYControlRotation) {\r\n            this.camera.rotationOffset += offsetY / this.angularSensibilityX;\r\n        }\r\n\r\n        if (this.axisXControlHeight) {\r\n            this.camera.heightOffset += offsetX / this.angularSensibilityY;\r\n        } else if (this.axisYControlHeight) {\r\n            this.camera.heightOffset += offsetY / this.angularSensibilityY;\r\n        }\r\n\r\n        if (this.axisXControlRadius) {\r\n            this.camera.radius -= offsetX / this.angularSensibilityY;\r\n        } else if (this.axisYControlRadius) {\r\n            this.camera.radius -= offsetY / this.angularSensibilityY;\r\n        }\r\n    }\r\n\r\n    public onMultiTouch(\r\n        pointA: Nullable<PointerTouch>,\r\n        pointB: Nullable<PointerTouch>,\r\n        previousPinchSquaredDistance: number,\r\n        pinchSquaredDistance: number,\r\n        previousMultiTouchPanPosition: Nullable<PointerTouch>,\r\n        multiTouchPanPosition: Nullable<PointerTouch>\r\n    ): void {\r\n        if (previousPinchSquaredDistance === 0 && previousMultiTouchPanPosition === null) {\r\n            // First time this method is called for new pinch.\r\n            // Next time this is called there will be a\r\n            // previousPinchSquaredDistance and pinchSquaredDistance to compare.\r\n            return;\r\n        }\r\n        if (pinchSquaredDistance === 0 && multiTouchPanPosition === null) {\r\n            // Last time this method is called at the end of a pinch.\r\n            return;\r\n        }\r\n        let pinchDelta = (pinchSquaredDistance - previousPinchSquaredDistance) / ((this.pinchPrecision * (this.angularSensibilityX + this.angularSensibilityY)) / 2);\r\n\r\n        if (this.pinchDeltaPercentage) {\r\n            pinchDelta *= 0.01 * this.pinchDeltaPercentage;\r\n            if (this.axisPinchControlRotation) {\r\n                this.camera.rotationOffset += pinchDelta * this.camera.rotationOffset;\r\n            }\r\n            if (this.axisPinchControlHeight) {\r\n                this.camera.heightOffset += pinchDelta * this.camera.heightOffset;\r\n            }\r\n            if (this.axisPinchControlRadius) {\r\n                this.camera.radius -= pinchDelta * this.camera.radius;\r\n            }\r\n        } else {\r\n            if (this.axisPinchControlRotation) {\r\n                this.camera.rotationOffset += pinchDelta;\r\n            }\r\n\r\n            if (this.axisPinchControlHeight) {\r\n                this.camera.heightOffset += pinchDelta;\r\n            }\r\n\r\n            if (this.axisPinchControlRadius) {\r\n                this.camera.radius -= pinchDelta;\r\n            }\r\n        }\r\n    }\r\n\r\n    /* Check for obvious misconfiguration. */\r\n    private _warningCounter: number = 0;\r\n    private _warning(): void {\r\n        if (!this.warningEnable || this._warningCounter++ % 100 !== 0) {\r\n            return;\r\n        }\r\n        const warn =\r\n            \"It probably only makes sense to control ONE camera \" + \"property with each pointer axis. Set 'warningEnable = false' \" + \"if you are sure. Currently enabled: \";\r\n\r\n        console.assert(\r\n            <number>(<unknown>this.axisXControlRotation) + <number>(<unknown>this.axisXControlHeight) + <number>(<unknown>this.axisXControlRadius) <= 1,\r\n            warn + \"axisXControlRotation: \" + this.axisXControlRotation + \", axisXControlHeight: \" + this.axisXControlHeight + \", axisXControlRadius: \" + this.axisXControlRadius\r\n        );\r\n        console.assert(\r\n            <number>(<unknown>this.axisYControlRotation) + <number>(<unknown>this.axisYControlHeight) + <number>(<unknown>this.axisYControlRadius) <= 1,\r\n            warn + \"axisYControlRotation: \" + this.axisYControlRotation + \", axisYControlHeight: \" + this.axisYControlHeight + \", axisYControlRadius: \" + this.axisYControlRadius\r\n        );\r\n        console.assert(\r\n            <number>(<unknown>this.axisPinchControlRotation) + <number>(<unknown>this.axisPinchControlHeight) + <number>(<unknown>this.axisPinchControlRadius) <= 1,\r\n            warn +\r\n                \"axisPinchControlRotation: \" +\r\n                this.axisPinchControlRotation +\r\n                \", axisPinchControlHeight: \" +\r\n                this.axisPinchControlHeight +\r\n                \", axisPinchControlRadius: \" +\r\n                this.axisPinchControlRadius\r\n        );\r\n    }\r\n}\r\n(<any>CameraInputTypes)[\"FollowCameraPointersInput\"] = FollowCameraPointersInput;\r\n"]}