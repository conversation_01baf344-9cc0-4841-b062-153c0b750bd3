{"version": 3, "file": "touchCamera.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Cameras/touchCamera.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAI1C,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAE/B,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IACnD,OAAO,GAAG,EAAE,CAAC,IAAI,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;AAC9D,CAAC,CAAC,CAAC;AAEH;;;;GAIG;AACH,MAAM,OAAO,WAAY,SAAQ,UAAU;IACvC;;;OAGG;IACH,IAAW,uBAAuB;QAC9B,MAAM,KAAK,GAAyB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAClE,IAAI,KAAK,EAAE;YACP,OAAO,KAAK,CAAC,uBAAuB,CAAC;SACxC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAW,uBAAuB,CAAC,KAAa;QAC5C,MAAM,KAAK,GAAyB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAClE,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,uBAAuB,GAAG,KAAK,CAAC;SACzC;IACL,CAAC;IAED;;;OAGG;IACH,IAAW,oBAAoB;QAC3B,MAAM,KAAK,GAAyB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAClE,IAAI,KAAK,EAAE;YACP,OAAO,KAAK,CAAC,oBAAoB,CAAC;SACrC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAW,oBAAoB,CAAC,KAAa;QACzC,MAAM,KAAK,GAAyB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAClE,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,oBAAoB,GAAG,KAAK,CAAC;SACtC;IACL,CAAC;IAED;;;;;;;;OAQG;IACH,YAAY,IAAY,EAAE,QAAiB,EAAE,KAAa;QACtD,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;QAEvB,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,aAAa,CAAC;IACzB,CAAC;IAED,gBAAgB;IACT,YAAY;QACf,MAAM,KAAK,GAAyB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAClE,MAAM,KAAK,GAAyB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAClE,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC;SAC9B;aAAM;YACH,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC;SAC3B;IACL,CAAC;CACJ", "sourcesContent": ["import { FreeCamera } from \"./freeCamera\";\r\nimport type { FreeCameraTouchInput } from \"../Cameras/Inputs/freeCameraTouchInput\";\r\nimport type { FreeCameraMouseInput } from \"../Cameras/Inputs/freeCameraMouseInput\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport { Node } from \"../node\";\r\n\r\nNode.AddNodeConstructor(\"TouchCamera\", (name, scene) => {\r\n    return () => new TouchCamera(name, Vector3.Zero(), scene);\r\n});\r\n\r\n/**\r\n * This represents a FPS type of camera controlled by touch.\r\n * This is like a universal camera minus the Gamepad controls.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#universal-camera\r\n */\r\nexport class TouchCamera extends FreeCamera {\r\n    /**\r\n     * Defines the touch sensibility for rotation.\r\n     * The higher the faster.\r\n     */\r\n    public get touchAngularSensibility(): number {\r\n        const touch = <FreeCameraTouchInput>this.inputs.attached[\"touch\"];\r\n        if (touch) {\r\n            return touch.touchAngularSensibility;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    public set touchAngularSensibility(value: number) {\r\n        const touch = <FreeCameraTouchInput>this.inputs.attached[\"touch\"];\r\n        if (touch) {\r\n            touch.touchAngularSensibility = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Defines the touch sensibility for move.\r\n     * The higher the faster.\r\n     */\r\n    public get touchMoveSensibility(): number {\r\n        const touch = <FreeCameraTouchInput>this.inputs.attached[\"touch\"];\r\n        if (touch) {\r\n            return touch.touchMoveSensibility;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    public set touchMoveSensibility(value: number) {\r\n        const touch = <FreeCameraTouchInput>this.inputs.attached[\"touch\"];\r\n        if (touch) {\r\n            touch.touchMoveSensibility = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Instantiates a new touch camera.\r\n     * This represents a FPS type of camera controlled by touch.\r\n     * This is like a universal camera minus the Gamepad controls.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#universal-camera\r\n     * @param name Define the name of the camera in the scene\r\n     * @param position Define the start position of the camera in the scene\r\n     * @param scene Define the scene the camera belongs to\r\n     */\r\n    constructor(name: string, position: Vector3, scene?: Scene) {\r\n        super(name, position, scene);\r\n        this.inputs.addTouch();\r\n\r\n        this._setupInputs();\r\n    }\r\n\r\n    /**\r\n     * Gets the current object class name.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"TouchCamera\";\r\n    }\r\n\r\n    /** @internal */\r\n    public _setupInputs() {\r\n        const touch = <FreeCameraTouchInput>this.inputs.attached[\"touch\"];\r\n        const mouse = <FreeCameraMouseInput>this.inputs.attached[\"mouse\"];\r\n        if (mouse) {\r\n            mouse.touchEnabled = false;\r\n        } else {\r\n            touch.allowMouse = true;\r\n        }\r\n    }\r\n}\r\n"]}