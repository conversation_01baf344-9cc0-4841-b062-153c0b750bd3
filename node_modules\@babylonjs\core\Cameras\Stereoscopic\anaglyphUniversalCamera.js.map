{"version": 3, "file": "anaglyphUniversalCamera.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Cameras/Stereoscopic/anaglyphUniversalCamera.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAEhE,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAElC,OAAO,EAAE,8BAA8B,EAAE,MAAM,yCAAyC,CAAC;AAEzF,IAAI,CAAC,kBAAkB,CAAC,yBAAyB,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;IACxE,OAAO,GAAG,EAAE,CAAC,IAAI,uBAAuB,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;AACvG,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACH,MAAM,OAAO,uBAAwB,SAAQ,eAAe;IACxD;;;;;;OAMG;IACH,YAAY,IAAY,EAAE,QAAiB,EAAE,kBAA0B,EAAE,KAAa;QAClF,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAavB,gBAAW,GAAG,8BAA8B,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAZpE,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,8BAA8B,EAAE,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,CAAC,CAAC;IAC7G,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,yBAAyB,CAAC;IACrC,CAAC;CAGJ", "sourcesContent": ["import { Camera } from \"../../Cameras/camera\";\r\nimport { UniversalCamera } from \"../../Cameras/universalCamera\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport { Node } from \"../../node\";\r\n\r\nimport { setStereoscopicAnaglyphRigMode } from \"../RigModes/stereoscopicAnaglyphRigMode\";\r\n\r\nNode.AddNodeConstructor(\"AnaglyphUniversalCamera\", (name, scene, options) => {\r\n    return () => new AnaglyphUniversalCamera(name, Vector3.Zero(), options.interaxial_distance, scene);\r\n});\r\n\r\n/**\r\n * Camera used to simulate anaglyphic rendering (based on UniversalCamera)\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#anaglyph-cameras\r\n */\r\nexport class AnaglyphUniversalCamera extends UniversalCamera {\r\n    /**\r\n     * Creates a new AnaglyphUniversalCamera\r\n     * @param name defines camera name\r\n     * @param position defines initial position\r\n     * @param interaxialDistance defines distance between each color axis\r\n     * @param scene defines the hosting scene\r\n     */\r\n    constructor(name: string, position: Vector3, interaxialDistance: number, scene?: Scene) {\r\n        super(name, position, scene);\r\n        this.interaxialDistance = interaxialDistance;\r\n        this.setCameraRigMode(Camera.RIG_MODE_STEREOSCOPIC_ANAGLYPH, { interaxialDistance: interaxialDistance });\r\n    }\r\n\r\n    /**\r\n     * Gets camera class name\r\n     * @returns AnaglyphUniversalCamera\r\n     */\r\n    public getClassName(): string {\r\n        return \"AnaglyphUniversalCamera\";\r\n    }\r\n\r\n    protected _setRigMode = setStereoscopicAnaglyphRigMode.bind(null, this);\r\n}\r\n"]}