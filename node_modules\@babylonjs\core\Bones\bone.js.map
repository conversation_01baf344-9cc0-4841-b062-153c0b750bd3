{"version": 3, "file": "bone.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Bones/bone.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAC/E,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAGhD,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAC/B,OAAO,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AAK3C;;;GAGG;AACH,MAAM,OAAO,IAAK,SAAQ,IAAI;IA8C1B,gBAAgB;IAChB,IAAI,OAAO;QACP,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,gBAAgB;IAChB,IAAI,OAAO,CAAC,KAAa;QACrB,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,CAAC,sCAAsC;QAEnE,oCAAoC;QACpC,IAAI,KAAK,CAAC,UAAU,KAAK,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;YACnD,OAAO;SACV;QAED,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAED;;;;;;;;;OASG;IACH;IACI;;OAEG;IACI,IAAY,EACnB,QAAkB,EAClB,aAA6B,IAAI,EACjC,cAAgC,IAAI,EACpC,WAA6B,IAAI,EACjC,aAA+B,IAAI,EACnC,QAA0B,IAAI;QAE9B,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;QAR1B,SAAI,GAAJ,IAAI,CAAQ;QA1EvB;;WAEG;QACI,aAAQ,GAAG,IAAI,KAAK,EAAQ,CAAC;QAEpC,oDAAoD;QAC7C,eAAU,GAAG,IAAI,KAAK,EAAa,CAAC;QAO3C;;;;WAIG;QACI,WAAM,GAAqB,IAAI,CAAC;QAM/B,uBAAkB,GAAG,IAAI,MAAM,EAAE,CAAC;QAClC,+BAA0B,GAAG,IAAI,MAAM,EAAE,CAAC;QAC1C,wBAAmB,GAAG,CAAC,CAAC;QACxB,oBAAe,GAAG,IAAI,MAAM,EAAE,CAAC;QAK/B,qBAAgB,GAAG,IAAI,CAAC;QACxB,mBAAc,GAAG,KAAK,CAAC;QAE/B,gBAAgB;QACT,yBAAoB,GAA4B,IAAI,CAAC;QAE5D,gBAAgB;QACT,4BAAuB,GAAqB,IAAI,CAAC;QA4CpD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;QAC1E,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QACjE,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QACvE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE1B,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAElC,IAAI,UAAU,IAAI,WAAW,EAAE;YAC3B,IAAI,CAAC,uBAAuB,EAAE,CAAC;SAClC;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,UAAU;IAEV;;;OAGG;IACI,WAAW;QACd,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,WAAmB,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;;OAGG;IACI,QAAQ;QACX,OAAO,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IACvF,CAAC;IAED,IAAW,MAAM,CAAC,SAAyB;QACvC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,MAAsB,EAAE,yBAAkC,IAAI;QAC3E,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE;YACxB,OAAO;SACV;QAED,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACjD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;gBACd,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aACzC;SACJ;QAED,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC;QAE1B,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACnC;QAED,IAAI,sBAAsB,EAAE;YACxB,IAAI,CAAC,uBAAuB,EAAE,CAAC;SAClC;QAED,IAAI,CAAC,WAAW,EAAE,CAAC;IACvB,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,MAAc;QAC7B,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACI,WAAW;QACd,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,MAAc;QAC7B,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,cAAc;QACjB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,YAAY;;QACf,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,aAAa,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,aAAa,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAE5C,IAAI,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,YAAY,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;YAEzE,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YAC3D,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,GAAG,MAAA,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,mCAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;YACrH,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YACrE,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;SAC5D;aAAM;YACH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC;SACjC;IACL,CAAC;IAED;;;;OAIG;IACI,4BAA4B;QAC/B,OAAO,IAAI,CAAC,0BAA0B,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACI,oBAAoB;QACvB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,aAAsC;QAC3D,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,SAAS,CAAC,gCAAgC,EAAE,CAAC;SACrD;QAED,IAAI,CAAC,oBAAoB,GAAG,aAAa,CAAC;QAE1C,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,SAAS,CAAC,gCAAgC,EAAE,CAAC;SACrD;IACL,CAAC;IAED,gDAAgD;IAEhD;;;OAGG;IACI,gBAAgB;QACnB,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED,qDAAqD;IACrD,IAAW,QAAQ;QACf,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,QAAQ,CAAC,WAAoB;QACpC,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAE1C,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAClC,CAAC;IAED,qDAAqD;IACrD,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;IAC9B,CAAC;IAED,IAAW,QAAQ,CAAC,WAAoB;QACpC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IAClC,CAAC;IAED,gEAAgE;IAChE,IAAW,kBAAkB;QACzB,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,kBAAkB,CAAC,WAAuB;QACjD,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC;IAC5C,CAAC;IAED,oDAAoD;IACpD,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;IAC3B,CAAC;IAED,IAAW,OAAO,CAAC,UAAmB;QAClC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,2BAA2B;QAClC,OAAO,IAAI,CAAC,SAAS,CAAC,2BAA2B,CAAC;IACtD,CAAC;IAED,UAAU;IACF,UAAU;QACd,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO;SACV;QAED,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;YACpC,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,IAAI,EAAE,CAAC;YACxC,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;SACxC;QACD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IAC9F,CAAC;IAEO,QAAQ;QACZ,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;YAC5B,OAAO;SACV;QAED,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;IACzG,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,MAAc,EAAE,sBAAsB,GAAG,IAAI,EAAE,iBAAiB,GAAG,IAAI;QACvF,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAElC,IAAI,sBAAsB,EAAE;YACxB,IAAI,CAAC,uBAAuB,EAAE,CAAC;SAClC;QAED,IAAI,iBAAiB,EAAE;YACnB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;SACzB;aAAM;YACH,IAAI,CAAC,WAAW,EAAE,CAAC;SACtB;IACL,CAAC;IAED;;OAEG;IACI,uBAAuB,CAAC,UAAmB,EAAE,cAAc,GAAG,IAAI;QACrE,IAAI,CAAC,UAAU,EAAE;YACb,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;SACjC;QAED,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;SACrF;aAAM;YACH,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;SAChD;QAED,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAErE,IAAI,cAAc,EAAE;YAChB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBACvD,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,CAAC;aAClD;SACJ;QAED,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClF,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,gBAAgB;IACT,sBAAsB;QACzB,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC/B,CAAC;IAEO,wBAAwB;QAC5B,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,GAAY,EAAE,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,KAAqB;QACrE,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEjC,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;YACtB,EAAE,CAAC,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;YACzB,EAAE,CAAC,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;YACzB,EAAE,CAAC,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;SAC5B;aAAM;YACH,IAAI,EAAE,GAAqB,IAAI,CAAC;YAEhC,uFAAuF;YACvF,IAAI,KAAK,EAAE;gBACP,EAAE,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;aAC/B;YAED,IAAI,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC;YAC3C,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAE9B,IAAI,IAAI,CAAC,MAAM,EAAE;gBACb,IAAI,KAAK,IAAI,EAAE,EAAE;oBACb,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC,CAAC;oBAClD,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;iBAChC;qBAAM;oBACH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC,CAAC;iBACrD;aACJ;iBAAM;gBACH,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;aAC9B;YAED,IAAI,CAAC,wBAAwB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvC,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,OAAO,CAAC,yBAAyB,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAEnD,EAAE,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1B,EAAE,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1B,EAAE,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;SAC7B;QAED,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAED;;;;;OAKG;IACI,WAAW,CAAC,QAAiB,EAAE,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,KAAqB;QAC5E,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEjC,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;YACtB,EAAE,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;SACnE;aAAM;YACH,IAAI,EAAE,GAAqB,IAAI,CAAC;YAEhC,uFAAuF;YACvF,IAAI,KAAK,EAAE;gBACP,EAAE,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;aAC/B;YAED,IAAI,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC;YAE3C,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAE7B,IAAI,IAAI,CAAC,MAAM,EAAE;gBACb,IAAI,KAAK,IAAI,EAAE,EAAE;oBACb,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC,CAAC;oBAClD,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;iBAChC;qBAAM;oBACH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC,CAAC;iBACrD;gBACD,IAAI,CAAC,MAAM,EAAE,CAAC;aACjB;iBAAM;gBACH,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;aAC9B;YAED,OAAO,CAAC,yBAAyB,CAAC,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;YACvD,EAAE,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;SACpD;QAED,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,QAAiB,EAAE,KAAqB;QAC/D,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACnD,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,aAAa,GAAG,KAAK;QAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAErC,mDAAmD;QACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAClC,MAAM,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;QACvC,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAEvC,8DAA8D;QAC9D,QAAQ,CAAC,MAAM,EAAE,CAAC;QAElB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC/B,MAAM,EAAE,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;YAClC,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YAC/B,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAC1B,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAC1B,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAE1B,KAAK,CAAC,wBAAwB,EAAE,CAAC;SACpC;QAED,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAEhC,IAAI,aAAa,EAAE;YACf,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE;gBAC/B,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;aACvC;SACJ;IACL,CAAC;IAED;;;OAGG;IACI,QAAQ,CAAC,KAAc;QAC1B,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,QAAQ;QACX,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,MAAe;QAChC,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;;OAOG;IACI,eAAe,CAAC,GAAW,EAAE,KAAa,EAAE,IAAY,EAAE,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,KAAqB;QACvG,IAAI,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE;YACvB,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC3B,UAAU,CAAC,yBAAyB,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAC7D,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAC/C,OAAO;SACV;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE;YACnD,OAAO;SACV;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,CAAC,yBAAyB,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QAE3D,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACxC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACjD,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,IAAa,EAAE,MAAc,EAAE,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,KAAqB;QACnF,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,wBAAwB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACvC,MAAM,CAAC,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;;OAMG;IACI,YAAY,CAAC,IAAa,EAAE,KAAa,EAAE,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,KAAqB;QACxF,IAAI,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE;YACvB,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC3B,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAEhD,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAC/C,OAAO;SACV;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE;YACnD,OAAO;SACV;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAE9C,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACxC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACjD,CAAC;IAED;;;;;OAKG;IACI,WAAW,CAAC,QAAiB,EAAE,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,KAAqB;QAC5E,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAC3E,CAAC;IAED;;;;;OAKG;IACI,qBAAqB,CAAC,IAAgB,EAAE,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,KAAqB;QACrF,IAAI,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE;YACvB,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEnC,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAE9B,OAAO;SACV;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE;YACnD,OAAO;SACV;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,CAAC,mBAAmB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAEzC,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAExC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACjD,CAAC;IAED;;;;;OAKG;IACI,iBAAiB,CAAC,MAAc,EAAE,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,KAAqB;QAC/E,IAAI,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE;YACvB,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC3B,UAAU,CAAC,uBAAuB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YACjD,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAC/C,OAAO;SACV;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE;YACnD,OAAO;SACV;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAEzB,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAEzC,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC;IAEO,iBAAiB,CAAC,IAAY,EAAE,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,KAAqB;QAC9E,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACnC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACtB,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACtB,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACrC,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAExC,IAAI,MAAM,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;YAChC,IAAI,KAAK,EAAE;gBACP,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC;gBAC7C,MAAM,CAAC,oBAAoB,EAAE,CAAC,aAAa,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;aACzE;iBAAM;gBACH,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC,CAAC;aACvD;YACD,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YACrC,cAAc,CAAC,MAAM,EAAE,CAAC;YACxB,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC/B,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;SAC5C;aAAM;YACH,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,EAAE;gBAC/B,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC;gBAC7C,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;gBACrC,cAAc,CAAC,MAAM,EAAE,CAAC;gBACxB,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;gBACtC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAC/B,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;aAC5C;iBAAM;gBACH,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;aAClC;SACJ;QAED,IAAI,CAAC,wBAAwB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAE1C,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAEO,yBAAyB,CAAC,SAAiB,EAAE,KAAqB;QACtE,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACrC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC;QAEhD,IAAI,KAAK,EAAE;YACP,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,cAAc,EAAE,EAAE,SAAS,CAAC,CAAC;YAC3D,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;SACvF;aAAM;YACH,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;SACrC;QAED,SAAS,CAAC,MAAM,EAAE,CAAC;QACnB,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACvB,2BAA2B;YAC3B,gDAAgD;YAChD,OAAO,KAAK,CAAC;SAChB;QAED,WAAW,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACzD,SAAS,CAAC,aAAa,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QAEhD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,QAAiC,IAAI;QACzE,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE3B,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;QAEzC,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;;;OAKG;IACI,gBAAgB,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,KAA8B,EAAE,MAAe;QACxF,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;YACtB,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAEjC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACpB,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACpB,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;SACvB;aAAM;YACH,IAAI,EAAE,GAAqB,IAAI,CAAC;YAEhC,uFAAuF;YACvF,IAAI,KAAK,EAAE;gBACP,EAAE,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;aAC/B;YAED,IAAI,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC;YAE3C,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAE5B,IAAI,KAAK,IAAI,EAAE,EAAE;gBACb,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC;gBAC3C,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;aAChC;iBAAM;gBACH,IAAI,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;aACtC;YAED,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACtB,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACtB,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;SACzB;IACL,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,QAAiC,IAAI;QAC5D,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE3B,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;QAE/C,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;;OAIG;IACI,wBAAwB,CAAC,KAAoB,EAAE,MAAe;QACjE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACI,yBAAyB;QAC5B,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEhB,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAC5F;aAAM;YACH,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAEpD,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;YAElD,IAAI,UAAU,EAAE;gBACZ,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;aAC9E;SACJ;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;QAE5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC1B,QAAQ,CAAC,CAAC,CAAC,CAAC,yBAAyB,EAAE,CAAC;SAC3C;IACL,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,SAAkB,EAAE,QAAiC,IAAI;QACzE,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE9B,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAEjD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;OAKG;IACI,iBAAiB,CAAC,SAAkB,EAAE,QAAiC,IAAI,EAAE,MAAe;QAC/F,IAAI,EAAE,GAAqB,IAAI,CAAC;QAEhC,uFAAuF;QACvF,IAAI,KAAK,EAAE;YACP,EAAE,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;SAC/B;QAED,IAAI,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC;QAE3C,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAE7B,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC;QAE1C,IAAI,KAAK,IAAI,EAAE,EAAE;YACb,GAAG,CAAC,aAAa,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;SAC9B;QAED,OAAO,CAAC,oBAAoB,CAAC,SAAS,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;QAErD,MAAM,CAAC,SAAS,EAAE,CAAC;IACvB,CAAC;IAED;;;;;OAKG;IACI,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,QAAiC,IAAI;QACzE,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE9B,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAE5C,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;OAKG;IACI,gBAAgB,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,QAAiC,IAAI,EAAE,MAAe;QAC/F,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE3B,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAEpD,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;IACpC,CAAC;IAED;;;;;OAKG;IACI,qBAAqB,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,QAAiC,IAAI;QACnF,MAAM,MAAM,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;QAErC,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAEtD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;OAKG;IACI,0BAA0B,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,QAAiC,IAAI,EAAE,MAAkB;QAC5G,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;YACtB,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACxC;aAAM;YACH,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAEzC,IAAI,KAAK,EAAE;gBACP,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,CAAC;aACnD;iBAAM;gBACH,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;aACtB;YAED,GAAG,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACjD,GAAG,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACjD,GAAG,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAEjD,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;SAC/C;IACL,CAAC;IAED;;;;;OAKG;IACI,iBAAiB,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,KAAoB;QAC9D,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QAEjC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAElD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;OAKG;IACI,sBAAsB,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,KAAoB,EAAE,MAAc;QACnF,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;YACtB,IAAI,CAAC,cAAc,EAAE,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;SACxD;aAAM;YACH,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAEzC,IAAI,KAAK,EAAE;gBACP,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,CAAC;aACnD;iBAAM;gBACH,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;aACtB;YAED,GAAG,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACjD,GAAG,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACjD,GAAG,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAEjD,GAAG,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;SACtC;IACL,CAAC;IAED;;;;;OAKG;IACI,4BAA4B,CAAC,QAAiB,EAAE,QAAiC,IAAI;QACxF,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE9B,IAAI,CAAC,iCAAiC,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAEhE,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;OAKG;IACI,iCAAiC,CAAC,QAAiB,EAAE,QAAiC,IAAI,EAAE,MAAe;QAC9G,IAAI,EAAE,GAAqB,IAAI,CAAC;QAEhC,uFAAuF;QACvF,IAAI,KAAK,EAAE;YACP,EAAE,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;SAC/B;QAED,IAAI,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC;QAE3C,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAE5B,IAAI,KAAK,IAAI,EAAE,EAAE;YACb,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC;YAC3C,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;SAChC;aAAM;YACH,IAAI,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;SACtC;QAED,OAAO,CAAC,yBAAyB,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;OAKG;IACI,4BAA4B,CAAC,QAAiB,EAAE,QAAiC,IAAI;QACxF,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE9B,IAAI,CAAC,iCAAiC,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAEhE,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;OAKG;IACI,iCAAiC,CAAC,QAAiB,EAAE,QAAiC,IAAI,EAAE,MAAe;QAC9G,IAAI,EAAE,GAAqB,IAAI,CAAC;QAEhC,uFAAuF;QACvF,IAAI,KAAK,EAAE;YACP,EAAE,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;SAC/B;QAED,IAAI,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC;QAE3C,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAE9B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC;QAE3C,IAAI,KAAK,IAAI,EAAE,EAAE;YACb,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;SAChC;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;QAEd,OAAO,CAAC,yBAAyB,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACI,oBAAoB;QACvB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IAC5C,CAAC;;AAtoCc,aAAQ,GAAc,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;AAC7D,aAAQ,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;AACjC,aAAQ,GAAa,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC", "sourcesContent": ["import type { Skeleton } from \"./skeleton\";\r\nimport { Vector3, Quaternion, Matrix, TmpVectors } from \"../Maths/math.vector\";\r\nimport { ArrayTools } from \"../Misc/arrayTools\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\nimport { Node } from \"../node\";\r\nimport { Space } from \"../Maths/math.axis\";\r\n\r\ndeclare type Animation = import(\"../Animations/animation\").Animation;\r\ndeclare type AnimationPropertiesOverride = import(\"../Animations/animationPropertiesOverride\").AnimationPropertiesOverride;\r\n\r\n/**\r\n * Class used to store bone information\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/bonesSkeletons\r\n */\r\nexport class Bone extends Node {\r\n    private static _TmpVecs: Vector3[] = ArrayTools.BuildArray(2, Vector3.Zero);\r\n    private static _TmpQuat = Quaternion.Identity();\r\n    private static _TmpMats: Matrix[] = ArrayTools.BuildArray(5, Matrix.Identity);\r\n\r\n    /**\r\n     * Gets the list of child bones\r\n     */\r\n    public children = new Array<Bone>();\r\n\r\n    /** Gets the animations associated with this bone */\r\n    public animations = new Array<Animation>();\r\n\r\n    /**\r\n     * Gets or sets bone length\r\n     */\r\n    public length: number;\r\n\r\n    /**\r\n     * @internal Internal only\r\n     * Set this value to map this bone to a different index in the transform matrices\r\n     * Set this value to -1 to exclude the bone from the transform matrices\r\n     */\r\n    public _index: Nullable<number> = null;\r\n\r\n    private _skeleton: Skeleton;\r\n    private _localMatrix: Matrix;\r\n    private _restPose: Matrix;\r\n    private _baseMatrix: Matrix;\r\n    private _absoluteTransform = new Matrix();\r\n    private _invertedAbsoluteTransform = new Matrix();\r\n    private _scalingDeterminant = 1;\r\n    private _worldTransform = new Matrix();\r\n\r\n    private _localScaling: Vector3;\r\n    private _localRotation: Quaternion;\r\n    private _localPosition: Vector3;\r\n    private _needToDecompose = true;\r\n    private _needToCompose = false;\r\n\r\n    /** @internal */\r\n    public _linkedTransformNode: Nullable<TransformNode> = null;\r\n\r\n    /** @internal */\r\n    public _waitingTransformNodeId: Nullable<string> = null;\r\n\r\n    /** @internal */\r\n    get _matrix(): Matrix {\r\n        this._compose();\r\n        return this._localMatrix;\r\n    }\r\n\r\n    /** @internal */\r\n    set _matrix(value: Matrix) {\r\n        this._needToCompose = false; // in case there was a pending compose\r\n\r\n        // skip if the matrices are the same\r\n        if (value.updateFlag === this._localMatrix.updateFlag) {\r\n            return;\r\n        }\r\n\r\n        this._localMatrix.copyFrom(value);\r\n        this._markAsDirtyAndDecompose();\r\n    }\r\n\r\n    /**\r\n     * Create a new bone\r\n     * @param name defines the bone name\r\n     * @param skeleton defines the parent skeleton\r\n     * @param parentBone defines the parent (can be null if the bone is the root)\r\n     * @param localMatrix defines the local matrix\r\n     * @param restPose defines the rest pose matrix\r\n     * @param baseMatrix defines the base matrix\r\n     * @param index defines index of the bone in the hierarchy\r\n     */\r\n    constructor(\r\n        /**\r\n         * defines the bone name\r\n         */\r\n        public name: string,\r\n        skeleton: Skeleton,\r\n        parentBone: Nullable<Bone> = null,\r\n        localMatrix: Nullable<Matrix> = null,\r\n        restPose: Nullable<Matrix> = null,\r\n        baseMatrix: Nullable<Matrix> = null,\r\n        index: Nullable<number> = null\r\n    ) {\r\n        super(name, skeleton.getScene());\r\n        this._skeleton = skeleton;\r\n        this._localMatrix = localMatrix ? localMatrix.clone() : Matrix.Identity();\r\n        this._restPose = restPose ? restPose : this._localMatrix.clone();\r\n        this._baseMatrix = baseMatrix ? baseMatrix : this._localMatrix.clone();\r\n        this._index = index;\r\n\r\n        skeleton.bones.push(this);\r\n\r\n        this.setParent(parentBone, false);\r\n\r\n        if (baseMatrix || localMatrix) {\r\n            this._updateDifferenceMatrix();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the current object class name.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"Bone\";\r\n    }\r\n\r\n    // Members\r\n\r\n    /**\r\n     * Gets the parent skeleton\r\n     * @returns a skeleton\r\n     */\r\n    public getSkeleton(): Skeleton {\r\n        return this._skeleton;\r\n    }\r\n\r\n    public get parent(): Bone {\r\n        return this._parentNode as Bone;\r\n    }\r\n\r\n    /**\r\n     * Gets parent bone\r\n     * @returns a bone or null if the bone is the root of the bone hierarchy\r\n     */\r\n    public getParent(): Nullable<Bone> {\r\n        return this.parent;\r\n    }\r\n\r\n    /**\r\n     * Returns an array containing the root bones\r\n     * @returns an array containing the root bones\r\n     */\r\n    public getChildren(): Array<Bone> {\r\n        return this.children;\r\n    }\r\n\r\n    /**\r\n     * Gets the node index in matrix array generated for rendering\r\n     * @returns the node index\r\n     */\r\n    public getIndex(): number {\r\n        return this._index === null ? this.getSkeleton().bones.indexOf(this) : this._index;\r\n    }\r\n\r\n    public set parent(newParent: Nullable<Bone>) {\r\n        this.setParent(newParent);\r\n    }\r\n\r\n    /**\r\n     * Sets the parent bone\r\n     * @param parent defines the parent (can be null if the bone is the root)\r\n     * @param updateDifferenceMatrix defines if the difference matrix must be updated\r\n     */\r\n    public setParent(parent: Nullable<Bone>, updateDifferenceMatrix: boolean = true): void {\r\n        if (this.parent === parent) {\r\n            return;\r\n        }\r\n\r\n        if (this.parent) {\r\n            const index = this.parent.children.indexOf(this);\r\n            if (index !== -1) {\r\n                this.parent.children.splice(index, 1);\r\n            }\r\n        }\r\n\r\n        this._parentNode = parent;\r\n\r\n        if (this.parent) {\r\n            this.parent.children.push(this);\r\n        }\r\n\r\n        if (updateDifferenceMatrix) {\r\n            this._updateDifferenceMatrix();\r\n        }\r\n\r\n        this.markAsDirty();\r\n    }\r\n\r\n    /**\r\n     * Gets the local matrix\r\n     * @returns a matrix\r\n     */\r\n    public getLocalMatrix(): Matrix {\r\n        this._compose();\r\n        return this._localMatrix;\r\n    }\r\n\r\n    /**\r\n     * Gets the base matrix (initial matrix which remains unchanged)\r\n     * @returns the base matrix (as known as bind pose matrix)\r\n     */\r\n    public getBaseMatrix(): Matrix {\r\n        return this._baseMatrix;\r\n    }\r\n\r\n    /**\r\n     * Gets the rest pose matrix\r\n     * @returns a matrix\r\n     */\r\n    public getRestPose(): Matrix {\r\n        return this._restPose;\r\n    }\r\n\r\n    /**\r\n     * Sets the rest pose matrix\r\n     * @param matrix the local-space rest pose to set for this bone\r\n     */\r\n    public setRestPose(matrix: Matrix): void {\r\n        this._restPose.copyFrom(matrix);\r\n    }\r\n\r\n    /**\r\n     * Gets the bind pose matrix\r\n     * @returns the bind pose matrix\r\n     * @deprecated Please use getBaseMatrix instead\r\n     */\r\n    public getBindPose(): Matrix {\r\n        return this._baseMatrix;\r\n    }\r\n\r\n    /**\r\n     * Sets the bind pose matrix\r\n     * @param matrix the local-space bind pose to set for this bone\r\n     * @deprecated Please use updateMatrix instead\r\n     */\r\n    public setBindPose(matrix: Matrix): void {\r\n        this.updateMatrix(matrix);\r\n    }\r\n\r\n    /**\r\n     * Gets a matrix used to store world matrix (ie. the matrix sent to shaders)\r\n     */\r\n    public getWorldMatrix(): Matrix {\r\n        return this._worldTransform;\r\n    }\r\n\r\n    /**\r\n     * Sets the local matrix to rest pose matrix\r\n     */\r\n    public returnToRest(): void {\r\n        if (this._linkedTransformNode) {\r\n            const localScaling = TmpVectors.Vector3[0];\r\n            const localRotation = TmpVectors.Quaternion[0];\r\n            const localPosition = TmpVectors.Vector3[1];\r\n\r\n            this.getRestPose().decompose(localScaling, localRotation, localPosition);\r\n\r\n            this._linkedTransformNode.position.copyFrom(localPosition);\r\n            this._linkedTransformNode.rotationQuaternion = this._linkedTransformNode.rotationQuaternion ?? Quaternion.Identity();\r\n            this._linkedTransformNode.rotationQuaternion.copyFrom(localRotation);\r\n            this._linkedTransformNode.scaling.copyFrom(localScaling);\r\n        } else {\r\n            this._matrix = this._restPose;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the inverse of the absolute transform matrix.\r\n     * This matrix will be multiplied by local matrix to get the difference matrix (ie. the difference between original state and current state)\r\n     * @returns a matrix\r\n     */\r\n    public getInvertedAbsoluteTransform(): Matrix {\r\n        return this._invertedAbsoluteTransform;\r\n    }\r\n\r\n    /**\r\n     * Gets the absolute transform matrix (ie base matrix * parent world matrix)\r\n     * @returns a matrix\r\n     */\r\n    public getAbsoluteTransform(): Matrix {\r\n        return this._absoluteTransform;\r\n    }\r\n\r\n    /**\r\n     * Links with the given transform node.\r\n     * The local matrix of this bone is copied from the transform node every frame.\r\n     * @param transformNode defines the transform node to link to\r\n     */\r\n    public linkTransformNode(transformNode: Nullable<TransformNode>): void {\r\n        if (this._linkedTransformNode) {\r\n            this._skeleton._numBonesWithLinkedTransformNode--;\r\n        }\r\n\r\n        this._linkedTransformNode = transformNode;\r\n\r\n        if (this._linkedTransformNode) {\r\n            this._skeleton._numBonesWithLinkedTransformNode++;\r\n        }\r\n    }\r\n\r\n    // Properties (matches TransformNode properties)\r\n\r\n    /**\r\n     * Gets the node used to drive the bone's transformation\r\n     * @returns a transform node or null\r\n     */\r\n    public getTransformNode() {\r\n        return this._linkedTransformNode;\r\n    }\r\n\r\n    /** Gets or sets current position (in local space) */\r\n    public get position(): Vector3 {\r\n        this._decompose();\r\n        return this._localPosition;\r\n    }\r\n\r\n    public set position(newPosition: Vector3) {\r\n        this._decompose();\r\n        this._localPosition.copyFrom(newPosition);\r\n\r\n        this._markAsDirtyAndCompose();\r\n    }\r\n\r\n    /** Gets or sets current rotation (in local space) */\r\n    public get rotation(): Vector3 {\r\n        return this.getRotation();\r\n    }\r\n\r\n    public set rotation(newRotation: Vector3) {\r\n        this.setRotation(newRotation);\r\n    }\r\n\r\n    /** Gets or sets current rotation quaternion (in local space) */\r\n    public get rotationQuaternion() {\r\n        this._decompose();\r\n        return this._localRotation;\r\n    }\r\n\r\n    public set rotationQuaternion(newRotation: Quaternion) {\r\n        this.setRotationQuaternion(newRotation);\r\n    }\r\n\r\n    /** Gets or sets current scaling (in local space) */\r\n    public get scaling(): Vector3 {\r\n        return this.getScale();\r\n    }\r\n\r\n    public set scaling(newScaling: Vector3) {\r\n        this.setScale(newScaling);\r\n    }\r\n\r\n    /**\r\n     * Gets the animation properties override\r\n     */\r\n    public get animationPropertiesOverride(): Nullable<AnimationPropertiesOverride> {\r\n        return this._skeleton.animationPropertiesOverride;\r\n    }\r\n\r\n    // Methods\r\n    private _decompose() {\r\n        if (!this._needToDecompose) {\r\n            return;\r\n        }\r\n\r\n        this._needToDecompose = false;\r\n\r\n        if (!this._localScaling) {\r\n            this._localScaling = Vector3.Zero();\r\n            this._localRotation = Quaternion.Zero();\r\n            this._localPosition = Vector3.Zero();\r\n        }\r\n        this._localMatrix.decompose(this._localScaling, this._localRotation, this._localPosition);\r\n    }\r\n\r\n    private _compose() {\r\n        if (!this._needToCompose) {\r\n            return;\r\n        }\r\n\r\n        if (!this._localScaling) {\r\n            this._needToCompose = false;\r\n            return;\r\n        }\r\n\r\n        this._needToCompose = false;\r\n        Matrix.ComposeToRef(this._localScaling, this._localRotation, this._localPosition, this._localMatrix);\r\n    }\r\n\r\n    /**\r\n     * Update the base and local matrices\r\n     * @param matrix defines the new base or local matrix\r\n     * @param updateDifferenceMatrix defines if the difference matrix must be updated\r\n     * @param updateLocalMatrix defines if the local matrix should be updated\r\n     */\r\n    public updateMatrix(matrix: Matrix, updateDifferenceMatrix = true, updateLocalMatrix = true): void {\r\n        this._baseMatrix.copyFrom(matrix);\r\n\r\n        if (updateDifferenceMatrix) {\r\n            this._updateDifferenceMatrix();\r\n        }\r\n\r\n        if (updateLocalMatrix) {\r\n            this._matrix = matrix;\r\n        } else {\r\n            this.markAsDirty();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _updateDifferenceMatrix(rootMatrix?: Matrix, updateChildren = true): void {\r\n        if (!rootMatrix) {\r\n            rootMatrix = this._baseMatrix;\r\n        }\r\n\r\n        if (this.parent) {\r\n            rootMatrix.multiplyToRef(this.parent._absoluteTransform, this._absoluteTransform);\r\n        } else {\r\n            this._absoluteTransform.copyFrom(rootMatrix);\r\n        }\r\n\r\n        this._absoluteTransform.invertToRef(this._invertedAbsoluteTransform);\r\n\r\n        if (updateChildren) {\r\n            for (let index = 0; index < this.children.length; index++) {\r\n                this.children[index]._updateDifferenceMatrix();\r\n            }\r\n        }\r\n\r\n        this._scalingDeterminant = this._absoluteTransform.determinant() < 0 ? -1 : 1;\r\n    }\r\n\r\n    /**\r\n     * Flag the bone as dirty (Forcing it to update everything)\r\n     * @returns this bone\r\n     */\r\n    public markAsDirty(): Bone {\r\n        this._currentRenderId++;\r\n        this._childUpdateId++;\r\n        this._skeleton._markAsDirty();\r\n        return this;\r\n    }\r\n\r\n    /** @internal */\r\n    public _markAsDirtyAndCompose() {\r\n        this.markAsDirty();\r\n        this._needToCompose = true;\r\n    }\r\n\r\n    private _markAsDirtyAndDecompose() {\r\n        this.markAsDirty();\r\n        this._needToDecompose = true;\r\n    }\r\n\r\n    /**\r\n     * Translate the bone in local or world space\r\n     * @param vec The amount to translate the bone\r\n     * @param space The space that the translation is in\r\n     * @param tNode The TransformNode that this bone is attached to. This is only used in world space\r\n     */\r\n    public translate(vec: Vector3, space = Space.LOCAL, tNode?: TransformNode): void {\r\n        const lm = this.getLocalMatrix();\r\n\r\n        if (space == Space.LOCAL) {\r\n            lm.addAtIndex(12, vec.x);\r\n            lm.addAtIndex(13, vec.y);\r\n            lm.addAtIndex(14, vec.z);\r\n        } else {\r\n            let wm: Nullable<Matrix> = null;\r\n\r\n            //tNode.getWorldMatrix() needs to be called before skeleton.computeAbsoluteTransforms()\r\n            if (tNode) {\r\n                wm = tNode.getWorldMatrix();\r\n            }\r\n\r\n            this._skeleton.computeAbsoluteTransforms();\r\n            const tmat = Bone._TmpMats[0];\r\n            const tvec = Bone._TmpVecs[0];\r\n\r\n            if (this.parent) {\r\n                if (tNode && wm) {\r\n                    tmat.copyFrom(this.parent.getAbsoluteTransform());\r\n                    tmat.multiplyToRef(wm, tmat);\r\n                } else {\r\n                    tmat.copyFrom(this.parent.getAbsoluteTransform());\r\n                }\r\n            } else {\r\n                Matrix.IdentityToRef(tmat);\r\n            }\r\n\r\n            tmat.setTranslationFromFloats(0, 0, 0);\r\n            tmat.invert();\r\n            Vector3.TransformCoordinatesToRef(vec, tmat, tvec);\r\n\r\n            lm.addAtIndex(12, tvec.x);\r\n            lm.addAtIndex(13, tvec.y);\r\n            lm.addAtIndex(14, tvec.z);\r\n        }\r\n\r\n        this._markAsDirtyAndDecompose();\r\n    }\r\n\r\n    /**\r\n     * Set the position of the bone in local or world space\r\n     * @param position The position to set the bone\r\n     * @param space The space that the position is in\r\n     * @param tNode The TransformNode that this bone is attached to.  This is only used in world space\r\n     */\r\n    public setPosition(position: Vector3, space = Space.LOCAL, tNode?: TransformNode): void {\r\n        const lm = this.getLocalMatrix();\r\n\r\n        if (space == Space.LOCAL) {\r\n            lm.setTranslationFromFloats(position.x, position.y, position.z);\r\n        } else {\r\n            let wm: Nullable<Matrix> = null;\r\n\r\n            //tNode.getWorldMatrix() needs to be called before skeleton.computeAbsoluteTransforms()\r\n            if (tNode) {\r\n                wm = tNode.getWorldMatrix();\r\n            }\r\n\r\n            this._skeleton.computeAbsoluteTransforms();\r\n\r\n            const tmat = Bone._TmpMats[0];\r\n            const vec = Bone._TmpVecs[0];\r\n\r\n            if (this.parent) {\r\n                if (tNode && wm) {\r\n                    tmat.copyFrom(this.parent.getAbsoluteTransform());\r\n                    tmat.multiplyToRef(wm, tmat);\r\n                } else {\r\n                    tmat.copyFrom(this.parent.getAbsoluteTransform());\r\n                }\r\n                tmat.invert();\r\n            } else {\r\n                Matrix.IdentityToRef(tmat);\r\n            }\r\n\r\n            Vector3.TransformCoordinatesToRef(position, tmat, vec);\r\n            lm.setTranslationFromFloats(vec.x, vec.y, vec.z);\r\n        }\r\n\r\n        this._markAsDirtyAndDecompose();\r\n    }\r\n\r\n    /**\r\n     * Set the absolute position of the bone (world space)\r\n     * @param position The position to set the bone\r\n     * @param tNode The TransformNode that this bone is attached to\r\n     */\r\n    public setAbsolutePosition(position: Vector3, tNode?: TransformNode) {\r\n        this.setPosition(position, Space.WORLD, tNode);\r\n    }\r\n\r\n    /**\r\n     * Scale the bone on the x, y and z axes (in local space)\r\n     * @param x The amount to scale the bone on the x axis\r\n     * @param y The amount to scale the bone on the y axis\r\n     * @param z The amount to scale the bone on the z axis\r\n     * @param scaleChildren sets this to true if children of the bone should be scaled as well (false by default)\r\n     */\r\n    public scale(x: number, y: number, z: number, scaleChildren = false): void {\r\n        const locMat = this.getLocalMatrix();\r\n\r\n        // Apply new scaling on top of current local matrix\r\n        const scaleMat = Bone._TmpMats[0];\r\n        Matrix.ScalingToRef(x, y, z, scaleMat);\r\n        scaleMat.multiplyToRef(locMat, locMat);\r\n\r\n        // Invert scaling matrix and apply the inverse to all children\r\n        scaleMat.invert();\r\n\r\n        for (const child of this.children) {\r\n            const cm = child.getLocalMatrix();\r\n            cm.multiplyToRef(scaleMat, cm);\r\n            cm.multiplyAtIndex(12, x);\r\n            cm.multiplyAtIndex(13, y);\r\n            cm.multiplyAtIndex(14, z);\r\n\r\n            child._markAsDirtyAndDecompose();\r\n        }\r\n\r\n        this._markAsDirtyAndDecompose();\r\n\r\n        if (scaleChildren) {\r\n            for (const child of this.children) {\r\n                child.scale(x, y, z, scaleChildren);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set the bone scaling in local space\r\n     * @param scale defines the scaling vector\r\n     */\r\n    public setScale(scale: Vector3): void {\r\n        this._decompose();\r\n        this._localScaling.copyFrom(scale);\r\n        this._markAsDirtyAndCompose();\r\n    }\r\n\r\n    /**\r\n     * Gets the current scaling in local space\r\n     * @returns the current scaling vector\r\n     */\r\n    public getScale(): Vector3 {\r\n        this._decompose();\r\n        return this._localScaling;\r\n    }\r\n\r\n    /**\r\n     * Gets the current scaling in local space and stores it in a target vector\r\n     * @param result defines the target vector\r\n     */\r\n    public getScaleToRef(result: Vector3) {\r\n        this._decompose();\r\n        result.copyFrom(this._localScaling);\r\n    }\r\n\r\n    /**\r\n     * Set the yaw, pitch, and roll of the bone in local or world space\r\n     * @param yaw The rotation of the bone on the y axis\r\n     * @param pitch The rotation of the bone on the x axis\r\n     * @param roll The rotation of the bone on the z axis\r\n     * @param space The space that the axes of rotation are in\r\n     * @param tNode The TransformNode that this bone is attached to.  This is only used in world space\r\n     */\r\n    public setYawPitchRoll(yaw: number, pitch: number, roll: number, space = Space.LOCAL, tNode?: TransformNode): void {\r\n        if (space === Space.LOCAL) {\r\n            const quat = Bone._TmpQuat;\r\n            Quaternion.RotationYawPitchRollToRef(yaw, pitch, roll, quat);\r\n            this.setRotationQuaternion(quat, space, tNode);\r\n            return;\r\n        }\r\n\r\n        const rotMatInv = Bone._TmpMats[0];\r\n        if (!this._getNegativeRotationToRef(rotMatInv, tNode)) {\r\n            return;\r\n        }\r\n\r\n        const rotMat = Bone._TmpMats[1];\r\n        Matrix.RotationYawPitchRollToRef(yaw, pitch, roll, rotMat);\r\n\r\n        rotMatInv.multiplyToRef(rotMat, rotMat);\r\n        this._rotateWithMatrix(rotMat, space, tNode);\r\n    }\r\n\r\n    /**\r\n     * Add a rotation to the bone on an axis in local or world space\r\n     * @param axis The axis to rotate the bone on\r\n     * @param amount The amount to rotate the bone\r\n     * @param space The space that the axis is in\r\n     * @param tNode The TransformNode that this bone is attached to. This is only used in world space\r\n     */\r\n    public rotate(axis: Vector3, amount: number, space = Space.LOCAL, tNode?: TransformNode): void {\r\n        const rmat = Bone._TmpMats[0];\r\n        rmat.setTranslationFromFloats(0, 0, 0);\r\n        Matrix.RotationAxisToRef(axis, amount, rmat);\r\n        this._rotateWithMatrix(rmat, space, tNode);\r\n    }\r\n\r\n    /**\r\n     * Set the rotation of the bone to a particular axis angle in local or world space\r\n     * @param axis The axis to rotate the bone on\r\n     * @param angle The angle that the bone should be rotated to\r\n     * @param space The space that the axis is in\r\n     * @param tNode The TransformNode that this bone is attached to.  This is only used in world space\r\n     */\r\n    public setAxisAngle(axis: Vector3, angle: number, space = Space.LOCAL, tNode?: TransformNode): void {\r\n        if (space === Space.LOCAL) {\r\n            const quat = Bone._TmpQuat;\r\n            Quaternion.RotationAxisToRef(axis, angle, quat);\r\n\r\n            this.setRotationQuaternion(quat, space, tNode);\r\n            return;\r\n        }\r\n\r\n        const rotMatInv = Bone._TmpMats[0];\r\n        if (!this._getNegativeRotationToRef(rotMatInv, tNode)) {\r\n            return;\r\n        }\r\n\r\n        const rotMat = Bone._TmpMats[1];\r\n        Matrix.RotationAxisToRef(axis, angle, rotMat);\r\n\r\n        rotMatInv.multiplyToRef(rotMat, rotMat);\r\n        this._rotateWithMatrix(rotMat, space, tNode);\r\n    }\r\n\r\n    /**\r\n     * Set the euler rotation of the bone in local or world space\r\n     * @param rotation The euler rotation that the bone should be set to\r\n     * @param space The space that the rotation is in\r\n     * @param tNode The TransformNode that this bone is attached to. This is only used in world space\r\n     */\r\n    public setRotation(rotation: Vector3, space = Space.LOCAL, tNode?: TransformNode): void {\r\n        this.setYawPitchRoll(rotation.y, rotation.x, rotation.z, space, tNode);\r\n    }\r\n\r\n    /**\r\n     * Set the quaternion rotation of the bone in local or world space\r\n     * @param quat The quaternion rotation that the bone should be set to\r\n     * @param space The space that the rotation is in\r\n     * @param tNode The TransformNode that this bone is attached to. This is only used in world space\r\n     */\r\n    public setRotationQuaternion(quat: Quaternion, space = Space.LOCAL, tNode?: TransformNode): void {\r\n        if (space === Space.LOCAL) {\r\n            this._decompose();\r\n            this._localRotation.copyFrom(quat);\r\n\r\n            this._markAsDirtyAndCompose();\r\n\r\n            return;\r\n        }\r\n\r\n        const rotMatInv = Bone._TmpMats[0];\r\n        if (!this._getNegativeRotationToRef(rotMatInv, tNode)) {\r\n            return;\r\n        }\r\n\r\n        const rotMat = Bone._TmpMats[1];\r\n        Matrix.FromQuaternionToRef(quat, rotMat);\r\n\r\n        rotMatInv.multiplyToRef(rotMat, rotMat);\r\n\r\n        this._rotateWithMatrix(rotMat, space, tNode);\r\n    }\r\n\r\n    /**\r\n     * Set the rotation matrix of the bone in local or world space\r\n     * @param rotMat The rotation matrix that the bone should be set to\r\n     * @param space The space that the rotation is in\r\n     * @param tNode The TransformNode that this bone is attached to. This is only used in world space\r\n     */\r\n    public setRotationMatrix(rotMat: Matrix, space = Space.LOCAL, tNode?: TransformNode): void {\r\n        if (space === Space.LOCAL) {\r\n            const quat = Bone._TmpQuat;\r\n            Quaternion.FromRotationMatrixToRef(rotMat, quat);\r\n            this.setRotationQuaternion(quat, space, tNode);\r\n            return;\r\n        }\r\n\r\n        const rotMatInv = Bone._TmpMats[0];\r\n        if (!this._getNegativeRotationToRef(rotMatInv, tNode)) {\r\n            return;\r\n        }\r\n\r\n        const rotMat2 = Bone._TmpMats[1];\r\n        rotMat2.copyFrom(rotMat);\r\n\r\n        rotMatInv.multiplyToRef(rotMat, rotMat2);\r\n\r\n        this._rotateWithMatrix(rotMat2, space, tNode);\r\n    }\r\n\r\n    private _rotateWithMatrix(rmat: Matrix, space = Space.LOCAL, tNode?: TransformNode): void {\r\n        const lmat = this.getLocalMatrix();\r\n        const lx = lmat.m[12];\r\n        const ly = lmat.m[13];\r\n        const lz = lmat.m[14];\r\n        const parent = this.getParent();\r\n        const parentScale = Bone._TmpMats[3];\r\n        const parentScaleInv = Bone._TmpMats[4];\r\n\r\n        if (parent && space == Space.WORLD) {\r\n            if (tNode) {\r\n                parentScale.copyFrom(tNode.getWorldMatrix());\r\n                parent.getAbsoluteTransform().multiplyToRef(parentScale, parentScale);\r\n            } else {\r\n                parentScale.copyFrom(parent.getAbsoluteTransform());\r\n            }\r\n            parentScaleInv.copyFrom(parentScale);\r\n            parentScaleInv.invert();\r\n            lmat.multiplyToRef(parentScale, lmat);\r\n            lmat.multiplyToRef(rmat, lmat);\r\n            lmat.multiplyToRef(parentScaleInv, lmat);\r\n        } else {\r\n            if (space == Space.WORLD && tNode) {\r\n                parentScale.copyFrom(tNode.getWorldMatrix());\r\n                parentScaleInv.copyFrom(parentScale);\r\n                parentScaleInv.invert();\r\n                lmat.multiplyToRef(parentScale, lmat);\r\n                lmat.multiplyToRef(rmat, lmat);\r\n                lmat.multiplyToRef(parentScaleInv, lmat);\r\n            } else {\r\n                lmat.multiplyToRef(rmat, lmat);\r\n            }\r\n        }\r\n\r\n        lmat.setTranslationFromFloats(lx, ly, lz);\r\n\r\n        this.computeAbsoluteTransforms();\r\n        this._markAsDirtyAndDecompose();\r\n    }\r\n\r\n    private _getNegativeRotationToRef(rotMatInv: Matrix, tNode?: TransformNode): boolean {\r\n        const scaleMatrix = Bone._TmpMats[2];\r\n        rotMatInv.copyFrom(this.getAbsoluteTransform());\r\n\r\n        if (tNode) {\r\n            rotMatInv.multiplyToRef(tNode.getWorldMatrix(), rotMatInv);\r\n            Matrix.ScalingToRef(tNode.scaling.x, tNode.scaling.y, tNode.scaling.z, scaleMatrix);\r\n        } else {\r\n            Matrix.IdentityToRef(scaleMatrix);\r\n        }\r\n\r\n        rotMatInv.invert();\r\n        if (isNaN(rotMatInv.m[0])) {\r\n            // Matrix failed to invert.\r\n            // This can happen if scale is zero for example.\r\n            return false;\r\n        }\r\n\r\n        scaleMatrix.multiplyAtIndex(0, this._scalingDeterminant);\r\n        rotMatInv.multiplyToRef(scaleMatrix, rotMatInv);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Get the position of the bone in local or world space\r\n     * @param space The space that the returned position is in\r\n     * @param tNode The TransformNode that this bone is attached to. This is only used in world space\r\n     * @returns The position of the bone\r\n     */\r\n    public getPosition(space = Space.LOCAL, tNode: Nullable<TransformNode> = null): Vector3 {\r\n        const pos = Vector3.Zero();\r\n\r\n        this.getPositionToRef(space, tNode, pos);\r\n\r\n        return pos;\r\n    }\r\n\r\n    /**\r\n     * Copy the position of the bone to a vector3 in local or world space\r\n     * @param space The space that the returned position is in\r\n     * @param tNode The TransformNode that this bone is attached to. This is only used in world space\r\n     * @param result The vector3 to copy the position to\r\n     */\r\n    public getPositionToRef(space = Space.LOCAL, tNode: Nullable<TransformNode>, result: Vector3): void {\r\n        if (space == Space.LOCAL) {\r\n            const lm = this.getLocalMatrix();\r\n\r\n            result.x = lm.m[12];\r\n            result.y = lm.m[13];\r\n            result.z = lm.m[14];\r\n        } else {\r\n            let wm: Nullable<Matrix> = null;\r\n\r\n            //tNode.getWorldMatrix() needs to be called before skeleton.computeAbsoluteTransforms()\r\n            if (tNode) {\r\n                wm = tNode.getWorldMatrix();\r\n            }\r\n\r\n            this._skeleton.computeAbsoluteTransforms();\r\n\r\n            let tmat = Bone._TmpMats[0];\r\n\r\n            if (tNode && wm) {\r\n                tmat.copyFrom(this.getAbsoluteTransform());\r\n                tmat.multiplyToRef(wm, tmat);\r\n            } else {\r\n                tmat = this.getAbsoluteTransform();\r\n            }\r\n\r\n            result.x = tmat.m[12];\r\n            result.y = tmat.m[13];\r\n            result.z = tmat.m[14];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the absolute position of the bone (world space)\r\n     * @param tNode The TransformNode that this bone is attached to\r\n     * @returns The absolute position of the bone\r\n     */\r\n    public getAbsolutePosition(tNode: Nullable<TransformNode> = null): Vector3 {\r\n        const pos = Vector3.Zero();\r\n\r\n        this.getPositionToRef(Space.WORLD, tNode, pos);\r\n\r\n        return pos;\r\n    }\r\n\r\n    /**\r\n     * Copy the absolute position of the bone (world space) to the result param\r\n     * @param tNode The TransformNode that this bone is attached to\r\n     * @param result The vector3 to copy the absolute position to\r\n     */\r\n    public getAbsolutePositionToRef(tNode: TransformNode, result: Vector3) {\r\n        this.getPositionToRef(Space.WORLD, tNode, result);\r\n    }\r\n\r\n    /**\r\n     * Compute the absolute transforms of this bone and its children\r\n     */\r\n    public computeAbsoluteTransforms(): void {\r\n        this._compose();\r\n\r\n        if (this.parent) {\r\n            this._localMatrix.multiplyToRef(this.parent._absoluteTransform, this._absoluteTransform);\r\n        } else {\r\n            this._absoluteTransform.copyFrom(this._localMatrix);\r\n\r\n            const poseMatrix = this._skeleton.getPoseMatrix();\r\n\r\n            if (poseMatrix) {\r\n                this._absoluteTransform.multiplyToRef(poseMatrix, this._absoluteTransform);\r\n            }\r\n        }\r\n\r\n        const children = this.children;\r\n        const len = children.length;\r\n\r\n        for (let i = 0; i < len; i++) {\r\n            children[i].computeAbsoluteTransforms();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the world direction from an axis that is in the local space of the bone\r\n     * @param localAxis The local direction that is used to compute the world direction\r\n     * @param tNode The TransformNode that this bone is attached to\r\n     * @returns The world direction\r\n     */\r\n    public getDirection(localAxis: Vector3, tNode: Nullable<TransformNode> = null): Vector3 {\r\n        const result = Vector3.Zero();\r\n\r\n        this.getDirectionToRef(localAxis, tNode, result);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Copy the world direction to a vector3 from an axis that is in the local space of the bone\r\n     * @param localAxis The local direction that is used to compute the world direction\r\n     * @param tNode The TransformNode that this bone is attached to\r\n     * @param result The vector3 that the world direction will be copied to\r\n     */\r\n    public getDirectionToRef(localAxis: Vector3, tNode: Nullable<TransformNode> = null, result: Vector3): void {\r\n        let wm: Nullable<Matrix> = null;\r\n\r\n        //tNode.getWorldMatrix() needs to be called before skeleton.computeAbsoluteTransforms()\r\n        if (tNode) {\r\n            wm = tNode.getWorldMatrix();\r\n        }\r\n\r\n        this._skeleton.computeAbsoluteTransforms();\r\n\r\n        const mat = Bone._TmpMats[0];\r\n\r\n        mat.copyFrom(this.getAbsoluteTransform());\r\n\r\n        if (tNode && wm) {\r\n            mat.multiplyToRef(wm, mat);\r\n        }\r\n\r\n        Vector3.TransformNormalToRef(localAxis, mat, result);\r\n\r\n        result.normalize();\r\n    }\r\n\r\n    /**\r\n     * Get the euler rotation of the bone in local or world space\r\n     * @param space The space that the rotation should be in\r\n     * @param tNode The TransformNode that this bone is attached to.  This is only used in world space\r\n     * @returns The euler rotation\r\n     */\r\n    public getRotation(space = Space.LOCAL, tNode: Nullable<TransformNode> = null): Vector3 {\r\n        const result = Vector3.Zero();\r\n\r\n        this.getRotationToRef(space, tNode, result);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Copy the euler rotation of the bone to a vector3.  The rotation can be in either local or world space\r\n     * @param space The space that the rotation should be in\r\n     * @param tNode The TransformNode that this bone is attached to.  This is only used in world space\r\n     * @param result The vector3 that the rotation should be copied to\r\n     */\r\n    public getRotationToRef(space = Space.LOCAL, tNode: Nullable<TransformNode> = null, result: Vector3): void {\r\n        const quat = Bone._TmpQuat;\r\n\r\n        this.getRotationQuaternionToRef(space, tNode, quat);\r\n\r\n        quat.toEulerAnglesToRef(result);\r\n    }\r\n\r\n    /**\r\n     * Get the quaternion rotation of the bone in either local or world space\r\n     * @param space The space that the rotation should be in\r\n     * @param tNode The TransformNode that this bone is attached to.  This is only used in world space\r\n     * @returns The quaternion rotation\r\n     */\r\n    public getRotationQuaternion(space = Space.LOCAL, tNode: Nullable<TransformNode> = null): Quaternion {\r\n        const result = Quaternion.Identity();\r\n\r\n        this.getRotationQuaternionToRef(space, tNode, result);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Copy the quaternion rotation of the bone to a quaternion.  The rotation can be in either local or world space\r\n     * @param space The space that the rotation should be in\r\n     * @param tNode The TransformNode that this bone is attached to.  This is only used in world space\r\n     * @param result The quaternion that the rotation should be copied to\r\n     */\r\n    public getRotationQuaternionToRef(space = Space.LOCAL, tNode: Nullable<TransformNode> = null, result: Quaternion): void {\r\n        if (space == Space.LOCAL) {\r\n            this._decompose();\r\n            result.copyFrom(this._localRotation);\r\n        } else {\r\n            const mat = Bone._TmpMats[0];\r\n            const amat = this.getAbsoluteTransform();\r\n\r\n            if (tNode) {\r\n                amat.multiplyToRef(tNode.getWorldMatrix(), mat);\r\n            } else {\r\n                mat.copyFrom(amat);\r\n            }\r\n\r\n            mat.multiplyAtIndex(0, this._scalingDeterminant);\r\n            mat.multiplyAtIndex(1, this._scalingDeterminant);\r\n            mat.multiplyAtIndex(2, this._scalingDeterminant);\r\n\r\n            mat.decompose(undefined, result, undefined);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the rotation matrix of the bone in local or world space\r\n     * @param space The space that the rotation should be in\r\n     * @param tNode The TransformNode that this bone is attached to.  This is only used in world space\r\n     * @returns The rotation matrix\r\n     */\r\n    public getRotationMatrix(space = Space.LOCAL, tNode: TransformNode): Matrix {\r\n        const result = Matrix.Identity();\r\n\r\n        this.getRotationMatrixToRef(space, tNode, result);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Copy the rotation matrix of the bone to a matrix.  The rotation can be in either local or world space\r\n     * @param space The space that the rotation should be in\r\n     * @param tNode The TransformNode that this bone is attached to.  This is only used in world space\r\n     * @param result The quaternion that the rotation should be copied to\r\n     */\r\n    public getRotationMatrixToRef(space = Space.LOCAL, tNode: TransformNode, result: Matrix): void {\r\n        if (space == Space.LOCAL) {\r\n            this.getLocalMatrix().getRotationMatrixToRef(result);\r\n        } else {\r\n            const mat = Bone._TmpMats[0];\r\n            const amat = this.getAbsoluteTransform();\r\n\r\n            if (tNode) {\r\n                amat.multiplyToRef(tNode.getWorldMatrix(), mat);\r\n            } else {\r\n                mat.copyFrom(amat);\r\n            }\r\n\r\n            mat.multiplyAtIndex(0, this._scalingDeterminant);\r\n            mat.multiplyAtIndex(1, this._scalingDeterminant);\r\n            mat.multiplyAtIndex(2, this._scalingDeterminant);\r\n\r\n            mat.getRotationMatrixToRef(result);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the world position of a point that is in the local space of the bone\r\n     * @param position The local position\r\n     * @param tNode The TransformNode that this bone is attached to\r\n     * @returns The world position\r\n     */\r\n    public getAbsolutePositionFromLocal(position: Vector3, tNode: Nullable<TransformNode> = null): Vector3 {\r\n        const result = Vector3.Zero();\r\n\r\n        this.getAbsolutePositionFromLocalToRef(position, tNode, result);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Get the world position of a point that is in the local space of the bone and copy it to the result param\r\n     * @param position The local position\r\n     * @param tNode The TransformNode that this bone is attached to\r\n     * @param result The vector3 that the world position should be copied to\r\n     */\r\n    public getAbsolutePositionFromLocalToRef(position: Vector3, tNode: Nullable<TransformNode> = null, result: Vector3): void {\r\n        let wm: Nullable<Matrix> = null;\r\n\r\n        //tNode.getWorldMatrix() needs to be called before skeleton.computeAbsoluteTransforms()\r\n        if (tNode) {\r\n            wm = tNode.getWorldMatrix();\r\n        }\r\n\r\n        this._skeleton.computeAbsoluteTransforms();\r\n\r\n        let tmat = Bone._TmpMats[0];\r\n\r\n        if (tNode && wm) {\r\n            tmat.copyFrom(this.getAbsoluteTransform());\r\n            tmat.multiplyToRef(wm, tmat);\r\n        } else {\r\n            tmat = this.getAbsoluteTransform();\r\n        }\r\n\r\n        Vector3.TransformCoordinatesToRef(position, tmat, result);\r\n    }\r\n\r\n    /**\r\n     * Get the local position of a point that is in world space\r\n     * @param position The world position\r\n     * @param tNode The TransformNode that this bone is attached to\r\n     * @returns The local position\r\n     */\r\n    public getLocalPositionFromAbsolute(position: Vector3, tNode: Nullable<TransformNode> = null): Vector3 {\r\n        const result = Vector3.Zero();\r\n\r\n        this.getLocalPositionFromAbsoluteToRef(position, tNode, result);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Get the local position of a point that is in world space and copy it to the result param\r\n     * @param position The world position\r\n     * @param tNode The TransformNode that this bone is attached to\r\n     * @param result The vector3 that the local position should be copied to\r\n     */\r\n    public getLocalPositionFromAbsoluteToRef(position: Vector3, tNode: Nullable<TransformNode> = null, result: Vector3): void {\r\n        let wm: Nullable<Matrix> = null;\r\n\r\n        //tNode.getWorldMatrix() needs to be called before skeleton.computeAbsoluteTransforms()\r\n        if (tNode) {\r\n            wm = tNode.getWorldMatrix();\r\n        }\r\n\r\n        this._skeleton.computeAbsoluteTransforms();\r\n\r\n        const tmat = Bone._TmpMats[0];\r\n\r\n        tmat.copyFrom(this.getAbsoluteTransform());\r\n\r\n        if (tNode && wm) {\r\n            tmat.multiplyToRef(wm, tmat);\r\n        }\r\n\r\n        tmat.invert();\r\n\r\n        Vector3.TransformCoordinatesToRef(position, tmat, result);\r\n    }\r\n\r\n    /**\r\n     * Set the current local matrix as the restPose for this bone.\r\n     */\r\n    public setCurrentPoseAsRest(): void {\r\n        this.setRestPose(this.getLocalMatrix());\r\n    }\r\n}\r\n"]}