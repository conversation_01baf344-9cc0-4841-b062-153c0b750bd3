{"version": 3, "file": "freeCameraGamepadInput.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Cameras/Inputs/freeCameraGamepadInput.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAIlD,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AAErE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AACnE,OAAO,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAC;AAEjD;;;GAGG;AACH,MAAM,OAAO,sBAAsB;IAAnC;QAWI;;;WAGG;QAEI,8BAAyB,GAAG,GAAG,CAAC;QAEvC;;;WAGG;QAEI,2BAAsB,GAAG,EAAE,CAAC;QAEnC;;;WAGG;QACI,kBAAa,GAAG,GAAG,CAAC;QAEnB,gBAAW,GAAG,GAAG,CAAC;QAgBlB,qBAAgB,GAAW,MAAM,CAAC,QAAQ,EAAE,CAAC;QAC7C,oBAAe,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAC1C,aAAQ,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QACnC,aAAQ,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;IA2F/C,CAAC;IA5GG;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,WAAW,KAAK,GAAG,CAAC;IACpC,CAAC;IAED,IAAW,WAAW,CAAC,KAAc;QACjC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IAC1C,CAAC;IAUD;;OAEG;IACI,aAAa;QAChB,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,cAAc,CAAC;QACtD,IAAI,CAAC,2BAA2B,GAAG,OAAO,CAAC,4BAA4B,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YACpF,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,YAAY,EAAE;gBACvC,4BAA4B;gBAC5B,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,EAAE;oBAChD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;iBAC1B;aACJ;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,8BAA8B,GAAG,OAAO,CAAC,+BAA+B,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YAC1F,IAAI,IAAI,CAAC,OAAO,KAAK,OAAO,EAAE;gBAC1B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;aACvB;QACL,CAAC,CAAC,CAAC;QAEH,yDAAyD;QACzD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACtD,yFAAyF;QACzF,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE;YAC1C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;SACtC;IACL,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,cAAc,CAAC,4BAA4B,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAC5G,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,cAAc,CAAC,+BAA+B,CAAC,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAClH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YACxC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;YACxC,IAAI,IAAI,CAAC,sBAAsB,KAAK,CAAC,EAAE;gBACnC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtG,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;aACzG;YAED,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;YACvC,IAAI,QAAQ,IAAI,IAAI,CAAC,yBAAyB,KAAK,CAAC,EAAE;gBAClD,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzG,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;aACjI;iBAAM;gBACH,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;aAC7B;YAED,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE;gBAC5B,MAAM,CAAC,yBAAyB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;aACpG;iBAAM;gBACH,MAAM,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;aACrE;YAED,MAAM,KAAK,GAAG,MAAM,CAAC,wBAAwB,EAAE,GAAG,IAAI,CAAC;YACvD,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;YAEzE,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YAC9F,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACxD,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;YACrD,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACnD;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,wBAAwB,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,SAAS,CAAC;IACrB,CAAC;CACJ;AA7HG;IADC,SAAS,EAAE;yEAC2B;AAOvC;IADC,SAAS,EAAE;sEACuB;AAwHjC,gBAAiB,CAAC,wBAAwB,CAAC,GAAG,sBAAsB,CAAC", "sourcesContent": ["import { serialize } from \"../../Misc/decorators\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { ICameraInput } from \"../../Cameras/cameraInputsManager\";\r\nimport { CameraInputTypes } from \"../../Cameras/cameraInputsManager\";\r\nimport type { FreeCamera } from \"../../Cameras/freeCamera\";\r\nimport { Matrix, Vector3, Vector2 } from \"../../Maths/math.vector\";\r\nimport { Gamepad } from \"../../Gamepads/gamepad\";\r\n\r\n/**\r\n * Manage the gamepad inputs to control a free camera.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n */\r\nexport class FreeCameraGamepadInput implements ICameraInput<FreeCamera> {\r\n    /**\r\n     * Define the camera the input is attached to.\r\n     */\r\n    public camera: FreeCamera;\r\n\r\n    /**\r\n     * Define the Gamepad controlling the input\r\n     */\r\n    public gamepad: Nullable<Gamepad>;\r\n\r\n    /**\r\n     * Defines the gamepad rotation sensibility.\r\n     * This is the threshold from when rotation starts to be accounted for to prevent jittering.\r\n     */\r\n    @serialize()\r\n    public gamepadAngularSensibility = 200;\r\n\r\n    /**\r\n     * Defines the gamepad move sensibility.\r\n     * This is the threshold from when moving starts to be accounted for for to prevent jittering.\r\n     */\r\n    @serialize()\r\n    public gamepadMoveSensibility = 40;\r\n\r\n    /**\r\n     * Defines the minimum value at which any analog stick input is ignored.\r\n     * Note: This value should only be a value between 0 and 1.\r\n     */\r\n    public deadzoneDelta = 0.1;\r\n\r\n    private _yAxisScale = 1.0;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that Yaxis (for right stick) should be inverted\r\n     */\r\n    public get invertYAxis() {\r\n        return this._yAxisScale !== 1.0;\r\n    }\r\n\r\n    public set invertYAxis(value: boolean) {\r\n        this._yAxisScale = value ? -1.0 : 1.0;\r\n    }\r\n\r\n    // private members\r\n    private _onGamepadConnectedObserver: Nullable<Observer<Gamepad>>;\r\n    private _onGamepadDisconnectedObserver: Nullable<Observer<Gamepad>>;\r\n    private _cameraTransform: Matrix = Matrix.Identity();\r\n    private _deltaTransform: Vector3 = Vector3.Zero();\r\n    private _vector3: Vector3 = Vector3.Zero();\r\n    private _vector2: Vector2 = Vector2.Zero();\r\n\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     */\r\n    public attachControl(): void {\r\n        const manager = this.camera.getScene().gamepadManager;\r\n        this._onGamepadConnectedObserver = manager.onGamepadConnectedObservable.add((gamepad) => {\r\n            if (gamepad.type !== Gamepad.POSE_ENABLED) {\r\n                // prioritize XBOX gamepads.\r\n                if (!this.gamepad || gamepad.type === Gamepad.XBOX) {\r\n                    this.gamepad = gamepad;\r\n                }\r\n            }\r\n        });\r\n\r\n        this._onGamepadDisconnectedObserver = manager.onGamepadDisconnectedObservable.add((gamepad) => {\r\n            if (this.gamepad === gamepad) {\r\n                this.gamepad = null;\r\n            }\r\n        });\r\n\r\n        // check if there are already other controllers connected\r\n        this.gamepad = manager.getGamepadByType(Gamepad.XBOX);\r\n        // if no xbox controller was found, but there are gamepad controllers, take the first one\r\n        if (!this.gamepad && manager.gamepads.length) {\r\n            this.gamepad = manager.gamepads[0];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    public detachControl(): void {\r\n        this.camera.getScene().gamepadManager.onGamepadConnectedObservable.remove(this._onGamepadConnectedObserver);\r\n        this.camera.getScene().gamepadManager.onGamepadDisconnectedObservable.remove(this._onGamepadDisconnectedObserver);\r\n        this.gamepad = null;\r\n    }\r\n\r\n    /**\r\n     * Update the current camera state depending on the inputs that have been used this frame.\r\n     * This is a dynamically created lambda to avoid the performance penalty of looping for inputs in the render loop.\r\n     */\r\n    public checkInputs(): void {\r\n        if (this.gamepad && this.gamepad.leftStick) {\r\n            const camera = this.camera;\r\n            const lsValues = this.gamepad.leftStick;\r\n            if (this.gamepadMoveSensibility !== 0) {\r\n                lsValues.x = Math.abs(lsValues.x) > this.deadzoneDelta ? lsValues.x / this.gamepadMoveSensibility : 0;\r\n                lsValues.y = Math.abs(lsValues.y) > this.deadzoneDelta ? lsValues.y / this.gamepadMoveSensibility : 0;\r\n            }\r\n\r\n            let rsValues = this.gamepad.rightStick;\r\n            if (rsValues && this.gamepadAngularSensibility !== 0) {\r\n                rsValues.x = Math.abs(rsValues.x) > this.deadzoneDelta ? rsValues.x / this.gamepadAngularSensibility : 0;\r\n                rsValues.y = (Math.abs(rsValues.y) > this.deadzoneDelta ? rsValues.y / this.gamepadAngularSensibility : 0) * this._yAxisScale;\r\n            } else {\r\n                rsValues = { x: 0, y: 0 };\r\n            }\r\n\r\n            if (!camera.rotationQuaternion) {\r\n                Matrix.RotationYawPitchRollToRef(camera.rotation.y, camera.rotation.x, 0, this._cameraTransform);\r\n            } else {\r\n                camera.rotationQuaternion.toRotationMatrix(this._cameraTransform);\r\n            }\r\n\r\n            const speed = camera._computeLocalCameraSpeed() * 50.0;\r\n            this._vector3.copyFromFloats(lsValues.x * speed, 0, -lsValues.y * speed);\r\n\r\n            Vector3.TransformCoordinatesToRef(this._vector3, this._cameraTransform, this._deltaTransform);\r\n            camera.cameraDirection.addInPlace(this._deltaTransform);\r\n            this._vector2.copyFromFloats(rsValues.y, rsValues.x);\r\n            camera.cameraRotation.addInPlace(this._vector2);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of the current input.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"FreeCameraGamepadInput\";\r\n    }\r\n\r\n    /**\r\n     * Get the friendly name associated with the input class.\r\n     * @returns the input friendly name\r\n     */\r\n    public getSimpleName(): string {\r\n        return \"gamepad\";\r\n    }\r\n}\r\n\r\n(<any>CameraInputTypes)[\"FreeCameraGamepadInput\"] = FreeCameraGamepadInput;\r\n"]}