{"version": 3, "file": "attachToBoxBehavior.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Behaviors/Meshes/attachToBoxBehavior.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAQtE;;GAEG;AACH,MAAM,iBAAiB;IACnB,YAAmB,SAAkB,EAAS,mBAAmB,IAAI,OAAO,EAAE,EAAS,OAAO,CAAC,EAAS,SAAS,KAAK;QAAnG,cAAS,GAAT,SAAS,CAAS;QAAS,qBAAgB,GAAhB,gBAAgB,CAAgB;QAAS,SAAI,GAAJ,IAAI,CAAI;QAAS,WAAM,GAAN,MAAM,CAAQ;IAAG,CAAC;CAC7H;AAED;;GAEG;AACH,MAAM,OAAO,mBAAmB;IA2B5B;;;OAGG;IACH,YAAoB,GAAkB;QAAlB,QAAG,GAAH,GAAG,CAAe;QA9BtC;;WAEG;QACI,SAAI,GAAG,qBAAqB,CAAC;QACpC;;WAEG;QACI,yBAAoB,GAAG,IAAI,CAAC;QACnC;;WAEG;QACI,iCAA4B,GAAG,IAAI,CAAC;QACnC,iBAAY,GAAG;YACnB,IAAI,iBAAiB,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;YACnC,IAAI,iBAAiB,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YACrC,IAAI,iBAAiB,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YACrC,IAAI,iBAAiB,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACtC,IAAI,iBAAiB,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACxC,IAAI,iBAAiB,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;SAC5D,CAAC;QAIM,eAAU,GAAG,IAAI,MAAM,EAAE,CAAC;QAC1B,eAAU,GAAG,IAAI,OAAO,EAAE,CAAC;QAuC3B,gBAAW,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC7B,qBAAgB,GAAG,IAAI,MAAM,EAAE,CAAC;QAjCpC,kBAAkB;IACtB,CAAC;IAED;;OAEG;IACI,IAAI;QACP,kBAAkB;IACtB,CAAC;IAEO,YAAY,CAAC,eAAwB;QACzC,0FAA0F;QAC1F,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE;gBAClC,IAAI,CAAC,OAAO,CAAC,kBAAkB,GAAG,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;aAChJ;YACD,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClE,OAAO,CAAC,yBAAyB,CAAC,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC;YACpF,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC,gBAAgB,EAAE,eAAe,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,gBAAgB,EAAE,eAAe,CAAC,CAAC,CAAC;QACrI,CAAC,CAAC,CAAC;QACH,qFAAqF;QACrF,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE;YACvC,IAAI,GAAG,CAAC,MAAM,EAAE;gBACZ,OAAO,CAAC,CAAC;aACZ;iBAAM,IAAI,CAAC,CAAC,MAAM,EAAE;gBACjB,OAAO,GAAG,CAAC;aACd;iBAAM;gBACH,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;aACtC;QACL,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;IAIO,YAAY,CAAC,GAAY,EAAE,EAAE,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAe;QACzE,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACvE,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;QAC/B,UAAU,CAAC,uBAAuB,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;IACnE,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,MAAY;QACf,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QAEtC,4CAA4C;QAC5C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;YACnE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;gBAC3B,OAAO;aACV;YAED,gDAAgD;YAChD,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;YAClD,IAAU,IAAI,CAAC,MAAM,CAAC,YAAa,CAAC,cAAc,EAAE;gBAChD,SAAS,GAAS,IAAI,CAAC,MAAM,CAAC,YAAa,CAAC,cAAc,CAAC;aAC9D;YACD,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;YACtE,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,EAAE;gBACrC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC,sBAAsB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aACpG;iBAAM;gBACH,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,sBAAsB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aACzF;YAED,0BAA0B;YAC1B,OAAO,CAAC,yBAAyB,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAClF,yEAAyE;YACzE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;gBAC5B,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE;oBACrC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC;iBACnB;gBACD,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE;oBACrC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC;iBACnB;gBACD,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE;oBACrC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC;iBACnB;YACL,CAAC,CAAC,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACpD,iBAAiB;YACjB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;gBAC5B,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC;YACrB,CAAC,CAAC,CAAC;YAEH,oCAAoC;YACpC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC5C,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE;gBACpB,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBACtG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aACjD;YACD,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE;gBACpB,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBACtG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aACjD;YACD,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE;gBACpB,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBACtG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aACjD;YAED,+CAA+C;YAC/C,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE;gBAC9B,IAAI,CAAC,GAAG,CAAC,kBAAkB,GAAG,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;aAChI;YACD,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACxD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,gBAAgB,EAAE,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;YAE3F,4DAA4D;YAC5D,IAAI,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE;gBACtB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,4BAA4B,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;aACrG;YACD,IAAI,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE;gBACtB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,4BAA4B,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;aACrG;YACD,IAAI,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE;gBACtB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,4BAA4B,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;aACrG;YACD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,MAAM;QACF,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IACxE,CAAC;CACJ", "sourcesContent": ["import { Vector3, <PERSON>, Quaternion } from \"../../Maths/math.vector\";\r\nimport type { Mesh } from \"../../Meshes/mesh\";\r\nimport type { TransformNode } from \"../../Meshes/transformNode\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { Behavior } from \"../../Behaviors/behavior\";\r\n\r\n/**\r\n * @internal\r\n */\r\nclass FaceDirectionInfo {\r\n    constructor(public direction: Vector3, public rotatedDirection = new Vector3(), public diff = 0, public ignore = false) {}\r\n}\r\n\r\n/**\r\n * A behavior that when attached to a mesh will will place a specified node on the meshes face pointing towards the camera\r\n */\r\nexport class AttachToBoxBehavior implements Behavior<Mesh> {\r\n    /**\r\n     *  The name of the behavior\r\n     */\r\n    public name = \"AttachToBoxBehavior\";\r\n    /**\r\n     * The distance away from the face of the mesh that the UI should be attached to (default: 0.15)\r\n     */\r\n    public distanceAwayFromFace = 0.15;\r\n    /**\r\n     * The distance from the bottom of the face that the UI should be attached to (default: 0.15)\r\n     */\r\n    public distanceAwayFromBottomOfFace = 0.15;\r\n    private _faceVectors = [\r\n        new FaceDirectionInfo(Vector3.Up()),\r\n        new FaceDirectionInfo(Vector3.Down()),\r\n        new FaceDirectionInfo(Vector3.Left()),\r\n        new FaceDirectionInfo(Vector3.Right()),\r\n        new FaceDirectionInfo(Vector3.Forward()),\r\n        new FaceDirectionInfo(Vector3.Forward().scaleInPlace(-1)),\r\n    ];\r\n    private _target: Mesh;\r\n    private _scene: Scene;\r\n    private _onRenderObserver: Nullable<Observer<Scene>>;\r\n    private _tmpMatrix = new Matrix();\r\n    private _tmpVector = new Vector3();\r\n\r\n    /**\r\n     * Creates the AttachToBoxBehavior, used to attach UI to the closest face of the box to a camera\r\n     * @param _ui The transform node that should be attached to the mesh\r\n     */\r\n    constructor(private _ui: TransformNode) {\r\n        /* Does nothing */\r\n    }\r\n\r\n    /**\r\n     *  Initializes the behavior\r\n     */\r\n    public init() {\r\n        /* Does nothing */\r\n    }\r\n\r\n    private _closestFace(targetDirection: Vector3) {\r\n        // Go over each face and calculate the angle between the face's normal and targetDirection\r\n        this._faceVectors.forEach((v) => {\r\n            if (!this._target.rotationQuaternion) {\r\n                this._target.rotationQuaternion = Quaternion.RotationYawPitchRoll(this._target.rotation.y, this._target.rotation.x, this._target.rotation.z);\r\n            }\r\n            this._target.rotationQuaternion.toRotationMatrix(this._tmpMatrix);\r\n            Vector3.TransformCoordinatesToRef(v.direction, this._tmpMatrix, v.rotatedDirection);\r\n            v.diff = Vector3.GetAngleBetweenVectors(v.rotatedDirection, targetDirection, Vector3.Cross(v.rotatedDirection, targetDirection));\r\n        });\r\n        // Return the face information of the one with the normal closest to target direction\r\n        return this._faceVectors.reduce((min, p) => {\r\n            if (min.ignore) {\r\n                return p;\r\n            } else if (p.ignore) {\r\n                return min;\r\n            } else {\r\n                return min.diff < p.diff ? min : p;\r\n            }\r\n        }, this._faceVectors[0]);\r\n    }\r\n\r\n    private _zeroVector = Vector3.Zero();\r\n    private _lookAtTmpMatrix = new Matrix();\r\n    private _lookAtToRef(pos: Vector3, up = new Vector3(0, 1, 0), ref: Quaternion) {\r\n        Matrix.LookAtLHToRef(this._zeroVector, pos, up, this._lookAtTmpMatrix);\r\n        this._lookAtTmpMatrix.invert();\r\n        Quaternion.FromRotationMatrixToRef(this._lookAtTmpMatrix, ref);\r\n    }\r\n\r\n    /**\r\n     * Attaches the AttachToBoxBehavior to the passed in mesh\r\n     * @param target The mesh that the specified node will be attached to\r\n     */\r\n    attach(target: Mesh) {\r\n        this._target = target;\r\n        this._scene = this._target.getScene();\r\n\r\n        // Every frame, update the app bars position\r\n        this._onRenderObserver = this._scene.onBeforeRenderObservable.add(() => {\r\n            if (!this._scene.activeCamera) {\r\n                return;\r\n            }\r\n\r\n            // Find the face closest to the cameras position\r\n            let cameraPos = this._scene.activeCamera.position;\r\n            if ((<any>this._scene.activeCamera).devicePosition) {\r\n                cameraPos = (<any>this._scene.activeCamera).devicePosition;\r\n            }\r\n            const facing = this._closestFace(cameraPos.subtract(target.position));\r\n            if (this._scene.activeCamera.leftCamera) {\r\n                this._scene.activeCamera.leftCamera.computeWorldMatrix().getRotationMatrixToRef(this._tmpMatrix);\r\n            } else {\r\n                this._scene.activeCamera.computeWorldMatrix().getRotationMatrixToRef(this._tmpMatrix);\r\n            }\r\n\r\n            // Get camera up direction\r\n            Vector3.TransformCoordinatesToRef(Vector3.Up(), this._tmpMatrix, this._tmpVector);\r\n            // Ignore faces to not select a parallel face for the up vector of the UI\r\n            this._faceVectors.forEach((v) => {\r\n                if (facing.direction.x && v.direction.x) {\r\n                    v.ignore = true;\r\n                }\r\n                if (facing.direction.y && v.direction.y) {\r\n                    v.ignore = true;\r\n                }\r\n                if (facing.direction.z && v.direction.z) {\r\n                    v.ignore = true;\r\n                }\r\n            });\r\n            const facingUp = this._closestFace(this._tmpVector);\r\n            // Unignore faces\r\n            this._faceVectors.forEach((v) => {\r\n                v.ignore = false;\r\n            });\r\n\r\n            // Position the app bar on that face\r\n            this._ui.position.copyFrom(target.position);\r\n            if (facing.direction.x) {\r\n                facing.rotatedDirection.scaleToRef(target.scaling.x / 2 + this.distanceAwayFromFace, this._tmpVector);\r\n                this._ui.position.addInPlace(this._tmpVector);\r\n            }\r\n            if (facing.direction.y) {\r\n                facing.rotatedDirection.scaleToRef(target.scaling.y / 2 + this.distanceAwayFromFace, this._tmpVector);\r\n                this._ui.position.addInPlace(this._tmpVector);\r\n            }\r\n            if (facing.direction.z) {\r\n                facing.rotatedDirection.scaleToRef(target.scaling.z / 2 + this.distanceAwayFromFace, this._tmpVector);\r\n                this._ui.position.addInPlace(this._tmpVector);\r\n            }\r\n\r\n            // Rotate to be oriented properly to the camera\r\n            if (!this._ui.rotationQuaternion) {\r\n                this._ui.rotationQuaternion = Quaternion.RotationYawPitchRoll(this._ui.rotation.y, this._ui.rotation.x, this._ui.rotation.z);\r\n            }\r\n            facing.rotatedDirection.scaleToRef(-1, this._tmpVector);\r\n            this._lookAtToRef(this._tmpVector, facingUp.rotatedDirection, this._ui.rotationQuaternion);\r\n\r\n            // Place ui the correct distance from the bottom of the mesh\r\n            if (facingUp.direction.x) {\r\n                this._ui.up.scaleToRef(this.distanceAwayFromBottomOfFace - target.scaling.x / 2, this._tmpVector);\r\n            }\r\n            if (facingUp.direction.y) {\r\n                this._ui.up.scaleToRef(this.distanceAwayFromBottomOfFace - target.scaling.y / 2, this._tmpVector);\r\n            }\r\n            if (facingUp.direction.z) {\r\n                this._ui.up.scaleToRef(this.distanceAwayFromBottomOfFace - target.scaling.z / 2, this._tmpVector);\r\n            }\r\n            this._ui.position.addInPlace(this._tmpVector);\r\n        });\r\n    }\r\n\r\n    /**\r\n     *  Detaches the behavior from the mesh\r\n     */\r\n    detach() {\r\n        this._scene.onBeforeRenderObservable.remove(this._onRenderObserver);\r\n    }\r\n}\r\n"]}