{"version": 3, "file": "anaglyphArcRotateCamera.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Cameras/Stereoscopic/anaglyphArcRotateCamera.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAEhE,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,EAAE,8BAA8B,EAAE,MAAM,yCAAyC,CAAC;AAEzF,IAAI,CAAC,kBAAkB,CAAC,yBAAyB,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;IACxE,OAAO,GAAG,EAAE,CAAC,IAAI,uBAAuB,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;AAClH,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACH,MAAM,OAAO,uBAAwB,SAAQ,eAAe;IACxD;;;;;;;;;OASG;IACH,YAAY,IAAY,EAAE,KAAa,EAAE,IAAY,EAAE,MAAc,EAAE,MAAe,EAAE,kBAA0B,EAAE,KAAa;QAC7H,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QAa1C,gBAAW,GAAG,8BAA8B,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAZpE,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,8BAA8B,EAAE,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,CAAC,CAAC;IAC7G,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,yBAAyB,CAAC;IACrC,CAAC;CAGJ", "sourcesContent": ["import { Camera } from \"../../Cameras/camera\";\r\nimport { ArcRotateCamera } from \"../../Cameras/arcRotateCamera\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport { Node } from \"../../node\";\r\nimport { setStereoscopicAnaglyphRigMode } from \"../RigModes/stereoscopicAnaglyphRigMode\";\r\n\r\nNode.AddNodeConstructor(\"AnaglyphArcRotateCamera\", (name, scene, options) => {\r\n    return () => new AnaglyphArcRotateCamera(name, 0, 0, 1.0, Vector3.Zero(), options.interaxial_distance, scene);\r\n});\r\n\r\n/**\r\n * Camera used to simulate anaglyphic rendering (based on ArcRotateCamera)\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#anaglyph-cameras\r\n */\r\nexport class AnaglyphArcRotateCamera extends ArcRotateCamera {\r\n    /**\r\n     * Creates a new AnaglyphArcRotateCamera\r\n     * @param name defines camera name\r\n     * @param alpha defines alpha angle (in radians)\r\n     * @param beta defines beta angle (in radians)\r\n     * @param radius defines radius\r\n     * @param target defines camera target\r\n     * @param interaxialDistance defines distance between each color axis\r\n     * @param scene defines the hosting scene\r\n     */\r\n    constructor(name: string, alpha: number, beta: number, radius: number, target: Vector3, interaxialDistance: number, scene?: Scene) {\r\n        super(name, alpha, beta, radius, target, scene);\r\n        this.interaxialDistance = interaxialDistance;\r\n        this.setCameraRigMode(Camera.RIG_MODE_STEREOSCOPIC_ANAGLYPH, { interaxialDistance: interaxialDistance });\r\n    }\r\n\r\n    /**\r\n     * Gets camera class name\r\n     * @returns AnaglyphArcRotateCamera\r\n     */\r\n    public getClassName(): string {\r\n        return \"AnaglyphArcRotateCamera\";\r\n    }\r\n\r\n    protected _setRigMode = setStereoscopicAnaglyphRigMode.bind(null, this);\r\n}\r\n"]}