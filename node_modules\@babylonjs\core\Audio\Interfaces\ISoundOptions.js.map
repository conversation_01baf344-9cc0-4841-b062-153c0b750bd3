{"version": 3, "file": "ISoundOptions.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Audio/Interfaces/ISoundOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\r\n * Interface used to define options for Sound class\r\n */\r\nexport interface ISoundOptions {\r\n    /**\r\n     * Does the sound autoplay once loaded.\r\n     */\r\n    autoplay?: boolean;\r\n    /**\r\n     * Does the sound loop after it finishes playing once.\r\n     */\r\n    loop?: boolean;\r\n    /**\r\n     * Sound's volume\r\n     */\r\n    volume?: number;\r\n    /**\r\n     * Is it a spatial sound?\r\n     */\r\n    spatialSound?: boolean;\r\n    /**\r\n     * Maximum distance to hear that sound\r\n     */\r\n    maxDistance?: number;\r\n    /**\r\n     * Uses user defined attenuation function\r\n     */\r\n    useCustomAttenuation?: boolean;\r\n    /**\r\n     * Define the roll off factor of spatial sounds.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    rolloffFactor?: number;\r\n    /**\r\n     * Define the reference distance the sound should be heard perfectly.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    refDistance?: number;\r\n    /**\r\n     * Define the distance attenuation model the sound will follow.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    distanceModel?: string;\r\n    /**\r\n     * Defines the playback speed (1 by default)\r\n     */\r\n    playbackRate?: number;\r\n    /**\r\n     * Defines if the sound is from a streaming source\r\n     */\r\n    streaming?: boolean;\r\n    /**\r\n     * Defines an optional length (in seconds) inside the sound file\r\n     */\r\n    length?: number;\r\n    /**\r\n     * Defines an optional offset (in seconds) inside the sound file\r\n     */\r\n    offset?: number;\r\n    /**\r\n     * If true, URLs will not be required to state the audio file codec to use.\r\n     */\r\n    skipCodecCheck?: boolean;\r\n}\r\n"]}