{"version": 3, "file": "arcRotateCameraInputsManager.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Cameras/arcRotateCameraInputsManager.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,4BAA4B,EAAE,MAAM,gDAAgD,CAAC;AAC9F,OAAO,EAAE,gCAAgC,EAAE,MAAM,oDAAoD,CAAC;AACtG,OAAO,EAAE,8BAA8B,EAAE,MAAM,kDAAkD,CAAC;AAClG,OAAO,EAAE,mBAAmB,EAAE,MAAM,gCAAgC,CAAC;AAErE;;;;GAIG;AACH,MAAM,OAAO,4BAA6B,SAAQ,mBAAoC;IAClF;;;OAGG;IACH,YAAY,MAAuB;QAC/B,KAAK,CAAC,MAAM,CAAC,CAAC;IAClB,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,IAAI,CAAC,GAAG,CAAC,IAAI,8BAA8B,EAAE,CAAC,CAAC;QAC/C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,IAAI,CAAC,GAAG,CAAC,IAAI,4BAA4B,EAAE,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,IAAI,CAAC,GAAG,CAAC,IAAI,gCAAgC,EAAE,CAAC,CAAC;QACjD,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ", "sourcesContent": ["import type { ArcRotateCamera } from \"./arcRotateCamera\";\r\nimport { ArcRotateCameraPointersInput } from \"../Cameras/Inputs/arcRotateCameraPointersInput\";\r\nimport { ArcRotateCameraKeyboardMoveInput } from \"../Cameras/Inputs/arcRotateCameraKeyboardMoveInput\";\r\nimport { ArcRotateCameraMouseWheelInput } from \"../Cameras/Inputs/arcRotateCameraMouseWheelInput\";\r\nimport { CameraInputsManager } from \"../Cameras/cameraInputsManager\";\r\n\r\n/**\r\n * Default Inputs manager for the ArcRotateCamera.\r\n * It groups all the default supported inputs for ease of use.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n */\r\nexport class ArcRotateCameraInputsManager extends CameraInputsManager<ArcRotateCamera> {\r\n    /**\r\n     * Instantiates a new ArcRotateCameraInputsManager.\r\n     * @param camera Defines the camera the inputs belong to\r\n     */\r\n    constructor(camera: ArcRotateCamera) {\r\n        super(camera);\r\n    }\r\n\r\n    /**\r\n     * Add mouse wheel input support to the input manager.\r\n     * @returns the current input manager\r\n     */\r\n    public addMouseWheel(): ArcRotateCameraInputsManager {\r\n        this.add(new ArcRotateCameraMouseWheelInput());\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Add pointers input support to the input manager.\r\n     * @returns the current input manager\r\n     */\r\n    public addPointers(): ArcRotateCameraInputsManager {\r\n        this.add(new ArcRotateCameraPointersInput());\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Add keyboard input support to the input manager.\r\n     * @returns the current input manager\r\n     */\r\n    public addKeyboard(): ArcRotateCameraInputsManager {\r\n        this.add(new ArcRotateCameraKeyboardMoveInput());\r\n        return this;\r\n    }\r\n}\r\n"]}