{"version": 3, "file": "actionEvent.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Actions/actionEvent.ts"], "names": [], "mappings": "AAwBA;;GAEG;AACH,MAAM,OAAO,WAAW;IACpB;;;;;;;;OAQG;IACH;IACI,mDAAmD;IAC5C,MAAW;IAClB,2DAA2D;IACpD,QAAgB;IACvB,2DAA2D;IACpD,QAAgB;IACvB,0DAA0D;IACnD,gBAAwC;IAC/C,kEAAkE;IAC3D,WAAiB;IACxB,oCAAoC;IAC7B,cAAoB;QAVpB,WAAM,GAAN,MAAM,CAAK;QAEX,aAAQ,GAAR,QAAQ,CAAQ;QAEhB,aAAQ,GAAR,QAAQ,CAAQ;QAEhB,qBAAgB,GAAhB,gBAAgB,CAAwB;QAExC,gBAAW,GAAX,WAAW,CAAM;QAEjB,mBAAc,GAAd,cAAc,CAAM;IAC5B,CAAC;IAEJ;;;;;;OAMG;IACI,MAAM,CAAC,SAAS,CAAC,MAAoB,EAAE,GAAS,EAAE,cAAoB;QACzE,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QAChC,OAAO,IAAI,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,gBAAgB,IAAI,MAAM,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC;IAC1H,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,mBAAmB,CAAC,MAAc,EAAE,KAAY,EAAE,GAAS,EAAE,cAAoB;QAC3F,OAAO,IAAI,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,gBAAgB,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC;IAChH,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,kBAAkB,CAAC,KAAY,EAAE,GAAQ;QACnD,OAAO,IAAI,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;IAC9F,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,sBAAsB,CAAC,IAAS,EAAE,UAAmB,EAAE,GAAW,EAAE,cAAoB;QAClG,OAAO,IAAI,WAAW,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC;IACxF,CAAC;CACJ", "sourcesContent": ["import type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Sprite } from \"../Sprites/sprite\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { Vector2 } from \"../Maths/math.vector\";\r\n\r\n/**\r\n * Interface used to define ActionEvent\r\n */\r\nexport interface IActionEvent {\r\n    /** The mesh or sprite that triggered the action */\r\n    source: any;\r\n    /** The X mouse cursor position at the time of the event */\r\n    pointerX: number;\r\n    /** The Y mouse cursor position at the time of the event */\r\n    pointerY: number;\r\n    /** The mesh that is currently pointed at (can be null) */\r\n    meshUnderPointer: Nullable<AbstractMesh>;\r\n    /** the original (browser) event that triggered the ActionEvent */\r\n    sourceEvent?: any;\r\n    /** additional data for the event */\r\n    additionalData?: any;\r\n}\r\n\r\n/**\r\n * ActionEvent is the event being sent when an action is triggered.\r\n */\r\nexport class ActionEvent implements IActionEvent {\r\n    /**\r\n     * Creates a new ActionEvent\r\n     * @param source The mesh or sprite that triggered the action\r\n     * @param pointerX The X mouse cursor position at the time of the event\r\n     * @param pointerY The Y mouse cursor position at the time of the event\r\n     * @param meshUnderPointer The mesh that is currently pointed at (can be null)\r\n     * @param sourceEvent the original (browser) event that triggered the ActionEvent\r\n     * @param additionalData additional data for the event\r\n     */\r\n    constructor(\r\n        /** The mesh or sprite that triggered the action */\r\n        public source: any,\r\n        /** The X mouse cursor position at the time of the event */\r\n        public pointerX: number,\r\n        /** The Y mouse cursor position at the time of the event */\r\n        public pointerY: number,\r\n        /** The mesh that is currently pointed at (can be null) */\r\n        public meshUnderPointer: Nullable<AbstractMesh>,\r\n        /** the original (browser) event that triggered the ActionEvent */\r\n        public sourceEvent?: any,\r\n        /** additional data for the event */\r\n        public additionalData?: any\r\n    ) {}\r\n\r\n    /**\r\n     * Helper function to auto-create an ActionEvent from a source mesh.\r\n     * @param source The source mesh that triggered the event\r\n     * @param evt The original (browser) event\r\n     * @param additionalData additional data for the event\r\n     * @returns the new ActionEvent\r\n     */\r\n    public static CreateNew(source: AbstractMesh, evt?: any, additionalData?: any): ActionEvent {\r\n        const scene = source.getScene();\r\n        return new ActionEvent(source, scene.pointerX, scene.pointerY, scene.meshUnderPointer || source, evt, additionalData);\r\n    }\r\n\r\n    /**\r\n     * Helper function to auto-create an ActionEvent from a source sprite\r\n     * @param source The source sprite that triggered the event\r\n     * @param scene Scene associated with the sprite\r\n     * @param evt The original (browser) event\r\n     * @param additionalData additional data for the event\r\n     * @returns the new ActionEvent\r\n     */\r\n    public static CreateNewFromSprite(source: Sprite, scene: Scene, evt?: any, additionalData?: any): ActionEvent {\r\n        return new ActionEvent(source, scene.pointerX, scene.pointerY, scene.meshUnderPointer, evt, additionalData);\r\n    }\r\n\r\n    /**\r\n     * Helper function to auto-create an ActionEvent from a scene. If triggered by a mesh use ActionEvent.CreateNew\r\n     * @param scene the scene where the event occurred\r\n     * @param evt The original (browser) event\r\n     * @returns the new ActionEvent\r\n     */\r\n    public static CreateNewFromScene(scene: Scene, evt: any): ActionEvent {\r\n        return new ActionEvent(null, scene.pointerX, scene.pointerY, scene.meshUnderPointer, evt);\r\n    }\r\n\r\n    /**\r\n     * Helper function to auto-create an ActionEvent from a primitive\r\n     * @param prim defines the target primitive\r\n     * @param pointerPos defines the pointer position\r\n     * @param evt The original (browser) event\r\n     * @param additionalData additional data for the event\r\n     * @returns the new ActionEvent\r\n     */\r\n    public static CreateNewFromPrimitive(prim: any, pointerPos: Vector2, evt?: Event, additionalData?: any): ActionEvent {\r\n        return new ActionEvent(prim, pointerPos.x, pointerPos.y, null, evt, additionalData);\r\n    }\r\n}\r\n"]}