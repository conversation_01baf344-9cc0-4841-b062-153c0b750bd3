{"version": 3, "file": "nodeMaterialDecorator.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Materials/Node/nodeMaterialDecorator.ts"], "names": [], "mappings": "AAGA;;GAEG;AACH,MAAM,CAAN,IAAY,sBAWX;AAXD,WAAY,sBAAsB;IAC9B,4BAA4B;IAC5B,yEAAO,CAAA;IACP,0BAA0B;IAC1B,qEAAK,CAAA;IACL,wBAAwB;IACxB,iEAAG,CAAA;IACH,4BAA4B;IAC5B,yEAAO,CAAA;IACP,mCAAmC;IACnC,mEAAI,CAAA;AACR,CAAC,EAXW,sBAAsB,KAAtB,sBAAsB,QAWjC;AAqDD;;;;;;GAMG;AACH,MAAM,UAAU,sBAAsB,CAClC,WAAmB,EACnB,eAAuC,sBAAsB,CAAC,OAAO,EACrE,YAAoB,YAAY,EAChC,OAAiC;IAEjC,OAAO,CAAC,MAAW,EAAE,WAAmB,EAAE,EAAE;QACxC,IAAI,SAAS,GAAqC,MAAM,CAAC,UAAU,CAAC;QACpE,IAAI,CAAC,SAAS,EAAE;YACZ,SAAS,GAAG,EAAE,CAAC;YACf,MAAM,CAAC,UAAU,GAAG,SAAS,CAAC;SACjC;QACD,SAAS,CAAC,IAAI,CAAC;YACX,YAAY,EAAE,WAAW;YACzB,WAAW,EAAE,WAAW;YACxB,IAAI,EAAE,YAAY;YAClB,SAAS,EAAE,SAAS;YACpB,OAAO,EAAE,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE;SACzB,CAAC,CAAC;IACP,CAAC,CAAC;AACN,CAAC", "sourcesContent": ["declare type Scene = import(\"../../scene\").Scene;\r\ndeclare type NodeMaterialBlock = import(\"./nodeMaterialBlock\").NodeMaterialBlock;\r\n\r\n/**\r\n * Enum defining the type of properties that can be edited in the property pages in the NME\r\n */\r\nexport enum PropertyTypeForEdition {\r\n    /** property is a boolean */\r\n    Boolean,\r\n    /** property is a float */\r\n    Float,\r\n    /** property is a int */\r\n    Int,\r\n    /** property is a Vector2 */\r\n    Vector2,\r\n    /** property is a list of values */\r\n    List,\r\n}\r\n\r\n/**\r\n * Interface that defines an option in a variable of type list\r\n */\r\nexport interface IEditablePropertyListOption {\r\n    /** label of the option */\r\n    label: string;\r\n    /** value of the option */\r\n    value: number;\r\n}\r\n\r\n/**\r\n * Interface that defines the options available for an editable property\r\n */\r\nexport interface IEditablePropertyOption {\r\n    /** min value */\r\n    min?: number;\r\n    /** max value */\r\n    max?: number;\r\n    /** notifiers: indicates which actions to take when the property is changed */\r\n    notifiers?: {\r\n        /** the material should be rebuilt */\r\n        rebuild?: boolean;\r\n        /** the preview should be updated */\r\n        update?: boolean;\r\n        /** the onPreviewCommandActivated observer of the preview manager should be triggered */\r\n        activatePreviewCommand?: boolean;\r\n        /** a callback to trigger */\r\n        callback?: (scene: Scene, block: NodeMaterialBlock) => boolean | undefined | void;\r\n        /** a callback to validate the property. Returns true if the property is ok, else false. If false, the rebuild/update/callback events won't be called */\r\n        onValidation?: (block: NodeMaterialBlock, propertyName: string) => boolean;\r\n    };\r\n    /** list of the options for a variable of type list */\r\n    options?: IEditablePropertyListOption[];\r\n}\r\n\r\n/**\r\n * Interface that describes an editable property\r\n */\r\nexport interface IPropertyDescriptionForEdition {\r\n    /** name of the property */\r\n    propertyName: string;\r\n    /** display name of the property */\r\n    displayName: string;\r\n    /** type of the property */\r\n    type: PropertyTypeForEdition;\r\n    /** group of the property - all properties with the same group value will be displayed in a specific section */\r\n    groupName: string;\r\n    /** options for the property */\r\n    options: IEditablePropertyOption;\r\n}\r\n\r\n/**\r\n * Decorator that flags a property in a node material block as being editable\r\n * @param displayName\r\n * @param propertyType\r\n * @param groupName\r\n * @param options\r\n */\r\nexport function editableInPropertyPage(\r\n    displayName: string,\r\n    propertyType: PropertyTypeForEdition = PropertyTypeForEdition.Boolean,\r\n    groupName: string = \"PROPERTIES\",\r\n    options?: IEditablePropertyOption\r\n) {\r\n    return (target: any, propertyKey: string) => {\r\n        let propStore: IPropertyDescriptionForEdition[] = target._propStore;\r\n        if (!propStore) {\r\n            propStore = [];\r\n            target._propStore = propStore;\r\n        }\r\n        propStore.push({\r\n            propertyName: propertyKey,\r\n            displayName: displayName,\r\n            type: propertyType,\r\n            groupName: groupName,\r\n            options: options ?? {},\r\n        });\r\n    };\r\n}\r\n"]}