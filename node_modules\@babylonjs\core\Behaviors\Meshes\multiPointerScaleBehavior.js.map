{"version": 3, "file": "multiPointerScaleBehavior.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Behaviors/Meshes/multiPointerScaleBehavior.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAKlD;;GAEG;AACH,MAAM,OAAO,yBAAyB;IASlC;;OAEG;IACH;QATQ,mBAAc,GAAG,CAAC,CAAC;QACnB,kBAAa,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrC,iBAAY,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEpC,yBAAoB,GAA8B,IAAI,CAAC;QAM3D,IAAI,CAAC,cAAc,GAAG,IAAI,mBAAmB,CAAC,EAAE,CAAC,CAAC;QAClD,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG,KAAK,CAAC;QACzC,IAAI,CAAC,cAAc,GAAG,IAAI,mBAAmB,CAAC,EAAE,CAAC,CAAC;QAClD,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG,KAAK,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,IAAI,KAAI,CAAC;IAER,mBAAmB;QACvB,OAAO,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,MAAM,EAAE,CAAC;IACxG,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,SAAe;QACzB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAE5B,sFAAsF;QACtF,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC/C,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;gBAC9D,IAAI,IAAI,CAAC,cAAc,CAAC,wBAAwB,IAAI,IAAI,CAAC,cAAc,CAAC,wBAAwB,EAAE;oBAC9F,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;iBACrC;qBAAM;oBACH,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;oBAC/C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;iBACpD;aACJ;QACL,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC/C,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;gBAC9D,IAAI,IAAI,CAAC,cAAc,CAAC,wBAAwB,IAAI,IAAI,CAAC,cAAc,CAAC,wBAAwB,EAAE;oBAC9F,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;iBACrC;qBAAM;oBACH,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;oBAC/C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;iBACpD;aACJ;QACL,CAAC,CAAC,CAAC;QAEH,2FAA2F;QAC3F,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC5D,QAAQ,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC/B,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;oBAC9D,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC;oBAC/D,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;iBAC3D;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC3C,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAE3C,sFAAsF;QACtF,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC/E,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;gBAC9D,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;gBAC/E,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,IAAI,EAAE;oBACxB,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;iBACxC;aACJ;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IACD;;OAEG;IACI,MAAM;QACT,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACtF,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC5D,QAAQ,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;YACvC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;YAClC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC;CACJ", "sourcesContent": ["import type { Mesh } from \"../../Meshes/mesh\";\r\nimport type { Behavior } from \"../behavior\";\r\nimport { PointerDragBehavior } from \"./pointerDragBehavior\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { Scene } from \"../../scene\";\r\n\r\n/**\r\n * A behavior that when attached to a mesh will allow the mesh to be scaled\r\n */\r\nexport class MultiPointerScaleBehavior implements Behavior<Mesh> {\r\n    private _dragBehaviorA: PointerDragBehavior;\r\n    private _dragBehaviorB: PointerDragBehavior;\r\n    private _startDistance = 0;\r\n    private _initialScale = new Vector3(0, 0, 0);\r\n    private _targetScale = new Vector3(0, 0, 0);\r\n    private _ownerNode: Mesh;\r\n    private _sceneRenderObserver: Nullable<Observer<Scene>> = null;\r\n\r\n    /**\r\n     * Instantiate a new behavior that when attached to a mesh will allow the mesh to be scaled\r\n     */\r\n    constructor() {\r\n        this._dragBehaviorA = new PointerDragBehavior({});\r\n        this._dragBehaviorA.moveAttached = false;\r\n        this._dragBehaviorB = new PointerDragBehavior({});\r\n        this._dragBehaviorB.moveAttached = false;\r\n    }\r\n\r\n    /**\r\n     *  The name of the behavior\r\n     */\r\n    public get name(): string {\r\n        return \"MultiPointerScale\";\r\n    }\r\n\r\n    /**\r\n     *  Initializes the behavior\r\n     */\r\n    public init() {}\r\n\r\n    private _getCurrentDistance() {\r\n        return this._dragBehaviorA.lastDragPosition.subtract(this._dragBehaviorB.lastDragPosition).length();\r\n    }\r\n\r\n    /**\r\n     * Attaches the scale behavior the passed in mesh\r\n     * @param ownerNode The mesh that will be scaled around once attached\r\n     */\r\n    public attach(ownerNode: Mesh): void {\r\n        this._ownerNode = ownerNode;\r\n\r\n        // Create 2 drag behaviors such that each will only be triggered by a separate pointer\r\n        this._dragBehaviorA.onDragStartObservable.add(() => {\r\n            if (this._dragBehaviorA.dragging && this._dragBehaviorB.dragging) {\r\n                if (this._dragBehaviorA.currentDraggingPointerId == this._dragBehaviorB.currentDraggingPointerId) {\r\n                    this._dragBehaviorA.releaseDrag();\r\n                } else {\r\n                    this._initialScale.copyFrom(ownerNode.scaling);\r\n                    this._startDistance = this._getCurrentDistance();\r\n                }\r\n            }\r\n        });\r\n        this._dragBehaviorB.onDragStartObservable.add(() => {\r\n            if (this._dragBehaviorA.dragging && this._dragBehaviorB.dragging) {\r\n                if (this._dragBehaviorA.currentDraggingPointerId == this._dragBehaviorB.currentDraggingPointerId) {\r\n                    this._dragBehaviorB.releaseDrag();\r\n                } else {\r\n                    this._initialScale.copyFrom(ownerNode.scaling);\r\n                    this._startDistance = this._getCurrentDistance();\r\n                }\r\n            }\r\n        });\r\n\r\n        // Once both drag behaviors are active scale based on the distance between the two pointers\r\n        [this._dragBehaviorA, this._dragBehaviorB].forEach((behavior) => {\r\n            behavior.onDragObservable.add(() => {\r\n                if (this._dragBehaviorA.dragging && this._dragBehaviorB.dragging) {\r\n                    const ratio = this._getCurrentDistance() / this._startDistance;\r\n                    this._initialScale.scaleToRef(ratio, this._targetScale);\r\n                }\r\n            });\r\n        });\r\n\r\n        ownerNode.addBehavior(this._dragBehaviorA);\r\n        ownerNode.addBehavior(this._dragBehaviorB);\r\n\r\n        // On every frame move towards target scaling to avoid jitter caused by vr controllers\r\n        this._sceneRenderObserver = ownerNode.getScene().onBeforeRenderObservable.add(() => {\r\n            if (this._dragBehaviorA.dragging && this._dragBehaviorB.dragging) {\r\n                const change = this._targetScale.subtract(ownerNode.scaling).scaleInPlace(0.1);\r\n                if (change.length() > 0.01) {\r\n                    ownerNode.scaling.addInPlace(change);\r\n                }\r\n            }\r\n        });\r\n    }\r\n    /**\r\n     *  Detaches the behavior from the mesh\r\n     */\r\n    public detach(): void {\r\n        this._ownerNode.getScene().onBeforeRenderObservable.remove(this._sceneRenderObserver);\r\n        [this._dragBehaviorA, this._dragBehaviorB].forEach((behavior) => {\r\n            behavior.onDragStartObservable.clear();\r\n            behavior.onDragObservable.clear();\r\n            this._ownerNode.removeBehavior(behavior);\r\n        });\r\n    }\r\n}\r\n"]}