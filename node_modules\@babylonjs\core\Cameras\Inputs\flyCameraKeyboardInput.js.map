{"version": 3, "file": "flyCameraKeyboardInput.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Cameras/Inputs/flyCameraKeyboardInput.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAGlD,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AAIrE,OAAO,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AAEjE,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEzC;;;GAGG;AACH,MAAM,OAAO,sBAAsB;IAAnC;QAMI;;WAEG;QAEI,gBAAW,GAAG,CAAC,EAAE,CAAC,CAAC;QAE1B;;WAEG;QAEI,iBAAY,GAAG,CAAC,EAAE,CAAC,CAAC;QAE3B;;WAEG;QAEI,WAAM,GAAG,CAAC,EAAE,CAAC,CAAC;QAErB;;WAEG;QAEI,aAAQ,GAAG,CAAC,EAAE,CAAC,CAAC;QAEvB;;WAEG;QAEI,cAAS,GAAG,CAAC,EAAE,CAAC,CAAC;QAExB;;WAEG;QAEI,aAAQ,GAAG,CAAC,EAAE,CAAC,CAAC;QAEf,UAAK,GAAG,IAAI,KAAK,EAAU,CAAC;IAgJxC,CAAC;IA1IG;;;OAGG;IACI,aAAa,CAAC,gBAA0B;QAC3C,8CAA8C;QAC9C,gBAAgB,GAAG,KAAK,CAAC,gCAAgC,CAAC,SAAS,CAAC,CAAC;QACrE,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,OAAO;SACV;QAED,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,GAAG,CAAC,GAAG,EAAE;YACtE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACrE,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;YAEvB,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB,CAAC,OAAO,EAAE;gBAC1C,IACI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBAC5C,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBAC7C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBACvC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBACzC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBACzC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAC5C;oBACE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;oBAE9C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;wBACd,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;qBAChC;oBACD,IAAI,CAAC,gBAAgB,EAAE;wBACnB,GAAG,CAAC,cAAc,EAAE,CAAC;qBACxB;iBACJ;aACJ;iBAAM;gBACH,IACI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBAC5C,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBAC7C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBACvC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBACzC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBACzC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAC5C;oBACE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;oBAE9C,IAAI,KAAK,IAAI,CAAC,EAAE;wBACZ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;qBAC/B;oBACD,IAAI,CAAC,gBAAgB,EAAE;wBACnB,GAAG,CAAC,cAAc,EAAE,CAAC;qBACxB;iBACJ;aACJ;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;aACrE;YAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;aAC1E;YACD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAChC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;SACrC;QACD,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,wBAAwB,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,YAAY;QACf,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAC3B,WAAW;YACX,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBACpD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAClC,MAAM,KAAK,GAAG,MAAM,CAAC,wBAAwB,EAAE,CAAC;gBAEhD,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC1C,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;iBACtD;qBAAM,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;oBAClD,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;iBACvD;qBAAM,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC5C,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;iBACtD;qBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC9C,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;iBACvD;qBAAM,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC/C,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;iBACtD;qBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC9C,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;iBACvD;gBAED,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,oBAAoB,EAAE;oBACxC,MAAM,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;iBAClC;gBAED,MAAM,CAAC,aAAa,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;gBAClE,OAAO,CAAC,oBAAoB,CAAC,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC,sBAAsB,EAAE,MAAM,CAAC,qBAAqB,CAAC,CAAC;gBAClH,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;aACnE;SACJ;IACL,CAAC;CACJ;AAhLG;IADC,SAAS,EAAE;2DACc;AAM1B;IADC,SAAS,EAAE;4DACe;AAM3B;IADC,SAAS,EAAE;sDACS;AAMrB;IADC,SAAS,EAAE;wDACW;AAMvB;IADC,SAAS,EAAE;yDACY;AAMxB;IADC,SAAS,EAAE;wDACW;AAoJrB,gBAAiB,CAAC,wBAAwB,CAAC,GAAG,sBAAsB,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { serialize } from \"../../Misc/decorators\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { ICameraInput } from \"../../Cameras/cameraInputsManager\";\r\nimport { CameraInputTypes } from \"../../Cameras/cameraInputsManager\";\r\nimport type { FlyCamera } from \"../../Cameras/flyCamera\";\r\nimport type { Engine } from \"../../Engines/engine\";\r\nimport type { KeyboardInfo } from \"../../Events/keyboardEvents\";\r\nimport { KeyboardEventTypes } from \"../../Events/keyboardEvents\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport { Tools } from \"../../Misc/tools\";\r\n\r\n/**\r\n * Listen to keyboard events to control the camera.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n */\r\nexport class FlyCameraKeyboardInput implements ICameraInput<FlyCamera> {\r\n    /**\r\n     * Defines the camera the input is attached to.\r\n     */\r\n    public camera: FlyCamera;\r\n\r\n    /**\r\n     * The list of keyboard keys used to control the forward move of the camera.\r\n     */\r\n    @serialize()\r\n    public keysForward = [87];\r\n\r\n    /**\r\n     * The list of keyboard keys used to control the backward move of the camera.\r\n     */\r\n    @serialize()\r\n    public keysBackward = [83];\r\n\r\n    /**\r\n     * The list of keyboard keys used to control the forward move of the camera.\r\n     */\r\n    @serialize()\r\n    public keysUp = [69];\r\n\r\n    /**\r\n     * The list of keyboard keys used to control the backward move of the camera.\r\n     */\r\n    @serialize()\r\n    public keysDown = [81];\r\n\r\n    /**\r\n     * The list of keyboard keys used to control the right strafe move of the camera.\r\n     */\r\n    @serialize()\r\n    public keysRight = [68];\r\n\r\n    /**\r\n     * The list of keyboard keys used to control the left strafe move of the camera.\r\n     */\r\n    @serialize()\r\n    public keysLeft = [65];\r\n\r\n    private _keys = new Array<number>();\r\n    private _onCanvasBlurObserver: Nullable<Observer<Engine>>;\r\n    private _onKeyboardObserver: Nullable<Observer<KeyboardInfo>>;\r\n    private _engine: Engine;\r\n    private _scene: Scene;\r\n\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public attachControl(noPreventDefault?: boolean): void {\r\n        // eslint-disable-next-line prefer-rest-params\r\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(arguments);\r\n        if (this._onCanvasBlurObserver) {\r\n            return;\r\n        }\r\n\r\n        this._scene = this.camera.getScene();\r\n        this._engine = this._scene.getEngine();\r\n\r\n        this._onCanvasBlurObserver = this._engine.onCanvasBlurObservable.add(() => {\r\n            this._keys.length = 0;\r\n        });\r\n\r\n        this._onKeyboardObserver = this._scene.onKeyboardObservable.add((info) => {\r\n            const evt = info.event;\r\n\r\n            if (info.type === KeyboardEventTypes.KEYDOWN) {\r\n                if (\r\n                    this.keysForward.indexOf(evt.keyCode) !== -1 ||\r\n                    this.keysBackward.indexOf(evt.keyCode) !== -1 ||\r\n                    this.keysUp.indexOf(evt.keyCode) !== -1 ||\r\n                    this.keysDown.indexOf(evt.keyCode) !== -1 ||\r\n                    this.keysLeft.indexOf(evt.keyCode) !== -1 ||\r\n                    this.keysRight.indexOf(evt.keyCode) !== -1\r\n                ) {\r\n                    const index = this._keys.indexOf(evt.keyCode);\r\n\r\n                    if (index === -1) {\r\n                        this._keys.push(evt.keyCode);\r\n                    }\r\n                    if (!noPreventDefault) {\r\n                        evt.preventDefault();\r\n                    }\r\n                }\r\n            } else {\r\n                if (\r\n                    this.keysForward.indexOf(evt.keyCode) !== -1 ||\r\n                    this.keysBackward.indexOf(evt.keyCode) !== -1 ||\r\n                    this.keysUp.indexOf(evt.keyCode) !== -1 ||\r\n                    this.keysDown.indexOf(evt.keyCode) !== -1 ||\r\n                    this.keysLeft.indexOf(evt.keyCode) !== -1 ||\r\n                    this.keysRight.indexOf(evt.keyCode) !== -1\r\n                ) {\r\n                    const index = this._keys.indexOf(evt.keyCode);\r\n\r\n                    if (index >= 0) {\r\n                        this._keys.splice(index, 1);\r\n                    }\r\n                    if (!noPreventDefault) {\r\n                        evt.preventDefault();\r\n                    }\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    public detachControl(): void {\r\n        if (this._scene) {\r\n            if (this._onKeyboardObserver) {\r\n                this._scene.onKeyboardObservable.remove(this._onKeyboardObserver);\r\n            }\r\n\r\n            if (this._onCanvasBlurObserver) {\r\n                this._engine.onCanvasBlurObservable.remove(this._onCanvasBlurObserver);\r\n            }\r\n            this._onKeyboardObserver = null;\r\n            this._onCanvasBlurObserver = null;\r\n        }\r\n        this._keys.length = 0;\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of the current input.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"FlyCameraKeyboardInput\";\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _onLostFocus(): void {\r\n        this._keys.length = 0;\r\n    }\r\n\r\n    /**\r\n     * Get the friendly name associated with the input class.\r\n     * @returns the input friendly name\r\n     */\r\n    public getSimpleName(): string {\r\n        return \"keyboard\";\r\n    }\r\n\r\n    /**\r\n     * Update the current camera state depending on the inputs that have been used this frame.\r\n     * This is a dynamically created lambda to avoid the performance penalty of looping for inputs in the render loop.\r\n     */\r\n    public checkInputs(): void {\r\n        if (this._onKeyboardObserver) {\r\n            const camera = this.camera;\r\n            // Keyboard\r\n            for (let index = 0; index < this._keys.length; index++) {\r\n                const keyCode = this._keys[index];\r\n                const speed = camera._computeLocalCameraSpeed();\r\n\r\n                if (this.keysForward.indexOf(keyCode) !== -1) {\r\n                    camera._localDirection.copyFromFloats(0, 0, speed);\r\n                } else if (this.keysBackward.indexOf(keyCode) !== -1) {\r\n                    camera._localDirection.copyFromFloats(0, 0, -speed);\r\n                } else if (this.keysUp.indexOf(keyCode) !== -1) {\r\n                    camera._localDirection.copyFromFloats(0, speed, 0);\r\n                } else if (this.keysDown.indexOf(keyCode) !== -1) {\r\n                    camera._localDirection.copyFromFloats(0, -speed, 0);\r\n                } else if (this.keysRight.indexOf(keyCode) !== -1) {\r\n                    camera._localDirection.copyFromFloats(speed, 0, 0);\r\n                } else if (this.keysLeft.indexOf(keyCode) !== -1) {\r\n                    camera._localDirection.copyFromFloats(-speed, 0, 0);\r\n                }\r\n\r\n                if (camera.getScene().useRightHandedSystem) {\r\n                    camera._localDirection.z *= -1;\r\n                }\r\n\r\n                camera.getViewMatrix().invertToRef(camera._cameraTransformMatrix);\r\n                Vector3.TransformNormalToRef(camera._localDirection, camera._cameraTransformMatrix, camera._transformedDirection);\r\n                camera.cameraDirection.addInPlace(camera._transformedDirection);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n(<any>CameraInputTypes)[\"FlyCameraKeyboardInput\"] = FlyCameraKeyboardInput;\r\n"]}