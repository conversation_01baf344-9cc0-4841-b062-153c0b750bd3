{"version": 3, "file": "octreeSceneComponent.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Culling/Octrees/octreeSceneComponent.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AACpC,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAElD,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AACzD,OAAO,EAAE,GAAG,EAAE,MAAM,mBAAmB,CAAC;AACxC,OAAO,EAAE,uBAAuB,EAAE,MAAM,sBAAsB,CAAC;AAE/D,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AA6BxD,KAAK,CAAC,SAAS,CAAC,6BAA6B,GAAG,UAAU,WAAW,GAAG,EAAE,EAAE,QAAQ,GAAG,CAAC;IACpF,IAAI,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;IACxE,IAAI,CAAC,SAAS,EAAE;QACZ,SAAS,GAAG,IAAI,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;KACjC;IAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;QACxB,IAAI,CAAC,gBAAgB,GAAG,IAAI,MAAM,CAAe,MAAM,CAAC,qBAAqB,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;KACzG;IAED,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;IAE5C,gBAAgB;IAChB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAE9E,OAAO,IAAI,CAAC,gBAAgB,CAAC;AACjC,CAAC,CAAC;AAEF,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,iBAAiB,EAAE;IACtD,GAAG,EAAE;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAuBH;;;;;;;;GAQG;AACH,YAAY,CAAC,SAAS,CAAC,6BAA6B,GAAG,UAAU,WAAW,GAAG,EAAE,EAAE,QAAQ,GAAG,CAAC;IAC3F,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;IAC9B,IAAI,SAAS,GAAG,KAAK,CAAC,aAAa,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;IACzE,IAAI,CAAC,SAAS,EAAE;QACZ,SAAS,GAAG,IAAI,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAC5C,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;KAClC;IAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;QACxB,IAAI,CAAC,gBAAgB,GAAG,IAAI,MAAM,CAAU,MAAM,CAAC,wBAAwB,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;KACvG;IAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAE9B,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;IAE5C,gBAAgB;IAChB,MAAM,IAAI,GAAG,YAAY,CAAC,WAAW,CAAC;IACtC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IAEnF,OAAO,IAAI,CAAC,gBAAgB,CAAC;AACjC,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,OAAO,oBAAoB;IAgB7B;;;OAGG;IACH,YAAY,KAAa;QAnBzB;;WAEG;QACa,SAAI,GAAG,uBAAuB,CAAC,WAAW,CAAC;QAO3D;;WAEG;QACa,oBAAe,GAAG,IAAI,CAAC;QAgE/B,aAAQ,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAzD7D,KAAK,GAAG,KAAK,IAAW,WAAW,CAAC,gBAAgB,CAAC;QACrD,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QACD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,IAAI,CAAC,KAAK,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE7E,IAAI,CAAC,KAAK,CAAC,0BAA0B,GAAG,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnF,IAAI,CAAC,KAAK,CAAC,6BAA6B,GAAG,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzF,IAAI,CAAC,KAAK,CAAC,gCAAgC,GAAG,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnG,CAAC;IAED;;OAEG;IACI,QAAQ;QACX,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC,IAAkB,EAAE,EAAE;YAC1D,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;YAC/C,IAAI,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,IAAI,EAAE;gBACnD,MAAM,KAAK,GAAG,WAAW,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAEvD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;oBACd,WAAW,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;iBAC/C;aACJ;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,IAAkB,EAAE,EAAE;YAC3D,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;YAC/C,IAAI,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,IAAI,EAAE;gBACnD,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;aAC7B;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACI,uBAAuB;;QAC1B,OAAO,CAAA,MAAA,IAAI,CAAC,KAAK,CAAC,gBAAgB,0CAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,KAAI,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE,CAAC;IACnH,CAAC;IAED;;;;OAIG;IACI,0BAA0B,CAAC,IAAkB;QAChD,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,8BAA8B,EAAE;YAC9D,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAC7E,OAAO,aAAa,CAAC;SACxB;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;IACzD,CAAC;IAGD;;;;;OAKG;IACI,gCAAgC,CAAC,IAAkB,EAAE,QAAa;QACrE,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YACnD,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEzE,OAAO,aAAa,CAAC;SACxB;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;IACzD,CAAC;IAED;;;;;OAKG;IACI,6BAA6B,CAAC,IAAkB,EAAE,QAAkB;QACvE,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,sBAAsB,EAAE;YACtD,MAAM,MAAM,GAAG,QAAQ,CAAC,oBAAoB,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACpH,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YAEzF,OAAO,aAAa,CAAC;SACxB;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;IACzD,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,sBAAsB;IAC1B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,sBAAsB;IAC1B,CAAC;CACJ", "sourcesContent": ["import type { ISmartArrayLike } from \"../../Misc/smartArray\";\r\nimport { Scene } from \"../../scene\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport type { SubMesh } from \"../../Meshes/subMesh\";\r\nimport { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport { Ray } from \"../../Culling/ray\";\r\nimport { SceneComponentConstants } from \"../../sceneComponent\";\r\n\r\nimport { Octree } from \"./octree\";\r\nimport { EngineStore } from \"../../Engines/engineStore\";\r\n\r\ndeclare type Collider = import(\"../../Collisions/collider\").Collider;\r\n\r\ndeclare module \"../../scene\" {\r\n    export interface Scene {\r\n        /**\r\n         * @internal\r\n         * Backing Filed\r\n         */\r\n        _selectionOctree: Octree<AbstractMesh>;\r\n\r\n        /**\r\n         * Gets the octree used to boost mesh selection (picking)\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimizeOctrees\r\n         */\r\n        selectionOctree: Octree<AbstractMesh>;\r\n\r\n        /**\r\n         * Creates or updates the octree used to boost selection (picking)\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimizeOctrees\r\n         * @param maxCapacity defines the maximum capacity per leaf\r\n         * @param maxDepth defines the maximum depth of the octree\r\n         * @returns an octree of AbstractMesh\r\n         */\r\n        createOrUpdateSelectionOctree(maxCapacity?: number, maxDepth?: number): Octree<AbstractMesh>;\r\n    }\r\n}\r\n\r\nScene.prototype.createOrUpdateSelectionOctree = function (maxCapacity = 64, maxDepth = 2): Octree<AbstractMesh> {\r\n    let component = this._getComponent(SceneComponentConstants.NAME_OCTREE);\r\n    if (!component) {\r\n        component = new OctreeSceneComponent(this);\r\n        this._addComponent(component);\r\n    }\r\n\r\n    if (!this._selectionOctree) {\r\n        this._selectionOctree = new Octree<AbstractMesh>(Octree.CreationFuncForMeshes, maxCapacity, maxDepth);\r\n    }\r\n\r\n    const worldExtends = this.getWorldExtends();\r\n\r\n    // Update octree\r\n    this._selectionOctree.update(worldExtends.min, worldExtends.max, this.meshes);\r\n\r\n    return this._selectionOctree;\r\n};\r\n\r\nObject.defineProperty(Scene.prototype, \"selectionOctree\", {\r\n    get: function (this: Scene) {\r\n        return this._selectionOctree;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\ndeclare module \"../../Meshes/abstractMesh\" {\r\n    export interface AbstractMesh {\r\n        /**\r\n         * @internal\r\n         * Backing Field\r\n         */\r\n        _submeshesOctree: Octree<SubMesh>;\r\n\r\n        /**\r\n         * This function will create an octree to help to select the right submeshes for rendering, picking and collision computations.\r\n         * Please note that you must have a decent number of submeshes to get performance improvements when using an octree\r\n         * @param maxCapacity defines the maximum size of each block (64 by default)\r\n         * @param maxDepth defines the maximum depth to use (no more than 2 levels by default)\r\n         * @returns the new octree\r\n         * @see https://www.babylonjs-playground.com/#NA4OQ#12\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimizeOctrees\r\n         */\r\n        createOrUpdateSubmeshesOctree(maxCapacity?: number, maxDepth?: number): Octree<SubMesh>;\r\n    }\r\n}\r\n\r\n/**\r\n * This function will create an octree to help to select the right submeshes for rendering, picking and collision computations.\r\n * Please note that you must have a decent number of submeshes to get performance improvements when using an octree\r\n * @param maxCapacity defines the maximum size of each block (64 by default)\r\n * @param maxDepth defines the maximum depth to use (no more than 2 levels by default)\r\n * @returns the new octree\r\n * @see https://www.babylonjs-playground.com/#NA4OQ#12\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimizeOctrees\r\n */\r\nAbstractMesh.prototype.createOrUpdateSubmeshesOctree = function (maxCapacity = 64, maxDepth = 2): Octree<SubMesh> {\r\n    const scene = this.getScene();\r\n    let component = scene._getComponent(SceneComponentConstants.NAME_OCTREE);\r\n    if (!component) {\r\n        component = new OctreeSceneComponent(scene);\r\n        scene._addComponent(component);\r\n    }\r\n\r\n    if (!this._submeshesOctree) {\r\n        this._submeshesOctree = new Octree<SubMesh>(Octree.CreationFuncForSubMeshes, maxCapacity, maxDepth);\r\n    }\r\n\r\n    this.computeWorldMatrix(true);\r\n\r\n    const boundingInfo = this.getBoundingInfo();\r\n\r\n    // Update octree\r\n    const bbox = boundingInfo.boundingBox;\r\n    this._submeshesOctree.update(bbox.minimumWorld, bbox.maximumWorld, this.subMeshes);\r\n\r\n    return this._submeshesOctree;\r\n};\r\n\r\n/**\r\n * Defines the octree scene component responsible to manage any octrees\r\n * in a given scene.\r\n */\r\nexport class OctreeSceneComponent {\r\n    /**\r\n     * The component name help to identify the component in the list of scene components.\r\n     */\r\n    public readonly name = SceneComponentConstants.NAME_OCTREE;\r\n\r\n    /**\r\n     * The scene the component belongs to.\r\n     */\r\n    public scene: Scene;\r\n\r\n    /**\r\n     * Indicates if the meshes have been checked to make sure they are isEnabled()\r\n     */\r\n    public readonly checksIsEnabled = true;\r\n\r\n    /**\r\n     * Creates a new instance of the component for the given scene\r\n     * @param scene Defines the scene to register the component in\r\n     */\r\n    constructor(scene?: Scene) {\r\n        scene = scene || <Scene>EngineStore.LastCreatedScene;\r\n        if (!scene) {\r\n            return;\r\n        }\r\n        this.scene = scene;\r\n\r\n        this.scene.getActiveMeshCandidates = this.getActiveMeshCandidates.bind(this);\r\n\r\n        this.scene.getActiveSubMeshCandidates = this.getActiveSubMeshCandidates.bind(this);\r\n        this.scene.getCollidingSubMeshCandidates = this.getCollidingSubMeshCandidates.bind(this);\r\n        this.scene.getIntersectingSubMeshCandidates = this.getIntersectingSubMeshCandidates.bind(this);\r\n    }\r\n\r\n    /**\r\n     * Registers the component in a given scene\r\n     */\r\n    public register(): void {\r\n        this.scene.onMeshRemovedObservable.add((mesh: AbstractMesh) => {\r\n            const sceneOctree = this.scene.selectionOctree;\r\n            if (sceneOctree !== undefined && sceneOctree !== null) {\r\n                const index = sceneOctree.dynamicContent.indexOf(mesh);\r\n\r\n                if (index !== -1) {\r\n                    sceneOctree.dynamicContent.splice(index, 1);\r\n                }\r\n            }\r\n        });\r\n\r\n        this.scene.onMeshImportedObservable.add((mesh: AbstractMesh) => {\r\n            const sceneOctree = this.scene.selectionOctree;\r\n            if (sceneOctree !== undefined && sceneOctree !== null) {\r\n                sceneOctree.addMesh(mesh);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Return the list of active meshes\r\n     * @returns the list of active meshes\r\n     */\r\n    public getActiveMeshCandidates(): ISmartArrayLike<AbstractMesh> {\r\n        return this.scene._selectionOctree?.select(this.scene.frustumPlanes) || this.scene._getDefaultMeshCandidates();\r\n    }\r\n\r\n    /**\r\n     * Return the list of active sub meshes\r\n     * @param mesh The mesh to get the candidates sub meshes from\r\n     * @returns the list of active sub meshes\r\n     */\r\n    public getActiveSubMeshCandidates(mesh: AbstractMesh): ISmartArrayLike<SubMesh> {\r\n        if (mesh._submeshesOctree && mesh.useOctreeForRenderingSelection) {\r\n            const intersections = mesh._submeshesOctree.select(this.scene.frustumPlanes);\r\n            return intersections;\r\n        }\r\n        return this.scene._getDefaultSubMeshCandidates(mesh);\r\n    }\r\n\r\n    private _tempRay = new Ray(Vector3.Zero(), new Vector3(1, 1, 1));\r\n    /**\r\n     * Return the list of sub meshes intersecting with a given local ray\r\n     * @param mesh defines the mesh to find the submesh for\r\n     * @param localRay defines the ray in local space\r\n     * @returns the list of intersecting sub meshes\r\n     */\r\n    public getIntersectingSubMeshCandidates(mesh: AbstractMesh, localRay: Ray): ISmartArrayLike<SubMesh> {\r\n        if (mesh._submeshesOctree && mesh.useOctreeForPicking) {\r\n            Ray.TransformToRef(localRay, mesh.getWorldMatrix(), this._tempRay);\r\n            const intersections = mesh._submeshesOctree.intersectsRay(this._tempRay);\r\n\r\n            return intersections;\r\n        }\r\n        return this.scene._getDefaultSubMeshCandidates(mesh);\r\n    }\r\n\r\n    /**\r\n     * Return the list of sub meshes colliding with a collider\r\n     * @param mesh defines the mesh to find the submesh for\r\n     * @param collider defines the collider to evaluate the collision against\r\n     * @returns the list of colliding sub meshes\r\n     */\r\n    public getCollidingSubMeshCandidates(mesh: AbstractMesh, collider: Collider): ISmartArrayLike<SubMesh> {\r\n        if (mesh._submeshesOctree && mesh.useOctreeForCollisions) {\r\n            const radius = collider._velocityWorldLength + Math.max(collider._radius.x, collider._radius.y, collider._radius.z);\r\n            const intersections = mesh._submeshesOctree.intersects(collider._basePointWorld, radius);\r\n\r\n            return intersections;\r\n        }\r\n        return this.scene._getDefaultSubMeshCandidates(mesh);\r\n    }\r\n\r\n    /**\r\n     * Rebuilds the elements related to this component in case of\r\n     * context lost for instance.\r\n     */\r\n    public rebuild(): void {\r\n        // Nothing to do here.\r\n    }\r\n\r\n    /**\r\n     * Disposes the component and the associated resources.\r\n     */\r\n    public dispose(): void {\r\n        // Nothing to do here.\r\n    }\r\n}\r\n"]}