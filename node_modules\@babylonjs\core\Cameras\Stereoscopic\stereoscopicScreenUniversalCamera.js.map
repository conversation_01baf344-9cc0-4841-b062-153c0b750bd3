{"version": 3, "file": "stereoscopicScreenUniversalCamera.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Cameras/Stereoscopic/stereoscopicScreenUniversalCamera.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAEhE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAE1D,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,QAAQ,EAAE,MAAM,2BAA2B,CAAC;AAErD;;;GAGG;AACH,MAAM,OAAO,iCAAkC,SAAQ,eAAe;IAIlE,IAAW,mBAAmB,CAAC,QAAgB;QAC3C,IAAI,CAAC,oBAAoB,GAAG,QAAQ,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED,IAAW,yBAAyB,CAAC,QAAgB;QACjD,IAAI,CAAC,0BAA0B,GAAG,QAAQ,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,IAAW,yBAAyB;QAChC,OAAO,IAAI,CAAC,0BAA0B,CAAC;IAC3C,CAAC;IACD;;;;;;;OAOG;IACH,YAAY,IAAY,EAAE,QAAiB,EAAE,KAAa,EAAE,4BAAoC,CAAC,EAAE,sBAA8B,KAAK;QAClI,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;QAChD,IAAI,CAAC,0BAA0B,GAAG,yBAAyB,CAAC;QAC5D,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,yCAAyC,EAAE;YACpE,eAAe,EAAE,CAAC;SACrB,CAAC,CAAC;QACH,IAAI,CAAC,gBAAgB,CAAC,eAAe,GAAG,CAAC,CAAC;QAC1C,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,GAAG,mBAAmB,CAAC;IACnE,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,6BAA6B,CAAC;IACzC,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,IAAY;QAC/B,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QACvE,MAAM,SAAS,GAAG,IAAI,aAAa,CAAC,KAAK,GAAG,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QACnE,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC;QAC1B,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;QACnD,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC;QAC1B,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;QACxB,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;OAEG;IACI,iBAAiB;QACpB,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;YAC5E,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAiB,CAAC;YAC1D,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YACrB,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YACrB,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;YACnB,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrC,IAAI,GAAG,CAAC,kBAAkB,EAAE;gBACxB,GAAG,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aAC5D;iBAAM;gBACH,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aACxC;YACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAiB,EAAE,WAAW,CAAC,CAAC;SAClF;IACL,CAAC;IAEO,aAAa,CAAC,MAAoB,EAAE,WAAmB;QAC3D,MAAM,CAAC,GAAG,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;QACvC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC;QAC7C,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxC,MAAM,CAAC,QAAQ,CAAC,oBAAoB,CAAC,WAAW,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACtG,MAAM,SAAS,GAAG,MAAM,CAAC,MAAuB,CAAC;QACjD,MAAM,CAAC,GAAG,SAAS,CAAC,cAAc,EAAE,CAAC;QACrC,CAAC,CAAC,wBAAwB,CAAC,WAAW,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC,gBAAgB,CAAC,CAAC,EAAE,WAAW,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3D,SAAS,CAAC,cAAc,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACvC,CAAC;IAES,WAAW;QACjB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC9D,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;YAC5E,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAiB,EAAE,WAAW,CAAC,CAAC;SAClF;IACL,CAAC;CACJ", "sourcesContent": ["import { Camera } from \"../../Cameras/camera\";\r\nimport { UniversalCamera } from \"../../Cameras/universalCamera\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Matrix, Vector3 } from \"../../Maths/math.vector\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { TargetCamera } from \"../targetCamera\";\r\nimport { TransformNode } from \"../../Meshes/transformNode\";\r\nimport { Viewport } from \"../../Maths/math.viewport\";\r\n\r\n/**\r\n * Camera used to simulate stereoscopic rendering on real screens (based on UniversalCamera)\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras\r\n */\r\nexport class StereoscopicScreenUniversalCamera extends UniversalCamera {\r\n    private _distanceToProjectionPlane: number;\r\n    private _distanceBetweenEyes: number;\r\n\r\n    public set distanceBetweenEyes(newValue: number) {\r\n        this._distanceBetweenEyes = newValue;\r\n    }\r\n\r\n    /**\r\n     * distance between the eyes\r\n     */\r\n    public get distanceBetweenEyes(): number {\r\n        return this._distanceBetweenEyes;\r\n    }\r\n\r\n    public set distanceToProjectionPlane(newValue: number) {\r\n        this._distanceToProjectionPlane = newValue;\r\n    }\r\n\r\n    /**\r\n     * Distance to projection plane (should be the same units the like distance between the eyes)\r\n     */\r\n    public get distanceToProjectionPlane(): number {\r\n        return this._distanceToProjectionPlane;\r\n    }\r\n    /**\r\n     * Creates a new StereoscopicScreenUniversalCamera\r\n     * @param name defines camera name\r\n     * @param position defines initial position\r\n     * @param scene defines the hosting scene\r\n     * @param distanceToProjectionPlane defines distance between each color axis. The rig cameras will receive this as their negative z position!\r\n     * @param distanceBetweenEyes defines is stereoscopic is done side by side or over under\r\n     */\r\n    constructor(name: string, position: Vector3, scene?: Scene, distanceToProjectionPlane: number = 1, distanceBetweenEyes: number = 0.065) {\r\n        super(name, position, scene);\r\n        this._distanceBetweenEyes = distanceBetweenEyes;\r\n        this._distanceToProjectionPlane = distanceToProjectionPlane;\r\n        this.setCameraRigMode(Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_PARALLEL, {\r\n            stereoHalfAngle: 0,\r\n        });\r\n        this._cameraRigParams.stereoHalfAngle = 0;\r\n        this._cameraRigParams.interaxialDistance = distanceBetweenEyes;\r\n    }\r\n\r\n    /**\r\n     * Gets camera class name\r\n     * @returns StereoscopicScreenUniversalCamera\r\n     */\r\n    public getClassName(): string {\r\n        return \"StereoscopicUniversalCamera\";\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public createRigCamera(name: string): Nullable<Camera> {\r\n        const camera = new TargetCamera(name, Vector3.Zero(), this.getScene());\r\n        const transform = new TransformNode(\"tm_\" + name, this.getScene());\r\n        camera.parent = transform;\r\n        transform.setPivotMatrix(Matrix.Identity(), false);\r\n        camera.isRigCamera = true;\r\n        camera.rigParent = this;\r\n        return camera;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _updateRigCameras() {\r\n        for (let cameraIndex = 0; cameraIndex < this._rigCameras.length; cameraIndex++) {\r\n            const cam = this._rigCameras[cameraIndex] as TargetCamera;\r\n            cam.minZ = this.minZ;\r\n            cam.maxZ = this.maxZ;\r\n            cam.fov = this.fov;\r\n            cam.upVector.copyFrom(this.upVector);\r\n            if (cam.rotationQuaternion) {\r\n                cam.rotationQuaternion.copyFrom(this.rotationQuaternion);\r\n            } else {\r\n                cam.rotation.copyFrom(this.rotation);\r\n            }\r\n            this._updateCamera(this._rigCameras[cameraIndex] as TargetCamera, cameraIndex);\r\n        }\r\n    }\r\n\r\n    private _updateCamera(camera: TargetCamera, cameraIndex: number) {\r\n        const b = this.distanceBetweenEyes / 2;\r\n        const z = b / this.distanceToProjectionPlane;\r\n        camera.position.copyFrom(this.position);\r\n        camera.position.addInPlaceFromFloats(cameraIndex === 0 ? -b : b, 0, -this._distanceToProjectionPlane);\r\n        const transform = camera.parent as TransformNode;\r\n        const m = transform.getPivotMatrix();\r\n        m.setTranslationFromFloats(cameraIndex === 0 ? b : -b, 0, 0);\r\n        m.setRowFromFloats(2, cameraIndex === 0 ? z : -z, 0, 1, 0);\r\n        transform.setPivotMatrix(m, false);\r\n    }\r\n\r\n    protected _setRigMode() {\r\n        this._rigCameras[0].viewport = new Viewport(0, 0, 0.5, 1);\r\n        this._rigCameras[1].viewport = new Viewport(0.5, 0, 0.5, 1.0);\r\n        for (let cameraIndex = 0; cameraIndex < this._rigCameras.length; cameraIndex++) {\r\n            this._updateCamera(this._rigCameras[cameraIndex] as TargetCamera, cameraIndex);\r\n        }\r\n    }\r\n}\r\n"]}