{"version": 3, "file": "freeCamera.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Cameras/freeCamera.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,kBAAkB,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AACnE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAGxD,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AAGpE,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAItC;;;;GAIG;AACH,MAAM,OAAO,UAAW,SAAQ,YAAY;IAkCxC;;;OAGG;IACH,IAAW,kBAAkB;QACzB,MAAM,KAAK,GAAyB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAClE,IAAI,KAAK,EAAE;YACP,OAAO,KAAK,CAAC,kBAAkB,CAAC;SACnC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;OAGG;IACH,IAAW,kBAAkB,CAAC,KAAa;QACvC,MAAM,KAAK,GAAyB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAClE,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC;SACpC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,MAAM,QAAQ,GAAgC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,MAAM,CAAC;SAC1B;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAW,MAAM,CAAC,KAAe;QAC7B,MAAM,QAAQ,GAAgC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;SAC3B;IACL,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,MAAM,QAAQ,GAAgC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,UAAU,CAAC;SAC9B;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAW,UAAU,CAAC,KAAe;QACjC,MAAM,QAAQ,GAAgC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC;SAC/B;IACL,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,MAAM,QAAQ,GAAgC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,QAAQ,CAAC;SAC5B;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAW,QAAQ,CAAC,KAAe;QAC/B,MAAM,QAAQ,GAAgC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC;SAC7B;IACL,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,MAAM,QAAQ,GAAgC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,YAAY,CAAC;SAChC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAW,YAAY,CAAC,KAAe;QACnC,MAAM,QAAQ,GAAgC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAC;SACjC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,MAAM,QAAQ,GAAgC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,QAAQ,CAAC;SAC5B;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAW,QAAQ,CAAC,KAAe;QAC/B,MAAM,QAAQ,GAAgC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC;SAC7B;IACL,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,MAAM,QAAQ,GAAgC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,SAAS,CAAC;SAC7B;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAW,SAAS,CAAC,KAAe;QAChC,MAAM,QAAQ,GAAgC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;SAC9B;IACL,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,MAAM,QAAQ,GAAgC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,cAAc,CAAC;SAClC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAW,cAAc,CAAC,KAAe;QACrC,MAAM,QAAQ,GAAgC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,cAAc,GAAG,KAAK,CAAC;SACnC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,eAAe;QACtB,MAAM,QAAQ,GAAgC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,eAAe,CAAC;SACnC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAW,eAAe,CAAC,KAAe;QACtC,MAAM,QAAQ,GAAgC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,eAAe,GAAG,KAAK,CAAC;SACpC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,MAAM,QAAQ,GAAgC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,YAAY,CAAC;SAChC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAW,YAAY,CAAC,KAAe;QACnC,MAAM,QAAQ,GAAgC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAC;SACjC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,MAAM,QAAQ,GAAgC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,QAAQ,EAAE;YACV,OAAO,QAAQ,CAAC,cAAc,CAAC;SAClC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAW,cAAc,CAAC,KAAe;QACrC,MAAM,QAAQ,GAAgC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,cAAc,GAAG,KAAK,CAAC;SACnC;IACL,CAAC;IAkBD;;;;;;;;;OASG;IACH,YAAY,IAAY,EAAE,QAAiB,EAAE,KAAa,EAAE,4BAA4B,GAAG,IAAI;QAC3F,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,4BAA4B,CAAC,CAAC;QAlR/D;;;;WAIG;QAEI,cAAS,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QAE5C;;;;WAIG;QAEI,oBAAe,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE9C;;WAEG;QAEI,oBAAe,GAAG,KAAK,CAAC;QAE/B;;WAEG;QAEI,iBAAY,GAAG,KAAK,CAAC;QAmOpB,wBAAmB,GAAG,KAAK,CAAC;QAC5B,iBAAY,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC9B,kBAAa,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC/B,iBAAY,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAwDtC,aAAa;QACL,mBAAc,GAAG,CAAC,CAAC,CAAC;QAgDpB,+BAA0B,GAAG,CAAC,WAAmB,EAAE,WAAoB,EAAE,eAAuC,IAAI,EAAE,EAAE;YAC5H,MAAM,cAAc,GAAG,CAAC,MAAe,EAAE,EAAE;gBACvC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAEnC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;gBAEvE,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,iBAAiB,EAAE;oBACxD,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;oBAC7C,IAAI,IAAI,CAAC,SAAS,IAAI,YAAY,EAAE;wBAChC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;qBAChC;iBACJ;YACL,CAAC,CAAC;YAEF,cAAc,CAAC,WAAW,CAAC,CAAC;QAChC,CAAC,CAAC;QArGE,IAAI,CAAC,MAAM,GAAG,IAAI,uBAAuB,CAAC,IAAI,CAAC,CAAC;QAChD,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,QAAQ,EAAE,CAAC;IACzC,CAAC;IAcD;;;;OAIG;IACI,aAAa,CAAC,OAAa,EAAE,gBAA0B;QAC1D,8CAA8C;QAC9C,gBAAgB,GAAG,KAAK,CAAC,gCAAgC,CAAC,SAAS,CAAC,CAAC;QACrE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;QAE5B,IAAI,CAAC,eAAe,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5C,IAAI,CAAC,cAAc,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5C,CAAC;IAKD;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,aAAa,CAAC,IAAY;QACjC,IAAI,CAAC,cAAc,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACI,iBAAiB,CAAC,YAAqB;QAC1C,IAAI,cAAuB,CAAC;QAE5B,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,cAAc,GAAG,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;SAC9F;aAAM;YACH,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;SAClC;QAED,cAAc,CAAC,uBAAuB,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAClF,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAEnD,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,CAAC;QACzD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,cAAc,EAAE,CAAC;SACjD;QAED,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC;QACxC,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QAEnD,kDAAkD;QAClD,IAAI,kBAAkB,GAAG,YAAY,CAAC;QAEtC,qEAAqE;QACrE,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,yFAAyF;YACzF,kBAAkB,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,CAAC;SAClE;QAED,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,EAAE,kBAAkB,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,0BAA0B,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC/I,CAAC;IAmBD,gBAAgB;IACT,YAAY;QACf,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;YACtC,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;SAC/C;QAED,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAE1B,KAAK,CAAC,YAAY,EAAE,CAAC;IACzB,CAAC;IAED,gBAAgB;IACT,oBAAoB;QACvB,OAAO,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC5J,CAAC;IAED,gBAAgB;IACT,eAAe;QAClB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,iBAAiB,EAAE;YAC3D,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAChD;aAAM;YACH,KAAK,CAAC,eAAe,EAAE,CAAC;SAC3B;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QACpB,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,YAAY,CAAC;IACxB,CAAC;CACJ;AA7ZG;IADC,kBAAkB,EAAE;6CACuB;AAQ5C;IADC,kBAAkB,EAAE;mDACyB;AAM9C;IADC,SAAS,EAAE;mDACmB;AAM/B;IADC,SAAS,EAAE;gDACgB", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { serializeAsVector3, serialize } from \"../Misc/decorators\";\r\nimport { Vector3, Vector2 } from \"../Maths/math.vector\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Engine } from \"../Engines/engine\";\r\nimport { TargetCamera } from \"./targetCamera\";\r\nimport { FreeCameraInputsManager } from \"./freeCameraInputsManager\";\r\nimport type { FreeCameraMouseInput } from \"../Cameras/Inputs/freeCameraMouseInput\";\r\nimport type { FreeCameraKeyboardMoveInput } from \"../Cameras/Inputs/freeCameraKeyboardMoveInput\";\r\nimport { Tools } from \"../Misc/tools\";\r\n\r\ndeclare type Collider = import(\"../Collisions/collider\").Collider;\r\n\r\n/**\r\n * This represents a free type of camera. It can be useful in First Person Shooter game for instance.\r\n * Please consider using the new UniversalCamera instead as it adds more functionality like the gamepad.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#universal-camera\r\n */\r\nexport class FreeCamera extends TargetCamera {\r\n    /**\r\n     * Define the collision ellipsoid of the camera.\r\n     * This is helpful to simulate a camera body like the player body around the camera\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_collisions#arcrotatecamera\r\n     */\r\n    @serializeAsVector3()\r\n    public ellipsoid = new Vector3(0.5, 1, 0.5);\r\n\r\n    /**\r\n     * Define an offset for the position of the ellipsoid around the camera.\r\n     * This can be helpful to determine the center of the body near the gravity center of the body\r\n     * instead of its head.\r\n     */\r\n    @serializeAsVector3()\r\n    public ellipsoidOffset = new Vector3(0, 0, 0);\r\n\r\n    /**\r\n     * Enable or disable collisions of the camera with the rest of the scene objects.\r\n     */\r\n    @serialize()\r\n    public checkCollisions = false;\r\n\r\n    /**\r\n     * Enable or disable gravity on the camera.\r\n     */\r\n    @serialize()\r\n    public applyGravity = false;\r\n\r\n    /**\r\n     * Define the input manager associated to the camera.\r\n     */\r\n    public inputs: FreeCameraInputsManager;\r\n\r\n    /**\r\n     * Gets the input sensibility for a mouse input. (default is 2000.0)\r\n     * Higher values reduce sensitivity.\r\n     */\r\n    public get angularSensibility(): number {\r\n        const mouse = <FreeCameraMouseInput>this.inputs.attached[\"mouse\"];\r\n        if (mouse) {\r\n            return mouse.angularSensibility;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * Sets the input sensibility for a mouse input. (default is 2000.0)\r\n     * Higher values reduce sensitivity.\r\n     */\r\n    public set angularSensibility(value: number) {\r\n        const mouse = <FreeCameraMouseInput>this.inputs.attached[\"mouse\"];\r\n        if (mouse) {\r\n            mouse.angularSensibility = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control the forward move of the camera.\r\n     */\r\n    public get keysUp(): number[] {\r\n        const keyboard = <FreeCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysUp;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    public set keysUp(value: number[]) {\r\n        const keyboard = <FreeCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysUp = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control the upward move of the camera.\r\n     */\r\n    public get keysUpward(): number[] {\r\n        const keyboard = <FreeCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysUpward;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    public set keysUpward(value: number[]) {\r\n        const keyboard = <FreeCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysUpward = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control the backward move of the camera.\r\n     */\r\n    public get keysDown(): number[] {\r\n        const keyboard = <FreeCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysDown;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    public set keysDown(value: number[]) {\r\n        const keyboard = <FreeCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysDown = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control the downward move of the camera.\r\n     */\r\n    public get keysDownward(): number[] {\r\n        const keyboard = <FreeCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysDownward;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    public set keysDownward(value: number[]) {\r\n        const keyboard = <FreeCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysDownward = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control the left strafe move of the camera.\r\n     */\r\n    public get keysLeft(): number[] {\r\n        const keyboard = <FreeCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysLeft;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    public set keysLeft(value: number[]) {\r\n        const keyboard = <FreeCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysLeft = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control the right strafe move of the camera.\r\n     */\r\n    public get keysRight(): number[] {\r\n        const keyboard = <FreeCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysRight;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    public set keysRight(value: number[]) {\r\n        const keyboard = <FreeCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysRight = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control the left rotation move of the camera.\r\n     */\r\n    public get keysRotateLeft(): number[] {\r\n        const keyboard = <FreeCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysRotateLeft;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    public set keysRotateLeft(value: number[]) {\r\n        const keyboard = <FreeCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysRotateLeft = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control the right rotation move of the camera.\r\n     */\r\n    public get keysRotateRight(): number[] {\r\n        const keyboard = <FreeCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysRotateRight;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    public set keysRotateRight(value: number[]) {\r\n        const keyboard = <FreeCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysRotateRight = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control the up rotation move of the camera.\r\n     */\r\n    public get keysRotateUp(): number[] {\r\n        const keyboard = <FreeCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysRotateUp;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    public set keysRotateUp(value: number[]) {\r\n        const keyboard = <FreeCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysRotateUp = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control the down rotation move of the camera.\r\n     */\r\n    public get keysRotateDown(): number[] {\r\n        const keyboard = <FreeCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysRotateDown;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    public set keysRotateDown(value: number[]) {\r\n        const keyboard = <FreeCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysRotateDown = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Event raised when the camera collide with a mesh in the scene.\r\n     */\r\n    public onCollide: (collidedMesh: AbstractMesh) => void;\r\n\r\n    private _collider: Collider;\r\n    private _needMoveForGravity = false;\r\n    private _oldPosition = Vector3.Zero();\r\n    private _diffPosition = Vector3.Zero();\r\n    private _newPosition = Vector3.Zero();\r\n\r\n    /** @internal */\r\n    public _localDirection: Vector3;\r\n    /** @internal */\r\n    public _transformedDirection: Vector3;\r\n\r\n    /**\r\n     * Instantiates a Free Camera.\r\n     * This represents a free type of camera. It can be useful in First Person Shooter game for instance.\r\n     * Please consider using the new UniversalCamera instead as it adds more functionality like touch to this camera.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#universal-camera\r\n     * @param name Define the name of the camera in the scene\r\n     * @param position Define the start position of the camera in the scene\r\n     * @param scene Define the scene the camera belongs to\r\n     * @param setActiveOnSceneIfNoneActive Defines whether the camera should be marked as active if not other active cameras have been defined\r\n     */\r\n    constructor(name: string, position: Vector3, scene?: Scene, setActiveOnSceneIfNoneActive = true) {\r\n        super(name, position, scene, setActiveOnSceneIfNoneActive);\r\n        this.inputs = new FreeCameraInputsManager(this);\r\n        this.inputs.addKeyboard().addMouse();\r\n    }\r\n\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public attachControl(noPreventDefault?: boolean): void;\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * @param ignored defines an ignored parameter kept for backward compatibility.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     * BACK COMPAT SIGNATURE ONLY.\r\n     */\r\n    public attachControl(ignored: any, noPreventDefault?: boolean): void;\r\n    /**\r\n     * Attached controls to the current camera.\r\n     * @param ignored defines an ignored parameter kept for backward compatibility.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public attachControl(ignored?: any, noPreventDefault?: boolean): void {\r\n        // eslint-disable-next-line prefer-rest-params\r\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(arguments);\r\n        this.inputs.attachElement(noPreventDefault);\r\n    }\r\n\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    public detachControl(): void {\r\n        this.inputs.detachElement();\r\n\r\n        this.cameraDirection = new Vector3(0, 0, 0);\r\n        this.cameraRotation = new Vector2(0, 0);\r\n    }\r\n\r\n    // Collisions\r\n    private _collisionMask = -1;\r\n\r\n    /**\r\n     * Define a collision mask to limit the list of object the camera can collide with\r\n     */\r\n    public get collisionMask(): number {\r\n        return this._collisionMask;\r\n    }\r\n\r\n    public set collisionMask(mask: number) {\r\n        this._collisionMask = !isNaN(mask) ? mask : -1;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _collideWithWorld(displacement: Vector3): void {\r\n        let globalPosition: Vector3;\r\n\r\n        if (this.parent) {\r\n            globalPosition = Vector3.TransformCoordinates(this.position, this.parent.getWorldMatrix());\r\n        } else {\r\n            globalPosition = this.position;\r\n        }\r\n\r\n        globalPosition.subtractFromFloatsToRef(0, this.ellipsoid.y, 0, this._oldPosition);\r\n        this._oldPosition.addInPlace(this.ellipsoidOffset);\r\n\r\n        const coordinator = this.getScene().collisionCoordinator;\r\n        if (!this._collider) {\r\n            this._collider = coordinator.createCollider();\r\n        }\r\n\r\n        this._collider._radius = this.ellipsoid;\r\n        this._collider.collisionMask = this._collisionMask;\r\n\r\n        //no need for clone, as long as gravity is not on.\r\n        let actualDisplacement = displacement;\r\n\r\n        //add gravity to the direction to prevent the dual-collision checking\r\n        if (this.applyGravity) {\r\n            //this prevents mending with cameraDirection, a global variable of the free camera class.\r\n            actualDisplacement = displacement.add(this.getScene().gravity);\r\n        }\r\n\r\n        coordinator.getNewPosition(this._oldPosition, actualDisplacement, this._collider, 3, null, this._onCollisionPositionChange, this.uniqueId);\r\n    }\r\n\r\n    private _onCollisionPositionChange = (collisionId: number, newPosition: Vector3, collidedMesh: Nullable<AbstractMesh> = null) => {\r\n        const updatePosition = (newPos: Vector3) => {\r\n            this._newPosition.copyFrom(newPos);\r\n\r\n            this._newPosition.subtractToRef(this._oldPosition, this._diffPosition);\r\n\r\n            if (this._diffPosition.length() > Engine.CollisionsEpsilon) {\r\n                this.position.addInPlace(this._diffPosition);\r\n                if (this.onCollide && collidedMesh) {\r\n                    this.onCollide(collidedMesh);\r\n                }\r\n            }\r\n        };\r\n\r\n        updatePosition(newPosition);\r\n    };\r\n\r\n    /** @internal */\r\n    public _checkInputs(): void {\r\n        if (!this._localDirection) {\r\n            this._localDirection = Vector3.Zero();\r\n            this._transformedDirection = Vector3.Zero();\r\n        }\r\n\r\n        this.inputs.checkInputs();\r\n\r\n        super._checkInputs();\r\n    }\r\n\r\n    /** @internal */\r\n    public _decideIfNeedsToMove(): boolean {\r\n        return this._needMoveForGravity || Math.abs(this.cameraDirection.x) > 0 || Math.abs(this.cameraDirection.y) > 0 || Math.abs(this.cameraDirection.z) > 0;\r\n    }\r\n\r\n    /** @internal */\r\n    public _updatePosition(): void {\r\n        if (this.checkCollisions && this.getScene().collisionsEnabled) {\r\n            this._collideWithWorld(this.cameraDirection);\r\n        } else {\r\n            super._updatePosition();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Destroy the camera and release the current resources hold by it.\r\n     */\r\n    public dispose(): void {\r\n        this.inputs.clear();\r\n        super.dispose();\r\n    }\r\n\r\n    /**\r\n     * Gets the current object class name.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"FreeCamera\";\r\n    }\r\n}\r\n"]}