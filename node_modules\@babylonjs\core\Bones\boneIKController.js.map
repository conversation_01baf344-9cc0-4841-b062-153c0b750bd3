{"version": 3, "file": "boneIKController.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Bones/boneIKController.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAGnE,OAAO,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AAC3C,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC;;;GAGG;AACH,MAAM,OAAO,gBAAgB;IAsEzB;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,QAAQ,CAAC,KAAa;QAC7B,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,YACI,IAAmB,EACnB,IAAU,EACV,OASC;QAxFL;;WAEG;QACI,mBAAc,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAEvC;;WAEG;QACI,uBAAkB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE3C;;WAEG;QACI,0BAAqB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE9C;;WAEG;QACI,cAAS,GAAG,CAAC,CAAC;QAQrB;;WAEG;QACI,gBAAW,GAAG,CAAC,CAAC;QAEf,eAAU,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;QACnC,cAAS,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QAC9B,cAAS,GAAG,IAAI,CAAC,EAAE,CAAC;QAMpB,cAAS,GAAG,IAAI,CAAC,EAAE,CAAC;QAGpB,uBAAkB,GAAG,KAAK,CAAC;QAE3B,cAAS,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;QAC5B,cAAS,GAAG,KAAK,CAAC;QAElB,gBAAW,GAAG,CAAC,CAAC;QAEhB,0BAAqB,GAAG,KAAK,CAAC;QAyClC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAE/B,IAAI,CAAC,KAAK,EAAE;YACR,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;YAClC,MAAM,CAAC,KAAK,CAAC,2DAA2D,CAAC,CAAC;YAC1E,OAAO;SACV;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAC1D,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;YAClC,MAAM,CAAC,KAAK,CAAC,sFAAsF,CAAC,CAAC;YACrG,OAAO;SACV;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAEjB,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnC,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE;YAC/C,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;YACrB,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;YACrB,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAEtB,IAAI,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE;gBAChD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;gBACjC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;aACxB;SACJ;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE1C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YAC5E,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;SAC/E;aAAM,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;YAChC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAE9B,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAC/D,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAEnD,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACjD,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACpD;aAAM;YACH,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAE9B,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC1C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YAE5E,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAEnD,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACpD;QAED,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACtE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC;QAExB,IAAI,OAAO,EAAE;YACT,IAAI,OAAO,CAAC,UAAU,EAAE;gBACpB,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;gBACrC,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;aAC5C;YAED,IAAI,OAAO,CAAC,cAAc,EAAE;gBACxB,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;gBAC7C,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;aAChD;iBAAM,IAAI,OAAO,CAAC,cAAc,EAAE;gBAC/B,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;aAChD;iBAAM,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE;gBAChC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;aACjD;YAED,IAAI,OAAO,CAAC,qBAAqB,EAAE;gBAC/B,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;aACtE;YAED,IAAI,OAAO,CAAC,SAAS,EAAE;gBACnB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;aACtC;YAED,IAAI,OAAO,CAAC,QAAQ,EAAE;gBAClB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;aAC7C;YAED,IAAI,OAAO,CAAC,QAAQ,EAAE;gBAClB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;aACpC;YAED,IAAI,OAAO,CAAC,WAAW,EAAE;gBACrB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;aAC1C;SACJ;IACL,CAAC;IAEO,YAAY,CAAC,GAAW;QAC5B,IAAI,GAAG,GAAG,CAAC,EAAE;YACT,GAAG,GAAG,CAAC,CAAC;SACX;QAED,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE,IAAI,GAAG,IAAI,SAAS,EAAE;YACnC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;SACjB;QAED,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;QAErB,MAAM,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;QAC5B,MAAM,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;QAE5B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,OAAO;SACV;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC;QACnC,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAE3C,MAAM,IAAI,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC1C,MAAM,IAAI,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAE1C,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,mBAAmB,EAAE,CAAC,CAAC;SAC1D;QAED,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,iCAAiC,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;SAC5G;aAAM,IAAI,IAAI,CAAC,cAAc,EAAE;YAC5B,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,EAAE,UAAU,CAAC,CAAC;SACnH;QAED,MAAM,OAAO,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,KAAK,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC3C,MAAM,KAAK,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC3C,MAAM,KAAK,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC3C,MAAM,MAAM,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAE5C,MAAM,OAAO,GAAG,gBAAgB,CAAC,QAAQ,CAAC;QAE1C,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAEzD,UAAU,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAE1C,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE;YACjD,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;SAChB;aAAM;YACH,MAAM,CAAC,SAAS,EAAE,CAAC;SACtB;QAED,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACrC,KAAK,CAAC,SAAS,EAAE,CAAC;QAElB,OAAO,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QACzC,KAAK,CAAC,SAAS,EAAE,CAAC;QAElB,OAAO,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACxC,KAAK,CAAC,SAAS,EAAE,CAAC;QAElB,MAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAEnD,MAAM,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;QAC5B,MAAM,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;QAE5B,IAAI,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAE1C,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,EAAE;YACpB,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;SACnC;QAED,IAAI,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD,IAAI,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAElD,IAAI,KAAK,GAAG,CAAC,EAAE;YACX,KAAK,GAAG,CAAC,CAAC;SACb;QAED,IAAI,KAAK,GAAG,CAAC,EAAE;YACX,KAAK,GAAG,CAAC,CAAC;SACb;QAED,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACZ,KAAK,GAAG,CAAC,CAAC,CAAC;SACd;QAED,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACZ,KAAK,GAAG,CAAC,CAAC,CAAC;SACd;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE9B,IAAI,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAExB,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,MAAM,CAAC,yBAAyB,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YAC/D,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAE/B,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YACrD,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAClC;aAAM;YACH,MAAM,OAAO,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAE7C,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACjC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YAEhB,MAAM,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC/C,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAClC;QAED,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,MAAM,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YACtD,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAClC;QAED,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;gBACtB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;oBACjB,UAAU,CAAC,uBAAuB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;iBACvE;gBACD,UAAU,CAAC,uBAAuB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBAClD,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBACnF,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;gBAE3E,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC3E,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;aACzB;iBAAM;gBACH,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC5D,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC9B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;aAC1B;YACD,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACpD;QAED,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QAC5D,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IAC1B,CAAC;IAEO,8BAA8B,CAAC,IAAU;QAC7C,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,EAAE;gBAC/C,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;aACnE;YACD,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;SACpG;IACL,CAAC;;AA1Wc,yBAAQ,GAAc,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;AACvH,yBAAQ,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;AACjC,yBAAQ,GAAa,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC", "sourcesContent": ["import type { Bone } from \"./bone\";\r\nimport { Vector3, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from \"../Maths/math.vector\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Space } from \"../Maths/math.axis\";\r\nimport { Logger } from \"../Misc/logger\";\r\n\r\n/**\r\n * Class used to apply inverse kinematics to bones\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/bonesSkeletons#boneikcontroller\r\n */\r\nexport class BoneIKController {\r\n    private static _TmpVecs: Vector3[] = [Vector3.Zero(), Vector3.Zero(), Vector3.Zero(), Vector3.Zero(), Vector3.Zero(), Vector3.Zero()];\r\n    private static _TmpQuat = Quaternion.Identity();\r\n    private static _TmpMats: Matrix[] = [Matrix.Identity(), Matrix.Identity()];\r\n\r\n    /**\r\n     * Gets or sets the target TransformNode\r\n     * Name kept as mesh for back compatibility\r\n     */\r\n    public targetMesh: TransformNode;\r\n\r\n    /** Gets or sets the mesh used as pole */\r\n    public poleTargetMesh: TransformNode;\r\n\r\n    /**\r\n     * Gets or sets the bone used as pole\r\n     */\r\n    public poleTargetBone: Nullable<Bone>;\r\n\r\n    /**\r\n     * Gets or sets the target position\r\n     */\r\n    public targetPosition = Vector3.Zero();\r\n\r\n    /**\r\n     * Gets or sets the pole target position\r\n     */\r\n    public poleTargetPosition = Vector3.Zero();\r\n\r\n    /**\r\n     * Gets or sets the pole target local offset\r\n     */\r\n    public poleTargetLocalOffset = Vector3.Zero();\r\n\r\n    /**\r\n     * Gets or sets the pole angle\r\n     */\r\n    public poleAngle = 0;\r\n\r\n    /**\r\n     * Gets or sets the TransformNode associated with the controller\r\n     * Name kept as mesh for back compatibility\r\n     */\r\n    public mesh: TransformNode;\r\n\r\n    /**\r\n     * The amount to slerp (spherical linear interpolation) to the target.  Set this to a value between 0 and 1 (a value of 1 disables slerp)\r\n     */\r\n    public slerpAmount = 1;\r\n\r\n    private _bone1Quat = Quaternion.Identity();\r\n    private _bone1Mat = Matrix.Identity();\r\n    private _bone2Ang = Math.PI;\r\n\r\n    private _bone1: Bone;\r\n    private _bone2: Bone;\r\n    private _bone1Length: number;\r\n    private _bone2Length: number;\r\n    private _maxAngle = Math.PI;\r\n    private _maxReach: number;\r\n\r\n    private _rightHandedSystem = false;\r\n\r\n    private _bendAxis = Vector3.Right();\r\n    private _slerping = false;\r\n\r\n    private _adjustRoll = 0;\r\n\r\n    private _notEnoughInformation = false;\r\n\r\n    /**\r\n     * Gets or sets maximum allowed angle\r\n     */\r\n    public get maxAngle(): number {\r\n        return this._maxAngle;\r\n    }\r\n\r\n    public set maxAngle(value: number) {\r\n        this._setMaxAngle(value);\r\n    }\r\n\r\n    /**\r\n     * Creates a new BoneIKController\r\n     * @param mesh defines the TransformNode to control\r\n     * @param bone defines the bone to control. The bone needs to have a parent bone. It also needs to have a length greater than 0 or a children we can use to infer its length.\r\n     * @param options defines options to set up the controller\r\n     * @param options.targetMesh\r\n     * @param options.poleTargetMesh\r\n     * @param options.poleTargetBone\r\n     * @param options.poleTargetLocalOffset\r\n     * @param options.poleAngle\r\n     * @param options.bendAxis\r\n     * @param options.maxAngle\r\n     * @param options.slerpAmount\r\n     */\r\n    constructor(\r\n        mesh: TransformNode,\r\n        bone: Bone,\r\n        options?: {\r\n            targetMesh?: TransformNode;\r\n            poleTargetMesh?: TransformNode;\r\n            poleTargetBone?: Bone;\r\n            poleTargetLocalOffset?: Vector3;\r\n            poleAngle?: number;\r\n            bendAxis?: Vector3;\r\n            maxAngle?: number;\r\n            slerpAmount?: number;\r\n        }\r\n    ) {\r\n        this._bone2 = bone;\r\n        const bone1 = bone.getParent();\r\n\r\n        if (!bone1) {\r\n            this._notEnoughInformation = true;\r\n            Logger.Error(\"BoneIKController: bone must have a parent for IK to work.\");\r\n            return;\r\n        }\r\n        this._bone1 = bone1;\r\n\r\n        if (this._bone2.children.length === 0 && !this._bone2.length) {\r\n            this._notEnoughInformation = true;\r\n            Logger.Error(\"BoneIKController: bone must not be a leaf or it should have a length for IK to work.\");\r\n            return;\r\n        }\r\n\r\n        this.mesh = mesh;\r\n\r\n        const bonePos = bone.getPosition();\r\n\r\n        if (bone.getAbsoluteTransform().determinant() > 0) {\r\n            this._rightHandedSystem = true;\r\n            this._bendAxis.x = 0;\r\n            this._bendAxis.y = 0;\r\n            this._bendAxis.z = -1;\r\n\r\n            if (bonePos.x > bonePos.y && bonePos.x > bonePos.z) {\r\n                this._adjustRoll = Math.PI * 0.5;\r\n                this._bendAxis.z = 1;\r\n            }\r\n        }\r\n\r\n        if (this._bone1.length && this._bone2.length) {\r\n            const boneScale1 = this._bone1.getScale();\r\n            const boneScale2 = this._bone2.getScale();\r\n\r\n            this._bone1Length = this._bone1.length * boneScale1.y * this.mesh.scaling.y;\r\n            this._bone2Length = this._bone2.length * boneScale2.y * this.mesh.scaling.y;\r\n        } else if (this._bone2.children[0]) {\r\n            mesh.computeWorldMatrix(true);\r\n\r\n            const pos1 = this._bone2.children[0].getAbsolutePosition(mesh);\r\n            const pos2 = this._bone2.getAbsolutePosition(mesh);\r\n            const pos3 = this._bone1.getAbsolutePosition(mesh);\r\n\r\n            this._bone2Length = Vector3.Distance(pos1, pos2);\r\n            this._bone1Length = Vector3.Distance(pos2, pos3);\r\n        } else {\r\n            mesh.computeWorldMatrix(true);\r\n\r\n            const boneScale2 = this._bone2.getScale();\r\n            this._bone2Length = this._bone2.length * boneScale2.y * this.mesh.scaling.y;\r\n\r\n            const pos2 = this._bone2.getAbsolutePosition(mesh);\r\n            const pos3 = this._bone1.getAbsolutePosition(mesh);\r\n\r\n            this._bone1Length = Vector3.Distance(pos2, pos3);\r\n        }\r\n\r\n        this._bone1.getRotationMatrixToRef(Space.WORLD, mesh, this._bone1Mat);\r\n        this.maxAngle = Math.PI;\r\n\r\n        if (options) {\r\n            if (options.targetMesh) {\r\n                this.targetMesh = options.targetMesh;\r\n                this.targetMesh.computeWorldMatrix(true);\r\n            }\r\n\r\n            if (options.poleTargetMesh) {\r\n                this.poleTargetMesh = options.poleTargetMesh;\r\n                this.poleTargetMesh.computeWorldMatrix(true);\r\n            } else if (options.poleTargetBone) {\r\n                this.poleTargetBone = options.poleTargetBone;\r\n            } else if (this._bone1.getParent()) {\r\n                this.poleTargetBone = this._bone1.getParent();\r\n            }\r\n\r\n            if (options.poleTargetLocalOffset) {\r\n                this.poleTargetLocalOffset.copyFrom(options.poleTargetLocalOffset);\r\n            }\r\n\r\n            if (options.poleAngle) {\r\n                this.poleAngle = options.poleAngle;\r\n            }\r\n\r\n            if (options.bendAxis) {\r\n                this._bendAxis.copyFrom(options.bendAxis);\r\n            }\r\n\r\n            if (options.maxAngle) {\r\n                this.maxAngle = options.maxAngle;\r\n            }\r\n\r\n            if (options.slerpAmount) {\r\n                this.slerpAmount = options.slerpAmount;\r\n            }\r\n        }\r\n    }\r\n\r\n    private _setMaxAngle(ang: number): void {\r\n        if (ang < 0) {\r\n            ang = 0;\r\n        }\r\n\r\n        if (ang > Math.PI || ang == undefined) {\r\n            ang = Math.PI;\r\n        }\r\n\r\n        this._maxAngle = ang;\r\n\r\n        const a = this._bone1Length;\r\n        const b = this._bone2Length;\r\n\r\n        this._maxReach = Math.sqrt(a * a + b * b - 2 * a * b * Math.cos(ang));\r\n    }\r\n\r\n    /**\r\n     * Force the controller to update the bones\r\n     */\r\n    public update(): void {\r\n        if (this._notEnoughInformation) {\r\n            return;\r\n        }\r\n\r\n        const target = this.targetPosition;\r\n        const poleTarget = this.poleTargetPosition;\r\n\r\n        const mat1 = BoneIKController._TmpMats[0];\r\n        const mat2 = BoneIKController._TmpMats[1];\r\n\r\n        if (this.targetMesh) {\r\n            target.copyFrom(this.targetMesh.getAbsolutePosition());\r\n        }\r\n\r\n        if (this.poleTargetBone) {\r\n            this.poleTargetBone.getAbsolutePositionFromLocalToRef(this.poleTargetLocalOffset, this.mesh, poleTarget);\r\n        } else if (this.poleTargetMesh) {\r\n            Vector3.TransformCoordinatesToRef(this.poleTargetLocalOffset, this.poleTargetMesh.getWorldMatrix(), poleTarget);\r\n        }\r\n\r\n        const bonePos = BoneIKController._TmpVecs[0];\r\n        const zaxis = BoneIKController._TmpVecs[1];\r\n        const xaxis = BoneIKController._TmpVecs[2];\r\n        const yaxis = BoneIKController._TmpVecs[3];\r\n        const upAxis = BoneIKController._TmpVecs[4];\r\n\r\n        const tmpQuat = BoneIKController._TmpQuat;\r\n\r\n        this._bone1.getAbsolutePositionToRef(this.mesh, bonePos);\r\n\r\n        poleTarget.subtractToRef(bonePos, upAxis);\r\n\r\n        if (upAxis.x == 0 && upAxis.y == 0 && upAxis.z == 0) {\r\n            upAxis.y = 1;\r\n        } else {\r\n            upAxis.normalize();\r\n        }\r\n\r\n        target.subtractToRef(bonePos, yaxis);\r\n        yaxis.normalize();\r\n\r\n        Vector3.CrossToRef(yaxis, upAxis, zaxis);\r\n        zaxis.normalize();\r\n\r\n        Vector3.CrossToRef(yaxis, zaxis, xaxis);\r\n        xaxis.normalize();\r\n\r\n        Matrix.FromXYZAxesToRef(xaxis, yaxis, zaxis, mat1);\r\n\r\n        const a = this._bone1Length;\r\n        const b = this._bone2Length;\r\n\r\n        let c = Vector3.Distance(bonePos, target);\r\n\r\n        if (this._maxReach > 0) {\r\n            c = Math.min(this._maxReach, c);\r\n        }\r\n\r\n        let acosa = (b * b + c * c - a * a) / (2 * b * c);\r\n        let acosb = (c * c + a * a - b * b) / (2 * c * a);\r\n\r\n        if (acosa > 1) {\r\n            acosa = 1;\r\n        }\r\n\r\n        if (acosb > 1) {\r\n            acosb = 1;\r\n        }\r\n\r\n        if (acosa < -1) {\r\n            acosa = -1;\r\n        }\r\n\r\n        if (acosb < -1) {\r\n            acosb = -1;\r\n        }\r\n\r\n        const angA = Math.acos(acosa);\r\n        const angB = Math.acos(acosb);\r\n\r\n        let angC = -angA - angB;\r\n\r\n        if (this._rightHandedSystem) {\r\n            Matrix.RotationYawPitchRollToRef(0, 0, this._adjustRoll, mat2);\r\n            mat2.multiplyToRef(mat1, mat1);\r\n\r\n            Matrix.RotationAxisToRef(this._bendAxis, angB, mat2);\r\n            mat2.multiplyToRef(mat1, mat1);\r\n        } else {\r\n            const _tmpVec = BoneIKController._TmpVecs[5];\r\n\r\n            _tmpVec.copyFrom(this._bendAxis);\r\n            _tmpVec.x *= -1;\r\n\r\n            Matrix.RotationAxisToRef(_tmpVec, -angB, mat2);\r\n            mat2.multiplyToRef(mat1, mat1);\r\n        }\r\n\r\n        if (this.poleAngle) {\r\n            Matrix.RotationAxisToRef(yaxis, this.poleAngle, mat2);\r\n            mat1.multiplyToRef(mat2, mat1);\r\n        }\r\n\r\n        if (this._bone1) {\r\n            if (this.slerpAmount < 1) {\r\n                if (!this._slerping) {\r\n                    Quaternion.FromRotationMatrixToRef(this._bone1Mat, this._bone1Quat);\r\n                }\r\n                Quaternion.FromRotationMatrixToRef(mat1, tmpQuat);\r\n                Quaternion.SlerpToRef(this._bone1Quat, tmpQuat, this.slerpAmount, this._bone1Quat);\r\n                angC = this._bone2Ang * (1.0 - this.slerpAmount) + angC * this.slerpAmount;\r\n\r\n                this._bone1.setRotationQuaternion(this._bone1Quat, Space.WORLD, this.mesh);\r\n                this._slerping = true;\r\n            } else {\r\n                this._bone1.setRotationMatrix(mat1, Space.WORLD, this.mesh);\r\n                this._bone1Mat.copyFrom(mat1);\r\n                this._slerping = false;\r\n            }\r\n            this._updateLinkedTransformRotation(this._bone1);\r\n        }\r\n\r\n        this._bone2.setAxisAngle(this._bendAxis, angC, Space.LOCAL);\r\n        this._updateLinkedTransformRotation(this._bone2);\r\n        this._bone2Ang = angC;\r\n    }\r\n\r\n    private _updateLinkedTransformRotation(bone: Bone): void {\r\n        if (bone._linkedTransformNode) {\r\n            if (!bone._linkedTransformNode.rotationQuaternion) {\r\n                bone._linkedTransformNode.rotationQuaternion = new Quaternion();\r\n            }\r\n            bone.getRotationQuaternionToRef(Space.LOCAL, null, bone._linkedTransformNode.rotationQuaternion);\r\n        }\r\n    }\r\n}\r\n"]}