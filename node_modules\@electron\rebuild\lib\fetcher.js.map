{"version": 3, "file": "fetcher.js", "sourceRoot": "", "sources": ["../src/fetcher.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAC1B,8CAAsB;AAEtB,MAAM,CAAC,GAAG,IAAA,eAAK,EAAC,kBAAkB,CAAC,CAAC;AAEpC,SAAS,KAAK,CAAC,CAAS;IACtB,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC5C,CAAC;AAEM,KAAK,UAAU,KAAK,CAAyE,GAAW,EAAE,YAAe,EAAE,OAAO,GAAG,CAAC;IAC3I,IAAI,OAAO,KAAK,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,wFAAwF,CAAC,CAAC;IAC7H,CAAC,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC;IACvB,IAAI;QACF,MAAM,QAAQ,GAAG,MAAM,aAAG,CAAC,GAAG,CAAC,GAAG,EAAE;YAClC,YAAY;SACb,CAAC,CAAC;QACH,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,EAAE;YAC/B,CAAC,CAAC,sBAAsB,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;YAC/C,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC;YAClB,OAAO,KAAK,CAAC,GAAG,EAAE,YAAY,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;SAC9C;QACD,CAAC,CAAC,uBAAuB,CAAC,CAAC;QAC3B,OAAO,QAAQ,CAAC,IAAU,CAAC;KAC5B;IAAC,OAAO,GAAG,EAAE;QACZ,CAAC,CAAC,gCAAgC,EAAE,GAAG,CAAC,CAAC;QACzC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC;QAClB,OAAO,KAAK,CAAC,GAAG,EAAE,YAAY,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;KAC9C;AACH,CAAC;AAnBD,sBAmBC"}