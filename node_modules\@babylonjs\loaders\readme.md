Babylon.js Loaders module
=====================

For usage documentation please visit https://doc.babylonjs.com/extensions and choose "loaders".

# Installation instructions

To install using npm :

```
npm install --save-dev @babylonjs/core @babylonjs/loaders
```

# How to use

Afterwards it can be imported to your project using:

```
import "@babylonjs/loaders/glTF";
```

This will extend Babylon's loader plugins to allow the load of gltf and glb files.

For more information you can have a look at our [ES6 dedicated documentation](https://doc.babylonjs.com/features/es6_support).
