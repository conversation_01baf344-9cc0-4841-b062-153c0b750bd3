{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,oCAAoC;AACpC,+BAA+B;AAC/B,6BAA6B;AAC7B,qCAAgF;AAChF,2CAAkD;AAElD,2CAAyB;AAEzB,MAAM,KAAK,GAAG,CAAC,CAAsB,EAAE,EAAE,CACvC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,qBAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAS,CAAC,MAAM,CAAC,CAAC,CAAC,qBAAS,CAAC,OAAO,CAAC;AAEjF,MAAM,eAAe,GAAG,CAAC,MAAoB,EAAE,UAAkB,EAAE,EAAE;IACnE,MAAM,EAAE,OAAO,KAA0B,MAAM,EAA3B,gBAAgB,UAAK,MAAM,EAAzC,WAAgC,CAAS,CAAC;IAChD,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,CACtD,CAAC,UAAU,EAAE,EAAE,CAAC,QAAQ,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,CACvD,CAAC;IACF,IAAI,aAAa,KAAK,SAAS,EAAE;QAC/B,MAAM,IAAI,KAAK,CACb,uBACE,sBAAa,CAAC,aAAoB,CACpC,mEAAmE,CACpE,CAAC;KACH;IAED,OAAO;QACL,KAAK,CAAC,MAAM,CAAC,sBAAa,CAAC,SAAS,CAAC,CAAC;QACtC,KAAK,CAAC,MAAM,CAAC,sBAAa,CAAC,sBAAsB,CAAC,CAAC;QACnD,KAAK,CAAC,MAAM,CAAC,sBAAa,CAAC,oCAAoC,CAAC,CAAC;QACjE,KAAK,CAAC,MAAM,CAAC,sBAAa,CAAC,6BAA6B,CAAC,CAAC;QAC1D,KAAK,CAAC,MAAM,CAAC,sBAAa,CAAC,qCAAqC,CAAC,CAAC;QAClE,KAAK,CAAC,MAAM,CAAC,sBAAa,CAAC,mBAAmB,CAAC,CAAC;QAChD,KAAK,CAAC,MAAM,CAAC,sBAAa,CAAC,oCAAoC,CAAC,CAAC;QACjE,KAAK,CAAC,MAAM,CAAC,sBAAa,CAAC,gCAAgC,CAAC,CAAC;KAC9D,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,cAAc,GAAG,CAAC,cAAsB,EAAE,EAAE;IAChD,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;QACnC,OAAO,IAAI,CAAC,OAAO,CACjB,cAAc,EACd,UAAU,EACV,YAAY,EACZ,8BAA8B,EAC9B,oBAAoB,CACrB,CAAC;KACH;IACD,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;QACnC,OAAO,IAAI,CAAC,OAAO,CACjB,cAAc,EACd,IAAI,EACJ,IAAI,EACJ,YAAY,EACZ,8BAA8B,EAC9B,oBAAoB,CACrB,CAAC;KACH;IACD,OAAO,cAAc,CAAC;AACxB,CAAC,CAAC;AAEF,MAAM,WAAW,GAAG,KAAK,EACvB,cAAsB,EACtB,WAAwB,EACxB,uBAAgC,EAChC,eAAoD,EACpD,SAAoC,EACpC,EAAE;IACF,MAAM,YAAY,GAAG,cAAc,CAAC,cAAc,CAAC,CAAC;IACpD,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;IAEjD,MAAM,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,oBAAQ,CAAC,CAAC;IACjD,MAAM,YAAY,GAAG,QAAQ,CAAC,WAAW,CAAC,oBAAQ,CAAC,CAAC;IACpD,+FAA+F;IAC/F,2FAA2F;IAC3F,MAAM,SAAS,GACb,aAAa,KAAK,YAAY,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;IAEnF,KAAK,MAAM,eAAe,IAAI,SAAS,EAAE;QACvC,IAAI,eAAe,KAAK,CAAC,CAAC,EAAE;YAC1B,MAAM,IAAI,KAAK,CACb,6GAA6G,CAC9G,CAAC;SACH;QAED,MAAM,gBAAgB,GAAG,eAAe,GAAG,oBAAQ,CAAC,MAAM,CAAC;QAE3D,MAAM,eAAe,GAAG,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QACnD,IAAI,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC,KAAK,eAAe,EAAE;YACjD,MAAM,IAAI,KAAK,CACb,+BAA+B,QAAQ,CACrC,WAAW,EACX,EAAE,CACH,mDAAmD,eAAe,sDAAsD,CAC1H,CAAC;SACH;QACD,MAAM,cAAc,GAAG,QAAQ,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC;QAEtD,MAAM,IAAI,GAAG,eAAe,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;QACtE,IAAI,IAAI,CAAC,MAAM,GAAG,cAAc,IAAI,uBAAuB,EAAE;YAC3D,MAAM,IAAI,KAAK,CACb,qEAAqE,cAAc,6CAA6C,IAAI,CAAC,MAAM,oFAAoF,CAChO,CAAC;SACH;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,MAAM,GAAG,GAAG,gBAAgB,GAAG,CAAC,GAAG,CAAC,CAAC;YACrC,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;YACnC,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YAEzB,IAAI,YAAY,KAAK,qBAAS,CAAC,OAAO,IAAI,QAAQ,KAAK,qBAAS,CAAC,OAAO,EAAE;gBACxE,OAAO,CAAC,IAAI,CACV,oBAAoB,SAAS,CAC3B,CAAC,CACF,gEAAgE,CAClE,CAAC;aACH;YACD,IAAI,QAAQ,KAAK,qBAAS,CAAC,OAAO,EAAE;gBAClC,IAAI,uBAAuB,EAAE;oBAC3B,MAAM,IAAI,KAAK,CACb,oEAAoE,SAAS,CAAC,CAAC,CAAC,EAAE,CACnF,CAAC;iBACH;gBACD,SAAS;aACV;YACD,QAAQ,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;SAC1B;KACF;IAED,MAAM,EAAE,CAAC,SAAS,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IAE3C,OAAO,SAAS,CAAC,MAAM,CAAC;AAC1B,CAAC,CAAC;AAEK,MAAM,kBAAkB,GAAG,KAAK,EACrC,cAAsB,EACU,EAAE;IAClC,MAAM,YAAY,GAAG,cAAc,CAAC,cAAc,CAAC,CAAC;IACpD,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;IACjD,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,oBAAQ,CAAC,GAAG,oBAAQ,CAAC,MAAM,CAAC;IAEtE,IAAI,gBAAgB,GAAG,oBAAQ,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE;QAC7C,MAAM,IAAI,KAAK,CACb,6GAA6G,CAC9G,CAAC;KACH;IACD,MAAM,eAAe,GAAI,QAAQ,CAAC,gBAAgB,CAAwB,CAAC;IAC3E,MAAM,cAAc,GAAG,QAAQ,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC;IACtD,MAAM,UAAU,GAA0B;QACxC,OAAO,EAAE,GAAG,eAAe,EAAiB;KAC7C,CAAC;IAEF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,EAAE;QACvC,MAAM,GAAG,GAAG,gBAAgB,GAAG,CAAC,GAAG,CAAC,CAAC;QACrC,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;QACnC,QAAQ,UAAU,CAAC,OAAO,EAAE;YAC1B,KAAK,oBAAW,CAAC,EAAE;gBACjB,UAAU,CAAC,CAAkB,CAAC,GAAG,YAAyB,CAAC;gBAC3D,MAAM;SACT;KACF;IAED,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC;AA7BW,QAAA,kBAAkB,sBA6B7B;AAEK,MAAM,SAAS,GAAG,KAAK,EAC5B,cAAsB,EACtB,UAAsB,EACL,EAAE;IACnB,IAAI,YAAoB,CAAC;IAEzB,QAAQ,UAAU,CAAC,OAAO,EAAE;QAC1B,KAAK,oBAAW,CAAC,EAAE;YACjB,YAAY,GAAG,MAAM,WAAW,CAC9B,cAAc,EACd,UAAU,CAAC,OAAO,EAClB,UAAU,CAAC,uBAAuB,IAAI,KAAK,EAC3C,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,EACtC,CAAC,CAAC,EAAE,EAAE,CAAC,sBAAa,CAAC,CAAC,CAAC,CACxB,CAAC;YACF,MAAM;QACR;YACE,MAAM,IAAI,KAAK,CAAC,oCAAoC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;KAC7E;IAED,0EAA0E;IAC1E,IAAI,UAAU,CAAC,yBAAyB,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC3E,MAAM,SAAS,GAAG,GAAG,cAAc,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAC3D,MAAM,MAAM,GAAG,EAAE,CAAC,SAAS,CAAC,UAAU,EAAE;YACtC,QAAQ;YACR,GAAG;YACH,SAAS;YACT,6DAA6D;YAC7D,QAAQ;YACR,SAAS;SACV,CAAC,CAAC;QACH,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACvB,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;YACxC,MAAM,IAAI,KAAK,CAAC,uCAAuC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;SACzE;KACF;IAED,OAAO,YAAY,CAAC;AACtB,CAAC,CAAC;AAtCW,QAAA,SAAS,aAsCpB"}