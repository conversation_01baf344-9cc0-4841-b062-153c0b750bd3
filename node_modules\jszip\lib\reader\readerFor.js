"use strict";

var utils = require("../utils");
var support = require("../support");
var ArrayReader = require("./ArrayReader");
var StringReader = require("./StringReader");
var NodeBufferReader = require("./NodeBufferReader");
var Uint8ArrayReader = require("./Uint8ArrayReader");

/**
 * Create a reader adapted to the data.
 * @param {String|ArrayBuffer|Uint8Array|Buffer} data the data to read.
 * @return {DataReader} the data reader.
 */
module.exports = function (data) {
    var type = utils.getTypeOf(data);
    utils.checkSupport(type);
    if (type === "string" && !support.uint8array) {
        return new StringReader(data);
    }
    if (type === "nodebuffer") {
        return new NodeBufferReader(data);
    }
    if (support.uint8array) {
        return new Uint8ArrayReader(utils.transformTo("uint8array", data));
    }
    return new ArrayReader(utils.transformTo("array", data));
};
