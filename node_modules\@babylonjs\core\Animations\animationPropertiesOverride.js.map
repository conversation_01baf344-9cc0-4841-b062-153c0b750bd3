{"version": 3, "file": "animationPropertiesOverride.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Animations/animationPropertiesOverride.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AAEpD;;GAEG;AACH,MAAM,OAAO,2BAA2B;IAAxC;QACI;;WAEG;QACI,mBAAc,GAAG,KAAK,CAAC;QAE9B;;WAEG;QACI,kBAAa,GAAG,IAAI,CAAC;QAE5B;;WAEG;QACI,aAAQ,GAAG,SAAS,CAAC,uBAAuB,CAAC;IACxD,CAAC;CAAA", "sourcesContent": ["import { Animation } from \"../Animations/animation\";\r\n\r\n/**\r\n * Class used to override all child animations of a given target\r\n */\r\nexport class AnimationPropertiesOverride {\r\n    /**\r\n     * Gets or sets a value indicating if animation blending must be used\r\n     */\r\n    public enableBlending = false;\r\n\r\n    /**\r\n     * Gets or sets the blending speed to use when enableBlending is true\r\n     */\r\n    public blendingSpeed = 0.01;\r\n\r\n    /**\r\n     * Gets or sets the default loop mode to use\r\n     */\r\n    public loopMode = Animation.ANIMATIONLOOPMODE_CYCLE;\r\n}\r\n"]}