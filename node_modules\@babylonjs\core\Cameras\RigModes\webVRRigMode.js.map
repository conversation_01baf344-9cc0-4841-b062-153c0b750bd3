{"version": 3, "file": "webVRRigMode.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Cameras/RigModes/webVRRigMode.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AACjD,OAAO,EAAE,QAAQ,EAAE,MAAM,2BAA2B,CAAC;AAErD;;GAEG;AACH,MAAM,UAAU,eAAe,CAAC,MAAc,EAAE,SAAc;IAC1D,IAAI,SAAS,CAAC,SAAS,EAAE;QACrB,MAAM,OAAO,GAAG,SAAS,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC7D,MAAM,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAE/D,UAAU;QACV,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC9D,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC1D,mCAAmC;QACnC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,OAAO,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;QACtE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;QACtE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,WAAW,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;QAC9E,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,cAAc,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC;QACpF,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,YAAY,GAAG,IAAI,MAAM,EAAE,CAAC;QACnE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,mBAAmB,GAAG,MAAM,CAAC,yBAAyB,CAAC;QAC7E,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC;QACtC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,MAAM,CAAC,mBAAmB,CAAC;QAElE,WAAW;QACX,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAChE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;QACvE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,OAAO,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;QACtE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,WAAW,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;QAC9E,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,cAAc,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC;QACpF,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,YAAY,GAAG,IAAI,MAAM,EAAE,CAAC;QACnE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,mBAAmB,GAAG,MAAM,CAAC,yBAAyB,CAAC;QAC7E,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC;QACtC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,MAAM,CAAC,mBAAmB,CAAC;KACrE;AACL,CAAC", "sourcesContent": ["import type { Camera } from \"../camera\";\r\nimport { Matrix } from \"../../Maths/math.vector\";\r\nimport { Viewport } from \"../../Maths/math.viewport\";\r\n\r\n/**\r\n * @internal\r\n */\r\nexport function setWebVRRigMode(camera: Camera, rigParams: any) {\r\n    if (rigParams.vrDisplay) {\r\n        const leftEye = rigParams.vrDisplay.getEyeParameters(\"left\");\r\n        const rightEye = rigParams.vrDisplay.getEyeParameters(\"right\");\r\n\r\n        //Left eye\r\n        camera._rigCameras[0].viewport = new Viewport(0, 0, 0.5, 1.0);\r\n        camera._rigCameras[0].setCameraRigParameter(\"left\", true);\r\n        //leaving this for future reference\r\n        camera._rigCameras[0].setCameraRigParameter(\"specs\", rigParams.specs);\r\n        camera._rigCameras[0].setCameraRigParameter(\"eyeParameters\", leftEye);\r\n        camera._rigCameras[0].setCameraRigParameter(\"frameData\", rigParams.frameData);\r\n        camera._rigCameras[0].setCameraRigParameter(\"parentCamera\", rigParams.parentCamera);\r\n        camera._rigCameras[0]._cameraRigParams.vrWorkMatrix = new Matrix();\r\n        camera._rigCameras[0].getProjectionMatrix = camera._getWebVRProjectionMatrix;\r\n        camera._rigCameras[0].parent = camera;\r\n        camera._rigCameras[0]._getViewMatrix = camera._getWebVRViewMatrix;\r\n\r\n        //Right eye\r\n        camera._rigCameras[1].viewport = new Viewport(0.5, 0, 0.5, 1.0);\r\n        camera._rigCameras[1].setCameraRigParameter(\"eyeParameters\", rightEye);\r\n        camera._rigCameras[1].setCameraRigParameter(\"specs\", rigParams.specs);\r\n        camera._rigCameras[1].setCameraRigParameter(\"frameData\", rigParams.frameData);\r\n        camera._rigCameras[1].setCameraRigParameter(\"parentCamera\", rigParams.parentCamera);\r\n        camera._rigCameras[1]._cameraRigParams.vrWorkMatrix = new Matrix();\r\n        camera._rigCameras[1].getProjectionMatrix = camera._getWebVRProjectionMatrix;\r\n        camera._rigCameras[1].parent = camera;\r\n        camera._rigCameras[1]._getViewMatrix = camera._getWebVRViewMatrix;\r\n    }\r\n}\r\n"]}