{"version": 3, "file": "fadeInOutBehavior.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Behaviors/Meshes/fadeInOutBehavior.ts"], "names": [], "mappings": "AAOA;;GAEG;AACH,MAAM,OAAO,iBAAiB;IAqB1B;;;OAGG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,KAAK,CAAC,KAAa;QAC1B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;IAC9B,CAAC;IAUD;;OAEG;IACH;QA5CA;;WAEG;QACI,gBAAW,GAAG,CAAC,CAAC;QAEvB;;WAEG;QACI,iBAAY,GAAG,CAAC,CAAC;QAExB;;WAEG;QACI,eAAU,GAAG,GAAG,CAAC;QAExB;;WAEG;QACI,gBAAW,GAAG,GAAG,CAAC;QAejB,0BAAqB,GAAG,IAAI,GAAG,EAAE,CAAC;QAClC,aAAQ,GAAG,KAAK,CAAC;QACjB,gBAAW,GAAG,CAAC,CAAC;QAChB,eAAU,GAAmB,IAAI,CAAC;QAElC,WAAM,GAAW,CAAC,CAAC;QACnB,UAAK,GAAW,GAAG,CAAC;QA0EpB,YAAO,GAAG,GAAG,EAAE;YACnB,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC;gBAE7F,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;gBAEvF,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,CAAC,EAAE;oBAChC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;oBAC3C,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,EAAE;wBAC/B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC;wBAC9B,IAAI,CAAC,eAAe,EAAE,CAAC;wBACvB,OAAO;qBACV;iBACJ;qBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,CAAC,EAAE;oBACvC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;oBAC3C,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;wBACtB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;wBACrB,IAAI,CAAC,eAAe,EAAE,CAAC;wBACvB,OAAO;qBACV;iBACJ;gBAED,IAAI,CAAC,eAAe,EAAE,CAAC;aAC1B;QACL,CAAC,CAAC;IA7Fa,CAAC;IAEhB;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,IAAI,KAAI,CAAC;IAEhB;;;OAGG;IACI,MAAM,CAAC,SAAe;QACzB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IAC/C,CAAC;IACD;;OAEG;IACI,MAAM;QACT,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,SAAkB,IAAI;QAChC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;QAC5D,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC;QAEzD,6BAA6B;QAC7B,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,oFAAoF;QACpF,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC,EAAE;YAClH,OAAO;SACV;QAED,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,uFAAuF;YACvF,2BAA2B;YAC3B,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;SACrB;QAED,yGAAyG;QACzG,kDAAkD;QAClD,IAAI,IAAI,CAAC,UAAW,CAAC,UAAU,IAAI,CAAC,EAAE;YAClC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC;SACjC;aAAM,IAAI,IAAI,CAAC,UAAW,CAAC,UAAU,IAAI,CAAC,EAAE;YACzC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;SACxB;QACD,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;IA4BO,iBAAiB,CAAC,IAAkB,EAAE,KAAa;QACvD,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YAChC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,eAAe;;QACnB,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC/B,IAAI,CAAC,uBAAuB,GAAG,MAAA,IAAI,CAAC,UAAU,0CAAE,QAAQ,GAAG,wBAAwB,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACzG;IACL,CAAC;IAEO,eAAe;;QACnB,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,MAAA,IAAI,CAAC,UAAU,0CAAE,QAAQ,GAAG,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YAC1F,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;SACvC;IACL,CAAC;CACJ", "sourcesContent": ["import type { Behavior } from \"../behavior\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../../Meshes/mesh\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Observer } from \"core/Misc\";\r\nimport type { Scene } from \"core/scene\";\r\n\r\n/**\r\n * A behavior that when attached to a mesh will allow the mesh to fade in and out\r\n */\r\nexport class FadeInOutBehavior implements Behavior<Mesh> {\r\n    /**\r\n     * Time in milliseconds to delay before fading in (Default: 0)\r\n     */\r\n    public fadeInDelay = 0;\r\n\r\n    /**\r\n     * Time in milliseconds to delay before fading out (Default: 0)\r\n     */\r\n    public fadeOutDelay = 0;\r\n\r\n    /**\r\n     * Time in milliseconds for the mesh to fade in (Default: 300)\r\n     */\r\n    public fadeInTime = 300;\r\n\r\n    /**\r\n     * Time in milliseconds for the mesh to fade out (Default: 300)\r\n     */\r\n    public fadeOutTime = 300;\r\n\r\n    /**\r\n     * Time in milliseconds to delay before fading in (Default: 0)\r\n     * Will set both fade in and out delay to the same value\r\n     */\r\n    public get delay(): number {\r\n        return this.fadeInDelay;\r\n    }\r\n\r\n    public set delay(value: number) {\r\n        this.fadeInDelay = value;\r\n        this.fadeOutDelay = value;\r\n    }\r\n\r\n    private _millisecondsPerFrame = 1000 / 60;\r\n    private _hovered = false;\r\n    private _hoverValue = 0;\r\n    private _ownerNode: Nullable<Mesh> = null;\r\n    private _onBeforeRenderObserver: Nullable<Observer<Scene>> | undefined;\r\n    private _delay: number = 0;\r\n    private _time: number = 300;\r\n\r\n    /**\r\n     * Instantiates the FadeInOutBehavior\r\n     */\r\n    constructor() {}\r\n\r\n    /**\r\n     *  The name of the behavior\r\n     */\r\n    public get name(): string {\r\n        return \"FadeInOut\";\r\n    }\r\n\r\n    /**\r\n     *  Initializes the behavior\r\n     */\r\n    public init() {}\r\n\r\n    /**\r\n     * Attaches the fade behavior on the passed in mesh\r\n     * @param ownerNode The mesh that will be faded in/out once attached\r\n     */\r\n    public attach(ownerNode: Mesh): void {\r\n        this._ownerNode = ownerNode;\r\n        this._setAllVisibility(this._ownerNode, 0);\r\n    }\r\n    /**\r\n     *  Detaches the behavior from the mesh\r\n     */\r\n    public detach(): void {\r\n        this._ownerNode = null;\r\n    }\r\n\r\n    /**\r\n     * Triggers the mesh to begin fading in (or out)\r\n     * @param fadeIn if the object should fade in or out (true to fade in)\r\n     */\r\n    public fadeIn(fadeIn: boolean = true) {\r\n        this._delay = fadeIn ? this.fadeInDelay : this.fadeOutDelay;\r\n        this._time = fadeIn ? this.fadeInTime : this.fadeOutTime;\r\n\r\n        // Cancel any pending updates\r\n        this._detachObserver();\r\n\r\n        // If fading in and already visible or fading out and already not visible do nothing\r\n        if (this._ownerNode && ((fadeIn && this._ownerNode.visibility >= 1) || (!fadeIn && this._ownerNode.visibility <= 0))) {\r\n            return;\r\n        }\r\n\r\n        this._hovered = fadeIn;\r\n        if (!this._hovered) {\r\n            // Make the delay the negative of fadeout delay so the hoverValue is kept above 1 until\r\n            // fadeOutDelay has elapsed\r\n            this._delay *= -1;\r\n        }\r\n\r\n        // Reset the hoverValue.  This is necessary because we may have been fading out, e.g. but not yet reached\r\n        // the delay, so the hover value is greater than 1\r\n        if (this._ownerNode!.visibility >= 1) {\r\n            this._hoverValue = this._time;\r\n        } else if (this._ownerNode!.visibility <= 0) {\r\n            this._hoverValue = 0;\r\n        }\r\n        this._update();\r\n    }\r\n\r\n    /**\r\n     * Triggers the mesh to begin fading out\r\n     */\r\n    public fadeOut() {\r\n        this.fadeIn(false);\r\n    }\r\n\r\n    private _update = () => {\r\n        if (this._ownerNode) {\r\n            this._hoverValue += this._hovered ? this._millisecondsPerFrame : -this._millisecondsPerFrame;\r\n\r\n            this._setAllVisibility(this._ownerNode, (this._hoverValue - this._delay) / this._time);\r\n\r\n            if (this._ownerNode.visibility > 1) {\r\n                this._setAllVisibility(this._ownerNode, 1);\r\n                if (this._hoverValue > this._time) {\r\n                    this._hoverValue = this._time;\r\n                    this._detachObserver();\r\n                    return;\r\n                }\r\n            } else if (this._ownerNode.visibility < 0) {\r\n                this._setAllVisibility(this._ownerNode, 0);\r\n                if (this._hoverValue < 0) {\r\n                    this._hoverValue = 0;\r\n                    this._detachObserver();\r\n                    return;\r\n                }\r\n            }\r\n\r\n            this._attachObserver();\r\n        }\r\n    };\r\n\r\n    private _setAllVisibility(mesh: AbstractMesh, value: number) {\r\n        mesh.visibility = value;\r\n        mesh.getChildMeshes().forEach((c) => {\r\n            this._setAllVisibility(c, value);\r\n        });\r\n    }\r\n\r\n    private _attachObserver() {\r\n        if (!this._onBeforeRenderObserver) {\r\n            this._onBeforeRenderObserver = this._ownerNode?.getScene().onBeforeRenderObservable.add(this._update);\r\n        }\r\n    }\r\n\r\n    private _detachObserver() {\r\n        if (this._onBeforeRenderObserver) {\r\n            this._ownerNode?.getScene().onBeforeRenderObservable.remove(this._onBeforeRenderObserver);\r\n            this._onBeforeRenderObserver = null;\r\n        }\r\n    }\r\n}\r\n"]}