{"version": 3, "file": "autoRotationBehavior.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Behaviors/Cameras/autoRotationBehavior.ts"], "names": [], "mappings": "AAMA,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AACzD,OAAO,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;AAErD;;;GAGG;AACH,MAAM,OAAO,oBAAoB;IAAjC;QAQY,wBAAmB,GAAG,KAAK,CAAC;QAC5B,uBAAkB,GAAG,IAAI,CAAC;QAC1B,0BAAqB,GAAG,IAAI,CAAC;QAC7B,4BAAuB,GAAG,IAAI,CAAC;QAEhC,gBAAW,GAAqB,IAAI,CAAC;QAqEpC,mBAAc,GAAG,KAAK,CAAC;QACvB,mBAAc,GAAqB,IAAI,CAAC;QACxC,yBAAoB,GAAG,CAAC,QAAQ,CAAC;QACjC,yBAAoB,GAAW,CAAC,CAAC;QAoGjC,qBAAgB,GAAG,CAAC,CAAC;IAwCjC,CAAC;IAhOG;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,cAAc,CAAC;IAC1B,CAAC;IASD;;OAEG;IACH,IAAW,kBAAkB,CAAC,IAAa;QACvC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAW,iBAAiB,CAAC,KAAa;QACtC,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAW,oBAAoB,CAAC,IAAY;QACxC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,IAAW,sBAAsB,CAAC,IAAY;QAC1C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAW,sBAAsB;QAC7B,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;IACnD,CAAC;IAWD;;OAEG;IACI,IAAI;QACP,aAAa;IACjB,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,MAAuB;QACjC,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;QAE9C,IAAI,CAAC,+BAA+B,GAAG,KAAK,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,EAAE;YACvF,IAAI,cAAc,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW,EAAE;gBACvD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC3B,OAAO;aACV;YAED,IAAI,cAAc,CAAC,IAAI,KAAK,iBAAiB,CAAC,SAAS,EAAE;gBACrD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;aAC/B;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,2BAA2B,GAAG,MAAM,CAAC,4BAA4B,CAAC,GAAG,CAAC,GAAG,EAAE;YAC5E,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;gBAC1B,OAAO;aACV;YACD,MAAM,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC;YAC9B,IAAI,EAAE,GAAG,CAAC,CAAC;YACX,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,EAAE;gBAC7B,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC;aAClC;YACD,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC;YAE1B,qGAAqG;YACrG,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAE7B,MAAM,cAAc,GAAG,GAAG,GAAG,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,qBAAqB,CAAC;YACpF,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACtF,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;YAE5D,yCAAyC;YACzC,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,IAAI,CAAC,eAAe,CAAC,KAAK,IAAI,IAAI,CAAC,oBAAoB,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;aACzE;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,OAAO;SACV;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;QAE9C,IAAI,IAAI,CAAC,+BAA+B,EAAE;YACtC,KAAK,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;SAC7E;QAED,IAAI,CAAC,eAAe,CAAC,4BAA4B,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAC3F,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAChC,CAAC;IAED;;;OAGG;IACI,wBAAwB,CAAC,UAAmB;QAC/C,IAAI,CAAC,oBAAoB,GAAG,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,aAAa,CAAC,GAAG,CAAC;IAChE,CAAC;IAED;;;OAGG;IACK,iBAAiB;QACrB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,WAAW,EAAE;YAC1C,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,OAAO,CAAC;SAC5E;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACK,cAAc;QAClB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,OAAO,KAAK,CAAC;SAChB;QACD,OAAO,IAAI,CAAC,eAAe,CAAC,oBAAoB,KAAK,CAAC,CAAC;IAC3D,CAAC;IAGO,kCAAkC;QACtC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,eAAe,GAAG,KAAK,CAAC;QAC5B,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,IAAI,IAAI,CAAC,eAAe,CAAC,oBAAoB,KAAK,CAAC,EAAE;YAC1G,eAAe,GAAG,IAAI,CAAC;SAC1B;QAED,gGAAgG;QAChG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;QACpD,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;IAC9E,CAAC;IAED;;OAEG;IACK,qBAAqB;QACzB,IAAI,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,kCAAkC,EAAE,EAAE;YACpE,IAAI,CAAC,oBAAoB,GAAG,aAAa,CAAC,GAAG,CAAC;SACjD;IACL,CAAC;IAED,QAAQ;IACA,aAAa;QACjB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,CACH,IAAI,CAAC,eAAe,CAAC,mBAAmB,KAAK,CAAC;YAC9C,IAAI,CAAC,eAAe,CAAC,kBAAkB,KAAK,CAAC;YAC7C,IAAI,CAAC,eAAe,CAAC,oBAAoB,KAAK,CAAC;YAC/C,IAAI,CAAC,eAAe,CAAC,gBAAgB,KAAK,CAAC;YAC3C,IAAI,CAAC,eAAe,CAAC,gBAAgB,KAAK,CAAC;YAC3C,IAAI,CAAC,cAAc,CACtB,CAAC;IACN,CAAC;CACJ", "sourcesContent": ["import type { Behavior } from \"../../Behaviors/behavior\";\r\nimport type { Camera } from \"../../Cameras/camera\";\r\nimport type { ArcRotateCamera } from \"../../Cameras/arcRotateCamera\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { PointerInfoPre } from \"../../Events/pointerEvents\";\r\nimport { PointerEventTypes } from \"../../Events/pointerEvents\";\r\nimport { PrecisionDate } from \"../../Misc/precisionDate\";\r\nimport { Epsilon } from \"../../Maths/math.constants\";\r\n\r\n/**\r\n * The autoRotation behavior (AutoRotationBehavior) is designed to create a smooth rotation of an ArcRotateCamera when there is no user interaction.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors/cameraBehaviors#autorotation-behavior\r\n */\r\nexport class AutoRotationBehavior implements Behavior<ArcRotateCamera> {\r\n    /**\r\n     * Gets the name of the behavior.\r\n     */\r\n    public get name(): string {\r\n        return \"AutoRotation\";\r\n    }\r\n\r\n    private _zoomStopsAnimation = false;\r\n    private _idleRotationSpeed = 0.05;\r\n    private _idleRotationWaitTime = 2000;\r\n    private _idleRotationSpinupTime = 2000;\r\n\r\n    public targetAlpha: Nullable<number> = null;\r\n\r\n    /**\r\n     * Sets the flag that indicates if user zooming should stop animation.\r\n     */\r\n    public set zoomStopsAnimation(flag: boolean) {\r\n        this._zoomStopsAnimation = flag;\r\n    }\r\n\r\n    /**\r\n     * Gets the flag that indicates if user zooming should stop animation.\r\n     */\r\n    public get zoomStopsAnimation(): boolean {\r\n        return this._zoomStopsAnimation;\r\n    }\r\n\r\n    /**\r\n     * Sets the default speed at which the camera rotates around the model.\r\n     */\r\n    public set idleRotationSpeed(speed: number) {\r\n        this._idleRotationSpeed = speed;\r\n    }\r\n\r\n    /**\r\n     * Gets the default speed at which the camera rotates around the model.\r\n     */\r\n    public get idleRotationSpeed() {\r\n        return this._idleRotationSpeed;\r\n    }\r\n\r\n    /**\r\n     * Sets the time (in milliseconds) to wait after user interaction before the camera starts rotating.\r\n     */\r\n    public set idleRotationWaitTime(time: number) {\r\n        this._idleRotationWaitTime = time;\r\n    }\r\n\r\n    /**\r\n     * Gets the time (milliseconds) to wait after user interaction before the camera starts rotating.\r\n     */\r\n    public get idleRotationWaitTime() {\r\n        return this._idleRotationWaitTime;\r\n    }\r\n\r\n    /**\r\n     * Sets the time (milliseconds) to take to spin up to the full idle rotation speed.\r\n     */\r\n    public set idleRotationSpinupTime(time: number) {\r\n        this._idleRotationSpinupTime = time;\r\n    }\r\n\r\n    /**\r\n     * Gets the time (milliseconds) to take to spin up to the full idle rotation speed.\r\n     */\r\n    public get idleRotationSpinupTime() {\r\n        return this._idleRotationSpinupTime;\r\n    }\r\n\r\n    /**\r\n     * Gets a value indicating if the camera is currently rotating because of this behavior\r\n     */\r\n    public get rotationInProgress(): boolean {\r\n        return Math.abs(this._cameraRotationSpeed) > 0;\r\n    }\r\n\r\n    // Default behavior functions\r\n    private _onPrePointerObservableObserver: Nullable<Observer<PointerInfoPre>>;\r\n    private _onAfterCheckInputsObserver: Nullable<Observer<Camera>>;\r\n    private _attachedCamera: Nullable<ArcRotateCamera>;\r\n    private _isPointerDown = false;\r\n    private _lastFrameTime: Nullable<number> = null;\r\n    private _lastInteractionTime = -Infinity;\r\n    private _cameraRotationSpeed: number = 0;\r\n\r\n    /**\r\n     * Initializes the behavior.\r\n     */\r\n    public init(): void {\r\n        // Do nothing\r\n    }\r\n\r\n    /**\r\n     * Attaches the behavior to its arc rotate camera.\r\n     * @param camera Defines the camera to attach the behavior to\r\n     */\r\n    public attach(camera: ArcRotateCamera): void {\r\n        this._attachedCamera = camera;\r\n        const scene = this._attachedCamera.getScene();\r\n\r\n        this._onPrePointerObservableObserver = scene.onPrePointerObservable.add((pointerInfoPre) => {\r\n            if (pointerInfoPre.type === PointerEventTypes.POINTERDOWN) {\r\n                this._isPointerDown = true;\r\n                return;\r\n            }\r\n\r\n            if (pointerInfoPre.type === PointerEventTypes.POINTERUP) {\r\n                this._isPointerDown = false;\r\n            }\r\n        });\r\n\r\n        this._onAfterCheckInputsObserver = camera.onAfterCheckInputsObservable.add(() => {\r\n            if (this._reachTargetAlpha()) {\r\n                return;\r\n            }\r\n            const now = PrecisionDate.Now;\r\n            let dt = 0;\r\n            if (this._lastFrameTime != null) {\r\n                dt = now - this._lastFrameTime;\r\n            }\r\n            this._lastFrameTime = now;\r\n\r\n            // Stop the animation if there is user interaction and the animation should stop for this interaction\r\n            this._applyUserInteraction();\r\n\r\n            const timeToRotation = now - this._lastInteractionTime - this._idleRotationWaitTime;\r\n            const scale = Math.max(Math.min(timeToRotation / this._idleRotationSpinupTime, 1), 0);\r\n            this._cameraRotationSpeed = this._idleRotationSpeed * scale;\r\n\r\n            // Step camera rotation by rotation speed\r\n            if (this._attachedCamera) {\r\n                this._attachedCamera.alpha -= this._cameraRotationSpeed * (dt / 1000);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Detaches the behavior from its current arc rotate camera.\r\n     */\r\n    public detach(): void {\r\n        if (!this._attachedCamera) {\r\n            return;\r\n        }\r\n        const scene = this._attachedCamera.getScene();\r\n\r\n        if (this._onPrePointerObservableObserver) {\r\n            scene.onPrePointerObservable.remove(this._onPrePointerObservableObserver);\r\n        }\r\n\r\n        this._attachedCamera.onAfterCheckInputsObservable.remove(this._onAfterCheckInputsObserver);\r\n        this._attachedCamera = null;\r\n    }\r\n\r\n    /**\r\n     * Force-reset the last interaction time\r\n     * @param customTime an optional time that will be used instead of the current last interaction time. For example `Date.now()`\r\n     */\r\n    public resetLastInteractionTime(customTime?: number): void {\r\n        this._lastInteractionTime = customTime ?? PrecisionDate.Now;\r\n    }\r\n\r\n    /**\r\n     * Returns true if camera alpha reaches the target alpha\r\n     * @returns true if camera alpha reaches the target alpha\r\n     */\r\n    private _reachTargetAlpha(): boolean {\r\n        if (this._attachedCamera && this.targetAlpha) {\r\n            return Math.abs(this._attachedCamera.alpha - this.targetAlpha) < Epsilon;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Returns true if user is scrolling.\r\n     * @returns true if user is scrolling.\r\n     */\r\n    private _userIsZooming(): boolean {\r\n        if (!this._attachedCamera) {\r\n            return false;\r\n        }\r\n        return this._attachedCamera.inertialRadiusOffset !== 0;\r\n    }\r\n\r\n    private _lastFrameRadius = 0;\r\n    private _shouldAnimationStopForInteraction(): boolean {\r\n        if (!this._attachedCamera) {\r\n            return false;\r\n        }\r\n\r\n        let zoomHasHitLimit = false;\r\n        if (this._lastFrameRadius === this._attachedCamera.radius && this._attachedCamera.inertialRadiusOffset !== 0) {\r\n            zoomHasHitLimit = true;\r\n        }\r\n\r\n        // Update the record of previous radius - works as an approx. indicator of hitting radius limits\r\n        this._lastFrameRadius = this._attachedCamera.radius;\r\n        return this._zoomStopsAnimation ? zoomHasHitLimit : this._userIsZooming();\r\n    }\r\n\r\n    /**\r\n     *  Applies any current user interaction to the camera. Takes into account maximum alpha rotation.\r\n     */\r\n    private _applyUserInteraction(): void {\r\n        if (this._userIsMoving() && !this._shouldAnimationStopForInteraction()) {\r\n            this._lastInteractionTime = PrecisionDate.Now;\r\n        }\r\n    }\r\n\r\n    // Tools\r\n    private _userIsMoving(): boolean {\r\n        if (!this._attachedCamera) {\r\n            return false;\r\n        }\r\n\r\n        return (\r\n            this._attachedCamera.inertialAlphaOffset !== 0 ||\r\n            this._attachedCamera.inertialBetaOffset !== 0 ||\r\n            this._attachedCamera.inertialRadiusOffset !== 0 ||\r\n            this._attachedCamera.inertialPanningX !== 0 ||\r\n            this._attachedCamera.inertialPanningY !== 0 ||\r\n            this._isPointerDown\r\n        );\r\n    }\r\n}\r\n"]}