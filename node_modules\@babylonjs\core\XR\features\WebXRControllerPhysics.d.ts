import { WebXRAbstractFeature } from "./WebXRAbstractFeature";
import type { WebXRInputSource } from "../webXRInputSource";
import { PhysicsImpostor } from "../../Physics/v1/physicsImpostor";
import type { WebXRInput } from "../webXRInput";
import type { WebXRSessionManager } from "../webXRSessionManager";
import type { Nullable } from "../../types";
/**
 * Options for the controller physics feature
 */
export declare class IWebXRControllerPhysicsOptions {
    /**
     * Should the headset get its own impostor
     */
    enableHeadsetImpostor?: boolean;
    /**
     * Optional parameters for the headset impostor
     */
    headsetImpostorParams?: {
        /**
         * The type of impostor to create. Default is sphere
         */
        impostorType: number;
        /**
         * the size of the impostor. Defaults to 10cm
         */
        impostorSize?: number | {
            width: number;
            height: number;
            depth: number;
        };
        /**
         * Friction definitions
         */
        friction?: number;
        /**
         * Restitution
         */
        restitution?: number;
    };
    /**
     * The physics properties of the future impostors
     */
    physicsProperties?: {
        /**
         * If set to true, a mesh impostor will be created when the controller mesh was loaded
         * Note that this requires a physics engine that supports mesh impostors!
         */
        useControllerMesh?: boolean;
        /**
         * The type of impostor to create. Default is sphere
         */
        impostorType?: number;
        /**
         * the size of the impostor. Defaults to 10cm
         */
        impostorSize?: number | {
            width: number;
            height: number;
            depth: number;
        };
        /**
         * Friction definitions
         */
        friction?: number;
        /**
         * Restitution
         */
        restitution?: number;
    };
    /**
     * the xr input to use with this pointer selection
     */
    xrInput: WebXRInput;
}
/**
 * Add physics impostor to your webxr controllers,
 * including naive calculation of their linear and angular velocity
 */
export declare class WebXRControllerPhysics extends WebXRAbstractFeature {
    private readonly _options;
    private _attachController;
    private _createPhysicsImpostor;
    private _controllers;
    private _debugMode;
    private _delta;
    private _headsetImpostor?;
    private _headsetMesh?;
    private _lastTimestamp;
    private _tmpQuaternion;
    private _tmpVector;
    /**
     * The module's name
     */
    static readonly Name = "xr-physics-controller";
    /**
     * The (Babylon) version of this module.
     * This is an integer representing the implementation version.
     * This number does not correspond to the webxr specs version
     */
    static readonly Version = 1;
    /**
     * Construct a new Controller Physics Feature
     * @param _xrSessionManager the corresponding xr session manager
     * @param _options options to create this feature with
     */
    constructor(_xrSessionManager: WebXRSessionManager, _options: IWebXRControllerPhysicsOptions);
    /**
     * @internal
     * enable debugging - will show console outputs and the impostor mesh
     */
    _enablePhysicsDebug(): void;
    /**
     * Manually add a controller (if no xrInput was provided or physics engine was not enabled)
     * @param xrController the controller to add
     */
    addController(xrController: WebXRInputSource): void;
    /**
     * attach this feature
     * Will usually be called by the features manager
     *
     * @returns true if successful.
     */
    attach(): boolean;
    /**
     * detach this feature.
     * Will usually be called by the features manager
     *
     * @returns true if successful.
     */
    detach(): boolean;
    /**
     * Get the headset impostor, if enabled
     * @returns the impostor
     */
    getHeadsetImpostor(): PhysicsImpostor | undefined;
    /**
     * Get the physics impostor of a specific controller.
     * The impostor is not attached to a mesh because a mesh for each controller is not obligatory
     * @param controller the controller or the controller id of which to get the impostor
     * @returns the impostor or null
     */
    getImpostorForController(controller: WebXRInputSource | string): Nullable<PhysicsImpostor>;
    /**
     * Update the physics properties provided in the constructor
     * @param newProperties the new properties object
     * @param newProperties.impostorType
     * @param newProperties.impostorSize
     * @param newProperties.friction
     * @param newProperties.restitution
     */
    setPhysicsProperties(newProperties: {
        impostorType?: number;
        impostorSize?: number | {
            width: number;
            height: number;
            depth: number;
        };
        friction?: number;
        restitution?: number;
    }): void;
    protected _onXRFrame(_xrFrame: any): void;
    private _detachController;
}
