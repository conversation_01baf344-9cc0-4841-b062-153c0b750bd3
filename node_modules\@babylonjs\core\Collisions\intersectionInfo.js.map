{"version": 3, "file": "intersectionInfo.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Collisions/intersectionInfo.ts"], "names": [], "mappings": "AAEA;;GAEG;AACH,MAAM,OAAO,gBAAgB;IAIzB,YAAmB,EAAoB,EAAS,EAAoB,EAAS,QAAgB;QAA1E,OAAE,GAAF,EAAE,CAAkB;QAAS,OAAE,GAAF,EAAE,CAAkB;QAAS,aAAQ,GAAR,QAAQ,CAAQ;QAHtF,WAAM,GAAG,CAAC,CAAC;QACX,cAAS,GAAG,CAAC,CAAC;IAE2E,CAAC;CACpG", "sourcesContent": ["import type { Nullable } from \"../types\";\r\n\r\n/**\r\n * @internal\r\n */\r\nexport class IntersectionInfo {\r\n    public faceId = 0;\r\n    public subMeshId = 0;\r\n\r\n    constructor(public bu: Nullable<number>, public bv: Nullable<number>, public distance: number) {}\r\n}\r\n"]}